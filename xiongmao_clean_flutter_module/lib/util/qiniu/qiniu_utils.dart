import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:qiniu_flutter_sdk/qiniu_flutter_sdk.dart';
import '../../generated/qi_niu_info_bean_entity.dart';
import '../../net/dio_utils.dart';
import '../../net/http_api.dart';
import '../toast_utils.dart';

typedef StatusCallback = void Function(StorageStatus status);
typedef ProgressCallback = void Function(double percent);
typedef SendProgressCallback = void Function(double percent);
typedef SuccessCallback = void Function(String? keyUrl, String? hashUrl);
typedef ErrorCallback = void Function(StorageError? error);

class QiNiuUtils {
  static const String sQiNiuHostName = "https://pccmedia.jiazhengye.cn/";

  // 用户输入的文件名
  String? _key;

  // 用户输入的 partSize
  final int _partSize = 4;

  // 用户输入的 token
  String? _token;

  /// storage 实例
  final Storage _storage = Storage();

  /// 当前选择的文件
  final String? _selectedFile;

  // 控制器，可以用于取消任务、获取上述的状态，进度等信息
  PutController? _putController;
  final List<Function> _disposerList = [];
  StatusCallback? _statusCallback; // 状态变化回调
  ProgressCallback? _progressCallback; // 任务进度
  SendProgressCallback? _sendProgressCallback; // 实际进度
  SuccessCallback? _successCallback; // 成功回调
  ErrorCallback? _errorCallback; // 失败回调

  QiNiuUtils(this._selectedFile, {String? key, StatusCallback? statusCallback, ProgressCallback? progressCallback, SendProgressCallback? sendProgressCallback, SuccessCallback? successCallback, ErrorCallback? errorCallback}) {
    _key = key;
    _statusCallback = statusCallback;
    _progressCallback = progressCallback;
    _sendProgressCallback = sendProgressCallback;
    _successCallback = successCallback;
    _errorCallback = errorCallback;
  }

  void _addDisposer(Function disposer) {
    _disposerList.add(disposer);
  }

  // 如果需要取消则在页面 dispose 时需要调用该方法
  void dispose() {
    for (var disposer in _disposerList) {
      try {
        disposer.call();
      } catch (error) {
        rethrow;
      }
    }
  }

  void _onStatus(StorageStatus status) {
    _printToConsole('状态变化: 当前任务状态：${status.toString()}');
    _statusCallback?.call(status);
  }

  void _onProgress(double percent) {
    _printToConsole('任务进度变化：进度：${percent.toStringAsFixed(4)}');
    _progressCallback?.call(percent);
  }

  void _onSendProgress(double percent) {
    _printToConsole('实际发送变化：进度：${percent.toStringAsFixed(4)}');
    _sendProgressCallback?.call(percent);
  }

  void _printToConsole(String? message) {
    if (message == null || message == '') {
      return;
    }
    debugPrint(message);
  }

  void upload() {
    DioUtils.instance.requestNetwork<QiNiuInfoBeanEntity>(
      Method.get,
      HttpApi.GET_QINIU_TOKEN,
      onSuccess: (data) {
        if (data == null || data.token == null || data.token!.isEmpty) {
          Toast.show("七牛token获取失败");
          return;
        }
        _token = data.token;
        _startUpload();
      },
      onError: (code, msg) {
        Toast.show(msg);
      },
    );
  }

  void _startUpload() {
    _printToConsole('创建 PutController');
    _putController = PutController();

    _printToConsole('添加实际发送进度订阅');
    _addDisposer(_putController!.addSendProgressListener(_onSendProgress));

    _printToConsole('添加任务进度订阅');
    _addDisposer(_putController!.addProgressListener(_onProgress));

    _printToConsole('添加状态订阅');
    _addDisposer(_putController!.addStatusListener(_onStatus));

    var usedToken = _token;

    if (_token == null || _token!.isEmpty) {
      _printToConsole('token 获取异常');
      return;
    }

    if (usedToken == null || usedToken == '') {
      _printToConsole('token 不能为空');
      return;
    }

    if (_selectedFile == null || _selectedFile!.isEmpty) {
      _printToConsole('请选择文件');
      return;
    }

    _printToConsole('开始上传文件');

    final putOptions = PutOptions(
      key: _key,
      partSize: _partSize,
      controller: _putController,
    );
    Future<PutResponse> upload = _storage.putFile(
      File(_selectedFile!),
      usedToken,
      options: putOptions,
    );

    upload
      ..then((PutResponse response) {
        _successCallback?.call(_getKeyUrl(response.key), _getHashUrl(response.hash));
        _printToConsole('上传已完成: 原始响应数据: ${jsonEncode(response.rawData)}');
        _printToConsole('------------------------');
      })
      ..catchError((dynamic error) {
        if (error is StorageError) {
          switch (error.type) {
            case StorageErrorType.CONNECT_TIMEOUT:
              _printToConsole('发生错误: 连接超时');
              break;
            case StorageErrorType.SEND_TIMEOUT:
              _printToConsole('发生错误: 发送数据超时');
              break;
            case StorageErrorType.RECEIVE_TIMEOUT:
              _printToConsole('发生错误: 响应数据超时');
              break;
            case StorageErrorType.RESPONSE:
              _printToConsole('发生错误: ${error.message}');
              break;
            case StorageErrorType.CANCEL:
              _printToConsole('发生错误: 请求取消');
              break;
            case StorageErrorType.UNKNOWN:
              _printToConsole('发生错误: 未知错误');
              break;
            case StorageErrorType.NO_AVAILABLE_HOST:
              _printToConsole('发生错误: 无可用 Host');
              break;
            case StorageErrorType.IN_PROGRESS:
              _printToConsole('发生错误: 已在队列中');
              break;
          }
          _errorCallback?.call(error);
        } else {
          _printToConsole('发生错误: ${error.toString()}');
          _errorCallback?.call(null);
        }

        _printToConsole('------------------------');
      });
  }

  String _getKeyUrl(String? key) {
    if (key == null || key.isEmpty) {
      return "";
    }
    return sQiNiuHostName + key;
  }

  String _getHashUrl(String? hash) {
    if (hash == null || hash.isEmpty) {
      return "";
    }
    return sQiNiuHostName + hash;
  }
}
