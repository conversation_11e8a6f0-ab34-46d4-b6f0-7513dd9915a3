import 'package:flutter/services.dart';

class DecimalTextInputFormatter extends TextInputFormatter {
  final int decimalRange;

  DecimalTextInputFormatter({required this.decimalRange});

  @override
  TextEditingValue formatEditUpdate(
      TextEditingValue oldValue, TextEditingValue newValue) {
    // 允许空字符串
    if (newValue.text.isEmpty) {
      return newValue;
    }

    // 检查是否为数字或小数点
    String newText = newValue.text.replaceAll(RegExp(r'[^0-9.]'), '');

    // 检查是否只有一个小数点
    if (newText.contains('.') && newText.split('.').length > 2) {
      return oldValue;
    }


    // 检查小数点后的位数
    if (newText.contains('.')) {
      String decimalPart = newText.split('.')[1];
      if (decimalPart.length > decimalRange) {
        return oldValue;
      }
    }

    // 防止在 '0' 后面添加其他数字（除非有小数点）
    if (newText.startsWith('0') && newText.length > 1 && !newText.contains('.')) {
      return oldValue;
    }

    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}