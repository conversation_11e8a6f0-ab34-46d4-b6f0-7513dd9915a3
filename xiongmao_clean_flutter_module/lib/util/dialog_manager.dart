import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

class DialogManager {
  static bool _isDialogShowing = false;

  static void showConfirmDialog({
    required BuildContext context,
    required String title,
    required String cancel,
    required String confirm,
    required String message,
    required VoidCallback onConfirm,
    required VoidCallback onCancel,
  }) {
    if (_isDialogShowing) return; // Avoid showing multiple dialogs at once
    _isDialogShowing = true;

    BrnDialogManager.showConfirmDialog(
      context,
      title: title,
      cancel: cancel,
      confirm: confirm,
      message: message,
      barrierDismissible: false,
      onConfirm: () {
        Navigator.of(context, rootNavigator: true).pop();
        _isDialogShowing = false;
        onConfirm();
      },
      onCancel: () {
        Navigator.of(context, rootNavigator: true).pop();
        _isDialogShowing = false;
        onCancel();
      },
    );
  }

  static void dismissAllDialogs(BuildContext context) {
    if (_isDialogShowing) {
      _isDialogShowing = false;
      Navigator.of(context, rootNavigator: true).pop();
    }
  }

  static bool hasOpenDialogs() {
    return _isDialogShowing;
  }

  static setDialogShowing(bool isDialogShowing) {
    _isDialogShowing = isDialogShowing;
  }
}
