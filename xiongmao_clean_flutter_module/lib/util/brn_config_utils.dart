import 'dart:ui';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

import '../res/colors.dart';

/**
 * bruno 贝壳框架的全局配置
 * 地址：https://bruno.ke.com/page/guide/theme
 */
class BrnConfigUtils {
  //q全部默认配置
  static BrnAllThemeConfig defaultAllConfig = BrnAllThemeConfig(
    //全局的默认配置
    commonConfig: defaultCommonConfig,
    //标题栏的默认配置
    appBarConfig: defaultAppBarConfig,
    // dialog 的配置
    dialogConfig: defaultDialogConfig,
    // 弹窗样式
    pickerConfig: defaultPickerConfig,
    formItemConfig: defaultFormItemConfig,
  );

  //全局配置
  static BrnCommonConfig defaultCommonConfig = BrnCommonConfig(
    //品牌色
    brandPrimary: Colours.base_primary,
    //字体大小
    fontSizeBase: 14,
    //标题字号
    fontSizeHead: 14,
    //文本字号
    fontSizeBebas: 14,
    //字标题字号
    fontSizeSubHead: 14,
    //辅助字体
    fontSizeCaption: 14,
    //页面大标题
    fontSizeHeadLg: 14,
  );

  //appbar的设置
  static BrnAppBarConfig defaultAppBarConfig = BrnAppBarConfig(
    titleStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_title, fontWeight: FontWeight.normal),
    backgroundColor: Colors.white,
  );

  //Dialog配置
  static BrnDialogConfig defaultDialogConfig = BrnDialogConfig(
    radius: 0.0,
    titleTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_title, fontWeight: FontWeight.normal),
  );

  //Dialog配置
  static BrnPickerConfig defaultPickerConfig = BrnPickerConfig(
    titleTextStyle: BrnTextStyle(fontSize: 12, color: Colours.base_primary_text_body, fontWeight: FontWeight.normal),
    confirmTextStyle: BrnTextStyle(fontSize: 14, color: Colours.base_primary),
    cancelTextStyle: BrnTextStyle(fontSize: 14, color: Colours.base_primary),
    cornerRadius: 0,
  );

  //表单的样式
  static BrnFormItemConfig defaultFormItemConfig = BrnFormItemConfig(
    titleTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_title, fontWeight: FontWeight.normal),
    contentTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_title, fontWeight: FontWeight.normal),
    hintTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_caption, fontWeight: FontWeight.normal),
    tipsTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_caption, fontWeight: FontWeight.normal),
    disableTextStyle: BrnTextStyle(fontSize: 16, color: Colours.base_primary_text_caption, fontWeight: FontWeight.normal),
    // inputFormatters: [
    //   LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
    // ],
  );
}
