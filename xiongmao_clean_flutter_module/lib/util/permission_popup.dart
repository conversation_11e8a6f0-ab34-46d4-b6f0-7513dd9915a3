import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

///这是一个权限提醒的弹窗
class PermissionPopup {
  static OverlayEntry? _entry;

  static void show(BuildContext context, String message) {
    if (_entry != null) {
      // 如果弹窗已经存在，先移除
      _entry!.remove();
    }

    _entry = OverlayEntry(
      builder: (context) => Positioned(
        top: 30, // 控制弹出位置
        left: 10,
        right: 10,
        child: Material(
          color: Colors.transparent,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 8,
                  offset: Offset(0, 2),
                ),
              ],
            ),
            child: Stack(
              children: [
                Positioned(
                  right: 0,
                  child: LoadImage(
                    'common/icon_safe_bg',
                    width: 74,
                    height: 74,
                    fit: BoxFit.fill,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CommonUtils.getSimpleText('权限说明', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                          Gaps.hGap2,
                          LoadImage(
                            'common/icon_safe',
                            width: 17,
                            height: 17,
                          ),
                        ],
                      ),
                      Gaps.vGap4,
                      CommonUtils.getSimpleText(message, 14, Colours.base_primary_text_title),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    // 插入到 Overlay 中
    Overlay.of(context).insert(_entry!);

    // 可选：设置一个定时器来自动关闭弹窗
    // Future.delayed(Duration(seconds: 5), () {
    //   if (_entry != null) {
    //     _entry!.remove();
    //     _entry = null; // 清空引用
    //   }
    // });
  }

  static void close() {
    if (_entry != null) {
      _entry!.remove();
      _entry = null; // 清空引用
    }
  }

  static void closeDelay({Duration delayDuration = const Duration(milliseconds: 300)}) {
    if (_entry != null) {
      Future.delayed(delayDuration, () {
        _entry!.remove();
        _entry = null; // 清空引用
      });
    }
  }
}
