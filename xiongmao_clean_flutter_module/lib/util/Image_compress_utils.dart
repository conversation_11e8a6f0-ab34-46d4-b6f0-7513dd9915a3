import 'dart:io';

import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart' as path_provider;

/// 图片压缩
class ImageCompressUtils {
  static Future<XFile?> imageCompressAndGetFile(String path) async {
    var dir = await path_provider.getTemporaryDirectory();

    var targetPath = "${dir.absolute.path}/${DateTime.now().millisecondsSinceEpoch}.jpg";

    var result = await FlutterImageCompress.compressAndGetFile(
      path,
      targetPath,
      quality: 88,
      rotate: 0,
    );
    print(result?.path);
    return result;
  }
}
