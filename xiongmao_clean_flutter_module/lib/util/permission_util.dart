import 'package:device_info_plus/device_info_plus.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/util/permission_popup.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import 'common_utils.dart';
import 'log_utils.dart';

_PermissionUtil permissionUtil = _PermissionUtil._();

class _PermissionUtil {
  var requesting = false;
  var hadRequested = false;

  _PermissionUtil._();

  void requestPermission(Permission permission, {String? tipMsg, RequestSuccessFun? requestSuccessFun, RequestStatusFun? requestStatusFun, RequestFailFun? requestFailFun}) async {
    MyLog.e("msg---requesting-----requesting---" + requesting.toString());
    // if (requesting) {
    //   return;
    // }
    requesting = true;

    hadRequested = SpUtil.getBool(permission.toString(), defValue: false) ?? false;

    if (defaultTargetPlatform == TargetPlatform.iOS) {
      MyLog.e("msg---permission-----permission---" + permission.toString());
      _requestIosPermission(permission, tipMsg: tipMsg, requestStatusFun: requestStatusFun, requestSuccessFun: requestSuccessFun, requestFailFun: requestFailFun);
    } else {
      MyLog.e("msg---permission--_requestAndPermission---permission---" + permission.toString());
      _requestAndPermission(permission, tipMsg: tipMsg, requestStatusFun: requestStatusFun, requestSuccessFun: requestSuccessFun, requestFailFun: requestFailFun);
    }
  }

  void _requestIosPermission(Permission permission, {String? tipMsg, RequestSuccessFun? requestSuccessFun, RequestStatusFun? requestStatusFun, RequestFailFun? requestFailFun}) async {
    PermissionStatus status = await permission.status;
    requestStatusFun?.call(status);
    MyLog.e("PermissionStatus = ${status.toString()}");

    if (status == PermissionStatus.granted) {
      requestSuccessFun?.call();
      requesting = false;
      _removePermissionPopup(); // 关闭说明窗
    } else if (status == PermissionStatus.permanentlyDenied) {
      CommonUtils.showGeneralDialog(
        "该权限被永久拒绝授权，请手动授权",
        () {
          // 点击取消，可以选择关闭弹窗或什么都不做
          requesting = false;
        },
        () {
          // 点击后执行
          openAppSettings();
        },
      );
    } else if (status == PermissionStatus.denied) {
      PermissionPopup.show(Get.context!, tipMsg!); // 弹出说明窗
      permission.request().then((value) {
        MyLog.e("msg-----1111-----" + value.toString());
        if (value.isGranted) {
          requestSuccessFun?.call();
        } else {
          openAppSettings();
          requestFailFun?.call();
        }
        _removePermissionPopup(); // 关闭说明窗
        requesting = false;
      });
    } else if (status == PermissionStatus.restricted || status == PermissionStatus.limited) {
      ///权限受到限制 请开启权限
      CommonUtils.showGeneralDialog(
        "该权限受到限制，请手动授权",
        () {
          // 点击取消，可以选择关闭弹窗或什么都不做
          requesting = false;
        },
        () {
          // 点击后执行
          openAppSettings();
        },
      );
    } else {
      permission.request().then((value) {
        MyLog.e("msg-----222-----" + value.toString());
        if (value.isGranted) {
          requestSuccessFun?.call();
        } else {
          requestFailFun?.call();
        }
        _removePermissionPopup(); // 关闭说明窗
        requesting = false;
      });
    }
  }

  void _requestAndPermission(Permission permission, {String? tipMsg, RequestSuccessFun? requestSuccessFun, RequestStatusFun? requestStatusFun, RequestFailFun? requestFailFun}) async {
    PermissionStatus status = await permission.status;
    requestStatusFun?.call(status);
    // // 获取设备信息
    // DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
    // String deviceModel = '';
    //
    // // 检测平台并获取设备型号
    // AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
    // deviceModel = androidInfo.brand; // 获取机型
    //
    // // 检查是否为华为设备 只针对相册的权限这样
    // if (deviceModel.contains('HUAWEI') && permission == Permission.photos) {
    //   PermissionPopup.show(Get.context!, tipMsg!); // 弹出说明窗
    //   PermissionPopup.closeDelay(delayDuration: const Duration(milliseconds: 1500));
    //   Future.delayed(const Duration(milliseconds: 1500), () {
    //     requestSuccessFun?.call(); // 如果是华为设备，直接调用成功回调
    //   });
    //   return;
    // }

    MyLog.e("PermissionStatus = ${status.toString()}");

    if (status == PermissionStatus.granted) {
      requestSuccessFun?.call();
      requesting = false;
      if (!hadRequested) {
        SpUtil.putBool(permission.toString(), true);
        hadRequested = true;
      }
      _removePermissionPopup(); // 关闭说明窗
    } else if (status == PermissionStatus.denied) {
      PermissionPopup.show(Get.context!, tipMsg!); // 弹出说明窗
      permission.request().then((value) {
        if (value.isGranted) {
          requestSuccessFun?.call();
        } else {
          requestFailFun?.call();
        }
        _removePermissionPopup(); // 关闭说明窗
        requesting = false;
      });
    } else if (status == PermissionStatus.permanentlyDenied) {
      // 弹出说明窗提示用户去设置
      CommonUtils.showGeneralDialog(
        "该权限被永久拒绝授权，请手动授权",
        () {
          // 点击取消，可以选择关闭弹窗或什么都不做
          requesting = false;
        },
        () {
          // 点击后执行
          openAppSettings();
        },
      );
    } else if (status == PermissionStatus.restricted || status == PermissionStatus.limited) {
      ///权限受到限制 请开启权限
      CommonUtils.showGeneralDialog(
        "该权限受到限制，请手动授权",
        () {
          // 点击取消，可以选择关闭弹窗或什么都不做
          requesting = false;
        },
        () {
          // 点击后执行
          openAppSettings();
        },
      );
    } else {
      permission.request().then((value) {
        MyLog.e("msg-----222-----" + value.toString());
        if (value.isGranted) {
          requestSuccessFun?.call();
        } else {
          requestFailFun?.call();
        }
        _removePermissionPopup(); // 关闭说明窗
        requesting = false;
      });
    }
  }

  void _removePermissionPopup() {
    // 关闭弹窗的逻辑
    // 这里可以调用一个方法来移除弹窗，例如：
    PermissionPopup.close(); // 假设你有一个关闭弹窗的方法
  }
}

typedef RequestSuccessFun = void Function();
typedef RequestFailFun = void Function();
typedef RequestStatusFun = void Function(PermissionStatus status);
