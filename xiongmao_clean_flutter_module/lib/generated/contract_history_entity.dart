import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_history_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_history_entity.g.dart';

@JsonSerializable()
class ContractHistoryEntity {
	int? page;
	int? size;
	int? total;
	List<ContractHistoryList>? list;

	ContractHistoryEntity();

	factory ContractHistoryEntity.fromJson(Map<String, dynamic> json) => $ContractHistoryEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractHistoryEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractHistoryList {
	String? id;
	@JSONField(name: "company_id")
	String? companyId;
	@JSONField(name: "user_id")
	String? userId;
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "before_amount")
	String? beforeAmount;
	String? amount;
	@JSONField(name: "after_amount")
	String? afterAmount;
	String? remark;
	@JSONField(name: "consume_type")
	String? consumeType;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;

	ContractHistoryList();

	factory ContractHistoryList.fromJson(Map<String, dynamic> json) => $ContractHistoryListFromJson(json);

	Map<String, dynamic> toJson() => $ContractHistoryListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}