import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/get_meta_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/get_meta_entity.g.dart';

@JsonSerializable()
class GetMetaEntity {
	@JSONField(name: "dimission_reason_list")
	List<GetMetaDimissionReasonList>? dimissionReasonList = [];
	@JSONField(name: "in_class_time_list")
	GetMetaInClassTimeList? inClassTimeList;
	@JSONField(name: "out_class_time_list")
	GetMetaOutClassTimeList? outClassTimeList;
	@JSONField(name: "exigency_user_relation_list")
	List<GetMetaExigencyUserRelationList>? exigencyUserRelationList = [];
	@JSONField(name: "bank_list")
	List<GetMetaBankList>? bankList = [];
	@JSONField(name: "superiors_level_list")
	List<SuperiorsLevelInfo>? superiorsLevelList = [];


	GetMetaEntity();

	factory GetMetaEntity.fromJson(Map<String, dynamic> json) => $GetMetaEntityFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaDimissionReasonList {
	String? id = '';
	String? name = '';

	GetMetaDimissionReasonList();

	factory GetMetaDimissionReasonList.fromJson(Map<String, dynamic> json) => $GetMetaDimissionReasonListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaDimissionReasonListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaInClassTimeList {
	@JSONField(name: "before_list")
	List<GetMetaInClassTimeListBeforeList>? beforeList = [];
	@JSONField(name: "after_list")
	List<GetMetaInClassTimeListAfterList>? afterList = [];

	GetMetaInClassTimeList();

	factory GetMetaInClassTimeList.fromJson(Map<String, dynamic> json) => $GetMetaInClassTimeListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaInClassTimeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaInClassTimeListBeforeList {
	String? id = '';
	String? name = '';

	GetMetaInClassTimeListBeforeList();

	factory GetMetaInClassTimeListBeforeList.fromJson(Map<String, dynamic> json) => $GetMetaInClassTimeListBeforeListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaInClassTimeListBeforeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaInClassTimeListAfterList {
	String? id = '';
	String? name = '';

	GetMetaInClassTimeListAfterList();

	factory GetMetaInClassTimeListAfterList.fromJson(Map<String, dynamic> json) => $GetMetaInClassTimeListAfterListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaInClassTimeListAfterListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaOutClassTimeList {
	@JSONField(name: "before_list")
	List<GetMetaOutClassTimeListBeforeList>? beforeList = [];
	@JSONField(name: "after_list")
	List<GetMetaOutClassTimeListAfterList>? afterList = [];

	GetMetaOutClassTimeList();

	factory GetMetaOutClassTimeList.fromJson(Map<String, dynamic> json) => $GetMetaOutClassTimeListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaOutClassTimeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaOutClassTimeListBeforeList {
	String? id = '';
	String? name = '';

	GetMetaOutClassTimeListBeforeList();

	factory GetMetaOutClassTimeListBeforeList.fromJson(Map<String, dynamic> json) => $GetMetaOutClassTimeListBeforeListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaOutClassTimeListBeforeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaOutClassTimeListAfterList {
	String? id = '';
	String? name = '';

	GetMetaOutClassTimeListAfterList();

	factory GetMetaOutClassTimeListAfterList.fromJson(Map<String, dynamic> json) => $GetMetaOutClassTimeListAfterListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaOutClassTimeListAfterListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaExigencyUserRelationList {
	String? id = '';
	String? name = '';

	GetMetaExigencyUserRelationList();

	factory GetMetaExigencyUserRelationList.fromJson(Map<String, dynamic> json) => $GetMetaExigencyUserRelationListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaExigencyUserRelationListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetMetaBankList {
	String? code = '';
	String? name = '';

	GetMetaBankList();

	factory GetMetaBankList.fromJson(Map<String, dynamic> json) => $GetMetaBankListFromJson(json);

	Map<String, dynamic> toJson() => $GetMetaBankListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class SuperiorsLevelInfo {
	String? id = '';
	String? name = '';

	SuperiorsLevelInfo();

	factory SuperiorsLevelInfo.fromJson(Map<String, dynamic> json) => $SuperiorsLevelInfoFromJson(json);

	Map<String, dynamic> toJson() => $SuperiorsLevelInfoToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}