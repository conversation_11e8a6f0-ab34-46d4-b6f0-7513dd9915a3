import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/base_uuid_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/base_uuid_entity.g.dart';

@JsonSerializable()
class BaseUuidEntity {
	String? uuid = '';

	BaseUuidEntity();

	factory BaseUuidEntity.fromJson(Map<String, dynamic> json) => $BaseUuidEntityFromJson(json);

	Map<String, dynamic> toJson() => $BaseUuidEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}