import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/role_detail_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/role_detail_entity.g.dart';

@JsonSerializable()
class RoleDetailEntity {
	String? uuid = '';
	@JSONField(name: "user_name")
	String? userName = '';
	@JSONField(name: "job_name")
	String? jobName = '';
	@JSONField(name: "project_short_name")
	String? projectShortName = '';
	String? age = '';
	String? avatar = '';
	@JSONField(name: "work_age")
	String? workAge = '';
	String? mobile = '';
	@JSONField(name: "is_has_id_card")
	String? isHasIdCard = '';
	@JSONField(name: "is_has_contract")
	String? isHasContract = '';
	@JSONField(name: "is_has_bank_card")
	String? isHasBankCard = '';
	@JSONField(name: "is_has_healthy_card")
	String? isHasHealthyCard = '';
	@JSONField(name: "is_head_office")
	String? isHeadOffice = '';

	RoleDetailEntity();

	factory RoleDetailEntity.fromJson(Map<String, dynamic> json) => $RoleDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $RoleDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}