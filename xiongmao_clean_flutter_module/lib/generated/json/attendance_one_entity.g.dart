import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_one_entity.dart';

AttendanceOneEntity $AttendanceOneEntityFromJson(Map<String, dynamic> json) {
  final AttendanceOneEntity attendanceOneEntity = AttendanceOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceOneEntity.uuid = uuid;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    attendanceOneEntity.projectUuid = projectUuid;
  }
  final String? groupName = jsonConvert.convert<String>(json['group_name']);
  if (groupName != null) {
    attendanceOneEntity.groupName = groupName;
  }
  final String? clockInType = jsonConvert.convert<String>(json['clock_in_type']);
  if (clockInType != null) {
    attendanceOneEntity.clockInType = clockInType;
  }
  final String? projectClassUuid = jsonConvert.convert<String>(json['project_class_uuid']);
  if (projectClassUuid != null) {
    attendanceOneEntity.projectClassUuid = projectClassUuid;
  }
  final String? projectClassName = jsonConvert.convert<String>(json['project_class_name']);
  if (projectClassName != null) {
    attendanceOneEntity.projectClassName = projectClassName;
  }
  final String? inClassDesc = jsonConvert.convert<String>(json['in_class_desc']);
  if (inClassDesc != null) {
    attendanceOneEntity.inClassDesc = inClassDesc;
  }
  final List<String>? workDayList = (json['work_day_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (workDayList != null) {
    attendanceOneEntity.workDayList = workDayList;
  }
  final List<AttendanceOneLeaderList>? leaderList = (json['leader_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AttendanceOneLeaderList>(e) as AttendanceOneLeaderList).toList();
  if (leaderList != null) {
    attendanceOneEntity.leaderList = leaderList;
  }
  final List<AttendanceOneAddressList>? addressList = (json['address_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AttendanceOneAddressList>(e) as AttendanceOneAddressList).toList();
  if (addressList != null) {
    attendanceOneEntity.addressList = addressList;
  }
  return attendanceOneEntity;
}

Map<String, dynamic> $AttendanceOneEntityToJson(AttendanceOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_uuid'] = entity.projectUuid;
  data['group_name'] = entity.groupName;
  data['clock_in_type'] = entity.clockInType;
  data['project_class_uuid'] = entity.projectClassUuid;
  data['project_class_name'] = entity.projectClassName;
  data['in_class_desc'] = entity.inClassDesc;
  data['work_day_list'] = entity.workDayList;
  data['leader_list'] = entity.leaderList?.map((v) => v.toJson()).toList();
  data['address_list'] = entity.addressList?.map((v) => v.toJson()).toList();
  return data;
}

extension AttendanceOneEntityExtension on AttendanceOneEntity {
  AttendanceOneEntity copyWith({
    String? uuid,
    String? projectUuid,
    String? groupName,
    String? clockInType,
    String? projectClassUuid,
    String? projectClassName,
    String? inClassDesc,
    List<String>? workDayList,
    List<AttendanceOneLeaderList>? leaderList,
    List<AttendanceOneAddressList>? addressList,
  }) {
    return AttendanceOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectUuid = projectUuid ?? this.projectUuid
      ..groupName = groupName ?? this.groupName
      ..clockInType = clockInType ?? this.clockInType
      ..projectClassUuid = projectClassUuid ?? this.projectClassUuid
      ..projectClassName = projectClassName ?? this.projectClassName
      ..inClassDesc = inClassDesc ?? this.inClassDesc
      ..workDayList = workDayList ?? this.workDayList
      ..leaderList = leaderList ?? this.leaderList
      ..addressList = addressList ?? this.addressList;
  }
}

AttendanceOneLeaderList $AttendanceOneLeaderListFromJson(Map<String, dynamic> json) {
  final AttendanceOneLeaderList attendanceOneLeaderList = AttendanceOneLeaderList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceOneLeaderList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    attendanceOneLeaderList.userName = userName;
  }
  return attendanceOneLeaderList;
}

Map<String, dynamic> $AttendanceOneLeaderListToJson(AttendanceOneLeaderList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  return data;
}

extension AttendanceOneLeaderListExtension on AttendanceOneLeaderList {
  AttendanceOneLeaderList copyWith({
    String? uuid,
    String? userName,
  }) {
    return AttendanceOneLeaderList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName;
  }
}

AttendanceOneAddressList $AttendanceOneAddressListFromJson(Map<String, dynamic> json) {
  final AttendanceOneAddressList attendanceOneAddressList = AttendanceOneAddressList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceOneAddressList.uuid = uuid;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    attendanceOneAddressList.address = address;
  }
  return attendanceOneAddressList;
}

Map<String, dynamic> $AttendanceOneAddressListToJson(AttendanceOneAddressList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['address'] = entity.address;
  return data;
}

extension AttendanceOneAddressListExtension on AttendanceOneAddressList {
  AttendanceOneAddressList copyWith({
    String? uuid,
    String? address,
  }) {
    return AttendanceOneAddressList()
      ..uuid = uuid ?? this.uuid
      ..address = address ?? this.address;
  }
}