import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/holiday_data_entity.dart';

HolidayDataEntity $HolidayDataEntityFromJson(Map<String, dynamic> json) {
  final HolidayDataEntity holidayDataEntity = HolidayDataEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    holidayDataEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    holidayDataEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    holidayDataEntity.total = total;
  }
  final List<HolidayDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<HolidayDataList>(e) as HolidayDataList).toList();
  if (list != null) {
    holidayDataEntity.list = list;
  }
  return holidayDataEntity;
}

Map<String, dynamic> $HolidayDataEntityToJson(HolidayDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension HolidayDataEntityExtension on HolidayDataEntity {
  HolidayDataEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<HolidayDataList>? list,
  }) {
    return HolidayDataEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

HolidayDataList $HolidayDataListFromJson(Map<String, dynamic> json) {
  final HolidayDataList holidayDataList = HolidayDataList();
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    holidayDataList.introduction = introduction;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    holidayDataList.uuid = uuid;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    holidayDataList.projectUuid = projectUuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    holidayDataList.projectName = projectName;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    holidayDataList.startDate = startDate;
  }
  final String? startType = jsonConvert.convert<String>(json['start_type']);
  if (startType != null) {
    holidayDataList.startType = startType;
  }
  final String? startTypeName = jsonConvert.convert<String>(json['start_type_name']);
  if (startTypeName != null) {
    holidayDataList.startTypeName = startTypeName;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    holidayDataList.endDate = endDate;
  }
  final String? endType = jsonConvert.convert<String>(json['end_type']);
  if (endType != null) {
    holidayDataList.endType = endType;
  }
  final String? endTypeName = jsonConvert.convert<String>(json['end_type_name']);
  if (endTypeName != null) {
    holidayDataList.endTypeName = endTypeName;
  }
  final String? holidayType = jsonConvert.convert<String>(json['holiday_type']);
  if (holidayType != null) {
    holidayDataList.holidayType = holidayType;
  }
  final String? holidayTypeName = jsonConvert.convert<String>(json['holiday_type_name']);
  if (holidayTypeName != null) {
    holidayDataList.holidayTypeName = holidayTypeName;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    holidayDataList.reason = reason;
  }
  return holidayDataList;
}

Map<String, dynamic> $HolidayDataListToJson(HolidayDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['introduction'] = entity.introduction;
  data['uuid'] = entity.uuid;
  data['project_uuid'] = entity.projectUuid;
  data['project_name'] = entity.projectName;
  data['start_date'] = entity.startDate;
  data['start_type'] = entity.startType;
  data['start_type_name'] = entity.startTypeName;
  data['end_date'] = entity.endDate;
  data['end_type'] = entity.endType;
  data['end_type_name'] = entity.endTypeName;
  data['holiday_type'] = entity.holidayType;
  data['holiday_type_name'] = entity.holidayTypeName;
  data['reason'] = entity.reason;
  return data;
}

extension HolidayDataListExtension on HolidayDataList {
  HolidayDataList copyWith({
    String? introduction,
    String? uuid,
    String? projectUuid,
    String? projectName,
    String? startDate,
    String? startType,
    String? startTypeName,
    String? endDate,
    String? endType,
    String? endTypeName,
    String? holidayType,
    String? holidayTypeName,
    String? reason,
  }) {
    return HolidayDataList()
      ..introduction = introduction ?? this.introduction
      ..uuid = uuid ?? this.uuid
      ..projectUuid = projectUuid ?? this.projectUuid
      ..projectName = projectName ?? this.projectName
      ..startDate = startDate ?? this.startDate
      ..startType = startType ?? this.startType
      ..startTypeName = startTypeName ?? this.startTypeName
      ..endDate = endDate ?? this.endDate
      ..endType = endType ?? this.endType
      ..endTypeName = endTypeName ?? this.endTypeName
      ..holidayType = holidayType ?? this.holidayType
      ..holidayTypeName = holidayTypeName ?? this.holidayTypeName
      ..reason = reason ?? this.reason;
  }
}