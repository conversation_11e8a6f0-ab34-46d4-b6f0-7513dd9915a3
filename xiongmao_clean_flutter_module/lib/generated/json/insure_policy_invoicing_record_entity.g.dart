import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_policy_invoicing_record_entity.dart';

InsurePolicyInvoicingRecordEntity $InsurePolicyInvoicingRecordEntityFromJson(Map<String, dynamic> json) {
  final InsurePolicyInvoicingRecordEntity insurePolicyInvoicingRecordEntity = InsurePolicyInvoicingRecordEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    insurePolicyInvoicingRecordEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    insurePolicyInvoicingRecordEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    insurePolicyInvoicingRecordEntity.total = total;
  }
  final List<InsurePolicyInvoicingRecordList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsurePolicyInvoicingRecordList>(e) as InsurePolicyInvoicingRecordList).toList();
  if (list != null) {
    insurePolicyInvoicingRecordEntity.list = list;
  }
  return insurePolicyInvoicingRecordEntity;
}

Map<String, dynamic> $InsurePolicyInvoicingRecordEntityToJson(InsurePolicyInvoicingRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension InsurePolicyInvoicingRecordEntityExtension on InsurePolicyInvoicingRecordEntity {
  InsurePolicyInvoicingRecordEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<InsurePolicyInvoicingRecordList>? list,
  }) {
    return InsurePolicyInvoicingRecordEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

InsurePolicyInvoicingRecordList $InsurePolicyInvoicingRecordListFromJson(Map<String, dynamic> json) {
  final InsurePolicyInvoicingRecordList insurePolicyInvoicingRecordList = InsurePolicyInvoicingRecordList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    insurePolicyInvoicingRecordList.uuid = uuid;
  }
  final String? createUserName = jsonConvert.convert<String>(json['create_user_name']);
  if (createUserName != null) {
    insurePolicyInvoicingRecordList.createUserName = createUserName;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    insurePolicyInvoicingRecordList.createTime = createTime;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    insurePolicyInvoicingRecordList.amount = amount;
  }
  final String? invoiceTitle = jsonConvert.convert<String>(json['invoice_title']);
  if (invoiceTitle != null) {
    insurePolicyInvoicingRecordList.invoiceTitle = invoiceTitle;
  }
  return insurePolicyInvoicingRecordList;
}

Map<String, dynamic> $InsurePolicyInvoicingRecordListToJson(InsurePolicyInvoicingRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['create_user_name'] = entity.createUserName;
  data['create_time'] = entity.createTime;
  data['amount'] = entity.amount;
  data['invoice_title'] = entity.invoiceTitle;
  return data;
}

extension InsurePolicyInvoicingRecordListExtension on InsurePolicyInvoicingRecordList {
  InsurePolicyInvoicingRecordList copyWith({
    String? uuid,
    String? createUserName,
    String? createTime,
    String? amount,
    String? invoiceTitle,
  }) {
    return InsurePolicyInvoicingRecordList()
      ..uuid = uuid ?? this.uuid
      ..createUserName = createUserName ?? this.createUserName
      ..createTime = createTime ?? this.createTime
      ..amount = amount ?? this.amount
      ..invoiceTitle = invoiceTitle ?? this.invoiceTitle;
  }
}