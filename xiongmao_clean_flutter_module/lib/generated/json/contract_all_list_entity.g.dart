import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';

ContractAllListEntity $ContractAllListEntityFromJson(Map<String, dynamic> json) {
  final ContractAllListEntity contractAllListEntity = ContractAllListEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    contractAllListEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    contractAllListEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    contractAllListEntity.total = total;
  }
  final List<ContractAllListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractAllListList>(e) as ContractAllListList).toList();
  if (list != null) {
    contractAllListEntity.list = list;
  }
  return contractAllListEntity;
}

Map<String, dynamic> $ContractAllListEntityToJson(ContractAllListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractAllListEntityExtension on ContractAllListEntity {
  ContractAllListEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ContractAllListList>? list,
  }) {
    return ContractAllListEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ContractAllListList $ContractAllListListFromJson(Map<String, dynamic> json) {
  final ContractAllListList contractAllListList = ContractAllListList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractAllListList.id = id;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    contractAllListList.uuid = uuid;
  }
  final String? mainLabelCode = jsonConvert.convert<String>(json['main_label_code']);
  if (mainLabelCode != null) {
    contractAllListList.mainLabelCode = mainLabelCode;
  }
  final String? subLabelCode = jsonConvert.convert<String>(json['sub_label_code']);
  if (subLabelCode != null) {
    contractAllListList.subLabelCode = subLabelCode;
  }
  final String? companyId = jsonConvert.convert<String>(json['company_id']);
  if (companyId != null) {
    contractAllListList.companyId = companyId;
  }
  final String? templateTitle = jsonConvert.convert<String>(json['template_title']);
  if (templateTitle != null) {
    contractAllListList.templateTitle = templateTitle;
  }
  final String? templateUrl = jsonConvert.convert<String>(json['template_url']);
  if (templateUrl != null) {
    contractAllListList.templateUrl = templateUrl;
  }
  final String? templateStatus = jsonConvert.convert<String>(json['template_status']);
  if (templateStatus != null) {
    contractAllListList.templateStatus = templateStatus;
  }
  final String? isSelf = jsonConvert.convert<String>(json['is_self']);
  if (isSelf != null) {
    contractAllListList.isSelf = isSelf;
  }
  final String? isDelete = jsonConvert.convert<String>(json['is_delete']);
  if (isDelete != null) {
    contractAllListList.isDelete = isDelete;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractAllListList.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractAllListList.updateTime = updateTime;
  }
  final ContractAllListListSetting? setting = jsonConvert.convert<ContractAllListListSetting>(json['setting']);
  if (setting != null) {
    contractAllListList.setting = setting;
  }
  return contractAllListList;
}

Map<String, dynamic> $ContractAllListListToJson(ContractAllListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['uuid'] = entity.uuid;
  data['main_label_code'] = entity.mainLabelCode;
  data['sub_label_code'] = entity.subLabelCode;
  data['company_id'] = entity.companyId;
  data['template_title'] = entity.templateTitle;
  data['template_url'] = entity.templateUrl;
  data['template_status'] = entity.templateStatus;
  data['is_self'] = entity.isSelf;
  data['is_delete'] = entity.isDelete;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  data['setting'] = entity.setting?.toJson();
  return data;
}

extension ContractAllListListExtension on ContractAllListList {
  ContractAllListList copyWith({
    String? id,
    String? uuid,
    String? mainLabelCode,
    String? subLabelCode,
    String? companyId,
    String? templateTitle,
    String? templateUrl,
    String? templateStatus,
    String? isSelf,
    String? isDelete,
    String? createTime,
    String? updateTime,
    ContractAllListListSetting? setting,
  }) {
    return ContractAllListList()
      ..id = id ?? this.id
      ..uuid = uuid ?? this.uuid
      ..mainLabelCode = mainLabelCode ?? this.mainLabelCode
      ..subLabelCode = subLabelCode ?? this.subLabelCode
      ..companyId = companyId ?? this.companyId
      ..templateTitle = templateTitle ?? this.templateTitle
      ..templateUrl = templateUrl ?? this.templateUrl
      ..templateStatus = templateStatus ?? this.templateStatus
      ..isSelf = isSelf ?? this.isSelf
      ..isDelete = isDelete ?? this.isDelete
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..setting = setting ?? this.setting;
  }
}

ContractAllListListSetting $ContractAllListListSettingFromJson(Map<String, dynamic> json) {
  final ContractAllListListSetting contractAllListListSetting = ContractAllListListSetting();
  final bool? labelDefaultSignTemplate = jsonConvert.convert<bool>(json['label_default_sign_template']);
  if (labelDefaultSignTemplate != null) {
    contractAllListListSetting.labelDefaultSignTemplate = labelDefaultSignTemplate;
  }
  return contractAllListListSetting;
}

Map<String, dynamic> $ContractAllListListSettingToJson(ContractAllListListSetting entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['label_default_sign_template'] = entity.labelDefaultSignTemplate;
  return data;
}

extension ContractAllListListSettingExtension on ContractAllListListSetting {
  ContractAllListListSetting copyWith({
    bool? labelDefaultSignTemplate,
  }) {
    return ContractAllListListSetting()
      ..labelDefaultSignTemplate = labelDefaultSignTemplate ?? this.labelDefaultSignTemplate;
  }
}