import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_icon_entity.dart';

ApproveIconEntity $ApproveIconEntityFromJson(Map<String, dynamic> json) {
  final ApproveIconEntity approveIconEntity = ApproveIconEntity();
  final List<ApproveIconList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveIconList>(e) as ApproveIconList).toList();
  if (list != null) {
    approveIconEntity.list = list;
  }
  return approveIconEntity;
}

Map<String, dynamic> $ApproveIconEntityToJson(ApproveIconEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveIconEntityExtension on ApproveIconEntity {
  ApproveIconEntity copyWith({
    List<ApproveIconList>? list,
  }) {
    return ApproveIconEntity()
      ..list = list ?? this.list;
  }
}

ApproveIconList $ApproveIconListFromJson(Map<String, dynamic> json) {
  final ApproveIconList approveIconList = ApproveIconList();
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    approveIconList.name = name;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    approveIconList.icon = icon;
  }
  return approveIconList;
}

Map<String, dynamic> $ApproveIconListToJson(ApproveIconList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['name'] = entity.name;
  data['icon'] = entity.icon;
  return data;
}

extension ApproveIconListExtension on ApproveIconList {
  ApproveIconList copyWith({
    String? name,
    String? icon,
  }) {
    return ApproveIconList()
      ..name = name ?? this.name
      ..icon = icon ?? this.icon;
  }
}