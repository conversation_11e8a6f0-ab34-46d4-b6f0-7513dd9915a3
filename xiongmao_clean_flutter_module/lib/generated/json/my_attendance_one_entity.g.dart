import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_one_entity.dart';

MyAttendanceOneEntity $MyAttendanceOneEntityFromJson(Map<String, dynamic> json) {
  final MyAttendanceOneEntity myAttendanceOneEntity = MyAttendanceOneEntity();
  final String? workDays = jsonConvert.convert<String>(json['work_days']);
  if (workDays != null) {
    myAttendanceOneEntity.workDays = workDays;
  }
  final String? actualWorkDays = jsonConvert.convert<String>(json['actual_work_days']);
  if (actualWorkDays != null) {
    myAttendanceOneEntity.actualWorkDays = actualWorkDays;
  }
  final String? xxDays = jsonConvert.convert<String>(json['xx_days']);
  if (xxDays != null) {
    myAttendanceOneEntity.xxDays = xxDays;
  }
  final String? xjDays = jsonConvert.convert<String>(json['xj_days']);
  if (xjDays != null) {
    myAttendanceOneEntity.xjDays = xjDays;
  }
  final String? jbHours = jsonConvert.convert<String>(json['jb_hours']);
  if (jbHours != null) {
    myAttendanceOneEntity.jbHours = jbHours;
  }
  final String? cdTimeLong = jsonConvert.convert<String>(json['cd_time_long']);
  if (cdTimeLong != null) {
    myAttendanceOneEntity.cdTimeLong = cdTimeLong;
  }
  final String? ztTimeLong = jsonConvert.convert<String>(json['zt_time_long']);
  if (ztTimeLong != null) {
    myAttendanceOneEntity.ztTimeLong = ztTimeLong;
  }
  final String? kgDays = jsonConvert.convert<String>(json['kg_days']);
  if (kgDays != null) {
    myAttendanceOneEntity.kgDays = kgDays;
  }
  final String? qkTimes = jsonConvert.convert<String>(json['qk_times']);
  if (qkTimes != null) {
    myAttendanceOneEntity.qkTimes = qkTimes;
  }
  final String? userUuid = jsonConvert.convert<String>(json['user_uuid']);
  if (userUuid != null) {
    myAttendanceOneEntity.userUuid = userUuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    myAttendanceOneEntity.userName = userName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    myAttendanceOneEntity.avatar = avatar;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    myAttendanceOneEntity.projectUuid = projectUuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    myAttendanceOneEntity.projectName = projectName;
  }
  final List<MyAttendanceOneList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MyAttendanceOneList>(e) as MyAttendanceOneList).toList();
  if (list != null) {
    myAttendanceOneEntity.list = list;
  }
  return myAttendanceOneEntity;
}

Map<String, dynamic> $MyAttendanceOneEntityToJson(MyAttendanceOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['work_days'] = entity.workDays;
  data['actual_work_days'] = entity.actualWorkDays;
  data['xx_days'] = entity.xxDays;
  data['xj_days'] = entity.xjDays;
  data['jb_hours'] = entity.jbHours;
  data['cd_time_long'] = entity.cdTimeLong;
  data['zt_time_long'] = entity.ztTimeLong;
  data['kg_days'] = entity.kgDays;
  data['qk_times'] = entity.qkTimes;
  data['user_uuid'] = entity.userUuid;
  data['user_name'] = entity.userName;
  data['avatar'] = entity.avatar;
  data['project_uuid'] = entity.projectUuid;
  data['project_name'] = entity.projectName;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension MyAttendanceOneEntityExtension on MyAttendanceOneEntity {
  MyAttendanceOneEntity copyWith({
    String? workDays,
    String? actualWorkDays,
    String? xxDays,
    String? xjDays,
    String? jbHours,
    String? cdTimeLong,
    String? ztTimeLong,
    String? kgDays,
    String? qkTimes,
    String? userUuid,
    String? userName,
    String? avatar,
    String? projectUuid,
    String? projectName,
    List<MyAttendanceOneList>? list,
  }) {
    return MyAttendanceOneEntity()
      ..workDays = workDays ?? this.workDays
      ..actualWorkDays = actualWorkDays ?? this.actualWorkDays
      ..xxDays = xxDays ?? this.xxDays
      ..xjDays = xjDays ?? this.xjDays
      ..jbHours = jbHours ?? this.jbHours
      ..cdTimeLong = cdTimeLong ?? this.cdTimeLong
      ..ztTimeLong = ztTimeLong ?? this.ztTimeLong
      ..kgDays = kgDays ?? this.kgDays
      ..qkTimes = qkTimes ?? this.qkTimes
      ..userUuid = userUuid ?? this.userUuid
      ..userName = userName ?? this.userName
      ..avatar = avatar ?? this.avatar
      ..projectUuid = projectUuid ?? this.projectUuid
      ..projectName = projectName ?? this.projectName
      ..list = list ?? this.list;
  }
}

MyAttendanceOneList $MyAttendanceOneListFromJson(Map<String, dynamic> json) {
  final MyAttendanceOneList myAttendanceOneList = MyAttendanceOneList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    myAttendanceOneList.uuid = uuid;
  }
  final String? userUuid = jsonConvert.convert<String>(json['user_uuid']);
  if (userUuid != null) {
    myAttendanceOneList.userUuid = userUuid;
  }
  final String? isCq = jsonConvert.convert<String>(json['is_cq']);
  if (isCq != null) {
    myAttendanceOneList.isCq = isCq;
  }
  final String? isXx = jsonConvert.convert<String>(json['is_xx']);
  if (isXx != null) {
    myAttendanceOneList.isXx = isXx;
  }
  final String? isXj = jsonConvert.convert<String>(json['is_xj']);
  if (isXj != null) {
    myAttendanceOneList.isXj = isXj;
  }
  final String? isJb = jsonConvert.convert<String>(json['is_jb']);
  if (isJb != null) {
    myAttendanceOneList.isJb = isJb;
  }
  final String? isQk = jsonConvert.convert<String>(json['is_qk']);
  if (isQk != null) {
    myAttendanceOneList.isQk = isQk;
  }
  final String? isCd = jsonConvert.convert<String>(json['is_cd']);
  if (isCd != null) {
    myAttendanceOneList.isCd = isCd;
  }
  final String? isZt = jsonConvert.convert<String>(json['is_zt']);
  if (isZt != null) {
    myAttendanceOneList.isZt = isZt;
  }
  final String? isKg = jsonConvert.convert<String>(json['is_kg']);
  if (isKg != null) {
    myAttendanceOneList.isKg = isKg;
  }
  final String? isEntry = jsonConvert.convert<String>(json['is_entry']);
  if (isEntry != null) {
    myAttendanceOneList.isEntry = isEntry;
  }
  final String? isLeave = jsonConvert.convert<String>(json['is_leave']);
  if (isLeave != null) {
    myAttendanceOneList.isLeave = isLeave;
  }
  final String? kqDate = jsonConvert.convert<String>(json['kq_date']);
  if (kqDate != null) {
    myAttendanceOneList.kqDate = kqDate;
  }
  final String? kqYear = jsonConvert.convert<String>(json['kq_year']);
  if (kqYear != null) {
    myAttendanceOneList.kqYear = kqYear;
  }
  final String? kqMonth = jsonConvert.convert<String>(json['kq_month']);
  if (kqMonth != null) {
    myAttendanceOneList.kqMonth = kqMonth;
  }
  final String? kqDay = jsonConvert.convert<String>(json['kq_day']);
  if (kqDay != null) {
    myAttendanceOneList.kqDay = kqDay;
  }
  return myAttendanceOneList;
}

Map<String, dynamic> $MyAttendanceOneListToJson(MyAttendanceOneList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_uuid'] = entity.userUuid;
  data['is_cq'] = entity.isCq;
  data['is_xx'] = entity.isXx;
  data['is_xj'] = entity.isXj;
  data['is_jb'] = entity.isJb;
  data['is_qk'] = entity.isQk;
  data['is_cd'] = entity.isCd;
  data['is_zt'] = entity.isZt;
  data['is_kg'] = entity.isKg;
  data['is_entry'] = entity.isEntry;
  data['is_leave'] = entity.isLeave;
  data['kq_date'] = entity.kqDate;
  data['kq_year'] = entity.kqYear;
  data['kq_month'] = entity.kqMonth;
  data['kq_day'] = entity.kqDay;
  return data;
}

extension MyAttendanceOneListExtension on MyAttendanceOneList {
  MyAttendanceOneList copyWith({
    String? uuid,
    String? userUuid,
    String? isCq,
    String? isXx,
    String? isXj,
    String? isJb,
    String? isQk,
    String? isCd,
    String? isZt,
    String? isKg,
    String? isEntry,
    String? isLeave,
    String? kqDate,
    String? kqYear,
    String? kqMonth,
    String? kqDay,
  }) {
    return MyAttendanceOneList()
      ..uuid = uuid ?? this.uuid
      ..userUuid = userUuid ?? this.userUuid
      ..isCq = isCq ?? this.isCq
      ..isXx = isXx ?? this.isXx
      ..isXj = isXj ?? this.isXj
      ..isJb = isJb ?? this.isJb
      ..isQk = isQk ?? this.isQk
      ..isCd = isCd ?? this.isCd
      ..isZt = isZt ?? this.isZt
      ..isKg = isKg ?? this.isKg
      ..isEntry = isEntry ?? this.isEntry
      ..isLeave = isLeave ?? this.isLeave
      ..kqDate = kqDate ?? this.kqDate
      ..kqYear = kqYear ?? this.kqYear
      ..kqMonth = kqMonth ?? this.kqMonth
      ..kqDay = kqDay ?? this.kqDay;
  }
}