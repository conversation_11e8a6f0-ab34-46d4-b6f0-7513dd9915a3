import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_tem_entity.dart';

ApproveTemEntity $ApproveTemEntityFromJson(Map<String, dynamic> json) {
  final ApproveTemEntity approveTemEntity = ApproveTemEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    approveTemEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    approveTemEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    approveTemEntity.total = total;
  }
  final List<ApproveTemList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveTemList>(e) as ApproveTemList).toList();
  if (list != null) {
    approveTemEntity.list = list;
  }
  return approveTemEntity;
}

Map<String, dynamic> $ApproveTemEntityToJson(ApproveTemEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveTemEntityExtension on ApproveTemEntity {
  ApproveTemEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ApproveTemList>? list,
  }) {
    return ApproveTemEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ApproveTemList $ApproveTemListFromJson(Map<String, dynamic> json) {
  final ApproveTemList approveTemList = ApproveTemList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveTemList.uuid = uuid;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    approveTemList.icon = icon;
  }
  final String? templateStatus = jsonConvert.convert<String>(json['template_status']);
  if (templateStatus != null) {
    approveTemList.templateStatus = templateStatus;
  }
  final String? templateStatusName = jsonConvert.convert<String>(json['template_status_name']);
  if (templateStatusName != null) {
    approveTemList.templateStatusName = templateStatusName;
  }
  final String? isSystem = jsonConvert.convert<String>(json['is_system']);
  if (isSystem != null) {
    approveTemList.isSystem = isSystem;
  }
  final String? isSystemName = jsonConvert.convert<String>(json['is_system_name']);
  if (isSystemName != null) {
    approveTemList.isSystemName = isSystemName;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    approveTemList.introduction = introduction;
  }
  final String? templateName = jsonConvert.convert<String>(json['template_name']);
  if (templateName != null) {
    approveTemList.templateName = templateName;
  }
  return approveTemList;
}

Map<String, dynamic> $ApproveTemListToJson(ApproveTemList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['icon'] = entity.icon;
  data['template_status'] = entity.templateStatus;
  data['template_status_name'] = entity.templateStatusName;
  data['is_system'] = entity.isSystem;
  data['is_system_name'] = entity.isSystemName;
  data['introduction'] = entity.introduction;
  data['template_name'] = entity.templateName;
  return data;
}

extension ApproveTemListExtension on ApproveTemList {
  ApproveTemList copyWith({
    String? uuid,
    String? icon,
    String? templateStatus,
    String? templateStatusName,
    String? isSystem,
    String? isSystemName,
    String? introduction,
    String? templateName,
  }) {
    return ApproveTemList()
      ..uuid = uuid ?? this.uuid
      ..icon = icon ?? this.icon
      ..templateStatus = templateStatus ?? this.templateStatus
      ..templateStatusName = templateStatusName ?? this.templateStatusName
      ..isSystem = isSystem ?? this.isSystem
      ..isSystemName = isSystemName ?? this.isSystemName
      ..introduction = introduction ?? this.introduction
      ..templateName = templateName ?? this.templateName;
  }
}