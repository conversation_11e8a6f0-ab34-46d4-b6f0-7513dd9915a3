import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/salary_config_entity.dart';

SalaryConfigEntity $SalaryConfigEntityFromJson(Map<String, dynamic> json) {
  final SalaryConfigEntity salaryConfigEntity = SalaryConfigEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    salaryConfigEntity.uuid = uuid;
  }
  final String? templateName = jsonConvert.convert<String>(json['template_name']);
  if (templateName != null) {
    salaryConfigEntity.templateName = templateName;
  }
  final String? levelTotal = jsonConvert.convert<String>(json['level_total']);
  if (levelTotal != null) {
    salaryConfigEntity.levelTotal = levelTotal;
  }
  final List<List<SalaryConfigItemListSalaryConfigItemList>>? itemList = (json['item_list'] as List<dynamic>?)?.map(
          (e) =>
          (e as List<dynamic>).map(
                  (e) => jsonConvert.convert<SalaryConfigItemListSalaryConfigItemList>(e) as SalaryConfigItemListSalaryConfigItemList).toList()).toList();
  if (itemList != null) {
    salaryConfigEntity.itemList = itemList;
  }
  return salaryConfigEntity;
}

Map<String, dynamic> $SalaryConfigEntityToJson(SalaryConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['template_name'] = entity.templateName;
  data['level_total'] = entity.levelTotal;
  data['item_list'] = entity.itemList;
  return data;
}

extension SalaryConfigEntityExtension on SalaryConfigEntity {
  SalaryConfigEntity copyWith({
    String? uuid,
    String? templateName,
    String? levelTotal,
    List<List<SalaryConfigItemListSalaryConfigItemList>>? itemList,
  }) {
    return SalaryConfigEntity()
      ..uuid = uuid ?? this.uuid
      ..templateName = templateName ?? this.templateName
      ..levelTotal = levelTotal ?? this.levelTotal
      ..itemList = itemList ?? this.itemList;
  }
}

SalaryConfigItemListSalaryConfigItemList $SalaryConfigItemListSalaryConfigItemListFromJson(Map<String, dynamic> json) {
  final SalaryConfigItemListSalaryConfigItemList salaryConfigItemListSalaryConfigItemList = SalaryConfigItemListSalaryConfigItemList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    salaryConfigItemListSalaryConfigItemList.uuid = uuid;
  }
  final String? itemName = jsonConvert.convert<String>(json['item_name']);
  if (itemName != null) {
    salaryConfigItemListSalaryConfigItemList.itemName = itemName;
  }
  final String? dataType = jsonConvert.convert<String>(json['data_type']);
  if (dataType != null) {
    salaryConfigItemListSalaryConfigItemList.dataType = dataType;
  }
  final String? dataTypeName = jsonConvert.convert<String>(json['data_type_name']);
  if (dataTypeName != null) {
    salaryConfigItemListSalaryConfigItemList.dataTypeName = dataTypeName;
  }
  final String? isEdit = jsonConvert.convert<String>(json['is_edit']);
  if (isEdit != null) {
    salaryConfigItemListSalaryConfigItemList.isEdit = isEdit;
  }
  final String? isEditName = jsonConvert.convert<String>(json['is_edit_name']);
  if (isEditName != null) {
    salaryConfigItemListSalaryConfigItemList.isEditName = isEditName;
  }
  final String? isAdjustmentStat = jsonConvert.convert<String>(json['is_adjustment_stat']);
  if (isAdjustmentStat != null) {
    salaryConfigItemListSalaryConfigItemList.isAdjustmentStat = isAdjustmentStat;
  }
  final String? dataTypeFormat = jsonConvert.convert<String>(json['data_type_format']);
  if (dataTypeFormat != null) {
    salaryConfigItemListSalaryConfigItemList.dataTypeFormat = dataTypeFormat;
  }
  final String? itemValue = jsonConvert.convert<String>(json['item_value']);
  if (itemValue != null) {
    salaryConfigItemListSalaryConfigItemList.itemValue = itemValue;
  }
  return salaryConfigItemListSalaryConfigItemList;
}

Map<String, dynamic> $SalaryConfigItemListSalaryConfigItemListToJson(SalaryConfigItemListSalaryConfigItemList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['item_name'] = entity.itemName;
  data['data_type'] = entity.dataType;
  data['data_type_name'] = entity.dataTypeName;
  data['is_edit'] = entity.isEdit;
  data['is_edit_name'] = entity.isEditName;
  data['is_adjustment_stat'] = entity.isAdjustmentStat;
  data['data_type_format'] = entity.dataTypeFormat;
  data['item_value'] = entity.itemValue;
  return data;
}

extension SalaryConfigItemListSalaryConfigItemListExtension on SalaryConfigItemListSalaryConfigItemList {
  SalaryConfigItemListSalaryConfigItemList copyWith({
    String? uuid,
    String? itemName,
    String? dataType,
    String? dataTypeName,
    String? isEdit,
    String? isEditName,
    String? isAdjustmentStat,
    String? dataTypeFormat,
    String? itemValue,
  }) {
    return SalaryConfigItemListSalaryConfigItemList()
      ..uuid = uuid ?? this.uuid
      ..itemName = itemName ?? this.itemName
      ..dataType = dataType ?? this.dataType
      ..dataTypeName = dataTypeName ?? this.dataTypeName
      ..isEdit = isEdit ?? this.isEdit
      ..isEditName = isEditName ?? this.isEditName
      ..isAdjustmentStat = isAdjustmentStat ?? this.isAdjustmentStat
      ..dataTypeFormat = dataTypeFormat ?? this.dataTypeFormat
      ..itemValue = itemValue ?? this.itemValue;
  }
}