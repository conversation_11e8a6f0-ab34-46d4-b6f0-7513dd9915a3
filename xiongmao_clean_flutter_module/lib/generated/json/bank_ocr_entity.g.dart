import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/bank_ocr_entity.dart';

BankOcrEntity $BankOcrEntityFromJson(Map<String, dynamic> json) {
  final BankOcrEntity bankOcrEntity = BankOcrEntity();
  final int? imageStatus = jsonConvert.convert<int>(json['image_status']);
  if (imageStatus != null) {
    bankOcrEntity.imageStatus = imageStatus;
  }
  final String? imageStatusName = jsonConvert.convert<String>(json['image_status_name']);
  if (imageStatusName != null) {
    bankOcrEntity.imageStatusName = imageStatusName;
  }
  final String? bankCardNumber = jsonConvert.convert<String>(json['bank_card_number']);
  if (bankCardNumber != null) {
    bankOcrEntity.bankCardNumber = bankCardNumber;
  }
  final String? validDate = jsonConvert.convert<String>(json['valid_date']);
  if (validDate != null) {
    bankOcrEntity.validDate = validDate;
  }
  final String? bankName = jsonConvert.convert<String>(json['bank_name']);
  if (bankName != null) {
    bankOcrEntity.bankName = bankName;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bank_code']);
  if (bankCode != null) {
    bankOcrEntity.bankCode = bankCode;
  }
  return bankOcrEntity;
}

Map<String, dynamic> $BankOcrEntityToJson(BankOcrEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['image_status'] = entity.imageStatus;
  data['image_status_name'] = entity.imageStatusName;
  data['bank_card_number'] = entity.bankCardNumber;
  data['valid_date'] = entity.validDate;
  data['bank_name'] = entity.bankName;
  data['bank_code'] = entity.bankCode;
  return data;
}

extension BankOcrEntityExtension on BankOcrEntity {
  BankOcrEntity copyWith({
    int? imageStatus,
    String? imageStatusName,
    String? bankCardNumber,
    String? validDate,
    String? bankName,
    String? bankCode,
  }) {
    return BankOcrEntity()
      ..imageStatus = imageStatus ?? this.imageStatus
      ..imageStatusName = imageStatusName ?? this.imageStatusName
      ..bankCardNumber = bankCardNumber ?? this.bankCardNumber
      ..validDate = validDate ?? this.validDate
      ..bankName = bankName ?? this.bankName
      ..bankCode = bankCode ?? this.bankCode;
  }
}