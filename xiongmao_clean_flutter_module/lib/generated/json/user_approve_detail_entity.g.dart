import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';

UserApproveDetailEntity $UserApproveDetailEntityFromJson(Map<String, dynamic> json) {
  final UserApproveDetailEntity userApproveDetailEntity = UserApproveDetailEntity();
  final String? applicationTitle = jsonConvert.convert<String>(json['application_title']);
  if (applicationTitle != null) {
    userApproveDetailEntity.applicationTitle = applicationTitle;
  }
  final String? applicationStatus = jsonConvert.convert<String>(json['application_status']);
  if (applicationStatus != null) {
    userApproveDetailEntity.applicationStatus = applicationStatus;
  }
  final String? applicationStatusName = jsonConvert.convert<String>(json['application_status_name']);
  if (applicationStatusName != null) {
    userApproveDetailEntity.applicationStatusName = applicationStatusName;
  }
  final List<UserApproveDetailSummaryList>? summaryList = (json['summary_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<UserApproveDetailSummaryList>(e) as UserApproveDetailSummaryList).toList();
  if (summaryList != null) {
    userApproveDetailEntity.summaryList = summaryList;
  }
  final List<dynamic>? applicationList = (json['application_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (applicationList != null) {
    userApproveDetailEntity.applicationList = applicationList;
  }
  final String? applicationType = jsonConvert.convert<String>(json['application_type']);
  if (applicationType != null) {
    userApproveDetailEntity.applicationType = applicationType;
  }
  final List<UserApproveDetailFieldList>? fieldList = (json['field_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<UserApproveDetailFieldList>(e) as UserApproveDetailFieldList).toList();
  if (fieldList != null) {
    userApproveDetailEntity.fieldList = fieldList;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    userApproveDetailEntity.createTime = createTime;
  }
  final UserApproveDetailMaterialSku? materialSku = jsonConvert.convert<UserApproveDetailMaterialSku>(json['material_sku']);
  if (materialSku != null) {
    userApproveDetailEntity.materialSku = materialSku;
  }
  final List<UserApproveDetailSpNodeList>? spNodeList = (json['sp_node_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<UserApproveDetailSpNodeList>(e) as UserApproveDetailSpNodeList).toList();
  if (spNodeList != null) {
    userApproveDetailEntity.spNodeList = spNodeList;
  }
  return userApproveDetailEntity;
}

Map<String, dynamic> $UserApproveDetailEntityToJson(UserApproveDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['application_title'] = entity.applicationTitle;
  data['application_status'] = entity.applicationStatus;
  data['application_status_name'] = entity.applicationStatusName;
  data['summary_list'] = entity.summaryList?.map((v) => v.toJson()).toList();
  data['application_list'] = entity.applicationList;
  data['application_type'] = entity.applicationType;
  data['field_list'] = entity.fieldList?.map((v) => v.toJson()).toList();
  data['create_time'] = entity.createTime;
  data['material_sku'] = entity.materialSku?.toJson();
  data['sp_node_list'] = entity.spNodeList?.map((v) => v.toJson()).toList();
  return data;
}

extension UserApproveDetailEntityExtension on UserApproveDetailEntity {
  UserApproveDetailEntity copyWith({
    String? applicationTitle,
    String? applicationStatus,
    String? applicationStatusName,
    List<UserApproveDetailSummaryList>? summaryList,
    List<dynamic>? applicationList,
    String? applicationType,
    List<UserApproveDetailFieldList>? fieldList,
    String? createTime,
    UserApproveDetailMaterialSku? materialSku,
    List<UserApproveDetailSpNodeList>? spNodeList,
  }) {
    return UserApproveDetailEntity()
      ..applicationTitle = applicationTitle ?? this.applicationTitle
      ..applicationStatus = applicationStatus ?? this.applicationStatus
      ..applicationStatusName = applicationStatusName ?? this.applicationStatusName
      ..summaryList = summaryList ?? this.summaryList
      ..applicationList = applicationList ?? this.applicationList
      ..applicationType = applicationType ?? this.applicationType
      ..fieldList = fieldList ?? this.fieldList
      ..createTime = createTime ?? this.createTime
      ..materialSku = materialSku ?? this.materialSku
      ..spNodeList = spNodeList ?? this.spNodeList;
  }
}

UserApproveDetailSummaryList $UserApproveDetailSummaryListFromJson(Map<String, dynamic> json) {
  final UserApproveDetailSummaryList userApproveDetailSummaryList = UserApproveDetailSummaryList();
  final String? item = jsonConvert.convert<String>(json['item']);
  if (item != null) {
    userApproveDetailSummaryList.item = item;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    userApproveDetailSummaryList.content = content;
  }
  return userApproveDetailSummaryList;
}

Map<String, dynamic> $UserApproveDetailSummaryListToJson(UserApproveDetailSummaryList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['item'] = entity.item;
  data['content'] = entity.content;
  return data;
}

extension UserApproveDetailSummaryListExtension on UserApproveDetailSummaryList {
  UserApproveDetailSummaryList copyWith({
    String? item,
    String? content,
  }) {
    return UserApproveDetailSummaryList()
      ..item = item ?? this.item
      ..content = content ?? this.content;
  }
}

UserApproveDetailFieldList $UserApproveDetailFieldListFromJson(Map<String, dynamic> json) {
  final UserApproveDetailFieldList userApproveDetailFieldList = UserApproveDetailFieldList();
  final String? fieldFormName = jsonConvert.convert<String>(json['field_form_name']);
  if (fieldFormName != null) {
    userApproveDetailFieldList.fieldFormName = fieldFormName;
  }
  final String? fieldName = jsonConvert.convert<String>(json['field_name']);
  if (fieldName != null) {
    userApproveDetailFieldList.fieldName = fieldName;
  }
  final String? fieldType = jsonConvert.convert<String>(json['field_type']);
  if (fieldType != null) {
    userApproveDetailFieldList.fieldType = fieldType;
  }
  final List<String>? fieldValue = (json['field_value'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (fieldValue != null) {
    userApproveDetailFieldList.fieldValue = fieldValue;
  }
  final List<dynamic>? tableList = (json['table_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (tableList != null) {
    userApproveDetailFieldList.tableList = tableList;
  }
  final List<dynamic>? attachmentList = (json['attachment_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (attachmentList != null) {
    userApproveDetailFieldList.attachmentList = attachmentList;
  }
  return userApproveDetailFieldList;
}

Map<String, dynamic> $UserApproveDetailFieldListToJson(UserApproveDetailFieldList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['field_form_name'] = entity.fieldFormName;
  data['field_name'] = entity.fieldName;
  data['field_type'] = entity.fieldType;
  data['field_value'] = entity.fieldValue;
  data['table_list'] = entity.tableList;
  data['attachment_list'] = entity.attachmentList;
  return data;
}

extension UserApproveDetailFieldListExtension on UserApproveDetailFieldList {
  UserApproveDetailFieldList copyWith({
    String? fieldFormName,
    String? fieldName,
    String? fieldType,
    List<String>? fieldValue,
    List<dynamic>? tableList,
    List<dynamic>? attachmentList,
  }) {
    return UserApproveDetailFieldList()
      ..fieldFormName = fieldFormName ?? this.fieldFormName
      ..fieldName = fieldName ?? this.fieldName
      ..fieldType = fieldType ?? this.fieldType
      ..fieldValue = fieldValue ?? this.fieldValue
      ..tableList = tableList ?? this.tableList
      ..attachmentList = attachmentList ?? this.attachmentList;
  }
}

UserApproveDetailMaterialSku $UserApproveDetailMaterialSkuFromJson(Map<String, dynamic> json) {
  final UserApproveDetailMaterialSku userApproveDetailMaterialSku = UserApproveDetailMaterialSku();
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    userApproveDetailMaterialSku.total = total;
  }
  final List<UserApproveDetailMaterialSkuList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<UserApproveDetailMaterialSkuList>(e) as UserApproveDetailMaterialSkuList).toList();
  if (list != null) {
    userApproveDetailMaterialSku.list = list;
  }
  return userApproveDetailMaterialSku;
}

Map<String, dynamic> $UserApproveDetailMaterialSkuToJson(UserApproveDetailMaterialSku entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension UserApproveDetailMaterialSkuExtension on UserApproveDetailMaterialSku {
  UserApproveDetailMaterialSku copyWith({
    int? total,
    List<UserApproveDetailMaterialSkuList>? list,
  }) {
    return UserApproveDetailMaterialSku()
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

UserApproveDetailMaterialSkuList $UserApproveDetailMaterialSkuListFromJson(Map<String, dynamic> json) {
  final UserApproveDetailMaterialSkuList userApproveDetailMaterialSkuList = UserApproveDetailMaterialSkuList();
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    userApproveDetailMaterialSkuList.number = number;
  }
  final String? productName = jsonConvert.convert<String>(json['product_name']);
  if (productName != null) {
    userApproveDetailMaterialSkuList.productName = productName;
  }
  final String? productNumber = jsonConvert.convert<String>(json['product_number']);
  if (productNumber != null) {
    userApproveDetailMaterialSkuList.productNumber = productNumber;
  }
  final String? specification = jsonConvert.convert<String>(json['specification']);
  if (specification != null) {
    userApproveDetailMaterialSkuList.specification = specification;
  }
  final String? coverUrl = jsonConvert.convert<String>(json['cover_url']);
  if (coverUrl != null) {
    userApproveDetailMaterialSkuList.coverUrl = coverUrl;
  }
  final String? num = jsonConvert.convert<String>(json['num']);
  if (num != null) {
    userApproveDetailMaterialSkuList.num = num;
  }
  final String? unitName = jsonConvert.convert<String>(json['unit_name']);
  if (unitName != null) {
    userApproveDetailMaterialSkuList.unitName = unitName;
  }
  return userApproveDetailMaterialSkuList;
}

Map<String, dynamic> $UserApproveDetailMaterialSkuListToJson(UserApproveDetailMaterialSkuList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['number'] = entity.number;
  data['product_name'] = entity.productName;
  data['product_number'] = entity.productNumber;
  data['specification'] = entity.specification;
  data['cover_url'] = entity.coverUrl;
  data['num'] = entity.num;
  data['unit_name'] = entity.unitName;
  return data;
}

extension UserApproveDetailMaterialSkuListExtension on UserApproveDetailMaterialSkuList {
  UserApproveDetailMaterialSkuList copyWith({
    String? number,
    String? productName,
    String? productNumber,
    String? specification,
    String? coverUrl,
    String? num,
    String? unitName,
  }) {
    return UserApproveDetailMaterialSkuList()
      ..number = number ?? this.number
      ..productName = productName ?? this.productName
      ..productNumber = productNumber ?? this.productNumber
      ..specification = specification ?? this.specification
      ..coverUrl = coverUrl ?? this.coverUrl
      ..num = num ?? this.num
      ..unitName = unitName ?? this.unitName;
  }
}

UserApproveDetailSpNodeList $UserApproveDetailSpNodeListFromJson(Map<String, dynamic> json) {
  final UserApproveDetailSpNodeList userApproveDetailSpNodeList = UserApproveDetailSpNodeList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    userApproveDetailSpNodeList.uuid = uuid;
  }
  final String? nodeName = jsonConvert.convert<String>(json['node_name']);
  if (nodeName != null) {
    userApproveDetailSpNodeList.nodeName = nodeName;
  }
  final String? subNodeName = jsonConvert.convert<String>(json['sub_node_name']);
  if (subNodeName != null) {
    userApproveDetailSpNodeList.subNodeName = subNodeName;
  }
  final int? nodeStatus = jsonConvert.convert<int>(json['node_status']);
  if (nodeStatus != null) {
    userApproveDetailSpNodeList.nodeStatus = nodeStatus;
  }
  final String? nodeStatusName = jsonConvert.convert<String>(json['node_status_name']);
  if (nodeStatusName != null) {
    userApproveDetailSpNodeList.nodeStatusName = nodeStatusName;
  }
  final List<dynamic>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => e).toList();
  if (userList != null) {
    userApproveDetailSpNodeList.userList = userList;
  }
  return userApproveDetailSpNodeList;
}

Map<String, dynamic> $UserApproveDetailSpNodeListToJson(UserApproveDetailSpNodeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['node_name'] = entity.nodeName;
  data['sub_node_name'] = entity.subNodeName;
  data['node_status'] = entity.nodeStatus;
  data['node_status_name'] = entity.nodeStatusName;
  data['user_list'] = entity.userList;
  return data;
}

extension UserApproveDetailSpNodeListExtension on UserApproveDetailSpNodeList {
  UserApproveDetailSpNodeList copyWith({
    String? uuid,
    String? nodeName,
    String? subNodeName,
    int? nodeStatus,
    String? nodeStatusName,
    List<dynamic>? userList,
  }) {
    return UserApproveDetailSpNodeList()
      ..uuid = uuid ?? this.uuid
      ..nodeName = nodeName ?? this.nodeName
      ..subNodeName = subNodeName ?? this.subNodeName
      ..nodeStatus = nodeStatus ?? this.nodeStatus
      ..nodeStatusName = nodeStatusName ?? this.nodeStatusName
      ..userList = userList ?? this.userList;
  }
}