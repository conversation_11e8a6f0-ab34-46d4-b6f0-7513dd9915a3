import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';

ContractCreateEntity $ContractCreateEntityFromJson(Map<String, dynamic> json) {
  final ContractCreateEntity contractCreateEntity = ContractCreateEntity();
  final String? contractUuid = jsonConvert.convert<String>(json['contract_uuid']);
  if (contractUuid != null) {
    contractCreateEntity.contractUuid = contractUuid;
  }
  final String? contractUrl = jsonConvert.convert<String>(json['contract_url']);
  if (contractUrl != null) {
    contractCreateEntity.contractUrl = contractUrl;
  }
  return contractCreateEntity;
}

Map<String, dynamic> $ContractCreateEntityToJson(ContractCreateEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['contract_uuid'] = entity.contractUuid;
  data['contract_url'] = entity.contractUrl;
  return data;
}

extension ContractCreateEntityExtension on ContractCreateEntity {
  ContractCreateEntity copyWith({
    String? contractUuid,
    String? contractUrl,
  }) {
    return ContractCreateEntity()
      ..contractUuid = contractUuid ?? this.contractUuid
      ..contractUrl = contractUrl ?? this.contractUrl;
  }
}