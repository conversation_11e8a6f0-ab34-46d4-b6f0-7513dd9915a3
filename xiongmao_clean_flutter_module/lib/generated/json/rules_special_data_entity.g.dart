import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/rules_special_data_entity.dart';

RulesSpecialDataEntity $RulesSpecialDataEntityFromJson(Map<String, dynamic> json) {
  final RulesSpecialDataEntity rulesSpecialDataEntity = RulesSpecialDataEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    rulesSpecialDataEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    rulesSpecialDataEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    rulesSpecialDataEntity.total = total;
  }
  final List<RulesSpecialDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<RulesSpecialDataList>(e) as RulesSpecialDataList).toList();
  if (list != null) {
    rulesSpecialDataEntity.list = list;
  }
  return rulesSpecialDataEntity;
}

Map<String, dynamic> $RulesSpecialDataEntityToJson(RulesSpecialDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension RulesSpecialDataEntityExtension on RulesSpecialDataEntity {
  RulesSpecialDataEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<RulesSpecialDataList>? list,
  }) {
    return RulesSpecialDataEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

RulesSpecialDataList $RulesSpecialDataListFromJson(Map<String, dynamic> json) {
  final RulesSpecialDataList rulesSpecialDataList = RulesSpecialDataList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    rulesSpecialDataList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    rulesSpecialDataList.userName = userName;
  }
  final String? dataType = jsonConvert.convert<String>(json['data_type']);
  if (dataType != null) {
    rulesSpecialDataList.dataType = dataType;
  }
  final List<RulesSpecialDataListDepartmentList>? departmentList = (json['department_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<RulesSpecialDataListDepartmentList>(e) as RulesSpecialDataListDepartmentList).toList();
  if (departmentList != null) {
    rulesSpecialDataList.departmentList = departmentList;
  }
  return rulesSpecialDataList;
}

Map<String, dynamic> $RulesSpecialDataListToJson(RulesSpecialDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['data_type'] = entity.dataType;
  data['department_list'] = entity.departmentList?.map((v) => v.toJson()).toList();
  return data;
}

extension RulesSpecialDataListExtension on RulesSpecialDataList {
  RulesSpecialDataList copyWith({
    String? uuid,
    String? userName,
    String? dataType,
    List<RulesSpecialDataListDepartmentList>? departmentList,
  }) {
    return RulesSpecialDataList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..dataType = dataType ?? this.dataType
      ..departmentList = departmentList ?? this.departmentList;
  }
}

RulesSpecialDataListDepartmentList $RulesSpecialDataListDepartmentListFromJson(Map<String, dynamic> json) {
  final RulesSpecialDataListDepartmentList rulesSpecialDataListDepartmentList = RulesSpecialDataListDepartmentList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    rulesSpecialDataListDepartmentList.uuid = uuid;
  }
  final String? departmentName = jsonConvert.convert<String>(json['department_name']);
  if (departmentName != null) {
    rulesSpecialDataListDepartmentList.departmentName = departmentName;
  }
  return rulesSpecialDataListDepartmentList;
}

Map<String, dynamic> $RulesSpecialDataListDepartmentListToJson(RulesSpecialDataListDepartmentList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['department_name'] = entity.departmentName;
  return data;
}

extension RulesSpecialDataListDepartmentListExtension on RulesSpecialDataListDepartmentList {
  RulesSpecialDataListDepartmentList copyWith({
    String? uuid,
    String? departmentName,
  }) {
    return RulesSpecialDataListDepartmentList()
      ..uuid = uuid ?? this.uuid
      ..departmentName = departmentName ?? this.departmentName;
  }
}