import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_policy_invoicing_record_one_entity.dart';

InsurePolicyInvoicingRecordOneEntity $InsurePolicyInvoicingRecordOneEntityFromJson(Map<String, dynamic> json) {
  final InsurePolicyInvoicingRecordOneEntity insurePolicyInvoicingRecordOneEntity = InsurePolicyInvoicingRecordOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    insurePolicyInvoicingRecordOneEntity.uuid = uuid;
  }
  final String? orderTotal = jsonConvert.convert<String>(json['order_total']);
  if (orderTotal != null) {
    insurePolicyInvoicingRecordOneEntity.orderTotal = orderTotal;
  }
  final String? refundTotal = jsonConvert.convert<String>(json['refund_total']);
  if (refundTotal != null) {
    insurePolicyInvoicingRecordOneEntity.refundTotal = refundTotal;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    insurePolicyInvoicingRecordOneEntity.createTime = createTime;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    insurePolicyInvoicingRecordOneEntity.amount = amount;
  }
  final String? invoiceTitle = jsonConvert.convert<String>(json['invoice_title']);
  if (invoiceTitle != null) {
    insurePolicyInvoicingRecordOneEntity.invoiceTitle = invoiceTitle;
  }
  final String? amountType = jsonConvert.convert<String>(json['amount_type']);
  if (amountType != null) {
    insurePolicyInvoicingRecordOneEntity.amountType = amountType;
  }
  final String? amountTypeName = jsonConvert.convert<String>(json['amount_type_name']);
  if (amountTypeName != null) {
    insurePolicyInvoicingRecordOneEntity.amountTypeName = amountTypeName;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    insurePolicyInvoicingRecordOneEntity.email = email;
  }
  final String? invoiceType = jsonConvert.convert<String>(json['invoice_type']);
  if (invoiceType != null) {
    insurePolicyInvoicingRecordOneEntity.invoiceType = invoiceType;
  }
  final String? invoiceTypeName = jsonConvert.convert<String>(json['invoice_type_name']);
  if (invoiceTypeName != null) {
    insurePolicyInvoicingRecordOneEntity.invoiceTypeName = invoiceTypeName;
  }
  final String? identificationNumber = jsonConvert.convert<String>(json['identification_number']);
  if (identificationNumber != null) {
    insurePolicyInvoicingRecordOneEntity.identificationNumber = identificationNumber;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    insurePolicyInvoicingRecordOneEntity.address = address;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    insurePolicyInvoicingRecordOneEntity.mobile = mobile;
  }
  final String? bank = jsonConvert.convert<String>(json['bank']);
  if (bank != null) {
    insurePolicyInvoicingRecordOneEntity.bank = bank;
  }
  final String? bankNo = jsonConvert.convert<String>(json['bank_no']);
  if (bankNo != null) {
    insurePolicyInvoicingRecordOneEntity.bankNo = bankNo;
  }
  final String? isMerge = jsonConvert.convert<String>(json['is_merge']);
  if (isMerge != null) {
    insurePolicyInvoicingRecordOneEntity.isMerge = isMerge;
  }
  final String? isMergeName = jsonConvert.convert<String>(json['is_merge_name']);
  if (isMergeName != null) {
    insurePolicyInvoicingRecordOneEntity.isMergeName = isMergeName;
  }
  final List<InsurePolicyInvoicingRecordOneOrderList>? orderList = (json['order_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsurePolicyInvoicingRecordOneOrderList>(e) as InsurePolicyInvoicingRecordOneOrderList).toList();
  if (orderList != null) {
    insurePolicyInvoicingRecordOneEntity.orderList = orderList;
  }
  return insurePolicyInvoicingRecordOneEntity;
}

Map<String, dynamic> $InsurePolicyInvoicingRecordOneEntityToJson(InsurePolicyInvoicingRecordOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['order_total'] = entity.orderTotal;
  data['refund_total'] = entity.refundTotal;
  data['create_time'] = entity.createTime;
  data['amount'] = entity.amount;
  data['invoice_title'] = entity.invoiceTitle;
  data['amount_type'] = entity.amountType;
  data['amount_type_name'] = entity.amountTypeName;
  data['email'] = entity.email;
  data['invoice_type'] = entity.invoiceType;
  data['invoice_type_name'] = entity.invoiceTypeName;
  data['identification_number'] = entity.identificationNumber;
  data['address'] = entity.address;
  data['mobile'] = entity.mobile;
  data['bank'] = entity.bank;
  data['bank_no'] = entity.bankNo;
  data['is_merge'] = entity.isMerge;
  data['is_merge_name'] = entity.isMergeName;
  data['order_list'] = entity.orderList?.map((v) => v.toJson()).toList();
  return data;
}

extension InsurePolicyInvoicingRecordOneEntityExtension on InsurePolicyInvoicingRecordOneEntity {
  InsurePolicyInvoicingRecordOneEntity copyWith({
    String? uuid,
    String? orderTotal,
    String? refundTotal,
    String? createTime,
    String? amount,
    String? invoiceTitle,
    String? amountType,
    String? amountTypeName,
    String? email,
    String? invoiceType,
    String? invoiceTypeName,
    String? identificationNumber,
    String? address,
    String? mobile,
    String? bank,
    String? bankNo,
    String? isMerge,
    String? isMergeName,
    List<InsurePolicyInvoicingRecordOneOrderList>? orderList,
  }) {
    return InsurePolicyInvoicingRecordOneEntity()
      ..uuid = uuid ?? this.uuid
      ..orderTotal = orderTotal ?? this.orderTotal
      ..refundTotal = refundTotal ?? this.refundTotal
      ..createTime = createTime ?? this.createTime
      ..amount = amount ?? this.amount
      ..invoiceTitle = invoiceTitle ?? this.invoiceTitle
      ..amountType = amountType ?? this.amountType
      ..amountTypeName = amountTypeName ?? this.amountTypeName
      ..email = email ?? this.email
      ..invoiceType = invoiceType ?? this.invoiceType
      ..invoiceTypeName = invoiceTypeName ?? this.invoiceTypeName
      ..identificationNumber = identificationNumber ?? this.identificationNumber
      ..address = address ?? this.address
      ..mobile = mobile ?? this.mobile
      ..bank = bank ?? this.bank
      ..bankNo = bankNo ?? this.bankNo
      ..isMerge = isMerge ?? this.isMerge
      ..isMergeName = isMergeName ?? this.isMergeName
      ..orderList = orderList ?? this.orderList;
  }
}

InsurePolicyInvoicingRecordOneOrderList $InsurePolicyInvoicingRecordOneOrderListFromJson(Map<String, dynamic> json) {
  final InsurePolicyInvoicingRecordOneOrderList insurePolicyInvoicingRecordOneOrderList = InsurePolicyInvoicingRecordOneOrderList();
  final String? orderNumber = jsonConvert.convert<String>(json['order_number']);
  if (orderNumber != null) {
    insurePolicyInvoicingRecordOneOrderList.orderNumber = orderNumber;
  }
  final String? policyNo = jsonConvert.convert<String>(json['policy_no']);
  if (policyNo != null) {
    insurePolicyInvoicingRecordOneOrderList.policyNo = policyNo;
  }
  final String? dateTime = jsonConvert.convert<String>(json['date_time']);
  if (dateTime != null) {
    insurePolicyInvoicingRecordOneOrderList.dateTime = dateTime;
  }
  final String? insuredUserName = jsonConvert.convert<String>(json['insured_user_name']);
  if (insuredUserName != null) {
    insurePolicyInvoicingRecordOneOrderList.insuredUserName = insuredUserName;
  }
  final String? insuredUserIdNumber = jsonConvert.convert<String>(json['insured_user_id_number']);
  if (insuredUserIdNumber != null) {
    insurePolicyInvoicingRecordOneOrderList.insuredUserIdNumber = insuredUserIdNumber;
  }
  final String? insuredUserStatusName = jsonConvert.convert<String>(json['insured_user_status_name']);
  if (insuredUserStatusName != null) {
    insurePolicyInvoicingRecordOneOrderList.insuredUserStatusName = insuredUserStatusName;
  }
  final String? orderStatusName = jsonConvert.convert<String>(json['order_status_name']);
  if (orderStatusName != null) {
    insurePolicyInvoicingRecordOneOrderList.orderStatusName = orderStatusName;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    insurePolicyInvoicingRecordOneOrderList.amount = amount;
  }
  return insurePolicyInvoicingRecordOneOrderList;
}

Map<String, dynamic> $InsurePolicyInvoicingRecordOneOrderListToJson(InsurePolicyInvoicingRecordOneOrderList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['order_number'] = entity.orderNumber;
  data['policy_no'] = entity.policyNo;
  data['date_time'] = entity.dateTime;
  data['insured_user_name'] = entity.insuredUserName;
  data['insured_user_id_number'] = entity.insuredUserIdNumber;
  data['insured_user_status_name'] = entity.insuredUserStatusName;
  data['order_status_name'] = entity.orderStatusName;
  data['amount'] = entity.amount;
  return data;
}

extension InsurePolicyInvoicingRecordOneOrderListExtension on InsurePolicyInvoicingRecordOneOrderList {
  InsurePolicyInvoicingRecordOneOrderList copyWith({
    String? orderNumber,
    String? policyNo,
    String? dateTime,
    String? insuredUserName,
    String? insuredUserIdNumber,
    String? insuredUserStatusName,
    String? orderStatusName,
    String? amount,
  }) {
    return InsurePolicyInvoicingRecordOneOrderList()
      ..orderNumber = orderNumber ?? this.orderNumber
      ..policyNo = policyNo ?? this.policyNo
      ..dateTime = dateTime ?? this.dateTime
      ..insuredUserName = insuredUserName ?? this.insuredUserName
      ..insuredUserIdNumber = insuredUserIdNumber ?? this.insuredUserIdNumber
      ..insuredUserStatusName = insuredUserStatusName ?? this.insuredUserStatusName
      ..orderStatusName = orderStatusName ?? this.orderStatusName
      ..amount = amount ?? this.amount;
  }
}