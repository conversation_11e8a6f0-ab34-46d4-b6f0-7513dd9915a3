import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/sku_list_entity.dart';

SkuListEntity $SkuListEntityFromJson(Map<String, dynamic> json) {
  final SkuListEntity skuListEntity = SkuListEntity();
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    skuListEntity.number = number;
  }
  final String? num = jsonConvert.convert<String>(json['num']);
  if (num != null) {
    skuListEntity.num = num;
  }
  return skuListEntity;
}

Map<String, dynamic> $SkuListEntityToJson(SkuListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['number'] = entity.number;
  data['num'] = entity.num;
  return data;
}

extension SkuListEntityExtension on SkuListEntity {
  SkuListEntity copyWith({
    String? number,
    String? num,
  }) {
    return SkuListEntity()
      ..number = number ?? this.number
      ..num = num ?? this.num;
  }
}