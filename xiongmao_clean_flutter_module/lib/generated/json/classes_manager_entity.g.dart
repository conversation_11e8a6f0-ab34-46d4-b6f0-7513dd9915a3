import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_manager_entity.dart';

ClassesManagerEntity $ClassesManagerEntityFromJson(Map<String, dynamic> json) {
  final ClassesManagerEntity classesManagerEntity = ClassesManagerEntity();
  final List<ClassesManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ClassesManagerList>(e) as ClassesManagerList).toList();
  if (list != null) {
    classesManagerEntity.list = list;
  }
  return classesManagerEntity;
}

Map<String, dynamic> $ClassesManagerEntityToJson(ClassesManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ClassesManagerEntityExtension on ClassesManagerEntity {
  ClassesManagerEntity copyWith({
    List<ClassesManagerList>? list,
  }) {
    return ClassesManagerEntity()
      ..list = list ?? this.list;
  }
}

ClassesManagerList $ClassesManagerListFromJson(Map<String, dynamic> json) {
  final ClassesManagerList classesManagerList = ClassesManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    classesManagerList.uuid = uuid;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    classesManagerList.className = className;
  }
  final String? isNoClock = jsonConvert.convert<String>(json['is_no_clock']);
  if (isNoClock != null) {
    classesManagerList.isNoClock = isNoClock;
  }
  final String? classType = jsonConvert.convert<String>(json['class_type']);
  if (classType != null) {
    classesManagerList.classType = classType;
  }
  final String? isDefaultRest = jsonConvert.convert<String>(json['is_default_rest']);
  if (isDefaultRest != null) {
    classesManagerList.isDefaultRest = isDefaultRest;
  }
  final String? classTypeName = jsonConvert.convert<String>(json['class_type_name']);
  if (classTypeName != null) {
    classesManagerList.classTypeName = classTypeName;
  }
  final String? isNoClockName = jsonConvert.convert<String>(json['is_no_clock_name']);
  if (isNoClockName != null) {
    classesManagerList.isNoClockName = isNoClockName;
  }
  final String? isDefaultRestName = jsonConvert.convert<String>(json['is_default_rest_name']);
  if (isDefaultRestName != null) {
    classesManagerList.isDefaultRestName = isDefaultRestName;
  }
  final String? classTime = jsonConvert.convert<String>(json['class_time']);
  if (classTime != null) {
    classesManagerList.classTime = classTime;
  }
  final String? avgLineTime = jsonConvert.convert<String>(json['avg_line_time']);
  if (avgLineTime != null) {
    classesManagerList.avgLineTime = avgLineTime;
  }
  final String? avgLineIsToday = jsonConvert.convert<String>(json['avg_line_is_today']);
  if (avgLineIsToday != null) {
    classesManagerList.avgLineIsToday = avgLineIsToday;
  }
  final String? outClassDesc = jsonConvert.convert<String>(json['out_class_desc']);
  if (outClassDesc != null) {
    classesManagerList.outClassDesc = outClassDesc;
  }
  final String? inClassDesc = jsonConvert.convert<String>(json['in_class_desc']);
  if (inClassDesc != null) {
    classesManagerList.inClassDesc = inClassDesc;
  }
  final int? segmentTotal = jsonConvert.convert<int>(json['segment_total']);
  if (segmentTotal != null) {
    classesManagerList.segmentTotal = segmentTotal;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    classesManagerList.isSelected = isSelected;
  }
  return classesManagerList;
}

Map<String, dynamic> $ClassesManagerListToJson(ClassesManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['class_name'] = entity.className;
  data['is_no_clock'] = entity.isNoClock;
  data['class_type'] = entity.classType;
  data['is_default_rest'] = entity.isDefaultRest;
  data['class_type_name'] = entity.classTypeName;
  data['is_no_clock_name'] = entity.isNoClockName;
  data['is_default_rest_name'] = entity.isDefaultRestName;
  data['class_time'] = entity.classTime;
  data['avg_line_time'] = entity.avgLineTime;
  data['avg_line_is_today'] = entity.avgLineIsToday;
  data['out_class_desc'] = entity.outClassDesc;
  data['in_class_desc'] = entity.inClassDesc;
  data['segment_total'] = entity.segmentTotal;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension ClassesManagerListExtension on ClassesManagerList {
  ClassesManagerList copyWith({
    String? uuid,
    String? className,
    String? isNoClock,
    String? classType,
    String? isDefaultRest,
    String? classTypeName,
    String? isNoClockName,
    String? isDefaultRestName,
    String? classTime,
    String? avgLineTime,
    String? avgLineIsToday,
    String? outClassDesc,
    String? inClassDesc,
    int? segmentTotal,
    bool? isSelected,
  }) {
    return ClassesManagerList()
      ..uuid = uuid ?? this.uuid
      ..className = className ?? this.className
      ..isNoClock = isNoClock ?? this.isNoClock
      ..classType = classType ?? this.classType
      ..isDefaultRest = isDefaultRest ?? this.isDefaultRest
      ..classTypeName = classTypeName ?? this.classTypeName
      ..isNoClockName = isNoClockName ?? this.isNoClockName
      ..isDefaultRestName = isDefaultRestName ?? this.isDefaultRestName
      ..classTime = classTime ?? this.classTime
      ..avgLineTime = avgLineTime ?? this.avgLineTime
      ..avgLineIsToday = avgLineIsToday ?? this.avgLineIsToday
      ..outClassDesc = outClassDesc ?? this.outClassDesc
      ..inClassDesc = inClassDesc ?? this.inClassDesc
      ..segmentTotal = segmentTotal ?? this.segmentTotal
      ..isSelected = isSelected ?? this.isSelected;
  }
}