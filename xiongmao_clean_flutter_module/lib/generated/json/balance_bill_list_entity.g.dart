import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';

BalanceBillListEntity $BalanceBillListEntityFromJson(Map<String, dynamic> json) {
  final BalanceBillListEntity balanceBillListEntity = BalanceBillListEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    balanceBillListEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    balanceBillListEntity.size = size;
  }
  final String? srTotal = jsonConvert.convert<String>(json['sr_total']);
  if (srTotal != null) {
    balanceBillListEntity.srTotal = srTotal;
  }
  final String? zcTotal = jsonConvert.convert<String>(json['zc_total']);
  if (zcTotal != null) {
    balanceBillListEntity.zcTotal = zcTotal;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    balanceBillListEntity.total = total;
  }
  final List<BalanceBillListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BalanceBillListList>(e) as BalanceBillListList).toList();
  if (list != null) {
    balanceBillListEntity.list = list;
  }
  return balanceBillListEntity;
}

Map<String, dynamic> $BalanceBillListEntityToJson(BalanceBillListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['sr_total'] = entity.srTotal;
  data['zc_total'] = entity.zcTotal;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension BalanceBillListEntityExtension on BalanceBillListEntity {
  BalanceBillListEntity copyWith({
    int? page,
    int? size,
    String? srTotal,
    String? zcTotal,
    int? total,
    List<BalanceBillListList>? list,
  }) {
    return BalanceBillListEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..srTotal = srTotal ?? this.srTotal
      ..zcTotal = zcTotal ?? this.zcTotal
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

BalanceBillListList $BalanceBillListListFromJson(Map<String, dynamic> json) {
  final BalanceBillListList balanceBillListList = BalanceBillListList();
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    balanceBillListList.userName = userName;
  }
  final String? beforeBalance = jsonConvert.convert<String>(json['before_balance']);
  if (beforeBalance != null) {
    balanceBillListList.beforeBalance = beforeBalance;
  }
  final String? money = jsonConvert.convert<String>(json['money']);
  if (money != null) {
    balanceBillListList.money = money;
  }
  final String? afterBalance = jsonConvert.convert<String>(json['after_balance']);
  if (afterBalance != null) {
    balanceBillListList.afterBalance = afterBalance;
  }
  final String? profitType = jsonConvert.convert<String>(json['profit_type']);
  if (profitType != null) {
    balanceBillListList.profitType = profitType;
  }
  final String? instruction = jsonConvert.convert<String>(json['instruction']);
  if (instruction != null) {
    balanceBillListList.instruction = instruction;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    balanceBillListList.createTime = createTime;
  }
  return balanceBillListList;
}

Map<String, dynamic> $BalanceBillListListToJson(BalanceBillListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_name'] = entity.userName;
  data['before_balance'] = entity.beforeBalance;
  data['money'] = entity.money;
  data['after_balance'] = entity.afterBalance;
  data['profit_type'] = entity.profitType;
  data['instruction'] = entity.instruction;
  data['create_time'] = entity.createTime;
  return data;
}

extension BalanceBillListListExtension on BalanceBillListList {
  BalanceBillListList copyWith({
    String? userName,
    String? beforeBalance,
    String? money,
    String? afterBalance,
    String? profitType,
    String? instruction,
    String? createTime,
  }) {
    return BalanceBillListList()
      ..userName = userName ?? this.userName
      ..beforeBalance = beforeBalance ?? this.beforeBalance
      ..money = money ?? this.money
      ..afterBalance = afterBalance ?? this.afterBalance
      ..profitType = profitType ?? this.profitType
      ..instruction = instruction ?? this.instruction
      ..createTime = createTime ?? this.createTime;
  }
}