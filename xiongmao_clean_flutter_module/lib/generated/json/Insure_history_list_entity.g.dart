import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/Insure_history_list_entity.dart';

InsureHistoryListEntity $InsureHistoryListEntityFromJson(Map<String, dynamic> json) {
  final InsureHistoryListEntity insureHistoryListEntity = InsureHistoryListEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    insureHistoryListEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    insureHistoryListEntity.size = size;
  }
  final int? claimsTotal = jsonConvert.convert<int>(json['claims_total']);
  if (claimsTotal != null) {
    insureHistoryListEntity.claimsTotal = claimsTotal;
  }
  final int? allTotal = jsonConvert.convert<int>(json['all_total']);
  if (allTotal != null) {
    insureHistoryListEntity.allTotal = allTotal;
  }
  final int? expiredTotal = jsonConvert.convert<int>(json['expired_total']);
  if (expiredTotal != null) {
    insureHistoryListEntity.expiredTotal = expiredTotal;
  }
  final int? nearWeekTotal = jsonConvert.convert<int>(json['near_week_total']);
  if (nearWeekTotal != null) {
    insureHistoryListEntity.nearWeekTotal = nearWeekTotal;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    insureHistoryListEntity.total = total;
  }
  final List<InsureHistoryListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsureHistoryListList>(e) as InsureHistoryListList).toList();
  if (list != null) {
    insureHistoryListEntity.list = list;
  }
  return insureHistoryListEntity;
}

Map<String, dynamic> $InsureHistoryListEntityToJson(InsureHistoryListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['claims_total'] = entity.claimsTotal;
  data['all_total'] = entity.allTotal;
  data['expired_total'] = entity.expiredTotal;
  data['near_week_total'] = entity.nearWeekTotal;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureHistoryListEntityExtension on InsureHistoryListEntity {
  InsureHistoryListEntity copyWith({
    int? page,
    int? size,
    int? claimsTotal,
    int? allTotal,
    int? expiredTotal,
    int? nearWeekTotal,
    int? total,
    List<InsureHistoryListList>? list,
  }) {
    return InsureHistoryListEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..claimsTotal = claimsTotal ?? this.claimsTotal
      ..allTotal = allTotal ?? this.allTotal
      ..expiredTotal = expiredTotal ?? this.expiredTotal
      ..nearWeekTotal = nearWeekTotal ?? this.nearWeekTotal
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

InsureHistoryListList $InsureHistoryListListFromJson(Map<String, dynamic> json) {
  final InsureHistoryListList insureHistoryListList = InsureHistoryListList();
  final String? orderNumber = jsonConvert.convert<String>(json['order_number']);
  if (orderNumber != null) {
    insureHistoryListList.orderNumber = orderNumber;
  }
  final String? insuranceCompanyName = jsonConvert.convert<String>(json['insurance_company_name']);
  if (insuranceCompanyName != null) {
    insureHistoryListList.insuranceCompanyName = insuranceCompanyName;
  }
  final String? policyNo = jsonConvert.convert<String>(json['policy_no']);
  if (policyNo != null) {
    insureHistoryListList.policyNo = policyNo;
  }
  final String? productName = jsonConvert.convert<String>(json['product_name']);
  if (productName != null) {
    insureHistoryListList.productName = productName;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    insureHistoryListList.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    insureHistoryListList.endTime = endTime;
  }
  final String? orderStatusName = jsonConvert.convert<String>(json['order_status_name']);
  if (orderStatusName != null) {
    insureHistoryListList.orderStatusName = orderStatusName;
  }
  final String? refundStatus = jsonConvert.convert<String>(json['refund_status']);
  if (refundStatus != null) {
    insureHistoryListList.refundStatus = refundStatus;
  }
  final String? refundStatusName = jsonConvert.convert<String>(json['refund_status_name']);
  if (refundStatusName != null) {
    insureHistoryListList.refundStatusName = refundStatusName;
  }
  final String? insuredUserName = jsonConvert.convert<String>(json['insured_user_name']);
  if (insuredUserName != null) {
    insureHistoryListList.insuredUserName = insuredUserName;
  }
  final String? insuredUserIdNumber = jsonConvert.convert<String>(json['insured_user_id_number']);
  if (insuredUserIdNumber != null) {
    insureHistoryListList.insuredUserIdNumber = insuredUserIdNumber;
  }
  final String? insuredUserStatusName = jsonConvert.convert<String>(json['insured_user_status_name']);
  if (insuredUserStatusName != null) {
    insureHistoryListList.insuredUserStatusName = insuredUserStatusName;
  }
  final String? payTime = jsonConvert.convert<String>(json['pay_time']);
  if (payTime != null) {
    insureHistoryListList.payTime = payTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    insureHistoryListList.createTime = createTime;
  }
  final String? policyRefundTime = jsonConvert.convert<String>(json['policy_refund_time']);
  if (policyRefundTime != null) {
    insureHistoryListList.policyRefundTime = policyRefundTime;
  }
  final String? elecPolicyUrl = jsonConvert.convert<String>(json['elec_policy_url']);
  if (elecPolicyUrl != null) {
    insureHistoryListList.elecPolicyUrl = elecPolicyUrl;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    insureHistoryListList.price = price;
  }
  final String? refundMoney = jsonConvert.convert<String>(json['refund_money']);
  if (refundMoney != null) {
    insureHistoryListList.refundMoney = refundMoney;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    insureHistoryListList.isSelected = isSelected;
  }
  return insureHistoryListList;
}

Map<String, dynamic> $InsureHistoryListListToJson(InsureHistoryListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['order_number'] = entity.orderNumber;
  data['insurance_company_name'] = entity.insuranceCompanyName;
  data['policy_no'] = entity.policyNo;
  data['product_name'] = entity.productName;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['order_status_name'] = entity.orderStatusName;
  data['refund_status'] = entity.refundStatus;
  data['refund_status_name'] = entity.refundStatusName;
  data['insured_user_name'] = entity.insuredUserName;
  data['insured_user_id_number'] = entity.insuredUserIdNumber;
  data['insured_user_status_name'] = entity.insuredUserStatusName;
  data['pay_time'] = entity.payTime;
  data['create_time'] = entity.createTime;
  data['policy_refund_time'] = entity.policyRefundTime;
  data['elec_policy_url'] = entity.elecPolicyUrl;
  data['price'] = entity.price;
  data['refund_money'] = entity.refundMoney;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension InsureHistoryListListExtension on InsureHistoryListList {
  InsureHistoryListList copyWith({
    String? orderNumber,
    String? insuranceCompanyName,
    String? policyNo,
    String? productName,
    String? startTime,
    String? endTime,
    String? orderStatusName,
    String? refundStatus,
    String? refundStatusName,
    String? insuredUserName,
    String? insuredUserIdNumber,
    String? insuredUserStatusName,
    String? payTime,
    String? createTime,
    String? policyRefundTime,
    String? elecPolicyUrl,
    String? price,
    String? refundMoney,
    bool? isSelected,
  }) {
    return InsureHistoryListList()
      ..orderNumber = orderNumber ?? this.orderNumber
      ..insuranceCompanyName = insuranceCompanyName ?? this.insuranceCompanyName
      ..policyNo = policyNo ?? this.policyNo
      ..productName = productName ?? this.productName
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..orderStatusName = orderStatusName ?? this.orderStatusName
      ..refundStatus = refundStatus ?? this.refundStatus
      ..refundStatusName = refundStatusName ?? this.refundStatusName
      ..insuredUserName = insuredUserName ?? this.insuredUserName
      ..insuredUserIdNumber = insuredUserIdNumber ?? this.insuredUserIdNumber
      ..insuredUserStatusName = insuredUserStatusName ?? this.insuredUserStatusName
      ..payTime = payTime ?? this.payTime
      ..createTime = createTime ?? this.createTime
      ..policyRefundTime = policyRefundTime ?? this.policyRefundTime
      ..elecPolicyUrl = elecPolicyUrl ?? this.elecPolicyUrl
      ..price = price ?? this.price
      ..refundMoney = refundMoney ?? this.refundMoney
      ..isSelected = isSelected ?? this.isSelected;
  }
}