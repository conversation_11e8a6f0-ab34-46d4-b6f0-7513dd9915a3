import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_one_entity.dart';

WorkOvertimeOneEntity $WorkOvertimeOneEntityFromJson(Map<String, dynamic> json) {
  final WorkOvertimeOneEntity workOvertimeOneEntity = WorkOvertimeOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workOvertimeOneEntity.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    workOvertimeOneEntity.projectName = projectName;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    workOvertimeOneEntity.projectUuid = projectUuid;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    workOvertimeOneEntity.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    workOvertimeOneEntity.endTime = endTime;
  }
  final String? overtimeLong = jsonConvert.convert<String>(json['overtime_long']);
  if (overtimeLong != null) {
    workOvertimeOneEntity.overtimeLong = overtimeLong;
  }
  final String? overtimeDate = jsonConvert.convert<String>(json['overtime_date']);
  if (overtimeDate != null) {
    workOvertimeOneEntity.overtimeDate = overtimeDate;
  }
  final String? overtimeType = jsonConvert.convert<String>(json['overtime_type']);
  if (overtimeType != null) {
    workOvertimeOneEntity.overtimeType = overtimeType;
  }
  final String? overtimeTypeName = jsonConvert.convert<String>(json['overtime_type_name']);
  if (overtimeTypeName != null) {
    workOvertimeOneEntity.overtimeTypeName = overtimeTypeName;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    workOvertimeOneEntity.reason = reason;
  }
  final String? createUserName = jsonConvert.convert<String>(json['create_user_name']);
  if (createUserName != null) {
    workOvertimeOneEntity.createUserName = createUserName;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    workOvertimeOneEntity.createTime = createTime;
  }
  final List<WorkOvertimeOneUserList>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkOvertimeOneUserList>(e) as WorkOvertimeOneUserList).toList();
  if (userList != null) {
    workOvertimeOneEntity.userList = userList;
  }
  return workOvertimeOneEntity;
}

Map<String, dynamic> $WorkOvertimeOneEntityToJson(WorkOvertimeOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_uuid'] = entity.projectUuid;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['overtime_long'] = entity.overtimeLong;
  data['overtime_date'] = entity.overtimeDate;
  data['overtime_type'] = entity.overtimeType;
  data['overtime_type_name'] = entity.overtimeTypeName;
  data['reason'] = entity.reason;
  data['create_user_name'] = entity.createUserName;
  data['create_time'] = entity.createTime;
  data['user_list'] = entity.userList?.map((v) => v.toJson()).toList();
  return data;
}

extension WorkOvertimeOneEntityExtension on WorkOvertimeOneEntity {
  WorkOvertimeOneEntity copyWith({
    String? uuid,
    String? projectName,
    String? projectUuid,
    String? startTime,
    String? endTime,
    String? overtimeLong,
    String? overtimeDate,
    String? overtimeType,
    String? overtimeTypeName,
    String? reason,
    String? createUserName,
    String? createTime,
    List<WorkOvertimeOneUserList>? userList,
  }) {
    return WorkOvertimeOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectUuid = projectUuid ?? this.projectUuid
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..overtimeLong = overtimeLong ?? this.overtimeLong
      ..overtimeDate = overtimeDate ?? this.overtimeDate
      ..overtimeType = overtimeType ?? this.overtimeType
      ..overtimeTypeName = overtimeTypeName ?? this.overtimeTypeName
      ..reason = reason ?? this.reason
      ..createUserName = createUserName ?? this.createUserName
      ..createTime = createTime ?? this.createTime
      ..userList = userList ?? this.userList;
  }
}

WorkOvertimeOneUserList $WorkOvertimeOneUserListFromJson(Map<String, dynamic> json) {
  final WorkOvertimeOneUserList workOvertimeOneUserList = WorkOvertimeOneUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workOvertimeOneUserList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    workOvertimeOneUserList.userName = userName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    workOvertimeOneUserList.avatar = avatar;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    workOvertimeOneUserList.projectName = projectName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    workOvertimeOneUserList.jobName = jobName;
  }
  final String? customStatusName = jsonConvert.convert<String>(json['customStatusName']);
  if (customStatusName != null) {
    workOvertimeOneUserList.customStatusName = customStatusName;
  }
  return workOvertimeOneUserList;
}

Map<String, dynamic> $WorkOvertimeOneUserListToJson(WorkOvertimeOneUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['avatar'] = entity.avatar;
  data['project_name'] = entity.projectName;
  data['job_name'] = entity.jobName;
  data['customStatusName'] = entity.customStatusName;
  return data;
}

extension WorkOvertimeOneUserListExtension on WorkOvertimeOneUserList {
  WorkOvertimeOneUserList copyWith({
    String? uuid,
    String? userName,
    String? avatar,
    String? projectName,
    String? jobName,
    String? customStatusName,
  }) {
    return WorkOvertimeOneUserList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..avatar = avatar ?? this.avatar
      ..projectName = projectName ?? this.projectName
      ..jobName = jobName ?? this.jobName
      ..customStatusName = customStatusName ?? this.customStatusName;
  }
}