import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_task_entity.dart';

CleanPlanTaskEntity $CleanPlanTaskEntityFromJson(Map<String, dynamic> json) {
  final CleanPlanTaskEntity cleanPlanTaskEntity = CleanPlanTaskEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    cleanPlanTaskEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    cleanPlanTaskEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    cleanPlanTaskEntity.total = total;
  }
  final int? overTimeTotal = jsonConvert.convert<int>(json['over_time_total']);
  if (overTimeTotal != null) {
    cleanPlanTaskEntity.overTimeTotal = overTimeTotal;
  }
  final int? finishTotal = jsonConvert.convert<int>(json['finished_total']);
  if (finishTotal != null) {
    cleanPlanTaskEntity.finishTotal = finishTotal;
  }
  final int? unFinishTotal = jsonConvert.convert<int>(json['unfinished_total']);
  if (unFinishTotal != null) {
    cleanPlanTaskEntity.unFinishTotal = unFinishTotal;
  }
  final int? waitTotal = jsonConvert.convert<int>(json['wait_total']);
  if (waitTotal != null) {
    cleanPlanTaskEntity.waitTotal = waitTotal;
  }
  final int? myTotal = jsonConvert.convert<int>(json['my_total']);
  if (myTotal != null) {
    cleanPlanTaskEntity.myTotal = myTotal;
  }
  final List<CleanPlanTaskList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanPlanTaskList>(e) as CleanPlanTaskList).toList();
  if (list != null) {
    cleanPlanTaskEntity.list = list;
  }
  return cleanPlanTaskEntity;
}

Map<String, dynamic> $CleanPlanTaskEntityToJson(CleanPlanTaskEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['over_time_total'] = entity.overTimeTotal;
  data['finished_total'] = entity.finishTotal;
  data['unfinished_total'] = entity.unFinishTotal;
  data['wait_total'] = entity.waitTotal;
  data['my_total'] = entity.myTotal;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CleanPlanTaskEntityExtension on CleanPlanTaskEntity {
  CleanPlanTaskEntity copyWith({
    int? page,
    int? size,
    int? total,
    int? overTimeTotal,
    int? finishTotal,
    int? unFinishTotal,
    int? waitTotal,
    int? myTotal,
    List<CleanPlanTaskList>? list,
  }) {
    return CleanPlanTaskEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..overTimeTotal = overTimeTotal ?? this.overTimeTotal
      ..finishTotal = finishTotal ?? this.finishTotal
      ..unFinishTotal = unFinishTotal ?? this.unFinishTotal
      ..waitTotal = waitTotal ?? this.waitTotal
      ..myTotal = myTotal ?? this.myTotal
      ..list = list ?? this.list;
  }
}

CleanPlanTaskList $CleanPlanTaskListFromJson(Map<String, dynamic> json) {
  final CleanPlanTaskList cleanPlanTaskList = CleanPlanTaskList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    cleanPlanTaskList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    cleanPlanTaskList.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    cleanPlanTaskList.projectShortName = projectShortName;
  }
  final String? workContent = jsonConvert.convert<String>(json['work_content']);
  if (workContent != null) {
    cleanPlanTaskList.workContent = workContent;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    cleanPlanTaskList.endDate = endDate;
  }
  final String? finishedTime = jsonConvert.convert<String>(json['finished_time']);
  if (finishedTime != null) {
    cleanPlanTaskList.finishedTime = finishedTime;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    cleanPlanTaskList.status = status;
  }
  final String? statusName = jsonConvert.convert<String>(json['status_name']);
  if (statusName != null) {
    cleanPlanTaskList.statusName = statusName;
  }
  final List<CleanPlanTaskListMediaList>? mediaList = (json['media_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanPlanTaskListMediaList>(e) as CleanPlanTaskListMediaList).toList();
  if (mediaList != null) {
    cleanPlanTaskList.mediaList = mediaList;
  }
  final List<CleanPlanTaskListDealMediaList>? dealMediaList = (json['deal_media_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanPlanTaskListDealMediaList>(e) as CleanPlanTaskListDealMediaList).toList();
  if (dealMediaList != null) {
    cleanPlanTaskList.dealMediaList = dealMediaList;
  }
  return cleanPlanTaskList;
}

Map<String, dynamic> $CleanPlanTaskListToJson(CleanPlanTaskList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['work_content'] = entity.workContent;
  data['end_date'] = entity.endDate;
  data['finished_time'] = entity.finishedTime;
  data['status'] = entity.status;
  data['status_name'] = entity.statusName;
  data['media_list'] = entity.mediaList?.map((v) => v.toJson()).toList();
  data['deal_media_list'] = entity.dealMediaList?.map((v) => v.toJson()).toList();
  return data;
}

extension CleanPlanTaskListExtension on CleanPlanTaskList {
  CleanPlanTaskList copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? workContent,
    String? endDate,
    String? finishedTime,
    String? status,
    String? statusName,
    List<CleanPlanTaskListMediaList>? mediaList,
    List<CleanPlanTaskListDealMediaList>? dealMediaList,
  }) {
    return CleanPlanTaskList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..workContent = workContent ?? this.workContent
      ..endDate = endDate ?? this.endDate
      ..finishedTime = finishedTime ?? this.finishedTime
      ..status = status ?? this.status
      ..statusName = statusName ?? this.statusName
      ..mediaList = mediaList ?? this.mediaList
      ..dealMediaList = dealMediaList ?? this.dealMediaList;
  }
}

CleanPlanTaskListMediaList $CleanPlanTaskListMediaListFromJson(Map<String, dynamic> json) {
  final CleanPlanTaskListMediaList cleanPlanTaskListMediaList = CleanPlanTaskListMediaList();
  final String? mediaType = jsonConvert.convert<String>(json['media_type']);
  if (mediaType != null) {
    cleanPlanTaskListMediaList.mediaType = mediaType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    cleanPlanTaskListMediaList.mediaUrl = mediaUrl;
  }
  return cleanPlanTaskListMediaList;
}

Map<String, dynamic> $CleanPlanTaskListMediaListToJson(CleanPlanTaskListMediaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['media_type'] = entity.mediaType;
  data['media_url'] = entity.mediaUrl;
  return data;
}

extension CleanPlanTaskListMediaListExtension on CleanPlanTaskListMediaList {
  CleanPlanTaskListMediaList copyWith({
    String? mediaType,
    String? mediaUrl,
  }) {
    return CleanPlanTaskListMediaList()
      ..mediaType = mediaType ?? this.mediaType
      ..mediaUrl = mediaUrl ?? this.mediaUrl;
  }
}

CleanPlanTaskListDealMediaList $CleanPlanTaskListDealMediaListFromJson(Map<String, dynamic> json) {
  final CleanPlanTaskListDealMediaList cleanPlanTaskListDealMediaList = CleanPlanTaskListDealMediaList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    cleanPlanTaskListDealMediaList.uuid = uuid;
  }
  final String? mediaType = jsonConvert.convert<String>(json['media_type']);
  if (mediaType != null) {
    cleanPlanTaskListDealMediaList.mediaType = mediaType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    cleanPlanTaskListDealMediaList.mediaUrl = mediaUrl;
  }
  return cleanPlanTaskListDealMediaList;
}

Map<String, dynamic> $CleanPlanTaskListDealMediaListToJson(CleanPlanTaskListDealMediaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['media_type'] = entity.mediaType;
  data['media_url'] = entity.mediaUrl;
  return data;
}

extension CleanPlanTaskListDealMediaListExtension on CleanPlanTaskListDealMediaList {
  CleanPlanTaskListDealMediaList copyWith({
    String? uuid,
    String? mediaType,
    String? mediaUrl,
  }) {
    return CleanPlanTaskListDealMediaList()
      ..uuid = uuid ?? this.uuid
      ..mediaType = mediaType ?? this.mediaType
      ..mediaUrl = mediaUrl ?? this.mediaUrl;
  }
}