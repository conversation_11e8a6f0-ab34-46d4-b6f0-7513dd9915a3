import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/schedule_export_data_entity.dart';

ScheduleExportDataEntity $ScheduleExportDataEntityFromJson(Map<String, dynamic> json) {
  final ScheduleExportDataEntity scheduleExportDataEntity = ScheduleExportDataEntity();
  final String? downloadUrl = jsonConvert.convert<String>(json['download_url']);
  if (downloadUrl != null) {
    scheduleExportDataEntity.downloadUrl = downloadUrl;
  }
  return scheduleExportDataEntity;
}

Map<String, dynamic> $ScheduleExportDataEntityToJson(ScheduleExportDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['download_url'] = entity.downloadUrl;
  return data;
}

extension ScheduleExportDataEntityExtension on ScheduleExportDataEntity {
  ScheduleExportDataEntity copyWith({
    String? downloadUrl,
  }) {
    return ScheduleExportDataEntity()
      ..downloadUrl = downloadUrl ?? this.downloadUrl;
  }
}