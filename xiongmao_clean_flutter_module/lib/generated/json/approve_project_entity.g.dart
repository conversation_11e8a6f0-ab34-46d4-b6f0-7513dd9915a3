import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_project_entity.dart';

ApproveProjectEntity $ApproveProjectEntityFromJson(Map<String, dynamic> json) {
  final ApproveProjectEntity approveProjectEntity = ApproveProjectEntity();
  final List<ApproveProjectList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveProjectList>(e) as ApproveProjectList).toList();
  if (list != null) {
    approveProjectEntity.list = list;
  }
  return approveProjectEntity;
}

Map<String, dynamic> $ApproveProjectEntityToJson(ApproveProjectEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveProjectEntityExtension on ApproveProjectEntity {
  ApproveProjectEntity copyWith({
    List<ApproveProjectList>? list,
  }) {
    return ApproveProjectEntity()
      ..list = list ?? this.list;
  }
}

ApproveProjectList $ApproveProjectListFromJson(Map<String, dynamic> json) {
  final ApproveProjectList approveProjectList = ApproveProjectList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveProjectList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    approveProjectList.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    approveProjectList.projectShortName = projectShortName;
  }
  final String? contractHumanNum = jsonConvert.convert<String>(json['contract_human_num']);
  if (contractHumanNum != null) {
    approveProjectList.contractHumanNum = contractHumanNum;
  }
  final String? totalJobNum = jsonConvert.convert<String>(json['total_job_num']);
  if (totalJobNum != null) {
    approveProjectList.totalJobNum = totalJobNum;
  }
  final String? onJobNum = jsonConvert.convert<String>(json['on_job_num']);
  if (onJobNum != null) {
    approveProjectList.onJobNum = onJobNum;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['is_selected']);
  if (isSelected != null) {
    approveProjectList.isSelected = isSelected;
  }
  return approveProjectList;
}

Map<String, dynamic> $ApproveProjectListToJson(ApproveProjectList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['contract_human_num'] = entity.contractHumanNum;
  data['total_job_num'] = entity.totalJobNum;
  data['on_job_num'] = entity.onJobNum;
  data['is_selected'] = entity.isSelected;
  return data;
}

extension ApproveProjectListExtension on ApproveProjectList {
  ApproveProjectList copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? contractHumanNum,
    String? totalJobNum,
    String? onJobNum,
    bool? isSelected,
  }) {
    return ApproveProjectList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..contractHumanNum = contractHumanNum ?? this.contractHumanNum
      ..totalJobNum = totalJobNum ?? this.totalJobNum
      ..onJobNum = onJobNum ?? this.onJobNum
      ..isSelected = isSelected ?? this.isSelected;
  }
}