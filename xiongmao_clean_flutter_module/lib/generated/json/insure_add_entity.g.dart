import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_add_entity.dart';

InsureAddEntity $InsureAddEntityFromJson(Map<String, dynamic> json) {
  final InsureAddEntity insureAddEntity = InsureAddEntity();
  final int? orderTotal = jsonConvert.convert<int>(json['order_total']);
  if (orderTotal != null) {
    insureAddEntity.orderTotal = orderTotal;
  }
  final int? refundTotal = jsonConvert.convert<int>(json['refund_total']);
  if (refundTotal != null) {
    insureAddEntity.refundTotal = refundTotal;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    insureAddEntity.amount = amount;
  }
  return insureAddEntity;
}

Map<String, dynamic> $InsureAddEntityToJson(InsureAddEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['order_total'] = entity.orderTotal;
  data['refund_total'] = entity.refundTotal;
  data['amount'] = entity.amount;
  return data;
}

extension InsureAddEntityExtension on InsureAddEntity {
  InsureAddEntity copyWith({
    int? orderTotal,
    int? refundTotal,
    String? amount,
  }) {
    return InsureAddEntity()
      ..orderTotal = orderTotal ?? this.orderTotal
      ..refundTotal = refundTotal ?? this.refundTotal
      ..amount = amount ?? this.amount;
  }
}