import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_register_info_entity.dart';

ContractRegisterInfoEntity $ContractRegisterInfoEntityFromJson(Map<String, dynamic> json) {
  final ContractRegisterInfoEntity contractRegisterInfoEntity = ContractRegisterInfoEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    contractRegisterInfoEntity.code = code;
  }
  final String? data = jsonConvert.convert<String>(json['data']);
  if (data != null) {
    contractRegisterInfoEntity.data = data;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    contractRegisterInfoEntity.msg = msg;
  }
  return contractRegisterInfoEntity;
}

Map<String, dynamic> $ContractRegisterInfoEntityToJson(ContractRegisterInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['data'] = entity.data;
  data['msg'] = entity.msg;
  return data;
}

extension ContractRegisterInfoEntityExtension on ContractRegisterInfoEntity {
  ContractRegisterInfoEntity copyWith({
    int? code,
    String? data,
    String? msg,
  }) {
    return ContractRegisterInfoEntity()
      ..code = code ?? this.code
      ..data = data ?? this.data
      ..msg = msg ?? this.msg;
  }
}