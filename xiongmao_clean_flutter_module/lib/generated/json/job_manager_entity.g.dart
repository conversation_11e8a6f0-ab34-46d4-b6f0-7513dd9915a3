import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/job_manager_entity.dart';

JobManagerEntity $JobManagerEntityFromJson(Map<String, dynamic> json) {
  final JobManagerEntity jobManagerEntity = JobManagerEntity();
  final List<JobManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<JobManagerList>(e) as JobManagerList).toList();
  if (list != null) {
    jobManagerEntity.list = list;
  }
  return jobManagerEntity;
}

Map<String, dynamic> $JobManagerEntityToJson(JobManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension JobManagerEntityExtension on JobManagerEntity {
  JobManagerEntity copyWith({
    List<JobManagerList>? list,
  }) {
    return JobManagerEntity()
      ..list = list ?? this.list;
  }
}

JobManagerList $JobManagerListFromJson(Map<String, dynamic> json) {
  final JobManagerList jobManagerList = JobManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    jobManagerList.uuid = uuid;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    jobManagerList.jobName = jobName;
  }
  final String? jobHumanNum = jsonConvert.convert<String>(json['job_human_num']);
  if (jobHumanNum != null) {
    jobManagerList.jobHumanNum = jobHumanNum;
  }
  final String? jobSalary = jsonConvert.convert<String>(json['job_salary']);
  if (jobSalary != null) {
    jobManagerList.jobSalary = jobSalary;
  }
  return jobManagerList;
}

Map<String, dynamic> $JobManagerListToJson(JobManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['job_name'] = entity.jobName;
  data['job_human_num'] = entity.jobHumanNum;
  data['job_salary'] = entity.jobSalary;
  return data;
}

extension JobManagerListExtension on JobManagerList {
  JobManagerList copyWith({
    String? uuid,
    String? jobName,
    String? jobHumanNum,
    String? jobSalary,
  }) {
    return JobManagerList()
      ..uuid = uuid ?? this.uuid
      ..jobName = jobName ?? this.jobName
      ..jobHumanNum = jobHumanNum ?? this.jobHumanNum
      ..jobSalary = jobSalary ?? this.jobSalary;
  }
}