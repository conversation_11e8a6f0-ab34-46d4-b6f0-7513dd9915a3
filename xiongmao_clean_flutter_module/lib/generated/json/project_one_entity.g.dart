import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/project_one_entity.dart';
import '../../business/common/project/bean/job_manager_entity.dart';


ProjectOneEntity $ProjectOneEntityFromJson(Map<String, dynamic> json) {
  final ProjectOneEntity projectOneEntity = ProjectOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    projectOneEntity.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    projectOneEntity.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    projectOneEntity.projectShortName = projectShortName;
  }
  final String? customUuid = jsonConvert.convert<String>(json['custom_uuid']);
  if (customUuid != null) {
    projectOneEntity.customUuid = customUuid;
  }
  final String? projectCatPid = jsonConvert.convert<String>(json['project_cat_pid']);
  if (projectCatPid != null) {
    projectOneEntity.projectCatPid = projectCatPid;
  }
  final String? projectCatId = jsonConvert.convert<String>(json['project_cat_id']);
  if (projectCatId != null) {
    projectOneEntity.projectCatId = projectCatId;
  }
  final String? square = jsonConvert.convert<String>(json['square']);
  if (square != null) {
    projectOneEntity.square = square;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    projectOneEntity.remark = remark;
  }
  final String? customName = jsonConvert.convert<String>(json['custom_name']);
  if (customName != null) {
    projectOneEntity.customName = customName;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    projectOneEntity.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    projectOneEntity.endTime = endTime;
  }
  final String? contractHumanNum = jsonConvert.convert<String>(json['contract_human_num']);
  if (contractHumanNum != null) {
    projectOneEntity.contractHumanNum = contractHumanNum;
  }
  final String? projectCatParentName = jsonConvert.convert<String>(json['project_cat_parent_name']);
  if (projectCatParentName != null) {
    projectOneEntity.projectCatParentName = projectCatParentName;
  }
  final String? projectCatName = jsonConvert.convert<String>(json['project_cat_name']);
  if (projectCatName != null) {
    projectOneEntity.projectCatName = projectCatName;
  }
  final String? managerUserUuid = jsonConvert.convert<String>(json['manager_user_uuid']);
  if (managerUserUuid != null) {
    projectOneEntity.managerUserUuid = managerUserUuid;
  }
  final String? managerUserName = jsonConvert.convert<String>(json['manager_user_name']);
  if (managerUserName != null) {
    projectOneEntity.managerUserName = managerUserName;
  }
  final String? contractCompanyUuid = jsonConvert.convert<String>(json['contract_company_uuid']);
  if (contractCompanyUuid != null) {
    projectOneEntity.contractCompanyUuid = contractCompanyUuid;
  }
  final String? contractCompanyName = jsonConvert.convert<String>(json['contract_company_name']);
  if (contractCompanyName != null) {
    projectOneEntity.contractCompanyName = contractCompanyName;
  }
  final List<String>? contractPic = (json['contract_pic'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (contractPic != null) {
    projectOneEntity.contractPic = contractPic;
  }
  final List<JobManagerList>? jobInfoList = (json['job_info_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<JobManagerList>(e) as JobManagerList).toList();
  if (jobInfoList != null) {
    projectOneEntity.jobInfoList = jobInfoList;
  }
  return projectOneEntity;
}

Map<String, dynamic> $ProjectOneEntityToJson(ProjectOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['custom_uuid'] = entity.customUuid;
  data['project_cat_pid'] = entity.projectCatPid;
  data['project_cat_id'] = entity.projectCatId;
  data['square'] = entity.square;
  data['remark'] = entity.remark;
  data['custom_name'] = entity.customName;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['contract_human_num'] = entity.contractHumanNum;
  data['project_cat_parent_name'] = entity.projectCatParentName;
  data['project_cat_name'] = entity.projectCatName;
  data['manager_user_uuid'] = entity.managerUserUuid;
  data['manager_user_name'] = entity.managerUserName;
  data['contract_company_uuid'] = entity.contractCompanyUuid;
  data['contract_company_name'] = entity.contractCompanyName;
  data['contract_pic'] = entity.contractPic;
  data['job_info_list'] = entity.jobInfoList?.map((v) => v.toJson()).toList();
  return data;
}

extension ProjectOneEntityExtension on ProjectOneEntity {
  ProjectOneEntity copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? customUuid,
    String? projectCatPid,
    String? projectCatId,
    String? square,
    String? remark,
    String? customName,
    String? startTime,
    String? endTime,
    String? contractHumanNum,
    String? projectCatParentName,
    String? projectCatName,
    String? managerUserUuid,
    String? managerUserName,
    String? contractCompanyUuid,
    String? contractCompanyName,
    List<String>? contractPic,
    List<JobManagerList>? jobInfoList,
  }) {
    return ProjectOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..customUuid = customUuid ?? this.customUuid
      ..projectCatPid = projectCatPid ?? this.projectCatPid
      ..projectCatId = projectCatId ?? this.projectCatId
      ..square = square ?? this.square
      ..remark = remark ?? this.remark
      ..customName = customName ?? this.customName
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..contractHumanNum = contractHumanNum ?? this.contractHumanNum
      ..projectCatParentName = projectCatParentName ?? this.projectCatParentName
      ..projectCatName = projectCatName ?? this.projectCatName
      ..managerUserUuid = managerUserUuid ?? this.managerUserUuid
      ..managerUserName = managerUserName ?? this.managerUserName
      ..contractCompanyUuid = contractCompanyUuid ?? this.contractCompanyUuid
      ..contractCompanyName = contractCompanyName ?? this.contractCompanyName
      ..contractPic = contractPic ?? this.contractPic
      ..jobInfoList = jobInfoList ?? this.jobInfoList;
  }
}