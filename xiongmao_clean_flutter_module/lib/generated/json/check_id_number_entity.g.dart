import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_entity.dart';

CheckIdNumberEntity $CheckIdNumberEntityFromJson(Map<String, dynamic> json) {
  final CheckIdNumberEntity checkIdNumberEntity = CheckIdNumberEntity();
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    checkIdNumberEntity.status = status;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    checkIdNumberEntity.uuid = uuid;
  }
  final int? dataType = jsonConvert.convert<int>(json['data_type']);
  if (dataType != null) {
    checkIdNumberEntity.dataType = dataType;
  }
  return checkIdNumberEntity;
}

Map<String, dynamic> $CheckIdNumberEntityToJson(CheckIdNumberEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['uuid'] = entity.uuid;
  data['data_type'] = entity.dataType;
  return data;
}

extension CheckIdNumberEntityExtension on CheckIdNumberEntity {
  CheckIdNumberEntity copyWith({
    int? status,
    String? uuid,
    int? dataType,
  }) {
    return CheckIdNumberEntity()
      ..status = status ?? this.status
      ..uuid = uuid ?? this.uuid
      ..dataType = dataType ?? this.dataType;
  }
}