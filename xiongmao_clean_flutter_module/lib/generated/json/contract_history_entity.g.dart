import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

ContractHistoryEntity $ContractHistoryEntityFromJson(Map<String, dynamic> json) {
  final ContractHistoryEntity contractHistoryEntity = ContractHistoryEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    contractHistoryEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    contractHistoryEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    contractHistoryEntity.total = total;
  }
  final List<ContractHistoryList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractHistoryList>(e) as ContractHistoryList).toList();
  if (list != null) {
    contractHistoryEntity.list = list;
  }
  return contractHistoryEntity;
}

Map<String, dynamic> $ContractHistoryEntityToJson(ContractHistoryEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractHistoryEntityExtension on ContractHistoryEntity {
  ContractHistoryEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ContractHistoryList>? list,
  }) {
    return ContractHistoryEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ContractHistoryList $ContractHistoryListFromJson(Map<String, dynamic> json) {
  final ContractHistoryList contractHistoryList = ContractHistoryList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractHistoryList.id = id;
  }
  final String? companyId = jsonConvert.convert<String>(json['company_id']);
  if (companyId != null) {
    contractHistoryList.companyId = companyId;
  }
  final String? userId = jsonConvert.convert<String>(json['user_id']);
  if (userId != null) {
    contractHistoryList.userId = userId;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    contractHistoryList.userName = userName;
  }
  final String? beforeAmount = jsonConvert.convert<String>(json['before_amount']);
  if (beforeAmount != null) {
    contractHistoryList.beforeAmount = beforeAmount;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    contractHistoryList.amount = amount;
  }
  final String? afterAmount = jsonConvert.convert<String>(json['after_amount']);
  if (afterAmount != null) {
    contractHistoryList.afterAmount = afterAmount;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    contractHistoryList.remark = remark;
  }
  final String? consumeType = jsonConvert.convert<String>(json['consume_type']);
  if (consumeType != null) {
    contractHistoryList.consumeType = consumeType;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractHistoryList.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractHistoryList.updateTime = updateTime;
  }
  return contractHistoryList;
}

Map<String, dynamic> $ContractHistoryListToJson(ContractHistoryList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['company_id'] = entity.companyId;
  data['user_id'] = entity.userId;
  data['user_name'] = entity.userName;
  data['before_amount'] = entity.beforeAmount;
  data['amount'] = entity.amount;
  data['after_amount'] = entity.afterAmount;
  data['remark'] = entity.remark;
  data['consume_type'] = entity.consumeType;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  return data;
}

extension ContractHistoryListExtension on ContractHistoryList {
  ContractHistoryList copyWith({
    String? id,
    String? companyId,
    String? userId,
    String? userName,
    String? beforeAmount,
    String? amount,
    String? afterAmount,
    String? remark,
    String? consumeType,
    String? createTime,
    String? updateTime,
  }) {
    return ContractHistoryList()
      ..id = id ?? this.id
      ..companyId = companyId ?? this.companyId
      ..userId = userId ?? this.userId
      ..userName = userName ?? this.userName
      ..beforeAmount = beforeAmount ?? this.beforeAmount
      ..amount = amount ?? this.amount
      ..afterAmount = afterAmount ?? this.afterAmount
      ..remark = remark ?? this.remark
      ..consumeType = consumeType ?? this.consumeType
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }
}