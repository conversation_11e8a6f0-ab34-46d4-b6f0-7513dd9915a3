import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/Insure_history_one_new_entity.dart';

InsureHistoryOneNewEntity $InsureHistoryOneNewEntityFromJson(Map<String, dynamic> json) {
  final InsureHistoryOneNewEntity insureHistoryOneNewEntity = InsureHistoryOneNewEntity();
  final String? orderNumber = jsonConvert.convert<String>(json['order_number']);
  if (orderNumber != null) {
    insureHistoryOneNewEntity.orderNumber = orderNumber;
  }
  final String? insuranceCompany = jsonConvert.convert<String>(json['insurance_company']);
  if (insuranceCompany != null) {
    insureHistoryOneNewEntity.insuranceCompany = insuranceCompany;
  }
  final String? insuranceCompanyName = jsonConvert.convert<String>(json['insurance_company_name']);
  if (insuranceCompanyName != null) {
    insureHistoryOneNewEntity.insuranceCompanyName = insuranceCompanyName;
  }
  final String? policyNo = jsonConvert.convert<String>(json['policy_no']);
  if (policyNo != null) {
    insureHistoryOneNewEntity.policyNo = policyNo;
  }
  final String? productName = jsonConvert.convert<String>(json['product_name']);
  if (productName != null) {
    insureHistoryOneNewEntity.productName = productName;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    insureHistoryOneNewEntity.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    insureHistoryOneNewEntity.endTime = endTime;
  }
  final String? orderStatus = jsonConvert.convert<String>(json['order_status']);
  if (orderStatus != null) {
    insureHistoryOneNewEntity.orderStatus = orderStatus;
  }
  final String? orderStatusName = jsonConvert.convert<String>(json['order_status_name']);
  if (orderStatusName != null) {
    insureHistoryOneNewEntity.orderStatusName = orderStatusName;
  }
  final String? refundStatus = jsonConvert.convert<String>(json['refund_status']);
  if (refundStatus != null) {
    insureHistoryOneNewEntity.refundStatus = refundStatus;
  }
  final String? refundStatusName = jsonConvert.convert<String>(json['refund_status_name']);
  if (refundStatusName != null) {
    insureHistoryOneNewEntity.refundStatusName = refundStatusName;
  }
  final String? insuredUserName = jsonConvert.convert<String>(json['insured_user_name']);
  if (insuredUserName != null) {
    insureHistoryOneNewEntity.insuredUserName = insuredUserName;
  }
  final String? insuredUserIdNumber = jsonConvert.convert<String>(json['insured_user_id_number']);
  if (insuredUserIdNumber != null) {
    insureHistoryOneNewEntity.insuredUserIdNumber = insuredUserIdNumber;
  }
  final String? payTime = jsonConvert.convert<String>(json['pay_time']);
  if (payTime != null) {
    insureHistoryOneNewEntity.payTime = payTime;
  }
  final String? elecPolicyUrl = jsonConvert.convert<String>(json['elec_policy_url']);
  if (elecPolicyUrl != null) {
    insureHistoryOneNewEntity.elecPolicyUrl = elecPolicyUrl;
  }
  final List<InsureHistoryOneNewInsuredUserList>? insuredUserList = (json['insured_user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsureHistoryOneNewInsuredUserList>(e) as InsureHistoryOneNewInsuredUserList).toList();
  if (insuredUserList != null) {
    insureHistoryOneNewEntity.insuredUserList = insuredUserList;
  }
  return insureHistoryOneNewEntity;
}

Map<String, dynamic> $InsureHistoryOneNewEntityToJson(InsureHistoryOneNewEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['order_number'] = entity.orderNumber;
  data['insurance_company'] = entity.insuranceCompany;
  data['insurance_company_name'] = entity.insuranceCompanyName;
  data['policy_no'] = entity.policyNo;
  data['product_name'] = entity.productName;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['order_status'] = entity.orderStatus;
  data['order_status_name'] = entity.orderStatusName;
  data['refund_status'] = entity.refundStatus;
  data['refund_status_name'] = entity.refundStatusName;
  data['insured_user_name'] = entity.insuredUserName;
  data['insured_user_id_number'] = entity.insuredUserIdNumber;
  data['pay_time'] = entity.payTime;
  data['elec_policy_url'] = entity.elecPolicyUrl;
  data['insured_user_list'] = entity.insuredUserList?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureHistoryOneNewEntityExtension on InsureHistoryOneNewEntity {
  InsureHistoryOneNewEntity copyWith({
    String? orderNumber,
    String? insuranceCompany,
    String? insuranceCompanyName,
    String? policyNo,
    String? productName,
    String? startTime,
    String? endTime,
    String? orderStatus,
    String? orderStatusName,
    String? refundStatus,
    String? refundStatusName,
    String? insuredUserName,
    String? insuredUserIdNumber,
    String? payTime,
    String? elecPolicyUrl,
    List<InsureHistoryOneNewInsuredUserList>? insuredUserList,
  }) {
    return InsureHistoryOneNewEntity()
      ..orderNumber = orderNumber ?? this.orderNumber
      ..insuranceCompany = insuranceCompany ?? this.insuranceCompany
      ..insuranceCompanyName = insuranceCompanyName ?? this.insuranceCompanyName
      ..policyNo = policyNo ?? this.policyNo
      ..productName = productName ?? this.productName
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..orderStatus = orderStatus ?? this.orderStatus
      ..orderStatusName = orderStatusName ?? this.orderStatusName
      ..refundStatus = refundStatus ?? this.refundStatus
      ..refundStatusName = refundStatusName ?? this.refundStatusName
      ..insuredUserName = insuredUserName ?? this.insuredUserName
      ..insuredUserIdNumber = insuredUserIdNumber ?? this.insuredUserIdNumber
      ..payTime = payTime ?? this.payTime
      ..elecPolicyUrl = elecPolicyUrl ?? this.elecPolicyUrl
      ..insuredUserList = insuredUserList ?? this.insuredUserList;
  }
}

InsureHistoryOneNewInsuredUserList $InsureHistoryOneNewInsuredUserListFromJson(Map<String, dynamic> json) {
  final InsureHistoryOneNewInsuredUserList insureHistoryOneNewInsuredUserList = InsureHistoryOneNewInsuredUserList();
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    insureHistoryOneNewInsuredUserList.userName = userName;
  }
  final String? idNumber = jsonConvert.convert<String>(json['id_number']);
  if (idNumber != null) {
    insureHistoryOneNewInsuredUserList.idNumber = idNumber;
  }
  final String? statusName = jsonConvert.convert<String>(json['status_name']);
  if (statusName != null) {
    insureHistoryOneNewInsuredUserList.statusName = statusName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    insureHistoryOneNewInsuredUserList.avatar = avatar;
  }
  final String? isValid = jsonConvert.convert<String>(json['is_valid']);
  if (isValid != null) {
    insureHistoryOneNewInsuredUserList.isValid = isValid;
  }
  return insureHistoryOneNewInsuredUserList;
}

Map<String, dynamic> $InsureHistoryOneNewInsuredUserListToJson(InsureHistoryOneNewInsuredUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_name'] = entity.userName;
  data['id_number'] = entity.idNumber;
  data['status_name'] = entity.statusName;
  data['avatar'] = entity.avatar;
  data['is_valid'] = entity.isValid;
  return data;
}

extension InsureHistoryOneNewInsuredUserListExtension on InsureHistoryOneNewInsuredUserList {
  InsureHistoryOneNewInsuredUserList copyWith({
    String? userName,
    String? idNumber,
    String? statusName,
    String? avatar,
    String? isValid,
  }) {
    return InsureHistoryOneNewInsuredUserList()
      ..userName = userName ?? this.userName
      ..idNumber = idNumber ?? this.idNumber
      ..statusName = statusName ?? this.statusName
      ..avatar = avatar ?? this.avatar
      ..isValid = isValid ?? this.isValid;
  }
}