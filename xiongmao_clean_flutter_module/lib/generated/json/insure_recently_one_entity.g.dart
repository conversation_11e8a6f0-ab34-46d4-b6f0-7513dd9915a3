import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_recently_one_entity.dart';

InsureRecentlyOneEntity $InsureRecentlyOneEntityFromJson(Map<String, dynamic> json) {
  final InsureRecentlyOneEntity insureRecentlyOneEntity = InsureRecentlyOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    insureRecentlyOneEntity.uuid = uuid;
  }
  final String? contractCompanyUuid = jsonConvert.convert<String>(json['contract_company_uuid']);
  if (contractCompanyUuid != null) {
    insureRecentlyOneEntity.contractCompanyUuid = contractCompanyUuid;
  }
  final String? invoiceTitle = jsonConvert.convert<String>(json['invoice_title']);
  if (invoiceTitle != null) {
    insureRecentlyOneEntity.invoiceTitle = invoiceTitle;
  }
  final String? amountType = jsonConvert.convert<String>(json['amount_type']);
  if (amountType != null) {
    insureRecentlyOneEntity.amountType = amountType;
  }
  final String? amountTypeName = jsonConvert.convert<String>(json['amount_type_name']);
  if (amountTypeName != null) {
    insureRecentlyOneEntity.amountTypeName = amountTypeName;
  }
  final String? email = jsonConvert.convert<String>(json['email']);
  if (email != null) {
    insureRecentlyOneEntity.email = email;
  }
  final String? invoiceType = jsonConvert.convert<String>(json['invoice_type']);
  if (invoiceType != null) {
    insureRecentlyOneEntity.invoiceType = invoiceType;
  }
  final String? invoiceTypeName = jsonConvert.convert<String>(json['invoice_type_name']);
  if (invoiceTypeName != null) {
    insureRecentlyOneEntity.invoiceTypeName = invoiceTypeName;
  }
  final String? identificationNumber = jsonConvert.convert<String>(json['identification_number']);
  if (identificationNumber != null) {
    insureRecentlyOneEntity.identificationNumber = identificationNumber;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    insureRecentlyOneEntity.address = address;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    insureRecentlyOneEntity.mobile = mobile;
  }
  final String? bank = jsonConvert.convert<String>(json['bank']);
  if (bank != null) {
    insureRecentlyOneEntity.bank = bank;
  }
  final String? bankNo = jsonConvert.convert<String>(json['bank_no']);
  if (bankNo != null) {
    insureRecentlyOneEntity.bankNo = bankNo;
  }
  final String? isMerge = jsonConvert.convert<String>(json['is_merge']);
  if (isMerge != null) {
    insureRecentlyOneEntity.isMerge = isMerge;
  }
  final String? isMergeName = jsonConvert.convert<String>(json['is_merge_name']);
  if (isMergeName != null) {
    insureRecentlyOneEntity.isMergeName = isMergeName;
  }
  return insureRecentlyOneEntity;
}

Map<String, dynamic> $InsureRecentlyOneEntityToJson(InsureRecentlyOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['contract_company_uuid'] = entity.contractCompanyUuid;
  data['invoice_title'] = entity.invoiceTitle;
  data['amount_type'] = entity.amountType;
  data['amount_type_name'] = entity.amountTypeName;
  data['email'] = entity.email;
  data['invoice_type'] = entity.invoiceType;
  data['invoice_type_name'] = entity.invoiceTypeName;
  data['identification_number'] = entity.identificationNumber;
  data['address'] = entity.address;
  data['mobile'] = entity.mobile;
  data['bank'] = entity.bank;
  data['bank_no'] = entity.bankNo;
  data['is_merge'] = entity.isMerge;
  data['is_merge_name'] = entity.isMergeName;
  return data;
}

extension InsureRecentlyOneEntityExtension on InsureRecentlyOneEntity {
  InsureRecentlyOneEntity copyWith({
    String? uuid,
    String? contractCompanyUuid,
    String? invoiceTitle,
    String? amountType,
    String? amountTypeName,
    String? email,
    String? invoiceType,
    String? invoiceTypeName,
    String? identificationNumber,
    String? address,
    String? mobile,
    String? bank,
    String? bankNo,
    String? isMerge,
    String? isMergeName,
  }) {
    return InsureRecentlyOneEntity()
      ..uuid = uuid ?? this.uuid
      ..contractCompanyUuid = contractCompanyUuid ?? this.contractCompanyUuid
      ..invoiceTitle = invoiceTitle ?? this.invoiceTitle
      ..amountType = amountType ?? this.amountType
      ..amountTypeName = amountTypeName ?? this.amountTypeName
      ..email = email ?? this.email
      ..invoiceType = invoiceType ?? this.invoiceType
      ..invoiceTypeName = invoiceTypeName ?? this.invoiceTypeName
      ..identificationNumber = identificationNumber ?? this.identificationNumber
      ..address = address ?? this.address
      ..mobile = mobile ?? this.mobile
      ..bank = bank ?? this.bank
      ..bankNo = bankNo ?? this.bankNo
      ..isMerge = isMerge ?? this.isMerge
      ..isMergeName = isMergeName ?? this.isMergeName;
  }
}