import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/bean/project_all_new_entity.dart';

ProjectAllNewEntity $ProjectAllNewEntityFromJson(Map<String, dynamic> json) {
  final ProjectAllNewEntity projectAllNewEntity = ProjectAllNewEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    projectAllNewEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    projectAllNewEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    projectAllNewEntity.total = total;
  }
  final List<ProjectAllNewList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ProjectAllNewList>(e) as ProjectAllNewList).toList();
  if (list != null) {
    projectAllNewEntity.list = list;
  }
  return projectAllNewEntity;
}

Map<String, dynamic> $ProjectAllNewEntityToJson(ProjectAllNewEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ProjectAllNewEntityExtension on ProjectAllNewEntity {
  ProjectAllNewEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ProjectAllNewList>? list,
  }) {
    return ProjectAllNewEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ProjectAllNewList $ProjectAllNewListFromJson(Map<String, dynamic> json) {
  final ProjectAllNewList projectAllNewList = ProjectAllNewList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    projectAllNewList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    projectAllNewList.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    projectAllNewList.projectShortName = projectShortName;
  }
  final String? projectCatParentName = jsonConvert.convert<String>(json['project_cat_parent_name']);
  if (projectCatParentName != null) {
    projectAllNewList.projectCatParentName = projectCatParentName;
  }
  final String? projectCatName = jsonConvert.convert<String>(json['project_cat_name']);
  if (projectCatName != null) {
    projectAllNewList.projectCatName = projectCatName;
  }
  final String? managerUserName = jsonConvert.convert<String>(json['manager_user_name']);
  if (managerUserName != null) {
    projectAllNewList.managerUserName = managerUserName;
  }
  final String? customName = jsonConvert.convert<String>(json['custom_name']);
  if (customName != null) {
    projectAllNewList.customName = customName;
  }
  final String? square = jsonConvert.convert<String>(json['square']);
  if (square != null) {
    projectAllNewList.square = square;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    projectAllNewList.remark = remark;
  }
  final String? taskNum = jsonConvert.convert<String>(json['task_num']);
  if (taskNum != null) {
    projectAllNewList.taskNum = taskNum;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    projectAllNewList.endTime = endTime;
  }
  return projectAllNewList;
}

Map<String, dynamic> $ProjectAllNewListToJson(ProjectAllNewList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['project_cat_parent_name'] = entity.projectCatParentName;
  data['project_cat_name'] = entity.projectCatName;
  data['manager_user_name'] = entity.managerUserName;
  data['custom_name'] = entity.customName;
  data['square'] = entity.square;
  data['remark'] = entity.remark;
  data['task_num'] = entity.taskNum;
  data['end_time'] = entity.endTime;
  return data;
}

extension ProjectAllNewListExtension on ProjectAllNewList {
  ProjectAllNewList copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? projectCatParentName,
    String? projectCatName,
    String? managerUserName,
    String? customName,
    String? square,
    String? remark,
    String? taskNum,
    String? endTime,
  }) {
    return ProjectAllNewList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..projectCatParentName = projectCatParentName ?? this.projectCatParentName
      ..projectCatName = projectCatName ?? this.projectCatName
      ..managerUserName = managerUserName ?? this.managerUserName
      ..customName = customName ?? this.customName
      ..square = square ?? this.square
      ..remark = remark ?? this.remark
      ..taskNum = taskNum ?? this.taskNum
      ..endTime = endTime ?? this.endTime;
  }
}