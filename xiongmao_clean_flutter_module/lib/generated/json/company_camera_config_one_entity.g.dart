import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/company_camera_config_one_entity.dart';

CompanyCameraConfigOneEntity $CompanyCameraConfigOneEntityFromJson(Map<String, dynamic> json) {
  final CompanyCameraConfigOneEntity companyCameraConfigOneEntity = CompanyCameraConfigOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    companyCameraConfigOneEntity.uuid = uuid;
  }
  final String? configurationName = jsonConvert.convert<String>(json['configuration_name']);
  if (configurationName != null) {
    companyCameraConfigOneEntity.configurationName = configurationName;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    companyCameraConfigOneEntity.introduction = introduction;
  }
  final String? savePhotoMobile = jsonConvert.convert<String>(json['save_photo_mobile']);
  if (savePhotoMobile != null) {
    companyCameraConfigOneEntity.savePhotoMobile = savePhotoMobile;
  }
  final String? openWorkPhoto = jsonConvert.convert<String>(json['open_work_photo']);
  if (openWorkPhoto != null) {
    companyCameraConfigOneEntity.openWorkPhoto = openWorkPhoto;
  }
  final String? dkConfirm = jsonConvert.convert<String>(json['dk_confirm']);
  if (dkConfirm != null) {
    companyCameraConfigOneEntity.dkConfirm = dkConfirm;
  }
  final String? dkSwitchCamera = jsonConvert.convert<String>(json['dk_switch_camera']);
  if (dkSwitchCamera != null) {
    companyCameraConfigOneEntity.dkSwitchCamera = dkSwitchCamera;
  }
  final String? watermarkLogo = jsonConvert.convert<String>(json['watermark_logo']);
  if (watermarkLogo != null) {
    companyCameraConfigOneEntity.watermarkLogo = watermarkLogo;
  }
  final String? watermarkLogoPos = jsonConvert.convert<String>(json['watermark_logo_pos']);
  if (watermarkLogoPos != null) {
    companyCameraConfigOneEntity.watermarkLogoPos = watermarkLogoPos;
  }
  final String? watermarkLogoWidth = jsonConvert.convert<String>(json['watermark_logo_width']);
  if (watermarkLogoWidth != null) {
    companyCameraConfigOneEntity.watermarkLogoWidth = watermarkLogoWidth;
  }
  final String? watermarkLogoHeight = jsonConvert.convert<String>(json['watermark_logo_height']);
  if (watermarkLogoHeight != null) {
    companyCameraConfigOneEntity.watermarkLogoHeight = watermarkLogoHeight;
  }
  final String? isShowApp = jsonConvert.convert<String>(json['is_show_app']);
  if (isShowApp != null) {
    companyCameraConfigOneEntity.isShowApp = isShowApp;
  }
  final String? isSeeKq = jsonConvert.convert<String>(json['is_see_kq']);
  if (isSeeKq != null) {
    companyCameraConfigOneEntity.isSeeKq = isSeeKq;
  }
  final String? isSeePhoto = jsonConvert.convert<String>(json['is_see_photo']);
  if (isSeePhoto != null) {
    companyCameraConfigOneEntity.isSeePhoto = isSeePhoto;
  }
  final String? isMinimalism = jsonConvert.convert<String>(json['is_minimalism']);
  if (isMinimalism != null) {
    companyCameraConfigOneEntity.isMinimalism = isMinimalism;
  }
  final String? watermarkLogoScale = jsonConvert.convert<String>(json['watermark_logo_scale']);
  if (watermarkLogoScale != null) {
    companyCameraConfigOneEntity.watermarkLogoScale = watermarkLogoScale;
  }
  final String? isDefault = jsonConvert.convert<String>(json['is_default']);
  if (isDefault != null) {
    companyCameraConfigOneEntity.isDefault = isDefault;
  }
  final String? configurationStatus = jsonConvert.convert<String>(json['configuration_status']);
  if (configurationStatus != null) {
    companyCameraConfigOneEntity.configurationStatus = configurationStatus;
  }
  final List<CompanyCameraConfigOneRangeUser>? rangeUser = (json['range_user'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CompanyCameraConfigOneRangeUser>(e) as CompanyCameraConfigOneRangeUser).toList();
  if (rangeUser != null) {
    companyCameraConfigOneEntity.rangeUser = rangeUser;
  }
  return companyCameraConfigOneEntity;
}

Map<String, dynamic> $CompanyCameraConfigOneEntityToJson(CompanyCameraConfigOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['configuration_name'] = entity.configurationName;
  data['introduction'] = entity.introduction;
  data['save_photo_mobile'] = entity.savePhotoMobile;
  data['open_work_photo'] = entity.openWorkPhoto;
  data['dk_confirm'] = entity.dkConfirm;
  data['dk_switch_camera'] = entity.dkSwitchCamera;
  data['watermark_logo'] = entity.watermarkLogo;
  data['watermark_logo_pos'] = entity.watermarkLogoPos;
  data['watermark_logo_width'] = entity.watermarkLogoWidth;
  data['watermark_logo_height'] = entity.watermarkLogoHeight;
  data['is_show_app'] = entity.isShowApp;
  data['is_see_kq'] = entity.isSeeKq;
  data['is_see_photo'] = entity.isSeePhoto;
  data['is_minimalism'] = entity.isMinimalism;
  data['watermark_logo_scale'] = entity.watermarkLogoScale;
  data['is_default'] = entity.isDefault;
  data['configuration_status'] = entity.configurationStatus;
  data['range_user'] = entity.rangeUser?.map((v) => v.toJson()).toList();
  return data;
}

extension CompanyCameraConfigOneEntityExtension on CompanyCameraConfigOneEntity {
  CompanyCameraConfigOneEntity copyWith({
    String? uuid,
    String? configurationName,
    String? introduction,
    String? savePhotoMobile,
    String? openWorkPhoto,
    String? dkConfirm,
    String? dkSwitchCamera,
    String? watermarkLogo,
    String? watermarkLogoPos,
    String? watermarkLogoWidth,
    String? watermarkLogoHeight,
    String? isShowApp,
    String? isSeeKq,
    String? isSeePhoto,
    String? isMinimalism,
    String? watermarkLogoScale,
    String? isDefault,
    String? configurationStatus,
    List<CompanyCameraConfigOneRangeUser>? rangeUser,
  }) {
    return CompanyCameraConfigOneEntity()
      ..uuid = uuid ?? this.uuid
      ..configurationName = configurationName ?? this.configurationName
      ..introduction = introduction ?? this.introduction
      ..savePhotoMobile = savePhotoMobile ?? this.savePhotoMobile
      ..openWorkPhoto = openWorkPhoto ?? this.openWorkPhoto
      ..dkConfirm = dkConfirm ?? this.dkConfirm
      ..dkSwitchCamera = dkSwitchCamera ?? this.dkSwitchCamera
      ..watermarkLogo = watermarkLogo ?? this.watermarkLogo
      ..watermarkLogoPos = watermarkLogoPos ?? this.watermarkLogoPos
      ..watermarkLogoWidth = watermarkLogoWidth ?? this.watermarkLogoWidth
      ..watermarkLogoHeight = watermarkLogoHeight ?? this.watermarkLogoHeight
      ..isShowApp = isShowApp ?? this.isShowApp
      ..isSeeKq = isSeeKq ?? this.isSeeKq
      ..isSeePhoto = isSeePhoto ?? this.isSeePhoto
      ..isMinimalism = isMinimalism ?? this.isMinimalism
      ..watermarkLogoScale = watermarkLogoScale ?? this.watermarkLogoScale
      ..isDefault = isDefault ?? this.isDefault
      ..configurationStatus = configurationStatus ?? this.configurationStatus
      ..rangeUser = rangeUser ?? this.rangeUser;
  }
}

CompanyCameraConfigOneRangeUser $CompanyCameraConfigOneRangeUserFromJson(Map<String, dynamic> json) {
  final CompanyCameraConfigOneRangeUser companyCameraConfigOneRangeUser = CompanyCameraConfigOneRangeUser();
  final String? type = jsonConvert.convert<String>(json['type']);
  if (type != null) {
    companyCameraConfigOneRangeUser.type = type;
  }
  final String? typeName = jsonConvert.convert<String>(json['type_name']);
  if (typeName != null) {
    companyCameraConfigOneRangeUser.typeName = typeName;
  }
  final String? compare = jsonConvert.convert<String>(json['compare']);
  if (compare != null) {
    companyCameraConfigOneRangeUser.compare = compare;
  }
  final String? compareName = jsonConvert.convert<String>(json['compare_name']);
  if (compareName != null) {
    companyCameraConfigOneRangeUser.compareName = compareName;
  }
  final List<CompanyCameraConfigOneRangeUserValueList>? valueList = (json['value_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CompanyCameraConfigOneRangeUserValueList>(e) as CompanyCameraConfigOneRangeUserValueList).toList();
  if (valueList != null) {
    companyCameraConfigOneRangeUser.valueList = valueList;
  }
  return companyCameraConfigOneRangeUser;
}

Map<String, dynamic> $CompanyCameraConfigOneRangeUserToJson(CompanyCameraConfigOneRangeUser entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['type'] = entity.type;
  data['type_name'] = entity.typeName;
  data['compare'] = entity.compare;
  data['compare_name'] = entity.compareName;
  data['value_list'] = entity.valueList?.map((v) => v.toJson()).toList();
  return data;
}

extension CompanyCameraConfigOneRangeUserExtension on CompanyCameraConfigOneRangeUser {
  CompanyCameraConfigOneRangeUser copyWith({
    String? type,
    String? typeName,
    String? compare,
    String? compareName,
    List<CompanyCameraConfigOneRangeUserValueList>? valueList,
  }) {
    return CompanyCameraConfigOneRangeUser()
      ..type = type ?? this.type
      ..typeName = typeName ?? this.typeName
      ..compare = compare ?? this.compare
      ..compareName = compareName ?? this.compareName
      ..valueList = valueList ?? this.valueList;
  }
}

CompanyCameraConfigOneRangeUserValueList $CompanyCameraConfigOneRangeUserValueListFromJson(Map<String, dynamic> json) {
  final CompanyCameraConfigOneRangeUserValueList companyCameraConfigOneRangeUserValueList = CompanyCameraConfigOneRangeUserValueList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    companyCameraConfigOneRangeUserValueList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    companyCameraConfigOneRangeUserValueList.name = name;
  }
  return companyCameraConfigOneRangeUserValueList;
}

Map<String, dynamic> $CompanyCameraConfigOneRangeUserValueListToJson(CompanyCameraConfigOneRangeUserValueList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension CompanyCameraConfigOneRangeUserValueListExtension on CompanyCameraConfigOneRangeUserValueList {
  CompanyCameraConfigOneRangeUserValueList copyWith({
    String? id,
    String? name,
  }) {
    return CompanyCameraConfigOneRangeUserValueList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}