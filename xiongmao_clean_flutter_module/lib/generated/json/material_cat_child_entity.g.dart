import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/material_cat_child_entity.dart';

MaterialCatChildEntity $MaterialCatChildEntityFromJson(Map<String, dynamic> json) {
  final MaterialCatChildEntity materialCatChildEntity = MaterialCatChildEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    materialCatChildEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    materialCatChildEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    materialCatChildEntity.total = total;
  }
  final List<MaterialCatChildList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MaterialCatChildList>(e) as MaterialCatChildList).toList();
  if (list != null) {
    materialCatChildEntity.list = list;
  }
  return materialCatChildEntity;
}

Map<String, dynamic> $MaterialCatChildEntityToJson(MaterialCatChildEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension MaterialCatChildEntityExtension on MaterialCatChildEntity {
  MaterialCatChildEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<MaterialCatChildList>? list,
  }) {
    return MaterialCatChildEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

MaterialCatChildList $MaterialCatChildListFromJson(Map<String, dynamic> json) {
  final MaterialCatChildList materialCatChildList = MaterialCatChildList();
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    materialCatChildList.number = number;
  }
  final String? productName = jsonConvert.convert<String>(json['product_name']);
  if (productName != null) {
    materialCatChildList.productName = productName;
  }
  final String? specification = jsonConvert.convert<String>(json['specification']);
  if (specification != null) {
    materialCatChildList.specification = specification;
  }
  final String? unitName = jsonConvert.convert<String>(json['unit_name']);
  if (unitName != null) {
    materialCatChildList.unitName = unitName;
  }
  final String? coverUrl = jsonConvert.convert<String>(json['cover_url']);
  if (coverUrl != null) {
    materialCatChildList.coverUrl = coverUrl;
  }
  final String? materialCategoryId = jsonConvert.convert<String>(json['material_category_id']);
  if (materialCategoryId != null) {
    materialCatChildList.materialCategoryId = materialCategoryId;
  }
  final int? num = jsonConvert.convert<int>(json['num']);
  if (num != null) {
    materialCatChildList.num = num;
  }
  final bool? check = jsonConvert.convert<bool>(json['check']);
  if (check != null) {
    materialCatChildList.check = check;
  }
  return materialCatChildList;
}

Map<String, dynamic> $MaterialCatChildListToJson(MaterialCatChildList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['number'] = entity.number;
  data['product_name'] = entity.productName;
  data['specification'] = entity.specification;
  data['unit_name'] = entity.unitName;
  data['cover_url'] = entity.coverUrl;
  data['material_category_id'] = entity.materialCategoryId;
  data['num'] = entity.num;
  data['check'] = entity.check;
  return data;
}

extension MaterialCatChildListExtension on MaterialCatChildList {
  MaterialCatChildList copyWith({
    String? number,
    String? productName,
    String? specification,
    String? unitName,
    String? coverUrl,
    String? materialCategoryId,
    int? num,
    bool? check,
  }) {
    return MaterialCatChildList()
      ..number = number ?? this.number
      ..productName = productName ?? this.productName
      ..specification = specification ?? this.specification
      ..unitName = unitName ?? this.unitName
      ..coverUrl = coverUrl ?? this.coverUrl
      ..materialCategoryId = materialCategoryId ?? this.materialCategoryId
      ..num = num ?? this.num
      ..check = check ?? this.check;
  }
}