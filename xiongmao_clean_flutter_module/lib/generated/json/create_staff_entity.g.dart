import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';

CreateStaffEntity $CreateStaffEntityFromJson(Map<String, dynamic> json) {
  final CreateStaffEntity createStaffEntity = CreateStaffEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    createStaffEntity.uuid = uuid;
  }
  final String? applicationNo = jsonConvert.convert<String>(json['application_no']);
  if (applicationNo != null) {
    createStaffEntity.applicationNo = applicationNo;
  }
  return createStaffEntity;
}

Map<String, dynamic> $CreateStaffEntityToJson(CreateStaffEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['application_no'] = entity.applicationNo;
  return data;
}

extension CreateStaffEntityExtension on CreateStaffEntity {
  CreateStaffEntity copyWith({
    String? uuid,
    String? applicationNo,
  }) {
    return CreateStaffEntity()
      ..uuid = uuid ?? this.uuid
      ..applicationNo = applicationNo ?? this.applicationNo;
  }
}