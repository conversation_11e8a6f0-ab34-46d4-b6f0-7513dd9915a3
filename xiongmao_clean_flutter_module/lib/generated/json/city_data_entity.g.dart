import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/city_data_entity.dart';

CityDataEntity $CityDataEntityFromJson(Map<String, dynamic> json) {
  final CityDataEntity cityDataEntity = CityDataEntity();
  final List<CityDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CityDataList>(e) as CityDataList).toList();
  if (list != null) {
    cityDataEntity.list = list;
  }
  return cityDataEntity;
}

Map<String, dynamic> $CityDataEntityToJson(CityDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CityDataEntityExtension on CityDataEntity {
  CityDataEntity copyWith({
    List<CityDataList>? list,
  }) {
    return CityDataEntity()
      ..list = list ?? this.list;
  }
}

CityDataList $CityDataListFromJson(Map<String, dynamic> json) {
  final CityDataList cityDataList = CityDataList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cityDataList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cityDataList.name = name;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    cityDataList.pid = pid;
  }
  final List<CityDataListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CityDataListList>(e) as CityDataListList).toList();
  if (list != null) {
    cityDataList.list = list;
  }
  return cityDataList;
}

Map<String, dynamic> $CityDataListToJson(CityDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['pid'] = entity.pid;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CityDataListExtension on CityDataList {
  CityDataList copyWith({
    String? id,
    String? name,
    String? pid,
    List<CityDataListList>? list,
  }) {
    return CityDataList()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..pid = pid ?? this.pid
      ..list = list ?? this.list;
  }
}

CityDataListList $CityDataListListFromJson(Map<String, dynamic> json) {
  final CityDataListList cityDataListList = CityDataListList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cityDataListList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cityDataListList.name = name;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    cityDataListList.pid = pid;
  }
  final List<CityDataListListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CityDataListListList>(e) as CityDataListListList).toList();
  if (list != null) {
    cityDataListList.list = list;
  }
  return cityDataListList;
}

Map<String, dynamic> $CityDataListListToJson(CityDataListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['pid'] = entity.pid;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CityDataListListExtension on CityDataListList {
  CityDataListList copyWith({
    String? id,
    String? name,
    String? pid,
    List<CityDataListListList>? list,
  }) {
    return CityDataListList()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..pid = pid ?? this.pid
      ..list = list ?? this.list;
  }
}

CityDataListListList $CityDataListListListFromJson(Map<String, dynamic> json) {
  final CityDataListListList cityDataListListList = CityDataListListList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    cityDataListListList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    cityDataListListList.name = name;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    cityDataListListList.pid = pid;
  }
  return cityDataListListList;
}

Map<String, dynamic> $CityDataListListListToJson(CityDataListListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['pid'] = entity.pid;
  return data;
}

extension CityDataListListListExtension on CityDataListListList {
  CityDataListListList copyWith({
    String? id,
    String? name,
    String? pid,
  }) {
    return CityDataListListList()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..pid = pid ?? this.pid;
  }
}