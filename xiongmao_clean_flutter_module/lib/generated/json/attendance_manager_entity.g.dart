import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/roster/bean/attendance_manager_entity.dart';

AttendanceManagerEntity $AttendanceManagerEntityFromJson(Map<String, dynamic> json) {
  final AttendanceManagerEntity attendanceManagerEntity = AttendanceManagerEntity();
  final List<AttendanceManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AttendanceManagerList>(e) as AttendanceManagerList).toList();
  if (list != null) {
    attendanceManagerEntity.list = list;
  }
  return attendanceManagerEntity;
}

Map<String, dynamic> $AttendanceManagerEntityToJson(AttendanceManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension AttendanceManagerEntityExtension on AttendanceManagerEntity {
  AttendanceManagerEntity copyWith({
    List<AttendanceManagerList>? list,
  }) {
    return AttendanceManagerEntity()
      ..list = list ?? this.list;
  }
}

AttendanceManagerList $AttendanceManagerListFromJson(Map<String, dynamic> json) {
  final AttendanceManagerList attendanceManagerList = AttendanceManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceManagerList.uuid = uuid;
  }
  final String? groupName = jsonConvert.convert<String>(json['group_name']);
  if (groupName != null) {
    attendanceManagerList.groupName = groupName;
  }
  final String? workDayDesc = jsonConvert.convert<String>(json['work_day_desc']);
  if (workDayDesc != null) {
    attendanceManagerList.workDayDesc = workDayDesc;
  }
  final String? inClassDesc = jsonConvert.convert<String>(json['in_class_desc']);
  if (inClassDesc != null) {
    attendanceManagerList.inClassDesc = inClassDesc;
  }
  final List<String>? addressList = (json['address_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (addressList != null) {
    attendanceManagerList.addressList = addressList;
  }
  final String? attendanceMethodName = jsonConvert.convert<String>(json['attendance_method_name']);
  if (attendanceMethodName != null) {
    attendanceManagerList.attendanceMethodName = attendanceMethodName;
  }
  return attendanceManagerList;
}

Map<String, dynamic> $AttendanceManagerListToJson(AttendanceManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['group_name'] = entity.groupName;
  data['work_day_desc'] = entity.workDayDesc;
  data['in_class_desc'] = entity.inClassDesc;
  data['address_list'] = entity.addressList;
  data['attendance_method_name'] = entity.attendanceMethodName;
  return data;
}

extension AttendanceManagerListExtension on AttendanceManagerList {
  AttendanceManagerList copyWith({
    String? uuid,
    String? groupName,
    String? workDayDesc,
    String? inClassDesc,
    List<String>? addressList,
    String? attendanceMethodName,
  }) {
    return AttendanceManagerList()
      ..uuid = uuid ?? this.uuid
      ..groupName = groupName ?? this.groupName
      ..workDayDesc = workDayDesc ?? this.workDayDesc
      ..inClassDesc = inClassDesc ?? this.inClassDesc
      ..addressList = addressList ?? this.addressList
      ..attendanceMethodName = attendanceMethodName ?? this.attendanceMethodName;
  }
}