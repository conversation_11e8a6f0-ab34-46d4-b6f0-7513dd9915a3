import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_electronic_entity.dart';

ContractMainElectronicEntity $ContractMainElectronicEntityFromJson(Map<String, dynamic> json) {
  final ContractMainElectronicEntity contractMainElectronicEntity = ContractMainElectronicEntity();
  final ContractMainElectronicSignSetting? signSetting = jsonConvert.convert<ContractMainElectronicSignSetting>(json['sign_setting']);
  if (signSetting != null) {
    contractMainElectronicEntity.signSetting = signSetting;
  }
  final String? contractCount = jsonConvert.convert<String>(json['contract_count']);
  if (contractCount != null) {
    contractMainElectronicEntity.contractCount = contractCount;
  }
  final List<ContractMainElectronicDepartmentList>? departmentList = (json['department_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractMainElectronicDepartmentList>(e) as ContractMainElectronicDepartmentList).toList();
  if (departmentList != null) {
    contractMainElectronicEntity.departmentList = departmentList;
  }
  return contractMainElectronicEntity;
}

Map<String, dynamic> $ContractMainElectronicEntityToJson(ContractMainElectronicEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['sign_setting'] = entity.signSetting?.toJson();
  data['contract_count'] = entity.contractCount;
  data['department_list'] = entity.departmentList?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractMainElectronicEntityExtension on ContractMainElectronicEntity {
  ContractMainElectronicEntity copyWith({
    ContractMainElectronicSignSetting? signSetting,
    String? contractCount,
    List<ContractMainElectronicDepartmentList>? departmentList,
  }) {
    return ContractMainElectronicEntity()
      ..signSetting = signSetting ?? this.signSetting
      ..contractCount = contractCount ?? this.contractCount
      ..departmentList = departmentList ?? this.departmentList;
  }
}

ContractMainElectronicSignSetting $ContractMainElectronicSignSettingFromJson(Map<String, dynamic> json) {
  final ContractMainElectronicSignSetting contractMainElectronicSignSetting = ContractMainElectronicSignSetting();
  final ContractMainElectronicSignSettingLabelEnabledStatus? labelEnabledStatus = jsonConvert.convert<ContractMainElectronicSignSettingLabelEnabledStatus>(json['label_enabled_status']);
  if (labelEnabledStatus != null) {
    contractMainElectronicSignSetting.labelEnabledStatus = labelEnabledStatus;
  }
  final ContractMainElectronicSignSettingDefaultSignLabelCode? defaultSignLabelCode = jsonConvert.convert<ContractMainElectronicSignSettingDefaultSignLabelCode>(json['default_sign_label_code']);
  if (defaultSignLabelCode != null) {
    contractMainElectronicSignSetting.defaultSignLabelCode = defaultSignLabelCode;
  }
  return contractMainElectronicSignSetting;
}

Map<String, dynamic> $ContractMainElectronicSignSettingToJson(ContractMainElectronicSignSetting entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['label_enabled_status'] = entity.labelEnabledStatus?.toJson();
  data['default_sign_label_code'] = entity.defaultSignLabelCode?.toJson();
  return data;
}

extension ContractMainElectronicSignSettingExtension on ContractMainElectronicSignSetting {
  ContractMainElectronicSignSetting copyWith({
    ContractMainElectronicSignSettingLabelEnabledStatus? labelEnabledStatus,
    ContractMainElectronicSignSettingDefaultSignLabelCode? defaultSignLabelCode,
  }) {
    return ContractMainElectronicSignSetting()
      ..labelEnabledStatus = labelEnabledStatus ?? this.labelEnabledStatus
      ..defaultSignLabelCode = defaultSignLabelCode ?? this.defaultSignLabelCode;
  }
}

ContractMainElectronicSignSettingLabelEnabledStatus $ContractMainElectronicSignSettingLabelEnabledStatusFromJson(Map<String, dynamic> json) {
  final ContractMainElectronicSignSettingLabelEnabledStatus contractMainElectronicSignSettingLabelEnabledStatus = ContractMainElectronicSignSettingLabelEnabledStatus();
  final bool? entryContract = jsonConvert.convert<bool>(json['EntryContract']);
  if (entryContract != null) {
    contractMainElectronicSignSettingLabelEnabledStatus.entryContract = entryContract;
  }
  final bool? separationContract = jsonConvert.convert<bool>(json['SeparationContract']);
  if (separationContract != null) {
    contractMainElectronicSignSettingLabelEnabledStatus.separationContract = separationContract;
  }
  return contractMainElectronicSignSettingLabelEnabledStatus;
}

Map<String, dynamic> $ContractMainElectronicSignSettingLabelEnabledStatusToJson(ContractMainElectronicSignSettingLabelEnabledStatus entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EntryContract'] = entity.entryContract;
  data['SeparationContract'] = entity.separationContract;
  return data;
}

extension ContractMainElectronicSignSettingLabelEnabledStatusExtension on ContractMainElectronicSignSettingLabelEnabledStatus {
  ContractMainElectronicSignSettingLabelEnabledStatus copyWith({
    bool? entryContract,
    bool? separationContract,
  }) {
    return ContractMainElectronicSignSettingLabelEnabledStatus()
      ..entryContract = entryContract ?? this.entryContract
      ..separationContract = separationContract ?? this.separationContract;
  }
}

ContractMainElectronicSignSettingDefaultSignLabelCode $ContractMainElectronicSignSettingDefaultSignLabelCodeFromJson(Map<String, dynamic> json) {
  final ContractMainElectronicSignSettingDefaultSignLabelCode contractMainElectronicSignSettingDefaultSignLabelCode = ContractMainElectronicSignSettingDefaultSignLabelCode();
  final String? entrycontractRehire = jsonConvert.convert<String>(json['EntryContract_Rehire']);
  if (entrycontractRehire != null) {
    contractMainElectronicSignSettingDefaultSignLabelCode.entrycontractRehire = entrycontractRehire;
  }
  final String? entrycontractService = jsonConvert.convert<String>(json['EntryContract_Service']);
  if (entrycontractService != null) {
    contractMainElectronicSignSettingDefaultSignLabelCode.entrycontractService = entrycontractService;
  }
  final String? separationcontractAgreement = jsonConvert.convert<String>(json['SeparationContract_Agreement']);
  if (separationcontractAgreement != null) {
    contractMainElectronicSignSettingDefaultSignLabelCode.separationcontractAgreement = separationcontractAgreement;
  }
  final String? entrycontractLabor = jsonConvert.convert<String>(json['EntryContract_Labor']);
  if (entrycontractLabor != null) {
    contractMainElectronicSignSettingDefaultSignLabelCode.entrycontractLabor = entrycontractLabor;
  }
  final String? entrycontractParttime = jsonConvert.convert<String>(json['EntryContract_PartTime']);
  if (entrycontractParttime != null) {
    contractMainElectronicSignSettingDefaultSignLabelCode.entrycontractParttime = entrycontractParttime;
  }
  return contractMainElectronicSignSettingDefaultSignLabelCode;
}

Map<String, dynamic> $ContractMainElectronicSignSettingDefaultSignLabelCodeToJson(ContractMainElectronicSignSettingDefaultSignLabelCode entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EntryContract_Rehire'] = entity.entrycontractRehire;
  data['EntryContract_Service'] = entity.entrycontractService;
  data['SeparationContract_Agreement'] = entity.separationcontractAgreement;
  data['EntryContract_Labor'] = entity.entrycontractLabor;
  data['EntryContract_PartTime'] = entity.entrycontractParttime;
  return data;
}

extension ContractMainElectronicSignSettingDefaultSignLabelCodeExtension on ContractMainElectronicSignSettingDefaultSignLabelCode {
  ContractMainElectronicSignSettingDefaultSignLabelCode copyWith({
    String? entrycontractRehire,
    String? entrycontractService,
    String? separationcontractAgreement,
    String? entrycontractLabor,
    String? entrycontractParttime,
  }) {
    return ContractMainElectronicSignSettingDefaultSignLabelCode()
      ..entrycontractRehire = entrycontractRehire ?? this.entrycontractRehire
      ..entrycontractService = entrycontractService ?? this.entrycontractService
      ..separationcontractAgreement = separationcontractAgreement ?? this.separationcontractAgreement
      ..entrycontractLabor = entrycontractLabor ?? this.entrycontractLabor
      ..entrycontractParttime = entrycontractParttime ?? this.entrycontractParttime;
  }
}

ContractMainElectronicDepartmentList $ContractMainElectronicDepartmentListFromJson(Map<String, dynamic> json) {
  final ContractMainElectronicDepartmentList contractMainElectronicDepartmentList = ContractMainElectronicDepartmentList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    contractMainElectronicDepartmentList.uuid = uuid;
  }
  final String? departmentName = jsonConvert.convert<String>(json['department_name']);
  if (departmentName != null) {
    contractMainElectronicDepartmentList.departmentName = departmentName;
  }
  return contractMainElectronicDepartmentList;
}

Map<String, dynamic> $ContractMainElectronicDepartmentListToJson(ContractMainElectronicDepartmentList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['department_name'] = entity.departmentName;
  return data;
}

extension ContractMainElectronicDepartmentListExtension on ContractMainElectronicDepartmentList {
  ContractMainElectronicDepartmentList copyWith({
    String? uuid,
    String? departmentName,
  }) {
    return ContractMainElectronicDepartmentList()
      ..uuid = uuid ?? this.uuid
      ..departmentName = departmentName ?? this.departmentName;
  }
}