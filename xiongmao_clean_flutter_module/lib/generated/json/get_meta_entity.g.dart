import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';

GetMetaEntity $GetMetaEntityFromJson(Map<String, dynamic> json) {
  final GetMetaEntity getMetaEntity = GetMetaEntity();
  final List<GetMetaDimissionReasonList>? dimissionReasonList = (json['dimission_reason_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaDimissionReasonList>(e) as GetMetaDimissionReasonList).toList();
  if (dimissionReasonList != null) {
    getMetaEntity.dimissionReasonList = dimissionReasonList;
  }
  final GetMetaInClassTimeList? inClassTimeList = jsonConvert.convert<GetMetaInClassTimeList>(json['in_class_time_list']);
  if (inClassTimeList != null) {
    getMetaEntity.inClassTimeList = inClassTimeList;
  }
  final GetMetaOutClassTimeList? outClassTimeList = jsonConvert.convert<GetMetaOutClassTimeList>(json['out_class_time_list']);
  if (outClassTimeList != null) {
    getMetaEntity.outClassTimeList = outClassTimeList;
  }
  final List<GetMetaExigencyUserRelationList>? exigencyUserRelationList = (json['exigency_user_relation_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaExigencyUserRelationList>(e) as GetMetaExigencyUserRelationList).toList();
  if (exigencyUserRelationList != null) {
    getMetaEntity.exigencyUserRelationList = exigencyUserRelationList;
  }
  final List<GetMetaBankList>? bankList = (json['bank_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaBankList>(e) as GetMetaBankList).toList();
  if (bankList != null) {
    getMetaEntity.bankList = bankList;
  }
  final List<SuperiorsLevelInfo>? superiorsLevelList = (json['superiors_level_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<SuperiorsLevelInfo>(e) as SuperiorsLevelInfo).toList();
  if (superiorsLevelList != null) {
    getMetaEntity.superiorsLevelList = superiorsLevelList;
  }
  return getMetaEntity;
}

Map<String, dynamic> $GetMetaEntityToJson(GetMetaEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['dimission_reason_list'] = entity.dimissionReasonList?.map((v) => v.toJson()).toList();
  data['in_class_time_list'] = entity.inClassTimeList?.toJson();
  data['out_class_time_list'] = entity.outClassTimeList?.toJson();
  data['exigency_user_relation_list'] = entity.exigencyUserRelationList?.map((v) => v.toJson()).toList();
  data['bank_list'] = entity.bankList?.map((v) => v.toJson()).toList();
  data['superiors_level_list'] = entity.superiorsLevelList?.map((v) => v.toJson()).toList();
  return data;
}

extension GetMetaEntityExtension on GetMetaEntity {
  GetMetaEntity copyWith({
    List<GetMetaDimissionReasonList>? dimissionReasonList,
    GetMetaInClassTimeList? inClassTimeList,
    GetMetaOutClassTimeList? outClassTimeList,
    List<GetMetaExigencyUserRelationList>? exigencyUserRelationList,
    List<GetMetaBankList>? bankList,
    List<SuperiorsLevelInfo>? superiorsLevelList,
  }) {
    return GetMetaEntity()
      ..dimissionReasonList = dimissionReasonList ?? this.dimissionReasonList
      ..inClassTimeList = inClassTimeList ?? this.inClassTimeList
      ..outClassTimeList = outClassTimeList ?? this.outClassTimeList
      ..exigencyUserRelationList = exigencyUserRelationList ?? this.exigencyUserRelationList
      ..bankList = bankList ?? this.bankList
      ..superiorsLevelList = superiorsLevelList ?? this.superiorsLevelList;
  }
}

GetMetaDimissionReasonList $GetMetaDimissionReasonListFromJson(Map<String, dynamic> json) {
  final GetMetaDimissionReasonList getMetaDimissionReasonList = GetMetaDimissionReasonList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaDimissionReasonList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaDimissionReasonList.name = name;
  }
  return getMetaDimissionReasonList;
}

Map<String, dynamic> $GetMetaDimissionReasonListToJson(GetMetaDimissionReasonList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaDimissionReasonListExtension on GetMetaDimissionReasonList {
  GetMetaDimissionReasonList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaDimissionReasonList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaInClassTimeList $GetMetaInClassTimeListFromJson(Map<String, dynamic> json) {
  final GetMetaInClassTimeList getMetaInClassTimeList = GetMetaInClassTimeList();
  final List<GetMetaInClassTimeListBeforeList>? beforeList = (json['before_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaInClassTimeListBeforeList>(e) as GetMetaInClassTimeListBeforeList).toList();
  if (beforeList != null) {
    getMetaInClassTimeList.beforeList = beforeList;
  }
  final List<GetMetaInClassTimeListAfterList>? afterList = (json['after_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaInClassTimeListAfterList>(e) as GetMetaInClassTimeListAfterList).toList();
  if (afterList != null) {
    getMetaInClassTimeList.afterList = afterList;
  }
  return getMetaInClassTimeList;
}

Map<String, dynamic> $GetMetaInClassTimeListToJson(GetMetaInClassTimeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['before_list'] = entity.beforeList?.map((v) => v.toJson()).toList();
  data['after_list'] = entity.afterList?.map((v) => v.toJson()).toList();
  return data;
}

extension GetMetaInClassTimeListExtension on GetMetaInClassTimeList {
  GetMetaInClassTimeList copyWith({
    List<GetMetaInClassTimeListBeforeList>? beforeList,
    List<GetMetaInClassTimeListAfterList>? afterList,
  }) {
    return GetMetaInClassTimeList()
      ..beforeList = beforeList ?? this.beforeList
      ..afterList = afterList ?? this.afterList;
  }
}

GetMetaInClassTimeListBeforeList $GetMetaInClassTimeListBeforeListFromJson(Map<String, dynamic> json) {
  final GetMetaInClassTimeListBeforeList getMetaInClassTimeListBeforeList = GetMetaInClassTimeListBeforeList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaInClassTimeListBeforeList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaInClassTimeListBeforeList.name = name;
  }
  return getMetaInClassTimeListBeforeList;
}

Map<String, dynamic> $GetMetaInClassTimeListBeforeListToJson(GetMetaInClassTimeListBeforeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaInClassTimeListBeforeListExtension on GetMetaInClassTimeListBeforeList {
  GetMetaInClassTimeListBeforeList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaInClassTimeListBeforeList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaInClassTimeListAfterList $GetMetaInClassTimeListAfterListFromJson(Map<String, dynamic> json) {
  final GetMetaInClassTimeListAfterList getMetaInClassTimeListAfterList = GetMetaInClassTimeListAfterList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaInClassTimeListAfterList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaInClassTimeListAfterList.name = name;
  }
  return getMetaInClassTimeListAfterList;
}

Map<String, dynamic> $GetMetaInClassTimeListAfterListToJson(GetMetaInClassTimeListAfterList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaInClassTimeListAfterListExtension on GetMetaInClassTimeListAfterList {
  GetMetaInClassTimeListAfterList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaInClassTimeListAfterList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaOutClassTimeList $GetMetaOutClassTimeListFromJson(Map<String, dynamic> json) {
  final GetMetaOutClassTimeList getMetaOutClassTimeList = GetMetaOutClassTimeList();
  final List<GetMetaOutClassTimeListBeforeList>? beforeList = (json['before_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaOutClassTimeListBeforeList>(e) as GetMetaOutClassTimeListBeforeList).toList();
  if (beforeList != null) {
    getMetaOutClassTimeList.beforeList = beforeList;
  }
  final List<GetMetaOutClassTimeListAfterList>? afterList = (json['after_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetMetaOutClassTimeListAfterList>(e) as GetMetaOutClassTimeListAfterList).toList();
  if (afterList != null) {
    getMetaOutClassTimeList.afterList = afterList;
  }
  return getMetaOutClassTimeList;
}

Map<String, dynamic> $GetMetaOutClassTimeListToJson(GetMetaOutClassTimeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['before_list'] = entity.beforeList?.map((v) => v.toJson()).toList();
  data['after_list'] = entity.afterList?.map((v) => v.toJson()).toList();
  return data;
}

extension GetMetaOutClassTimeListExtension on GetMetaOutClassTimeList {
  GetMetaOutClassTimeList copyWith({
    List<GetMetaOutClassTimeListBeforeList>? beforeList,
    List<GetMetaOutClassTimeListAfterList>? afterList,
  }) {
    return GetMetaOutClassTimeList()
      ..beforeList = beforeList ?? this.beforeList
      ..afterList = afterList ?? this.afterList;
  }
}

GetMetaOutClassTimeListBeforeList $GetMetaOutClassTimeListBeforeListFromJson(Map<String, dynamic> json) {
  final GetMetaOutClassTimeListBeforeList getMetaOutClassTimeListBeforeList = GetMetaOutClassTimeListBeforeList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaOutClassTimeListBeforeList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaOutClassTimeListBeforeList.name = name;
  }
  return getMetaOutClassTimeListBeforeList;
}

Map<String, dynamic> $GetMetaOutClassTimeListBeforeListToJson(GetMetaOutClassTimeListBeforeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaOutClassTimeListBeforeListExtension on GetMetaOutClassTimeListBeforeList {
  GetMetaOutClassTimeListBeforeList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaOutClassTimeListBeforeList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaOutClassTimeListAfterList $GetMetaOutClassTimeListAfterListFromJson(Map<String, dynamic> json) {
  final GetMetaOutClassTimeListAfterList getMetaOutClassTimeListAfterList = GetMetaOutClassTimeListAfterList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaOutClassTimeListAfterList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaOutClassTimeListAfterList.name = name;
  }
  return getMetaOutClassTimeListAfterList;
}

Map<String, dynamic> $GetMetaOutClassTimeListAfterListToJson(GetMetaOutClassTimeListAfterList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaOutClassTimeListAfterListExtension on GetMetaOutClassTimeListAfterList {
  GetMetaOutClassTimeListAfterList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaOutClassTimeListAfterList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaExigencyUserRelationList $GetMetaExigencyUserRelationListFromJson(Map<String, dynamic> json) {
  final GetMetaExigencyUserRelationList getMetaExigencyUserRelationList = GetMetaExigencyUserRelationList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getMetaExigencyUserRelationList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaExigencyUserRelationList.name = name;
  }
  return getMetaExigencyUserRelationList;
}

Map<String, dynamic> $GetMetaExigencyUserRelationListToJson(GetMetaExigencyUserRelationList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension GetMetaExigencyUserRelationListExtension on GetMetaExigencyUserRelationList {
  GetMetaExigencyUserRelationList copyWith({
    String? id,
    String? name,
  }) {
    return GetMetaExigencyUserRelationList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

GetMetaBankList $GetMetaBankListFromJson(Map<String, dynamic> json) {
  final GetMetaBankList getMetaBankList = GetMetaBankList();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    getMetaBankList.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getMetaBankList.name = name;
  }
  return getMetaBankList;
}

Map<String, dynamic> $GetMetaBankListToJson(GetMetaBankList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['name'] = entity.name;
  return data;
}

extension GetMetaBankListExtension on GetMetaBankList {
  GetMetaBankList copyWith({
    String? code,
    String? name,
  }) {
    return GetMetaBankList()
      ..code = code ?? this.code
      ..name = name ?? this.name;
  }
}

SuperiorsLevelInfo $SuperiorsLevelInfoFromJson(Map<String, dynamic> json) {
  final SuperiorsLevelInfo superiorsLevelInfo = SuperiorsLevelInfo();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    superiorsLevelInfo.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    superiorsLevelInfo.name = name;
  }
  return superiorsLevelInfo;
}

Map<String, dynamic> $SuperiorsLevelInfoToJson(SuperiorsLevelInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension SuperiorsLevelInfoExtension on SuperiorsLevelInfo {
  SuperiorsLevelInfo copyWith({
    String? id,
    String? name,
  }) {
    return SuperiorsLevelInfo()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}