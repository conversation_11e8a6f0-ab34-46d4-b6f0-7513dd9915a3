import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_staff_belong_entity.dart';
import '../../business/common/company/bean/department_company_entity.dart';


DepartmentCompanyStaffBelongEntity $DepartmentCompanyStaffBelongEntityFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyStaffBelongEntity departmentCompanyStaffBelongEntity = DepartmentCompanyStaffBelongEntity();
  final List<DepartmentCompanyList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<DepartmentCompanyList>(e) as DepartmentCompanyList).toList();
  if (list != null) {
    departmentCompanyStaffBelongEntity.list = list;
  }
  return departmentCompanyStaffBelongEntity;
}

Map<String, dynamic> $DepartmentCompanyStaffBelongEntityToJson(DepartmentCompanyStaffBelongEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension DepartmentCompanyStaffBelongEntityExtension on DepartmentCompanyStaffBelongEntity {
  DepartmentCompanyStaffBelongEntity copyWith({
    List<DepartmentCompanyList>? list,
  }) {
    return DepartmentCompanyStaffBelongEntity()
      ..list = list ?? this.list;
  }
}