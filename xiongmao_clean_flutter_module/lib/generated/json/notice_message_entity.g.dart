import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/bean/notice_message_entity.dart';

NoticeMessageEntity $NoticeMessageEntityFromJson(Map<String, dynamic> json) {
  final NoticeMessageEntity noticeMessageEntity = NoticeMessageEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    noticeMessageEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    noticeMessageEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    noticeMessageEntity.total = total;
  }
  final List<NoticeMessageList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<NoticeMessageList>(e) as NoticeMessageList).toList();
  if (list != null) {
    noticeMessageEntity.list = list;
  }
  return noticeMessageEntity;
}

Map<String, dynamic> $NoticeMessageEntityToJson(NoticeMessageEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension NoticeMessageEntityExtension on NoticeMessageEntity {
  NoticeMessageEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<NoticeMessageList>? list,
  }) {
    return NoticeMessageEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

NoticeMessageList $NoticeMessageListFromJson(Map<String, dynamic> json) {
  final NoticeMessageList noticeMessageList = NoticeMessageList();
  final String? noticeType = jsonConvert.convert<String>(json['notice_type']);
  if (noticeType != null) {
    noticeMessageList.noticeType = noticeType;
  }
  final String? noticeTypeName = jsonConvert.convert<String>(json['notice_type_name']);
  if (noticeTypeName != null) {
    noticeMessageList.noticeTypeName = noticeTypeName;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    noticeMessageList.content = content;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    noticeMessageList.createTime = createTime;
  }
  return noticeMessageList;
}

Map<String, dynamic> $NoticeMessageListToJson(NoticeMessageList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['notice_type'] = entity.noticeType;
  data['notice_type_name'] = entity.noticeTypeName;
  data['content'] = entity.content;
  data['create_time'] = entity.createTime;
  return data;
}

extension NoticeMessageListExtension on NoticeMessageList {
  NoticeMessageList copyWith({
    String? noticeType,
    String? noticeTypeName,
    String? content,
    String? createTime,
  }) {
    return NoticeMessageList()
      ..noticeType = noticeType ?? this.noticeType
      ..noticeTypeName = noticeTypeName ?? this.noticeTypeName
      ..content = content ?? this.content
      ..createTime = createTime ?? this.createTime;
  }
}