import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/recharge_pre_entity.dart';

RechargePreEntity $RechargePreEntityFromJson(Map<String, dynamic> json) {
  final RechargePreEntity rechargePreEntity = RechargePreEntity();
  final String? balance = jsonConvert.convert<String>(json['balance']);
  if (balance != null) {
    rechargePreEntity.balance = balance;
  }
  final RechargePreToAccount? toAccount = jsonConvert.convert<RechargePreToAccount>(json['to_account']);
  if (toAccount != null) {
    rechargePreEntity.toAccount = toAccount;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    rechargePreEntity.status = status;
  }
  final RechargePrePrompt? prompt = jsonConvert.convert<RechargePrePrompt>(json['prompt']);
  if (prompt != null) {
    rechargePreEntity.prompt = prompt;
  }
  final String? promptUrl = jsonConvert.convert<String>(json['prompt_url']);
  if (promptUrl != null) {
    rechargePreEntity.promptUrl = promptUrl;
  }
  return rechargePreEntity;
}

Map<String, dynamic> $RechargePreEntityToJson(RechargePreEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['balance'] = entity.balance;
  data['to_account'] = entity.toAccount?.toJson();
  data['status'] = entity.status;
  data['prompt'] = entity.prompt?.toJson();
  data['prompt_url'] = entity.promptUrl;
  return data;
}

extension RechargePreEntityExtension on RechargePreEntity {
  RechargePreEntity copyWith({
    String? balance,
    RechargePreToAccount? toAccount,
    int? status,
    RechargePrePrompt? prompt,
    String? promptUrl,
  }) {
    return RechargePreEntity()
      ..balance = balance ?? this.balance
      ..toAccount = toAccount ?? this.toAccount
      ..status = status ?? this.status
      ..prompt = prompt ?? this.prompt
      ..promptUrl = promptUrl ?? this.promptUrl;
  }
}

RechargePreToAccount $RechargePreToAccountFromJson(Map<String, dynamic> json) {
  final RechargePreToAccount rechargePreToAccount = RechargePreToAccount();
  final String? accountName = jsonConvert.convert<String>(json['account_name']);
  if (accountName != null) {
    rechargePreToAccount.accountName = accountName;
  }
  final String? accountNo = jsonConvert.convert<String>(json['account_no']);
  if (accountNo != null) {
    rechargePreToAccount.accountNo = accountNo;
  }
  final String? accountBank = jsonConvert.convert<String>(json['account_bank']);
  if (accountBank != null) {
    rechargePreToAccount.accountBank = accountBank;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bank_code']);
  if (bankCode != null) {
    rechargePreToAccount.bankCode = bankCode;
  }
  return rechargePreToAccount;
}

Map<String, dynamic> $RechargePreToAccountToJson(RechargePreToAccount entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['account_name'] = entity.accountName;
  data['account_no'] = entity.accountNo;
  data['account_bank'] = entity.accountBank;
  data['bank_code'] = entity.bankCode;
  return data;
}

extension RechargePreToAccountExtension on RechargePreToAccount {
  RechargePreToAccount copyWith({
    String? accountName,
    String? accountNo,
    String? accountBank,
    String? bankCode,
  }) {
    return RechargePreToAccount()
      ..accountName = accountName ?? this.accountName
      ..accountNo = accountNo ?? this.accountNo
      ..accountBank = accountBank ?? this.accountBank
      ..bankCode = bankCode ?? this.bankCode;
  }
}

RechargePrePrompt $RechargePrePromptFromJson(Map<String, dynamic> json) {
  final RechargePrePrompt rechargePrePrompt = RechargePrePrompt();
  final String? text = jsonConvert.convert<String>(json['text']);
  if (text != null) {
    rechargePrePrompt.text = text;
  }
  final String? highlight = jsonConvert.convert<String>(json['highlight']);
  if (highlight != null) {
    rechargePrePrompt.highlight = highlight;
  }
  return rechargePrePrompt;
}

Map<String, dynamic> $RechargePrePromptToJson(RechargePrePrompt entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['text'] = entity.text;
  data['highlight'] = entity.highlight;
  return data;
}

extension RechargePrePromptExtension on RechargePrePrompt {
  RechargePrePrompt copyWith({
    String? text,
    String? highlight,
  }) {
    return RechargePrePrompt()
      ..text = text ?? this.text
      ..highlight = highlight ?? this.highlight;
  }
}