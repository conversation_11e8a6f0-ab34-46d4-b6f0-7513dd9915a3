import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/business_format_entity.dart';

BusinessFormatEntity $BusinessFormatEntityFromJson(Map<String, dynamic> json) {
  final BusinessFormatEntity businessFormatEntity = BusinessFormatEntity();
  final List<BusinessFormatList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BusinessFormatList>(e) as BusinessFormatList).toList();
  if (list != null) {
    businessFormatEntity.list = list;
  }
  return businessFormatEntity;
}

Map<String, dynamic> $BusinessFormatEntityToJson(BusinessFormatEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension BusinessFormatEntityExtension on BusinessFormatEntity {
  BusinessFormatEntity copyWith({
    List<BusinessFormatList>? list,
  }) {
    return BusinessFormatEntity()
      ..list = list ?? this.list;
  }
}

BusinessFormatList $BusinessFormatListFromJson(Map<String, dynamic> json) {
  final BusinessFormatList businessFormatList = BusinessFormatList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    businessFormatList.id = id;
  }
  final String? catName = jsonConvert.convert<String>(json['cat_name']);
  if (catName != null) {
    businessFormatList.catName = catName;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    businessFormatList.pid = pid;
  }
  final String? catLevel = jsonConvert.convert<String>(json['cat_level']);
  if (catLevel != null) {
    businessFormatList.catLevel = catLevel;
  }
  final List<BusinessFormatListChildList>? childList = (json['child_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BusinessFormatListChildList>(e) as BusinessFormatListChildList).toList();
  if (childList != null) {
    businessFormatList.childList = childList;
  }
  return businessFormatList;
}

Map<String, dynamic> $BusinessFormatListToJson(BusinessFormatList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['cat_name'] = entity.catName;
  data['pid'] = entity.pid;
  data['cat_level'] = entity.catLevel;
  data['child_list'] = entity.childList?.map((v) => v.toJson()).toList();
  return data;
}

extension BusinessFormatListExtension on BusinessFormatList {
  BusinessFormatList copyWith({
    String? id,
    String? catName,
    String? pid,
    String? catLevel,
    List<BusinessFormatListChildList>? childList,
  }) {
    return BusinessFormatList()
      ..id = id ?? this.id
      ..catName = catName ?? this.catName
      ..pid = pid ?? this.pid
      ..catLevel = catLevel ?? this.catLevel
      ..childList = childList ?? this.childList;
  }
}

BusinessFormatListChildList $BusinessFormatListChildListFromJson(Map<String, dynamic> json) {
  final BusinessFormatListChildList businessFormatListChildList = BusinessFormatListChildList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    businessFormatListChildList.id = id;
  }
  final String? catName = jsonConvert.convert<String>(json['cat_name']);
  if (catName != null) {
    businessFormatListChildList.catName = catName;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    businessFormatListChildList.pid = pid;
  }
  final String? catLevel = jsonConvert.convert<String>(json['cat_level']);
  if (catLevel != null) {
    businessFormatListChildList.catLevel = catLevel;
  }
  return businessFormatListChildList;
}

Map<String, dynamic> $BusinessFormatListChildListToJson(BusinessFormatListChildList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['cat_name'] = entity.catName;
  data['pid'] = entity.pid;
  data['cat_level'] = entity.catLevel;
  return data;
}

extension BusinessFormatListChildListExtension on BusinessFormatListChildList {
  BusinessFormatListChildList copyWith({
    String? id,
    String? catName,
    String? pid,
    String? catLevel,
  }) {
    return BusinessFormatListChildList()
      ..id = id ?? this.id
      ..catName = catName ?? this.catName
      ..pid = pid ?? this.pid
      ..catLevel = catLevel ?? this.catLevel;
  }
}