import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/account_bank_entity.dart';

AccountBankEntity $AccountBankEntityFromJson(Map<String, dynamic> json) {
  final AccountBankEntity accountBankEntity = AccountBankEntity();
  final List<AccountBankList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AccountBankList>(e) as AccountBankList).toList();
  if (list != null) {
    accountBankEntity.list = list;
  }
  return accountBankEntity;
}

Map<String, dynamic> $AccountBankEntityToJson(AccountBankEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension AccountBankEntityExtension on AccountBankEntity {
  AccountBankEntity copyWith({
    List<AccountBankList>? list,
  }) {
    return AccountBankEntity()
      ..list = list ?? this.list;
  }
}

AccountBankList $AccountBankListFromJson(Map<String, dynamic> json) {
  final AccountBankList accountBankList = AccountBankList();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    accountBankList.code = code;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    accountBankList.name = name;
  }
  final String? icon = jsonConvert.convert<String>(json['icon']);
  if (icon != null) {
    accountBankList.icon = icon;
  }
  return accountBankList;
}

Map<String, dynamic> $AccountBankListToJson(AccountBankList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['name'] = entity.name;
  data['icon'] = entity.icon;
  return data;
}

extension AccountBankListExtension on AccountBankList {
  AccountBankList copyWith({
    String? code,
    String? name,
    String? icon,
  }) {
    return AccountBankList()
      ..code = code ?? this.code
      ..name = name ?? this.name
      ..icon = icon ?? this.icon;
  }
}