import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_area_entity.dart';

CleanAreaEntity $CleanAreaEntityFromJson(Map<String, dynamic> json) {
  final CleanAreaEntity cleanAreaEntity = CleanAreaEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    cleanAreaEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    cleanAreaEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    cleanAreaEntity.total = total;
  }
  final List<CleanAreaList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanAreaList>(e) as CleanAreaList).toList();
  if (list != null) {
    cleanAreaEntity.list = list;
  }
  return cleanAreaEntity;
}

Map<String, dynamic> $CleanAreaEntityToJson(CleanAreaEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CleanAreaEntityExtension on CleanAreaEntity {
  CleanAreaEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<CleanAreaList>? list,
  }) {
    return CleanAreaEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

CleanAreaList $CleanAreaListFromJson(Map<String, dynamic> json) {
  final CleanAreaList cleanAreaList = CleanAreaList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    cleanAreaList.uuid = uuid;
  }
  final String? areaName = jsonConvert.convert<String>(json['area_name']);
  if (areaName != null) {
    cleanAreaList.areaName = areaName;
  }
  return cleanAreaList;
}

Map<String, dynamic> $CleanAreaListToJson(CleanAreaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['area_name'] = entity.areaName;
  return data;
}

extension CleanAreaListExtension on CleanAreaList {
  CleanAreaList copyWith({
    String? uuid,
    String? areaName,
  }) {
    return CleanAreaList()
      ..uuid = uuid ?? this.uuid
      ..areaName = areaName ?? this.areaName;
  }
}