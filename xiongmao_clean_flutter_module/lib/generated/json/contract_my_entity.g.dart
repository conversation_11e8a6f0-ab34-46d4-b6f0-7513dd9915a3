import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_my_entity.dart';

ContractMyEntity $ContractMyEntityFromJson(Map<String, dynamic> json) {
  final ContractMyEntity contractMyEntity = ContractMyEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    contractMyEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    contractMyEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    contractMyEntity.total = total;
  }
  final List<ContractMyList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractMyList>(e) as ContractMyList).toList();
  if (list != null) {
    contractMyEntity.list = list;
  }
  return contractMyEntity;
}

Map<String, dynamic> $ContractMyEntityToJson(ContractMyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractMyEntityExtension on ContractMyEntity {
  ContractMyEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ContractMyList>? list,
  }) {
    return ContractMyEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ContractMyList $ContractMyListFromJson(Map<String, dynamic> json) {
  final ContractMyList contractMyList = ContractMyList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    contractMyList.uuid = uuid;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    contractMyList.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    contractMyList.endTime = endTime;
  }
  final String? mainLabelCode = jsonConvert.convert<String>(json['main_label_code']);
  if (mainLabelCode != null) {
    contractMyList.mainLabelCode = mainLabelCode;
  }
  final String? subLabelCode = jsonConvert.convert<String>(json['sub_label_code']);
  if (subLabelCode != null) {
    contractMyList.subLabelCode = subLabelCode;
  }
  final String? contractTitle = jsonConvert.convert<String>(json['contract_title']);
  if (contractTitle != null) {
    contractMyList.contractTitle = contractTitle;
  }
  final String? createUserName = jsonConvert.convert<String>(json['create_user_name']);
  if (createUserName != null) {
    contractMyList.createUserName = createUserName;
  }
  final String? contractType = jsonConvert.convert<String>(json['contract_type']);
  if (contractType != null) {
    contractMyList.contractType = contractType;
  }
  final String? contractStatus = jsonConvert.convert<String>(json['contract_status']);
  if (contractStatus != null) {
    contractMyList.contractStatus = contractStatus;
  }
  final String? contractStatusName = jsonConvert.convert<String>(json['contract_status_name']);
  if (contractStatusName != null) {
    contractMyList.contractStatusName = contractStatusName;
  }
  final String? contractTypeName = jsonConvert.convert<String>(json['contract_type_name']);
  if (contractTypeName != null) {
    contractMyList.contractTypeName = contractTypeName;
  }
  final List<String>? contractPicList = (json['contract_pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (contractPicList != null) {
    contractMyList.contractPicList = contractPicList;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractMyList.createTime = createTime;
  }
  final String? eleContractUuid = jsonConvert.convert<String>(json['ele_contract_uuid']);
  if (eleContractUuid != null) {
    contractMyList.eleContractUuid = eleContractUuid;
  }
  final String? eleContractUrl = jsonConvert.convert<String>(json['ele_contract_url']);
  if (eleContractUrl != null) {
    contractMyList.eleContractUrl = eleContractUrl;
  }
  final String? leftReason = jsonConvert.convert<String>(json['left_reason']);
  if (leftReason != null) {
    contractMyList.leftReason = leftReason;
  }
  final String? leftReasonName = jsonConvert.convert<String>(json['left_reason_name']);
  if (leftReasonName != null) {
    contractMyList.leftReasonName = leftReasonName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    contractMyList.remark = remark;
  }
  return contractMyList;
}

Map<String, dynamic> $ContractMyListToJson(ContractMyList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['main_label_code'] = entity.mainLabelCode;
  data['sub_label_code'] = entity.subLabelCode;
  data['contract_title'] = entity.contractTitle;
  data['create_user_name'] = entity.createUserName;
  data['contract_type'] = entity.contractType;
  data['contract_status'] = entity.contractStatus;
  data['contract_status_name'] = entity.contractStatusName;
  data['contract_type_name'] = entity.contractTypeName;
  data['contract_pic_list'] = entity.contractPicList;
  data['create_time'] = entity.createTime;
  data['ele_contract_uuid'] = entity.eleContractUuid;
  data['ele_contract_url'] = entity.eleContractUrl;
  data['left_reason'] = entity.leftReason;
  data['left_reason_name'] = entity.leftReasonName;
  data['remark'] = entity.remark;
  return data;
}

extension ContractMyListExtension on ContractMyList {
  ContractMyList copyWith({
    String? uuid,
    String? startTime,
    String? endTime,
    String? mainLabelCode,
    String? subLabelCode,
    String? contractTitle,
    String? createUserName,
    String? contractType,
    String? contractStatus,
    String? contractStatusName,
    String? contractTypeName,
    List<String>? contractPicList,
    String? createTime,
    String? eleContractUuid,
    String? eleContractUrl,
    String? leftReason,
    String? leftReasonName,
    String? remark,
  }) {
    return ContractMyList()
      ..uuid = uuid ?? this.uuid
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..mainLabelCode = mainLabelCode ?? this.mainLabelCode
      ..subLabelCode = subLabelCode ?? this.subLabelCode
      ..contractTitle = contractTitle ?? this.contractTitle
      ..createUserName = createUserName ?? this.createUserName
      ..contractType = contractType ?? this.contractType
      ..contractStatus = contractStatus ?? this.contractStatus
      ..contractStatusName = contractStatusName ?? this.contractStatusName
      ..contractTypeName = contractTypeName ?? this.contractTypeName
      ..contractPicList = contractPicList ?? this.contractPicList
      ..createTime = createTime ?? this.createTime
      ..eleContractUuid = eleContractUuid ?? this.eleContractUuid
      ..eleContractUrl = eleContractUrl ?? this.eleContractUrl
      ..leftReason = leftReason ?? this.leftReason
      ..leftReasonName = leftReasonName ?? this.leftReasonName
      ..remark = remark ?? this.remark;
  }
}