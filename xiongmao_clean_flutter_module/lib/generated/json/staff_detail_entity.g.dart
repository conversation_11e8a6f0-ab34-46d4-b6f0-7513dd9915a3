import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/salary_config_entity.dart';


StaffDetailEntity $StaffDetailEntityFromJson(Map<String, dynamic> json) {
  final StaffDetailEntity staffDetailEntity = StaffDetailEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    staffDetailEntity.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    staffDetailEntity.userName = userName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    staffDetailEntity.avatar = avatar;
  }
  final String? salary = jsonConvert.convert<String>(json['salary']);
  if (salary != null) {
    staffDetailEntity.salary = salary;
  }
  final String? roleName = jsonConvert.convert<String>(json['role_name']);
  if (roleName != null) {
    staffDetailEntity.roleName = roleName;
  }
  final String? sex = jsonConvert.convert<String>(json['sex']);
  if (sex != null) {
    staffDetailEntity.sex = sex;
  }
  final String? sexName = jsonConvert.convert<String>(json['sex_name']);
  if (sexName != null) {
    staffDetailEntity.sexName = sexName;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    staffDetailEntity.address = address;
  }
  final String? age = jsonConvert.convert<String>(json['age']);
  if (age != null) {
    staffDetailEntity.age = age;
  }
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    staffDetailEntity.status = status;
  }
  final String? nationName = jsonConvert.convert<String>(json['nation_name']);
  if (nationName != null) {
    staffDetailEntity.nationName = nationName;
  }
  final String? provinceName = jsonConvert.convert<String>(json['province_name']);
  if (provinceName != null) {
    staffDetailEntity.provinceName = provinceName;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    staffDetailEntity.cityName = cityName;
  }
  final String? workAge = jsonConvert.convert<String>(json['work_age']);
  if (workAge != null) {
    staffDetailEntity.workAge = workAge;
  }
  final String? applicationNo = jsonConvert.convert<String>(json['application_no']);
  if (applicationNo != null) {
    staffDetailEntity.applicationNo = applicationNo;
  }
  final String? isHasIdCard = jsonConvert.convert<String>(json['is_has_id_card']);
  if (isHasIdCard != null) {
    staffDetailEntity.isHasIdCard = isHasIdCard;
  }
  final String? isHasBankCard = jsonConvert.convert<String>(json['is_has_bank_card']);
  if (isHasBankCard != null) {
    staffDetailEntity.isHasBankCard = isHasBankCard;
  }
  final String? isHasContract = jsonConvert.convert<String>(json['is_has_contract']);
  if (isHasContract != null) {
    staffDetailEntity.isHasContract = isHasContract;
  }
  final String? isHasHealthyCard = jsonConvert.convert<String>(json['is_has_healthy_card']);
  if (isHasHealthyCard != null) {
    staffDetailEntity.isHasHealthyCard = isHasHealthyCard;
  }
  final String? isHasCreditWind = jsonConvert.convert<String>(json['is_has_credit_wind']);
  if (isHasCreditWind != null) {
    staffDetailEntity.isHasCreditWind = isHasCreditWind;
  }
  final String? searchCreditTime = jsonConvert.convert<String>(json['search_credit_time']);
  if (searchCreditTime != null) {
    staffDetailEntity.searchCreditTime = searchCreditTime;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    staffDetailEntity.createTime = createTime;
  }
  final String? signUrl = jsonConvert.convert<String>(json['sign_url']);
  if (signUrl != null) {
    staffDetailEntity.signUrl = signUrl;
  }
  final String? superiorsUserUuid = jsonConvert.convert<String>(json['superiors_user_uuid']);
  if (superiorsUserUuid != null) {
    staffDetailEntity.superiorsUserUuid = superiorsUserUuid;
  }
  final String? superiorsUserName = jsonConvert.convert<String>(json['superiors_user_name']);
  if (superiorsUserName != null) {
    staffDetailEntity.superiorsUserName = superiorsUserName;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    staffDetailEntity.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    staffDetailEntity.projectShortName = projectShortName;
  }
  final String? projectCatName = jsonConvert.convert<String>(json['project_cat_name']);
  if (projectCatName != null) {
    staffDetailEntity.projectCatName = projectCatName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    staffDetailEntity.jobName = jobName;
  }
  final String? leftRemark = jsonConvert.convert<String>(json['left_remark']);
  if (leftRemark != null) {
    staffDetailEntity.leftRemark = leftRemark;
  }
  final String? insuranceType = jsonConvert.convert<String>(json['insurance_type']);
  if (insuranceType != null) {
    staffDetailEntity.insuranceType = insuranceType;
  }
  final String? insuranceProductName = jsonConvert.convert<String>(json['insurance_product_name']);
  if (insuranceProductName != null) {
    staffDetailEntity.insuranceProductName = insuranceProductName;
  }
  final StaffDetailIdentity? identity = jsonConvert.convert<StaffDetailIdentity>(json['identity']);
  if (identity != null) {
    staffDetailEntity.identity = identity;
  }
  final StaffDetailBank? bank = jsonConvert.convert<StaffDetailBank>(json['bank']);
  if (bank != null) {
    staffDetailEntity.bank = bank;
  }
  final StaffDetailContact? contact = jsonConvert.convert<StaffDetailContact>(json['contact']);
  if (contact != null) {
    staffDetailEntity.contact = contact;
  }
  final StaffDetailWorkInfo? workInfo = jsonConvert.convert<StaffDetailWorkInfo>(json['work_info']);
  if (workInfo != null) {
    staffDetailEntity.workInfo = workInfo;
  }
  final StaffDetailHealthyPic? healthyPic = jsonConvert.convert<StaffDetailHealthyPic>(json['healthy_pic']);
  if (healthyPic != null) {
    staffDetailEntity.healthyPic = healthyPic;
  }
  final List<StaffDetailIdentityPicList>? noCrimePicList = (json['no_crime_pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (noCrimePicList != null) {
    staffDetailEntity.noCrimePicList = noCrimePicList;
  }
  final List<StaffDetailIdentityPicList>? entryRegistrationPicList = (json['entry_registration_pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (entryRegistrationPicList != null) {
    staffDetailEntity.entryRegistrationPicList = entryRegistrationPicList;
  }
  final List<StaffDetailCreditList>? creditList = (json['credit_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailCreditList>(e) as StaffDetailCreditList).toList();
  if (creditList != null) {
    staffDetailEntity.creditList = creditList;
  }
  final StaffDetailContract? contract = jsonConvert.convert<StaffDetailContract>(json['contract']);
  if (contract != null) {
    staffDetailEntity.contract = contract;
  }
  final StaffDetailLeftContract? leftContract = jsonConvert.convert<StaffDetailLeftContract>(json['left_contract']);
  if (leftContract != null) {
    staffDetailEntity.leftContract = leftContract;
  }
  final StaffDetailSalaryInsurance? salaryInsurance = jsonConvert.convert<StaffDetailSalaryInsurance>(json['salary_insurance']);
  if (salaryInsurance != null) {
    staffDetailEntity.salaryInsurance = salaryInsurance;
  }
  return staffDetailEntity;
}

Map<String, dynamic> $StaffDetailEntityToJson(StaffDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['avatar'] = entity.avatar;
  data['salary'] = entity.salary;
  data['role_name'] = entity.roleName;
  data['sex'] = entity.sex;
  data['sex_name'] = entity.sexName;
  data['address'] = entity.address;
  data['age'] = entity.age;
  data['status'] = entity.status;
  data['nation_name'] = entity.nationName;
  data['province_name'] = entity.provinceName;
  data['city_name'] = entity.cityName;
  data['work_age'] = entity.workAge;
  data['application_no'] = entity.applicationNo;
  data['is_has_id_card'] = entity.isHasIdCard;
  data['is_has_bank_card'] = entity.isHasBankCard;
  data['is_has_contract'] = entity.isHasContract;
  data['is_has_healthy_card'] = entity.isHasHealthyCard;
  data['is_has_credit_wind'] = entity.isHasCreditWind;
  data['search_credit_time'] = entity.searchCreditTime;
  data['create_time'] = entity.createTime;
  data['sign_url'] = entity.signUrl;
  data['superiors_user_uuid'] = entity.superiorsUserUuid;
  data['superiors_user_name'] = entity.superiorsUserName;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['project_cat_name'] = entity.projectCatName;
  data['job_name'] = entity.jobName;
  data['left_remark'] = entity.leftRemark;
  data['insurance_type'] = entity.insuranceType;
  data['insurance_product_name'] = entity.insuranceProductName;
  data['identity'] = entity.identity?.toJson();
  data['bank'] = entity.bank?.toJson();
  data['contact'] = entity.contact?.toJson();
  data['work_info'] = entity.workInfo?.toJson();
  data['healthy_pic'] = entity.healthyPic?.toJson();
  data['no_crime_pic_list'] = entity.noCrimePicList?.map((v) => v.toJson()).toList();
  data['entry_registration_pic_list'] = entity.entryRegistrationPicList?.map((v) => v.toJson()).toList();
  data['credit_list'] = entity.creditList?.map((v) => v.toJson()).toList();
  data['contract'] = entity.contract?.toJson();
  data['left_contract'] = entity.leftContract?.toJson();
  data['salary_insurance'] = entity.salaryInsurance?.toJson();
  return data;
}

extension StaffDetailEntityExtension on StaffDetailEntity {
  StaffDetailEntity copyWith({
    String? uuid,
    String? userName,
    String? avatar,
    String? salary,
    String? roleName,
    String? sex,
    String? sexName,
    String? address,
    String? age,
    String? status,
    String? nationName,
    String? provinceName,
    String? cityName,
    String? workAge,
    String? applicationNo,
    String? isHasIdCard,
    String? isHasBankCard,
    String? isHasContract,
    String? isHasHealthyCard,
    String? isHasCreditWind,
    String? searchCreditTime,
    String? createTime,
    String? signUrl,
    String? superiorsUserUuid,
    String? superiorsUserName,
    String? projectName,
    String? projectShortName,
    String? projectCatName,
    String? jobName,
    String? leftRemark,
    String? insuranceType,
    String? insuranceProductName,
    StaffDetailIdentity? identity,
    StaffDetailBank? bank,
    StaffDetailContact? contact,
    StaffDetailWorkInfo? workInfo,
    StaffDetailHealthyPic? healthyPic,
    List<StaffDetailIdentityPicList>? noCrimePicList,
    List<StaffDetailIdentityPicList>? entryRegistrationPicList,
    List<StaffDetailCreditList>? creditList,
    StaffDetailContract? contract,
    StaffDetailLeftContract? leftContract,
    StaffDetailSalaryInsurance? salaryInsurance,
  }) {
    return StaffDetailEntity()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..avatar = avatar ?? this.avatar
      ..salary = salary ?? this.salary
      ..roleName = roleName ?? this.roleName
      ..sex = sex ?? this.sex
      ..sexName = sexName ?? this.sexName
      ..address = address ?? this.address
      ..age = age ?? this.age
      ..status = status ?? this.status
      ..nationName = nationName ?? this.nationName
      ..provinceName = provinceName ?? this.provinceName
      ..cityName = cityName ?? this.cityName
      ..workAge = workAge ?? this.workAge
      ..applicationNo = applicationNo ?? this.applicationNo
      ..isHasIdCard = isHasIdCard ?? this.isHasIdCard
      ..isHasBankCard = isHasBankCard ?? this.isHasBankCard
      ..isHasContract = isHasContract ?? this.isHasContract
      ..isHasHealthyCard = isHasHealthyCard ?? this.isHasHealthyCard
      ..isHasCreditWind = isHasCreditWind ?? this.isHasCreditWind
      ..searchCreditTime = searchCreditTime ?? this.searchCreditTime
      ..createTime = createTime ?? this.createTime
      ..signUrl = signUrl ?? this.signUrl
      ..superiorsUserUuid = superiorsUserUuid ?? this.superiorsUserUuid
      ..superiorsUserName = superiorsUserName ?? this.superiorsUserName
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..projectCatName = projectCatName ?? this.projectCatName
      ..jobName = jobName ?? this.jobName
      ..leftRemark = leftRemark ?? this.leftRemark
      ..insuranceType = insuranceType ?? this.insuranceType
      ..insuranceProductName = insuranceProductName ?? this.insuranceProductName
      ..identity = identity ?? this.identity
      ..bank = bank ?? this.bank
      ..contact = contact ?? this.contact
      ..workInfo = workInfo ?? this.workInfo
      ..healthyPic = healthyPic ?? this.healthyPic
      ..noCrimePicList = noCrimePicList ?? this.noCrimePicList
      ..entryRegistrationPicList = entryRegistrationPicList ?? this.entryRegistrationPicList
      ..creditList = creditList ?? this.creditList
      ..contract = contract ?? this.contract
      ..leftContract = leftContract ?? this.leftContract
      ..salaryInsurance = salaryInsurance ?? this.salaryInsurance;
  }
}

StaffDetailIdentity $StaffDetailIdentityFromJson(Map<String, dynamic> json) {
  final StaffDetailIdentity staffDetailIdentity = StaffDetailIdentity();
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    staffDetailIdentity.userName = userName;
  }
  final String? idNumber = jsonConvert.convert<String>(json['id_number']);
  if (idNumber != null) {
    staffDetailIdentity.idNumber = idNumber;
  }
  final String? birthday = jsonConvert.convert<String>(json['birthday']);
  if (birthday != null) {
    staffDetailIdentity.birthday = birthday;
  }
  final String? provinceId = jsonConvert.convert<String>(json['province_id']);
  if (provinceId != null) {
    staffDetailIdentity.provinceId = provinceId;
  }
  final String? provinceName = jsonConvert.convert<String>(json['province_name']);
  if (provinceName != null) {
    staffDetailIdentity.provinceName = provinceName;
  }
  final String? cityId = jsonConvert.convert<String>(json['city_id']);
  if (cityId != null) {
    staffDetailIdentity.cityId = cityId;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    staffDetailIdentity.cityName = cityName;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    staffDetailIdentity.address = address;
  }
  final String? marriageStatus = jsonConvert.convert<String>(json['marriage_status']);
  if (marriageStatus != null) {
    staffDetailIdentity.marriageStatus = marriageStatus;
  }
  final String? marriageStatusName = jsonConvert.convert<String>(json['marriage_status_name']);
  if (marriageStatusName != null) {
    staffDetailIdentity.marriageStatusName = marriageStatusName;
  }
  final String? nationId = jsonConvert.convert<String>(json['nation_id']);
  if (nationId != null) {
    staffDetailIdentity.nationId = nationId;
  }
  final String? nationName = jsonConvert.convert<String>(json['nation_name']);
  if (nationName != null) {
    staffDetailIdentity.nationName = nationName;
  }
  final String? isIdLong = jsonConvert.convert<String>(json['is_id_long']);
  if (isIdLong != null) {
    staffDetailIdentity.isIdLong = isIdLong;
  }
  final String? idStartDate = jsonConvert.convert<String>(json['id_start_date']);
  if (idStartDate != null) {
    staffDetailIdentity.idStartDate = idStartDate;
  }
  final String? idEndDate = jsonConvert.convert<String>(json['id_end_date']);
  if (idEndDate != null) {
    staffDetailIdentity.idEndDate = idEndDate;
  }
  final List<StaffDetailIdentityPicList>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (picList != null) {
    staffDetailIdentity.picList = picList;
  }
  return staffDetailIdentity;
}

Map<String, dynamic> $StaffDetailIdentityToJson(StaffDetailIdentity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_name'] = entity.userName;
  data['id_number'] = entity.idNumber;
  data['birthday'] = entity.birthday;
  data['province_id'] = entity.provinceId;
  data['province_name'] = entity.provinceName;
  data['city_id'] = entity.cityId;
  data['city_name'] = entity.cityName;
  data['address'] = entity.address;
  data['marriage_status'] = entity.marriageStatus;
  data['marriage_status_name'] = entity.marriageStatusName;
  data['nation_id'] = entity.nationId;
  data['nation_name'] = entity.nationName;
  data['is_id_long'] = entity.isIdLong;
  data['id_start_date'] = entity.idStartDate;
  data['id_end_date'] = entity.idEndDate;
  data['pic_list'] = entity.picList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailIdentityExtension on StaffDetailIdentity {
  StaffDetailIdentity copyWith({
    String? userName,
    String? idNumber,
    String? birthday,
    String? provinceId,
    String? provinceName,
    String? cityId,
    String? cityName,
    String? address,
    String? marriageStatus,
    String? marriageStatusName,
    String? nationId,
    String? nationName,
    String? isIdLong,
    String? idStartDate,
    String? idEndDate,
    List<StaffDetailIdentityPicList>? picList,
  }) {
    return StaffDetailIdentity()
      ..userName = userName ?? this.userName
      ..idNumber = idNumber ?? this.idNumber
      ..birthday = birthday ?? this.birthday
      ..provinceId = provinceId ?? this.provinceId
      ..provinceName = provinceName ?? this.provinceName
      ..cityId = cityId ?? this.cityId
      ..cityName = cityName ?? this.cityName
      ..address = address ?? this.address
      ..marriageStatus = marriageStatus ?? this.marriageStatus
      ..marriageStatusName = marriageStatusName ?? this.marriageStatusName
      ..nationId = nationId ?? this.nationId
      ..nationName = nationName ?? this.nationName
      ..isIdLong = isIdLong ?? this.isIdLong
      ..idStartDate = idStartDate ?? this.idStartDate
      ..idEndDate = idEndDate ?? this.idEndDate
      ..picList = picList ?? this.picList;
  }
}

StaffDetailIdentityPicList $StaffDetailIdentityPicListFromJson(Map<String, dynamic> json) {
  final StaffDetailIdentityPicList staffDetailIdentityPicList = StaffDetailIdentityPicList();
  final String? sourceUrl = jsonConvert.convert<String>(json['source_url']);
  if (sourceUrl != null) {
    staffDetailIdentityPicList.sourceUrl = sourceUrl;
  }
  final String? isFront = jsonConvert.convert<String>(json['is_front']);
  if (isFront != null) {
    staffDetailIdentityPicList.isFront = isFront;
  }
  return staffDetailIdentityPicList;
}

Map<String, dynamic> $StaffDetailIdentityPicListToJson(StaffDetailIdentityPicList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['source_url'] = entity.sourceUrl;
  data['is_front'] = entity.isFront;
  return data;
}

extension StaffDetailIdentityPicListExtension on StaffDetailIdentityPicList {
  StaffDetailIdentityPicList copyWith({
    String? sourceUrl,
    String? isFront,
  }) {
    return StaffDetailIdentityPicList()
      ..sourceUrl = sourceUrl ?? this.sourceUrl
      ..isFront = isFront ?? this.isFront;
  }
}

StaffDetailBank $StaffDetailBankFromJson(Map<String, dynamic> json) {
  final StaffDetailBank staffDetailBank = StaffDetailBank();
  final String? bankNo = jsonConvert.convert<String>(json['bank_no']);
  if (bankNo != null) {
    staffDetailBank.bankNo = bankNo;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bank_code']);
  if (bankCode != null) {
    staffDetailBank.bankCode = bankCode;
  }
  final String? bank = jsonConvert.convert<String>(json['bank']);
  if (bank != null) {
    staffDetailBank.bank = bank;
  }
  final String? bankName = jsonConvert.convert<String>(json['bank_name']);
  if (bankName != null) {
    staffDetailBank.bankName = bankName;
  }
  final List<StaffDetailIdentityPicList>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (picList != null) {
    staffDetailBank.picList = picList;
  }
  return staffDetailBank;
}

Map<String, dynamic> $StaffDetailBankToJson(StaffDetailBank entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bank_no'] = entity.bankNo;
  data['bank_code'] = entity.bankCode;
  data['bank'] = entity.bank;
  data['bank_name'] = entity.bankName;
  data['pic_list'] = entity.picList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailBankExtension on StaffDetailBank {
  StaffDetailBank copyWith({
    String? bankNo,
    String? bankCode,
    String? bank,
    String? bankName,
    List<StaffDetailIdentityPicList>? picList,
  }) {
    return StaffDetailBank()
      ..bankNo = bankNo ?? this.bankNo
      ..bankCode = bankCode ?? this.bankCode
      ..bank = bank ?? this.bank
      ..bankName = bankName ?? this.bankName
      ..picList = picList ?? this.picList;
  }
}

StaffDetailContact $StaffDetailContactFromJson(Map<String, dynamic> json) {
  final StaffDetailContact staffDetailContact = StaffDetailContact();
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    staffDetailContact.mobile = mobile;
  }
  final String? exigencyUserName = jsonConvert.convert<String>(json['exigency_user_name']);
  if (exigencyUserName != null) {
    staffDetailContact.exigencyUserName = exigencyUserName;
  }
  final String? exigencyUserMobile = jsonConvert.convert<String>(json['exigency_user_mobile']);
  if (exigencyUserMobile != null) {
    staffDetailContact.exigencyUserMobile = exigencyUserMobile;
  }
  final String? exigencyUserRelation = jsonConvert.convert<String>(json['exigency_user_relation']);
  if (exigencyUserRelation != null) {
    staffDetailContact.exigencyUserRelation = exigencyUserRelation;
  }
  final String? exigencyUserRelationName = jsonConvert.convert<String>(json['exigency_user_relation_name']);
  if (exigencyUserRelationName != null) {
    staffDetailContact.exigencyUserRelationName = exigencyUserRelationName;
  }
  return staffDetailContact;
}

Map<String, dynamic> $StaffDetailContactToJson(StaffDetailContact entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['mobile'] = entity.mobile;
  data['exigency_user_name'] = entity.exigencyUserName;
  data['exigency_user_mobile'] = entity.exigencyUserMobile;
  data['exigency_user_relation'] = entity.exigencyUserRelation;
  data['exigency_user_relation_name'] = entity.exigencyUserRelationName;
  return data;
}

extension StaffDetailContactExtension on StaffDetailContact {
  StaffDetailContact copyWith({
    String? mobile,
    String? exigencyUserName,
    String? exigencyUserMobile,
    String? exigencyUserRelation,
    String? exigencyUserRelationName,
  }) {
    return StaffDetailContact()
      ..mobile = mobile ?? this.mobile
      ..exigencyUserName = exigencyUserName ?? this.exigencyUserName
      ..exigencyUserMobile = exigencyUserMobile ?? this.exigencyUserMobile
      ..exigencyUserRelation = exigencyUserRelation ?? this.exigencyUserRelation
      ..exigencyUserRelationName = exigencyUserRelationName ?? this.exigencyUserRelationName;
  }
}

StaffDetailWorkInfo $StaffDetailWorkInfoFromJson(Map<String, dynamic> json) {
  final StaffDetailWorkInfo staffDetailWorkInfo = StaffDetailWorkInfo();
  final String? workStartTime = jsonConvert.convert<String>(json['work_start_time']);
  if (workStartTime != null) {
    staffDetailWorkInfo.workStartTime = workStartTime;
  }
  final String? workEndTime = jsonConvert.convert<String>(json['work_end_time']);
  if (workEndTime != null) {
    staffDetailWorkInfo.workEndTime = workEndTime;
  }
  final String? roleId = jsonConvert.convert<String>(json['role_id']);
  if (roleId != null) {
    staffDetailWorkInfo.roleId = roleId;
  }
  final String? contractType = jsonConvert.convert<String>(json['contract_type']);
  if (contractType != null) {
    staffDetailWorkInfo.contractType = contractType;
  }
  final String? contractTypeName = jsonConvert.convert<String>(json['contract_type_name']);
  if (contractTypeName != null) {
    staffDetailWorkInfo.contractTypeName = contractTypeName;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    staffDetailWorkInfo.projectUuid = projectUuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    staffDetailWorkInfo.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    staffDetailWorkInfo.projectShortName = projectShortName;
  }
  final String? roleName = jsonConvert.convert<String>(json['role_name']);
  if (roleName != null) {
    staffDetailWorkInfo.roleName = roleName;
  }
  final String? groupUuid = jsonConvert.convert<String>(json['group_uuid']);
  if (groupUuid != null) {
    staffDetailWorkInfo.groupUuid = groupUuid;
  }
  final String? groupName = jsonConvert.convert<String>(json['group_name']);
  if (groupName != null) {
    staffDetailWorkInfo.groupName = groupName;
  }
  final String? jobUuid = jsonConvert.convert<String>(json['job_uuid']);
  if (jobUuid != null) {
    staffDetailWorkInfo.jobUuid = jobUuid;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    staffDetailWorkInfo.jobName = jobName;
  }
  final String? isHeadOffice = jsonConvert.convert<String>(json['is_head_office']);
  if (isHeadOffice != null) {
    staffDetailWorkInfo.isHeadOffice = isHeadOffice;
  }
  final String? departmentUuid = jsonConvert.convert<String>(json['department_uuid']);
  if (departmentUuid != null) {
    staffDetailWorkInfo.departmentUuid = departmentUuid;
  }
  final String? departmentName = jsonConvert.convert<String>(json['department_name']);
  if (departmentName != null) {
    staffDetailWorkInfo.departmentName = departmentName;
  }
  final String? contractCompanyUuid = jsonConvert.convert<String>(json['contract_company_uuid']);
  if (contractCompanyUuid != null) {
    staffDetailWorkInfo.contractCompanyUuid = contractCompanyUuid;
  }
  final String? contractCompanyName = jsonConvert.convert<String>(json['contract_company_name']);
  if (contractCompanyName != null) {
    staffDetailWorkInfo.contractCompanyName = contractCompanyName;
  }
  final List<StaffDetailIdentityPicList>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (picList != null) {
    staffDetailWorkInfo.picList = picList;
  }
  return staffDetailWorkInfo;
}

Map<String, dynamic> $StaffDetailWorkInfoToJson(StaffDetailWorkInfo entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['work_start_time'] = entity.workStartTime;
  data['work_end_time'] = entity.workEndTime;
  data['role_id'] = entity.roleId;
  data['contract_type'] = entity.contractType;
  data['contract_type_name'] = entity.contractTypeName;
  data['project_uuid'] = entity.projectUuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['role_name'] = entity.roleName;
  data['group_uuid'] = entity.groupUuid;
  data['group_name'] = entity.groupName;
  data['job_uuid'] = entity.jobUuid;
  data['job_name'] = entity.jobName;
  data['is_head_office'] = entity.isHeadOffice;
  data['department_uuid'] = entity.departmentUuid;
  data['department_name'] = entity.departmentName;
  data['contract_company_uuid'] = entity.contractCompanyUuid;
  data['contract_company_name'] = entity.contractCompanyName;
  data['pic_list'] = entity.picList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailWorkInfoExtension on StaffDetailWorkInfo {
  StaffDetailWorkInfo copyWith({
    String? workStartTime,
    String? workEndTime,
    String? roleId,
    String? contractType,
    String? contractTypeName,
    String? projectUuid,
    String? projectName,
    String? projectShortName,
    String? roleName,
    String? groupUuid,
    String? groupName,
    String? jobUuid,
    String? jobName,
    String? isHeadOffice,
    String? departmentUuid,
    String? departmentName,
    String? contractCompanyUuid,
    String? contractCompanyName,
    List<StaffDetailIdentityPicList>? picList,
  }) {
    return StaffDetailWorkInfo()
      ..workStartTime = workStartTime ?? this.workStartTime
      ..workEndTime = workEndTime ?? this.workEndTime
      ..roleId = roleId ?? this.roleId
      ..contractType = contractType ?? this.contractType
      ..contractTypeName = contractTypeName ?? this.contractTypeName
      ..projectUuid = projectUuid ?? this.projectUuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..roleName = roleName ?? this.roleName
      ..groupUuid = groupUuid ?? this.groupUuid
      ..groupName = groupName ?? this.groupName
      ..jobUuid = jobUuid ?? this.jobUuid
      ..jobName = jobName ?? this.jobName
      ..isHeadOffice = isHeadOffice ?? this.isHeadOffice
      ..departmentUuid = departmentUuid ?? this.departmentUuid
      ..departmentName = departmentName ?? this.departmentName
      ..contractCompanyUuid = contractCompanyUuid ?? this.contractCompanyUuid
      ..contractCompanyName = contractCompanyName ?? this.contractCompanyName
      ..picList = picList ?? this.picList;
  }
}

StaffDetailHealthyPic $StaffDetailHealthyPicFromJson(Map<String, dynamic> json) {
  final StaffDetailHealthyPic staffDetailHealthyPic = StaffDetailHealthyPic();
  final List<StaffDetailHealthyPicPicList>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailHealthyPicPicList>(e) as StaffDetailHealthyPicPicList).toList();
  if (picList != null) {
    staffDetailHealthyPic.picList = picList;
  }
  return staffDetailHealthyPic;
}

Map<String, dynamic> $StaffDetailHealthyPicToJson(StaffDetailHealthyPic entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['pic_list'] = entity.picList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailHealthyPicExtension on StaffDetailHealthyPic {
  StaffDetailHealthyPic copyWith({
    List<StaffDetailHealthyPicPicList>? picList,
  }) {
    return StaffDetailHealthyPic()
      ..picList = picList ?? this.picList;
  }
}

StaffDetailHealthyPicPicList $StaffDetailHealthyPicPicListFromJson(Map<String, dynamic> json) {
  final StaffDetailHealthyPicPicList staffDetailHealthyPicPicList = StaffDetailHealthyPicPicList();
  final String? sourceUrl = jsonConvert.convert<String>(json['source_url']);
  if (sourceUrl != null) {
    staffDetailHealthyPicPicList.sourceUrl = sourceUrl;
  }
  final String? isFront = jsonConvert.convert<String>(json['is_front']);
  if (isFront != null) {
    staffDetailHealthyPicPicList.isFront = isFront;
  }
  return staffDetailHealthyPicPicList;
}

Map<String, dynamic> $StaffDetailHealthyPicPicListToJson(StaffDetailHealthyPicPicList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['source_url'] = entity.sourceUrl;
  data['is_front'] = entity.isFront;
  return data;
}

extension StaffDetailHealthyPicPicListExtension on StaffDetailHealthyPicPicList {
  StaffDetailHealthyPicPicList copyWith({
    String? sourceUrl,
    String? isFront,
  }) {
    return StaffDetailHealthyPicPicList()
      ..sourceUrl = sourceUrl ?? this.sourceUrl
      ..isFront = isFront ?? this.isFront;
  }
}

StaffDetailCreditList $StaffDetailCreditListFromJson(Map<String, dynamic> json) {
  final StaffDetailCreditList staffDetailCreditList = StaffDetailCreditList();
  final String? item = jsonConvert.convert<String>(json['item']);
  if (item != null) {
    staffDetailCreditList.item = item;
  }
  final int? creditType = jsonConvert.convert<int>(json['credit_type']);
  if (creditType != null) {
    staffDetailCreditList.creditType = creditType;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    staffDetailCreditList.status = status;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    staffDetailCreditList.title = title;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    staffDetailCreditList.message = message;
  }
  final String? explain = jsonConvert.convert<String>(json['explain']);
  if (explain != null) {
    staffDetailCreditList.explain = explain;
  }
  return staffDetailCreditList;
}

Map<String, dynamic> $StaffDetailCreditListToJson(StaffDetailCreditList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['item'] = entity.item;
  data['credit_type'] = entity.creditType;
  data['status'] = entity.status;
  data['title'] = entity.title;
  data['message'] = entity.message;
  data['explain'] = entity.explain;
  return data;
}

extension StaffDetailCreditListExtension on StaffDetailCreditList {
  StaffDetailCreditList copyWith({
    String? item,
    int? creditType,
    int? status,
    String? title,
    String? message,
    String? explain,
  }) {
    return StaffDetailCreditList()
      ..item = item ?? this.item
      ..creditType = creditType ?? this.creditType
      ..status = status ?? this.status
      ..title = title ?? this.title
      ..message = message ?? this.message
      ..explain = explain ?? this.explain;
  }
}

StaffDetailContract $StaffDetailContractFromJson(Map<String, dynamic> json) {
  final StaffDetailContract staffDetailContract = StaffDetailContract();
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    staffDetailContract.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    staffDetailContract.endTime = endTime;
  }
  final String? contractType = jsonConvert.convert<String>(json['contract_type']);
  if (contractType != null) {
    staffDetailContract.contractType = contractType;
  }
  final String? contractSignType = jsonConvert.convert<String>(json['contract_sign_type']);
  if (contractSignType != null) {
    staffDetailContract.contractSignType = contractSignType;
  }
  final List<StaffDetailIdentityPicList>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffDetailIdentityPicList>(e) as StaffDetailIdentityPicList).toList();
  if (picList != null) {
    staffDetailContract.picList = picList;
  }
  return staffDetailContract;
}

Map<String, dynamic> $StaffDetailContractToJson(StaffDetailContract entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['contract_type'] = entity.contractType;
  data['contract_sign_type'] = entity.contractSignType;
  data['pic_list'] = entity.picList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailContractExtension on StaffDetailContract {
  StaffDetailContract copyWith({
    String? startTime,
    String? endTime,
    String? contractType,
    String? contractSignType,
    List<StaffDetailIdentityPicList>? picList,
  }) {
    return StaffDetailContract()
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..contractType = contractType ?? this.contractType
      ..contractSignType = contractSignType ?? this.contractSignType
      ..picList = picList ?? this.picList;
  }
}

StaffDetailLeftContract $StaffDetailLeftContractFromJson(Map<String, dynamic> json) {
  final StaffDetailLeftContract staffDetailLeftContract = StaffDetailLeftContract();
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    staffDetailLeftContract.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    staffDetailLeftContract.endTime = endTime;
  }
  final String? contractType = jsonConvert.convert<String>(json['contract_type']);
  if (contractType != null) {
    staffDetailLeftContract.contractType = contractType;
  }
  final String? contractTypeName = jsonConvert.convert<String>(json['contract_type_name']);
  if (contractTypeName != null) {
    staffDetailLeftContract.contractTypeName = contractTypeName;
  }
  final String? contractSignType = jsonConvert.convert<String>(json['contract_sign_type']);
  if (contractSignType != null) {
    staffDetailLeftContract.contractSignType = contractSignType;
  }
  final String? contractSignTypeName = jsonConvert.convert<String>(json['contract_sign_type_name']);
  if (contractSignTypeName != null) {
    staffDetailLeftContract.contractSignTypeName = contractSignTypeName;
  }
  final String? leftReasonName = jsonConvert.convert<String>(json['left_reason_name']);
  if (leftReasonName != null) {
    staffDetailLeftContract.leftReasonName = leftReasonName;
  }
  final String? leftReasonRemark = jsonConvert.convert<String>(json['left_reason_remark']);
  if (leftReasonRemark != null) {
    staffDetailLeftContract.leftReasonRemark = leftReasonRemark;
  }
  final List<String>? picList = (json['pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (picList != null) {
    staffDetailLeftContract.picList = picList;
  }
  final String? signStatusName = jsonConvert.convert<String>(json['sign_status_name']);
  if (signStatusName != null) {
    staffDetailLeftContract.signStatusName = signStatusName;
  }
  final String? eleContractUrl = jsonConvert.convert<String>(json['ele_contract_url']);
  if (eleContractUrl != null) {
    staffDetailLeftContract.eleContractUrl = eleContractUrl;
  }
  return staffDetailLeftContract;
}

Map<String, dynamic> $StaffDetailLeftContractToJson(StaffDetailLeftContract entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['contract_type'] = entity.contractType;
  data['contract_type_name'] = entity.contractTypeName;
  data['contract_sign_type'] = entity.contractSignType;
  data['contract_sign_type_name'] = entity.contractSignTypeName;
  data['left_reason_name'] = entity.leftReasonName;
  data['left_reason_remark'] = entity.leftReasonRemark;
  data['pic_list'] = entity.picList;
  data['sign_status_name'] = entity.signStatusName;
  data['ele_contract_url'] = entity.eleContractUrl;
  return data;
}

extension StaffDetailLeftContractExtension on StaffDetailLeftContract {
  StaffDetailLeftContract copyWith({
    String? startTime,
    String? endTime,
    String? contractType,
    String? contractTypeName,
    String? contractSignType,
    String? contractSignTypeName,
    String? leftReasonName,
    String? leftReasonRemark,
    List<String>? picList,
    String? signStatusName,
    String? eleContractUrl,
  }) {
    return StaffDetailLeftContract()
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..contractType = contractType ?? this.contractType
      ..contractTypeName = contractTypeName ?? this.contractTypeName
      ..contractSignType = contractSignType ?? this.contractSignType
      ..contractSignTypeName = contractSignTypeName ?? this.contractSignTypeName
      ..leftReasonName = leftReasonName ?? this.leftReasonName
      ..leftReasonRemark = leftReasonRemark ?? this.leftReasonRemark
      ..picList = picList ?? this.picList
      ..signStatusName = signStatusName ?? this.signStatusName
      ..eleContractUrl = eleContractUrl ?? this.eleContractUrl;
  }
}

StaffDetailSalaryInsurance $StaffDetailSalaryInsuranceFromJson(Map<String, dynamic> json) {
  final StaffDetailSalaryInsurance staffDetailSalaryInsurance = StaffDetailSalaryInsurance();
  final String? insuranceType = jsonConvert.convert<String>(json['insurance_type']);
  if (insuranceType != null) {
    staffDetailSalaryInsurance.insuranceType = insuranceType;
  }
  final String? insuranceProductName = jsonConvert.convert<String>(json['insurance_product_name']);
  if (insuranceProductName != null) {
    staffDetailSalaryInsurance.insuranceProductName = insuranceProductName;
  }
  final String? salary = jsonConvert.convert<String>(json['salary']);
  if (salary != null) {
    staffDetailSalaryInsurance.salary = salary;
  }
  final String? salaryLevel = jsonConvert.convert<String>(json['salary_level']);
  if (salaryLevel != null) {
    staffDetailSalaryInsurance.salaryLevel = salaryLevel;
  }
  final List<SalaryConfigItemListSalaryConfigItemList>? itemList = (json['salary_item_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<SalaryConfigItemListSalaryConfigItemList>(e) as SalaryConfigItemListSalaryConfigItemList).toList();
  if (itemList != null) {
    staffDetailSalaryInsurance.itemList = itemList;
  }
  return staffDetailSalaryInsurance;
}

Map<String, dynamic> $StaffDetailSalaryInsuranceToJson(StaffDetailSalaryInsurance entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['insurance_type'] = entity.insuranceType;
  data['insurance_product_name'] = entity.insuranceProductName;
  data['salary'] = entity.salary;
  data['salary_level'] = entity.salaryLevel;
  data['salary_item_list'] = entity.itemList?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffDetailSalaryInsuranceExtension on StaffDetailSalaryInsurance {
  StaffDetailSalaryInsurance copyWith({
    String? insuranceType,
    String? insuranceProductName,
    String? salary,
    String? salaryLevel,
    List<SalaryConfigItemListSalaryConfigItemList>? itemList,
  }) {
    return StaffDetailSalaryInsurance()
      ..insuranceType = insuranceType ?? this.insuranceType
      ..insuranceProductName = insuranceProductName ?? this.insuranceProductName
      ..salary = salary ?? this.salary
      ..salaryLevel = salaryLevel ?? this.salaryLevel
      ..itemList = itemList ?? this.itemList;
  }
}