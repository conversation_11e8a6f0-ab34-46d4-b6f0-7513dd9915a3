import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_role_entity.dart';

ApproveRoleEntity $ApproveRoleEntityFromJson(Map<String, dynamic> json) {
  final ApproveRoleEntity approveRoleEntity = ApproveRoleEntity();
  final List<ApproveRoleList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveRoleList>(e) as ApproveRoleList).toList();
  if (list != null) {
    approveRoleEntity.list = list;
  }
  return approveRoleEntity;
}

Map<String, dynamic> $ApproveRoleEntityToJson(ApproveRoleEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveRoleEntityExtension on ApproveRoleEntity {
  ApproveRoleEntity copyWith({
    List<ApproveRoleList>? list,
  }) {
    return ApproveRoleEntity()
      ..list = list ?? this.list;
  }
}

ApproveRoleList $ApproveRoleListFromJson(Map<String, dynamic> json) {
  final ApproveRoleList approveRoleList = ApproveRoleList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    approveRoleList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    approveRoleList.name = name;
  }
  final String? roleDesc = jsonConvert.convert<String>(json['role_desc']);
  if (roleDesc != null) {
    approveRoleList.roleDesc = roleDesc;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['is_selected']);
  if (isSelected != null) {
    approveRoleList.isSelected = isSelected;
  }
  return approveRoleList;
}

Map<String, dynamic> $ApproveRoleListToJson(ApproveRoleList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['role_desc'] = entity.roleDesc;
  data['is_selected'] = entity.isSelected;
  return data;
}

extension ApproveRoleListExtension on ApproveRoleList {
  ApproveRoleList copyWith({
    String? id,
    String? name,
    String? roleDesc,
    bool? isSelected,
  }) {
    return ApproveRoleList()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..roleDesc = roleDesc ?? this.roleDesc
      ..isSelected = isSelected ?? this.isSelected;
  }
}