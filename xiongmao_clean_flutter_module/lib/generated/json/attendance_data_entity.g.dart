import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/attendance_data_entity.dart';

AttendanceDataEntity $AttendanceDataEntityFromJson(Map<String, dynamic> json) {
  final AttendanceDataEntity attendanceDataEntity = AttendanceDataEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    attendanceDataEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    attendanceDataEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    attendanceDataEntity.total = total;
  }
  final int? allTotal = jsonConvert.convert<int>(json['all_total']);
  if (allTotal != null) {
    attendanceDataEntity.allTotal = allTotal;
  }
  final String? cqTotal = jsonConvert.convert<String>(json['cq_total']);
  if (cqTotal != null) {
    attendanceDataEntity.cqTotal = cqTotal;
  }
  final String? actualCqTotal = jsonConvert.convert<String>(json['actual_cq_total']);
  if (actualCqTotal != null) {
    attendanceDataEntity.actualCqTotal = actualCqTotal;
  }
  final String? cdTotal = jsonConvert.convert<String>(json['cd_total']);
  if (cdTotal != null) {
    attendanceDataEntity.cdTotal = cdTotal;
  }
  final String? ztTotal = jsonConvert.convert<String>(json['zt_total']);
  if (ztTotal != null) {
    attendanceDataEntity.ztTotal = ztTotal;
  }
  final String? qkTotal = jsonConvert.convert<String>(json['qk_total']);
  if (qkTotal != null) {
    attendanceDataEntity.qkTotal = qkTotal;
  }
  final String? kgTotal = jsonConvert.convert<String>(json['kg_total']);
  if (kgTotal != null) {
    attendanceDataEntity.kgTotal = kgTotal;
  }
  final String? xjTotal = jsonConvert.convert<String>(json['xj_total']);
  if (xjTotal != null) {
    attendanceDataEntity.xjTotal = xjTotal;
  }
  final String? jbTotal = jsonConvert.convert<String>(json['jb_total']);
  if (jbTotal != null) {
    attendanceDataEntity.jbTotal = jbTotal;
  }
  final List<AttendanceDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AttendanceDataList>(e) as AttendanceDataList).toList();
  if (list != null) {
    attendanceDataEntity.list = list;
  }
  return attendanceDataEntity;
}

Map<String, dynamic> $AttendanceDataEntityToJson(AttendanceDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['all_total'] = entity.allTotal;
  data['cq_total'] = entity.cqTotal;
  data['actual_cq_total'] = entity.actualCqTotal;
  data['cd_total'] = entity.cdTotal;
  data['zt_total'] = entity.ztTotal;
  data['qk_total'] = entity.qkTotal;
  data['kg_total'] = entity.kgTotal;
  data['xj_total'] = entity.xjTotal;
  data['jb_total'] = entity.jbTotal;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension AttendanceDataEntityExtension on AttendanceDataEntity {
  AttendanceDataEntity copyWith({
    int? page,
    int? size,
    int? total,
    int? allTotal,
    String? cqTotal,
    String? actualCqTotal,
    String? cdTotal,
    String? ztTotal,
    String? qkTotal,
    String? kgTotal,
    String? xjTotal,
    String? jbTotal,
    List<AttendanceDataList>? list,
  }) {
    return AttendanceDataEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..allTotal = allTotal ?? this.allTotal
      ..cqTotal = cqTotal ?? this.cqTotal
      ..actualCqTotal = actualCqTotal ?? this.actualCqTotal
      ..cdTotal = cdTotal ?? this.cdTotal
      ..ztTotal = ztTotal ?? this.ztTotal
      ..qkTotal = qkTotal ?? this.qkTotal
      ..kgTotal = kgTotal ?? this.kgTotal
      ..xjTotal = xjTotal ?? this.xjTotal
      ..jbTotal = jbTotal ?? this.jbTotal
      ..list = list ?? this.list;
  }
}

AttendanceDataList $AttendanceDataListFromJson(Map<String, dynamic> json) {
  final AttendanceDataList attendanceDataList = AttendanceDataList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceDataList.uuid = uuid;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    attendanceDataList.jobName = jobName;
  }
  final String? workDays = jsonConvert.convert<String>(json['work_days']);
  if (workDays != null) {
    attendanceDataList.workDays = workDays;
  }
  final String? actualWorkDays = jsonConvert.convert<String>(json['actual_work_days']);
  if (actualWorkDays != null) {
    attendanceDataList.actualWorkDays = actualWorkDays;
  }
  final String? xjDays = jsonConvert.convert<String>(json['xj_days']);
  if (xjDays != null) {
    attendanceDataList.xjDays = xjDays;
  }
  final String? jbHours = jsonConvert.convert<String>(json['jb_hours']);
  if (jbHours != null) {
    attendanceDataList.jbHours = jbHours;
  }
  final String? cdTimeLong = jsonConvert.convert<String>(json['cd_time_long']);
  if (cdTimeLong != null) {
    attendanceDataList.cdTimeLong = cdTimeLong;
  }
  final String? ztTimeLong = jsonConvert.convert<String>(json['zt_time_long']);
  if (ztTimeLong != null) {
    attendanceDataList.ztTimeLong = ztTimeLong;
  }
  final String? kgDays = jsonConvert.convert<String>(json['kg_days']);
  if (kgDays != null) {
    attendanceDataList.kgDays = kgDays;
  }
  final String? qkTimes = jsonConvert.convert<String>(json['qk_times']);
  if (qkTimes != null) {
    attendanceDataList.qkTimes = qkTimes;
  }
  final String? entryDate = jsonConvert.convert<String>(json['entry_date']);
  if (entryDate != null) {
    attendanceDataList.entryDate = entryDate;
  }
  final String? leaveDate = jsonConvert.convert<String>(json['leave_date']);
  if (leaveDate != null) {
    attendanceDataList.leaveDate = leaveDate;
  }
  final String? userUuid = jsonConvert.convert<String>(json['user_uuid']);
  if (userUuid != null) {
    attendanceDataList.userUuid = userUuid;
  }
  final String? scheduleUuid = jsonConvert.convert<String>(json['schedule_uuid']);
  if (scheduleUuid != null) {
    attendanceDataList.scheduleUuid = scheduleUuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    attendanceDataList.userName = userName;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    attendanceDataList.className = className;
  }
  final String? classUuid = jsonConvert.convert<String>(json['class_uuid']);
  if (classUuid != null) {
    attendanceDataList.classUuid = classUuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    attendanceDataList.projectName = projectName;
  }
  final String? kqDayUuid = jsonConvert.convert<String>(json['kq_day_uuid']);
  if (kqDayUuid != null) {
    attendanceDataList.kqDayUuid = kqDayUuid;
  }
  final String? kqDate = jsonConvert.convert<String>(json['kq_date']);
  if (kqDate != null) {
    attendanceDataList.kqDate = kqDate;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    attendanceDataList.avatar = avatar;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    attendanceDataList.projectUuid = projectUuid;
  }
  final String? isWorkDay = jsonConvert.convert<String>(json['is_work_day']);
  if (isWorkDay != null) {
    attendanceDataList.isWorkDay = isWorkDay;
  }
  return attendanceDataList;
}

Map<String, dynamic> $AttendanceDataListToJson(AttendanceDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['job_name'] = entity.jobName;
  data['work_days'] = entity.workDays;
  data['actual_work_days'] = entity.actualWorkDays;
  data['xj_days'] = entity.xjDays;
  data['jb_hours'] = entity.jbHours;
  data['cd_time_long'] = entity.cdTimeLong;
  data['zt_time_long'] = entity.ztTimeLong;
  data['kg_days'] = entity.kgDays;
  data['qk_times'] = entity.qkTimes;
  data['entry_date'] = entity.entryDate;
  data['leave_date'] = entity.leaveDate;
  data['user_uuid'] = entity.userUuid;
  data['schedule_uuid'] = entity.scheduleUuid;
  data['user_name'] = entity.userName;
  data['class_name'] = entity.className;
  data['class_uuid'] = entity.classUuid;
  data['project_name'] = entity.projectName;
  data['kq_day_uuid'] = entity.kqDayUuid;
  data['kq_date'] = entity.kqDate;
  data['avatar'] = entity.avatar;
  data['project_uuid'] = entity.projectUuid;
  data['is_work_day'] = entity.isWorkDay;
  return data;
}

extension AttendanceDataListExtension on AttendanceDataList {
  AttendanceDataList copyWith({
    String? uuid,
    String? jobName,
    String? workDays,
    String? actualWorkDays,
    String? xjDays,
    String? jbHours,
    String? cdTimeLong,
    String? ztTimeLong,
    String? kgDays,
    String? qkTimes,
    String? entryDate,
    String? leaveDate,
    String? userUuid,
    String? scheduleUuid,
    String? userName,
    String? className,
    String? classUuid,
    String? projectName,
    String? kqDayUuid,
    String? kqDate,
    String? avatar,
    String? projectUuid,
    String? isWorkDay,
  }) {
    return AttendanceDataList()
      ..uuid = uuid ?? this.uuid
      ..jobName = jobName ?? this.jobName
      ..workDays = workDays ?? this.workDays
      ..actualWorkDays = actualWorkDays ?? this.actualWorkDays
      ..xjDays = xjDays ?? this.xjDays
      ..jbHours = jbHours ?? this.jbHours
      ..cdTimeLong = cdTimeLong ?? this.cdTimeLong
      ..ztTimeLong = ztTimeLong ?? this.ztTimeLong
      ..kgDays = kgDays ?? this.kgDays
      ..qkTimes = qkTimes ?? this.qkTimes
      ..entryDate = entryDate ?? this.entryDate
      ..leaveDate = leaveDate ?? this.leaveDate
      ..userUuid = userUuid ?? this.userUuid
      ..scheduleUuid = scheduleUuid ?? this.scheduleUuid
      ..userName = userName ?? this.userName
      ..className = className ?? this.className
      ..classUuid = classUuid ?? this.classUuid
      ..projectName = projectName ?? this.projectName
      ..kqDayUuid = kqDayUuid ?? this.kqDayUuid
      ..kqDate = kqDate ?? this.kqDate
      ..avatar = avatar ?? this.avatar
      ..projectUuid = projectUuid ?? this.projectUuid
      ..isWorkDay = isWorkDay ?? this.isWorkDay;
  }
}