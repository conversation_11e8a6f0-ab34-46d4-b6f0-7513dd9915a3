import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/company_camera_config_entity.dart';

CompanyCameraConfigEntity $CompanyCameraConfigEntityFromJson(Map<String, dynamic> json) {
  final CompanyCameraConfigEntity companyCameraConfigEntity = CompanyCameraConfigEntity();
  final List<CompanyCameraConfigList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CompanyCameraConfigList>(e) as CompanyCameraConfigList).toList();
  if (list != null) {
    companyCameraConfigEntity.list = list;
  }
  return companyCameraConfigEntity;
}

Map<String, dynamic> $CompanyCameraConfigEntityToJson(CompanyCameraConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CompanyCameraConfigEntityExtension on CompanyCameraConfigEntity {
  CompanyCameraConfigEntity copyWith({
    List<CompanyCameraConfigList>? list,
  }) {
    return CompanyCameraConfigEntity()
      ..list = list ?? this.list;
  }
}

CompanyCameraConfigList $CompanyCameraConfigListFromJson(Map<String, dynamic> json) {
  final CompanyCameraConfigList companyCameraConfigList = CompanyCameraConfigList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    companyCameraConfigList.uuid = uuid;
  }
  final String? configurationName = jsonConvert.convert<String>(json['configuration_name']);
  if (configurationName != null) {
    companyCameraConfigList.configurationName = configurationName;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    companyCameraConfigList.introduction = introduction;
  }
  final String? isDefault = jsonConvert.convert<String>(json['is_default']);
  if (isDefault != null) {
    companyCameraConfigList.isDefault = isDefault;
  }
  return companyCameraConfigList;
}

Map<String, dynamic> $CompanyCameraConfigListToJson(CompanyCameraConfigList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['configuration_name'] = entity.configurationName;
  data['introduction'] = entity.introduction;
  data['is_default'] = entity.isDefault;
  return data;
}

extension CompanyCameraConfigListExtension on CompanyCameraConfigList {
  CompanyCameraConfigList copyWith({
    String? uuid,
    String? configurationName,
    String? introduction,
    String? isDefault,
  }) {
    return CompanyCameraConfigList()
      ..uuid = uuid ?? this.uuid
      ..configurationName = configurationName ?? this.configurationName
      ..introduction = introduction ?? this.introduction
      ..isDefault = isDefault ?? this.isDefault;
  }
}