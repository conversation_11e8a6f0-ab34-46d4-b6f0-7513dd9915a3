import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/schedule_data_entity.dart';

ScheduleDataEntity $ScheduleDataEntityFromJson(Map<String, dynamic> json) {
  final ScheduleDataEntity scheduleDataEntity = ScheduleDataEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    scheduleDataEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    scheduleDataEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    scheduleDataEntity.total = total;
  }
  final List<ScheduleDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ScheduleDataList>(e) as ScheduleDataList).toList();
  if (list != null) {
    scheduleDataEntity.list = list;
  }
  return scheduleDataEntity;
}

Map<String, dynamic> $ScheduleDataEntityToJson(ScheduleDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ScheduleDataEntityExtension on ScheduleDataEntity {
  ScheduleDataEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ScheduleDataList>? list,
  }) {
    return ScheduleDataEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ScheduleDataList $ScheduleDataListFromJson(Map<String, dynamic> json) {
  final ScheduleDataList scheduleDataList = ScheduleDataList();
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    scheduleDataList.userName = userName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    scheduleDataList.jobName = jobName;
  }
  final List<ScheduleDataListScheduleList>? scheduleList = (json['schedule_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ScheduleDataListScheduleList>(e) as ScheduleDataListScheduleList).toList();
  if (scheduleList != null) {
    scheduleDataList.scheduleList = scheduleList;
  }
  return scheduleDataList;
}

Map<String, dynamic> $ScheduleDataListToJson(ScheduleDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_name'] = entity.userName;
  data['job_name'] = entity.jobName;
  data['schedule_list'] = entity.scheduleList?.map((v) => v.toJson()).toList();
  return data;
}

extension ScheduleDataListExtension on ScheduleDataList {
  ScheduleDataList copyWith({
    String? userName,
    String? jobName,
    List<ScheduleDataListScheduleList>? scheduleList,
  }) {
    return ScheduleDataList()
      ..userName = userName ?? this.userName
      ..jobName = jobName ?? this.jobName
      ..scheduleList = scheduleList ?? this.scheduleList;
  }
}

ScheduleDataListScheduleList $ScheduleDataListScheduleListFromJson(Map<String, dynamic> json) {
  final ScheduleDataListScheduleList scheduleDataListScheduleList = ScheduleDataListScheduleList();
  final String? scheduleDate = jsonConvert.convert<String>(json['schedule_date']);
  if (scheduleDate != null) {
    scheduleDataListScheduleList.scheduleDate = scheduleDate;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    scheduleDataListScheduleList.uuid = uuid;
  }
  final String? classUuid = jsonConvert.convert<String>(json['class_uuid']);
  if (classUuid != null) {
    scheduleDataListScheduleList.classUuid = classUuid;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    scheduleDataListScheduleList.projectUuid = projectUuid;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    scheduleDataListScheduleList.className = className;
  }
  final String? isWorkDay = jsonConvert.convert<String>(json['is_work_day']);
  if (isWorkDay != null) {
    scheduleDataListScheduleList.isWorkDay = isWorkDay;
  }
  return scheduleDataListScheduleList;
}

Map<String, dynamic> $ScheduleDataListScheduleListToJson(ScheduleDataListScheduleList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['schedule_date'] = entity.scheduleDate;
  data['uuid'] = entity.uuid;
  data['class_uuid'] = entity.classUuid;
  data['project_uuid'] = entity.projectUuid;
  data['class_name'] = entity.className;
  data['is_work_day'] = entity.isWorkDay;
  return data;
}

extension ScheduleDataListScheduleListExtension on ScheduleDataListScheduleList {
  ScheduleDataListScheduleList copyWith({
    String? scheduleDate,
    String? uuid,
    String? classUuid,
    String? projectUuid,
    String? className,
    String? isWorkDay,
  }) {
    return ScheduleDataListScheduleList()
      ..scheduleDate = scheduleDate ?? this.scheduleDate
      ..uuid = uuid ?? this.uuid
      ..classUuid = classUuid ?? this.classUuid
      ..projectUuid = projectUuid ?? this.projectUuid
      ..className = className ?? this.className
      ..isWorkDay = isWorkDay ?? this.isWorkDay;
  }
}