import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_account_entity.dart';

BalanceAccountEntity $BalanceAccountEntityFromJson(Map<String, dynamic> json) {
  final BalanceAccountEntity balanceAccountEntity = BalanceAccountEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    balanceAccountEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    balanceAccountEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    balanceAccountEntity.total = total;
  }
  final List<BalanceAccountList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BalanceAccountList>(e) as BalanceAccountList).toList();
  if (list != null) {
    balanceAccountEntity.list = list;
  }
  return balanceAccountEntity;
}

Map<String, dynamic> $BalanceAccountEntityToJson(BalanceAccountEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension BalanceAccountEntityExtension on BalanceAccountEntity {
  BalanceAccountEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<BalanceAccountList>? list,
  }) {
    return BalanceAccountEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

BalanceAccountList $BalanceAccountListFromJson(Map<String, dynamic> json) {
  final BalanceAccountList balanceAccountList = BalanceAccountList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    balanceAccountList.uuid = uuid;
  }
  final String? accountName = jsonConvert.convert<String>(json['account_name']);
  if (accountName != null) {
    balanceAccountList.accountName = accountName;
  }
  final String? accountUserName = jsonConvert.convert<String>(json['account_user_name']);
  if (accountUserName != null) {
    balanceAccountList.accountUserName = accountUserName;
  }
  final String? accountNo = jsonConvert.convert<String>(json['account_no']);
  if (accountNo != null) {
    balanceAccountList.accountNo = accountNo;
  }
  final String? accountCode = jsonConvert.convert<String>(json['account_code']);
  if (accountCode != null) {
    balanceAccountList.accountCode = accountCode;
  }
  final String? accountType = jsonConvert.convert<String>(json['account_type']);
  if (accountType != null) {
    balanceAccountList.accountType = accountType;
  }
  final String? accountIcon = jsonConvert.convert<String>(json['account_icon']);
  if (accountIcon != null) {
    balanceAccountList.accountIcon = accountIcon;
  }
  return balanceAccountList;
}

Map<String, dynamic> $BalanceAccountListToJson(BalanceAccountList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['account_name'] = entity.accountName;
  data['account_user_name'] = entity.accountUserName;
  data['account_no'] = entity.accountNo;
  data['account_code'] = entity.accountCode;
  data['account_type'] = entity.accountType;
  data['account_icon'] = entity.accountIcon;
  return data;
}

extension BalanceAccountListExtension on BalanceAccountList {
  BalanceAccountList copyWith({
    String? uuid,
    String? accountName,
    String? accountUserName,
    String? accountNo,
    String? accountCode,
    String? accountType,
    String? accountIcon,
  }) {
    return BalanceAccountList()
      ..uuid = uuid ?? this.uuid
      ..accountName = accountName ?? this.accountName
      ..accountUserName = accountUserName ?? this.accountUserName
      ..accountNo = accountNo ?? this.accountNo
      ..accountCode = accountCode ?? this.accountCode
      ..accountType = accountType ?? this.accountType
      ..accountIcon = accountIcon ?? this.accountIcon;
  }
}