import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_type_entity.dart';

BalanceTypeEntity $BalanceTypeEntityFromJson(Map<String, dynamic> json) {
  final BalanceTypeEntity balanceTypeEntity = BalanceTypeEntity();
  final List<BalanceTypeSrList>? srList = (json['sr_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BalanceTypeSrList>(e) as BalanceTypeSrList).toList();
  if (srList != null) {
    balanceTypeEntity.srList = srList;
  }
  final List<BalanceTypeZcList>? zcList = (json['zc_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<BalanceTypeZcList>(e) as BalanceTypeZcList).toList();
  if (zcList != null) {
    balanceTypeEntity.zcList = zcList;
  }
  return balanceTypeEntity;
}

Map<String, dynamic> $BalanceTypeEntityToJson(BalanceTypeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['sr_list'] = entity.srList?.map((v) => v.toJson()).toList();
  data['zc_list'] = entity.zcList?.map((v) => v.toJson()).toList();
  return data;
}

extension BalanceTypeEntityExtension on BalanceTypeEntity {
  BalanceTypeEntity copyWith({
    List<BalanceTypeSrList>? srList,
    List<BalanceTypeZcList>? zcList,
  }) {
    return BalanceTypeEntity()
      ..srList = srList ?? this.srList
      ..zcList = zcList ?? this.zcList;
  }
}

BalanceTypeSrList $BalanceTypeSrListFromJson(Map<String, dynamic> json) {
  final BalanceTypeSrList balanceTypeSrList = BalanceTypeSrList();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    balanceTypeSrList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    balanceTypeSrList.name = name;
  }
  return balanceTypeSrList;
}

Map<String, dynamic> $BalanceTypeSrListToJson(BalanceTypeSrList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension BalanceTypeSrListExtension on BalanceTypeSrList {
  BalanceTypeSrList copyWith({
    int? id,
    String? name,
  }) {
    return BalanceTypeSrList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}

BalanceTypeZcList $BalanceTypeZcListFromJson(Map<String, dynamic> json) {
  final BalanceTypeZcList balanceTypeZcList = BalanceTypeZcList();
  final int? id = jsonConvert.convert<int>(json['id']);
  if (id != null) {
    balanceTypeZcList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    balanceTypeZcList.name = name;
  }
  return balanceTypeZcList;
}

Map<String, dynamic> $BalanceTypeZcListToJson(BalanceTypeZcList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension BalanceTypeZcListExtension on BalanceTypeZcList {
  BalanceTypeZcList copyWith({
    int? id,
    String? name,
  }) {
    return BalanceTypeZcList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}