import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/company_one_entity.dart';

CompanyOneEntity $CompanyOneEntityFromJson(Map<String, dynamic> json) {
  final CompanyOneEntity companyOneEntity = CompanyOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    companyOneEntity.uuid = uuid;
  }
  final String? fullName = jsonConvert.convert<String>(json['full_name']);
  if (fullName != null) {
    companyOneEntity.fullName = fullName;
  }
  final String? companyName = jsonConvert.convert<String>(json['company_name']);
  if (companyName != null) {
    companyOneEntity.companyName = companyName;
  }
  final String? creditCode = jsonConvert.convert<String>(json['credit_code']);
  if (creditCode != null) {
    companyOneEntity.creditCode = creditCode;
  }
  final String? invoiceTitle = jsonConvert.convert<String>(json['invoice_title']);
  if (invoiceTitle != null) {
    companyOneEntity.invoiceTitle = invoiceTitle;
  }
  final String? identificationNumber = jsonConvert.convert<String>(json['identification_number']);
  if (identificationNumber != null) {
    companyOneEntity.identificationNumber = identificationNumber;
  }
  final String? businessLicense = jsonConvert.convert<String>(json['business_license']);
  if (businessLicense != null) {
    companyOneEntity.businessLicense = businessLicense;
  }
  return companyOneEntity;
}

Map<String, dynamic> $CompanyOneEntityToJson(CompanyOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['full_name'] = entity.fullName;
  data['company_name'] = entity.companyName;
  data['credit_code'] = entity.creditCode;
  data['invoice_title'] = entity.invoiceTitle;
  data['identification_number'] = entity.identificationNumber;
  data['business_license'] = entity.businessLicense;
  return data;
}

extension CompanyOneEntityExtension on CompanyOneEntity {
  CompanyOneEntity copyWith({
    String? uuid,
    String? fullName,
    String? companyName,
    String? creditCode,
    String? invoiceTitle,
    String? identificationNumber,
    String? businessLicense,
  }) {
    return CompanyOneEntity()
      ..uuid = uuid ?? this.uuid
      ..fullName = fullName ?? this.fullName
      ..companyName = companyName ?? this.companyName
      ..creditCode = creditCode ?? this.creditCode
      ..invoiceTitle = invoiceTitle ?? this.invoiceTitle
      ..identificationNumber = identificationNumber ?? this.identificationNumber
      ..businessLicense = businessLicense ?? this.businessLicense;
  }
}