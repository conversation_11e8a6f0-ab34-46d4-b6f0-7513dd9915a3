import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/create_project_entity.dart';

CreateProjectEntity $CreateProjectEntityFromJson(Map<String, dynamic> json) {
  final CreateProjectEntity createProjectEntity = CreateProjectEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    createProjectEntity.uuid = uuid;
  }
  return createProjectEntity;
}

Map<String, dynamic> $CreateProjectEntityToJson(CreateProjectEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  return data;
}

extension CreateProjectEntityExtension on CreateProjectEntity {
  CreateProjectEntity copyWith({
    String? uuid,
  }) {
    return CreateProjectEntity()
      ..uuid = uuid ?? this.uuid;
  }
}