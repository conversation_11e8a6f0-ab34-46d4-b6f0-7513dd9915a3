import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';

ContractRegisterEntity $ContractRegisterEntityFromJson(Map<String, dynamic> json) {
  final ContractRegisterEntity contractRegisterEntity = ContractRegisterEntity();
  final int? code = jsonConvert.convert<int>(json['code']);
  if (code != null) {
    contractRegisterEntity.code = code;
  }
  final String? data = jsonConvert.convert<String>(json['data']);
  if (data != null) {
    contractRegisterEntity.data = data;
  }
  final String? msg = jsonConvert.convert<String>(json['msg']);
  if (msg != null) {
    contractRegisterEntity.msg = msg;
  }
  return contractRegisterEntity;
}

Map<String, dynamic> $ContractRegisterEntityToJson(ContractRegisterEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  data['data'] = entity.data;
  data['msg'] = entity.msg;
  return data;
}

extension ContractRegisterEntityExtension on ContractRegisterEntity {
  ContractRegisterEntity copyWith({
    int? code,
    String? data,
    String? msg,
  }) {
    return ContractRegisterEntity()
      ..code = code ?? this.code
      ..data = data ?? this.data
      ..msg = msg ?? this.msg;
  }
}