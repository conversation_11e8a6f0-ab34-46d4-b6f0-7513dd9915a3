import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/project_manager_entity.dart';

ProjectManagerEntity $ProjectManagerEntityFromJson(Map<String, dynamic> json) {
  final ProjectManagerEntity projectManagerEntity = ProjectManagerEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    projectManagerEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    projectManagerEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    projectManagerEntity.total = total;
  }
  final List<ProjectManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ProjectManagerList>(e) as ProjectManagerList).toList();
  if (list != null) {
    projectManagerEntity.list = list;
  }
  return projectManagerEntity;
}

Map<String, dynamic> $ProjectManagerEntityToJson(ProjectManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ProjectManagerEntityExtension on ProjectManagerEntity {
  ProjectManagerEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ProjectManagerList>? list,
  }) {
    return ProjectManagerEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ProjectManagerList $ProjectManagerListFromJson(Map<String, dynamic> json) {
  final ProjectManagerList projectManagerList = ProjectManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    projectManagerList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    projectManagerList.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    projectManagerList.projectShortName = projectShortName;
  }
  final String? contractHumanNum = jsonConvert.convert<String>(json['contract_human_num']);
  if (contractHumanNum != null) {
    projectManagerList.contractHumanNum = contractHumanNum;
  }
  final String? totalJobNum = jsonConvert.convert<String>(json['total_job_num']);
  if (totalJobNum != null) {
    projectManagerList.totalJobNum = totalJobNum;
  }
  final String? projectCatParentName = jsonConvert.convert<String>(json['project_cat_parent_name']);
  if (projectCatParentName != null) {
    projectManagerList.projectCatParentName = projectCatParentName;
  }
  final String? projectCatName = jsonConvert.convert<String>(json['project_cat_name']);
  if (projectCatName != null) {
    projectManagerList.projectCatName = projectCatName;
  }
  final String? managerUserName = jsonConvert.convert<String>(json['manager_user_name']);
  if (managerUserName != null) {
    projectManagerList.managerUserName = managerUserName;
  }
  final String? customName = jsonConvert.convert<String>(json['custom_name']);
  if (customName != null) {
    projectManagerList.customName = customName;
  }
  final String? square = jsonConvert.convert<String>(json['square']);
  if (square != null) {
    projectManagerList.square = square;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    projectManagerList.createTime = createTime;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    projectManagerList.remark = remark;
  }
  final String? onJobNum = jsonConvert.convert<String>(json['on_job_num']);
  if (onJobNum != null) {
    projectManagerList.onJobNum = onJobNum;
  }
  final String? isGroupClockIn = jsonConvert.convert<String>(json['is_group_clock_in']);
  if (isGroupClockIn != null) {
    projectManagerList.isGroupClockIn = isGroupClockIn;
  }
  final String? contractCompanyName = jsonConvert.convert<String>(json['contract_company_name']);
  if (contractCompanyName != null) {
    projectManagerList.contractCompanyName = contractCompanyName;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    projectManagerList.isSelected = isSelected;
  }
  return projectManagerList;
}

Map<String, dynamic> $ProjectManagerListToJson(ProjectManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['contract_human_num'] = entity.contractHumanNum;
  data['total_job_num'] = entity.totalJobNum;
  data['project_cat_parent_name'] = entity.projectCatParentName;
  data['project_cat_name'] = entity.projectCatName;
  data['manager_user_name'] = entity.managerUserName;
  data['custom_name'] = entity.customName;
  data['square'] = entity.square;
  data['create_time'] = entity.createTime;
  data['remark'] = entity.remark;
  data['on_job_num'] = entity.onJobNum;
  data['is_group_clock_in'] = entity.isGroupClockIn;
  data['contract_company_name'] = entity.contractCompanyName;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension ProjectManagerListExtension on ProjectManagerList {
  ProjectManagerList copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? contractHumanNum,
    String? totalJobNum,
    String? projectCatParentName,
    String? projectCatName,
    String? managerUserName,
    String? customName,
    String? square,
    String? createTime,
    String? remark,
    String? onJobNum,
    String? isGroupClockIn,
    String? contractCompanyName,
    bool? isSelected,
  }) {
    return ProjectManagerList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..contractHumanNum = contractHumanNum ?? this.contractHumanNum
      ..totalJobNum = totalJobNum ?? this.totalJobNum
      ..projectCatParentName = projectCatParentName ?? this.projectCatParentName
      ..projectCatName = projectCatName ?? this.projectCatName
      ..managerUserName = managerUserName ?? this.managerUserName
      ..customName = customName ?? this.customName
      ..square = square ?? this.square
      ..createTime = createTime ?? this.createTime
      ..remark = remark ?? this.remark
      ..onJobNum = onJobNum ?? this.onJobNum
      ..isGroupClockIn = isGroupClockIn ?? this.isGroupClockIn
      ..contractCompanyName = contractCompanyName ?? this.contractCompanyName
      ..isSelected = isSelected ?? this.isSelected;
  }
}