import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_list_entity.dart';

ContractTagListEntity $ContractTagListEntityFromJson(Map<String, dynamic> json) {
  final ContractTagListEntity contractTagListEntity = ContractTagListEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    contractTagListEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    contractTagListEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    contractTagListEntity.total = total;
  }
  final List<ContractTagListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractTagListList>(e) as ContractTagListList).toList();
  if (list != null) {
    contractTagListEntity.list = list;
  }
  return contractTagListEntity;
}

Map<String, dynamic> $ContractTagListEntityToJson(ContractTagListEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractTagListEntityExtension on ContractTagListEntity {
  ContractTagListEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ContractTagListList>? list,
  }) {
    return ContractTagListEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ContractTagListList $ContractTagListListFromJson(Map<String, dynamic> json) {
  final ContractTagListList contractTagListList = ContractTagListList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractTagListList.id = id;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    contractTagListList.pid = pid;
  }
  final String? labelName = jsonConvert.convert<String>(json['label_name']);
  if (labelName != null) {
    contractTagListList.labelName = labelName;
  }
  final String? isDelete = jsonConvert.convert<String>(json['is_delete']);
  if (isDelete != null) {
    contractTagListList.isDelete = isDelete;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractTagListList.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractTagListList.updateTime = updateTime;
  }
  return contractTagListList;
}

Map<String, dynamic> $ContractTagListListToJson(ContractTagListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['pid'] = entity.pid;
  data['label_name'] = entity.labelName;
  data['is_delete'] = entity.isDelete;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  return data;
}

extension ContractTagListListExtension on ContractTagListList {
  ContractTagListList copyWith({
    String? id,
    String? pid,
    String? labelName,
    String? isDelete,
    String? createTime,
    String? updateTime,
  }) {
    return ContractTagListList()
      ..id = id ?? this.id
      ..pid = pid ?? this.pid
      ..labelName = labelName ?? this.labelName
      ..isDelete = isDelete ?? this.isDelete
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }
}