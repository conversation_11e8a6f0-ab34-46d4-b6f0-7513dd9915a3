import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/material_cat_entity.dart';

import '../../business/common/approve/bean/material_cat_child_entity.dart';



MaterialCatEntity $MaterialCatEntityFromJson(Map<String, dynamic> json) {
  final MaterialCatEntity materialCatEntity = MaterialCatEntity();
  final List<MaterialCatList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MaterialCatList>(e) as MaterialCatList).toList();
  if (list != null) {
    materialCatEntity.list = list;
  }
  return materialCatEntity;
}

Map<String, dynamic> $MaterialCatEntityToJson(MaterialCatEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension MaterialCatEntityExtension on MaterialCatEntity {
  MaterialCatEntity copyWith({
    List<MaterialCatList>? list,
  }) {
    return MaterialCatEntity()
      ..list = list ?? this.list;
  }
}

MaterialCatList $MaterialCatListFromJson(Map<String, dynamic> json) {
  final MaterialCatList materialCatList = MaterialCatList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    materialCatList.id = id;
  }
  final String? catName = jsonConvert.convert<String>(json['cat_name']);
  if (catName != null) {
    materialCatList.catName = catName;
  }
  final int? skuTotal = jsonConvert.convert<int>(json['sku_total']);
  if (skuTotal != null) {
    materialCatList.skuTotal = skuTotal;
  }
  final List<MaterialCatChildList>? childList = (json['childList'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MaterialCatChildList>(e) as MaterialCatChildList).toList();
  if (childList != null) {
    materialCatList.childList = childList;
  }
  final int? count = jsonConvert.convert<int>(json['count']);
  if (count != null) {
    materialCatList.count = count;
  }
  return materialCatList;
}

Map<String, dynamic> $MaterialCatListToJson(MaterialCatList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['cat_name'] = entity.catName;
  data['sku_total'] = entity.skuTotal;
  data['childList'] = entity.childList.map((v) => v.toJson()).toList();
  data['count'] = entity.count;
  return data;
}

extension MaterialCatListExtension on MaterialCatList {
  MaterialCatList copyWith({
    String? id,
    String? catName,
    int? skuTotal,
    List<MaterialCatChildList>? childList,
    int? count,
  }) {
    return MaterialCatList()
      ..id = id ?? this.id
      ..catName = catName ?? this.catName
      ..skuTotal = skuTotal ?? this.skuTotal
      ..childList = childList ?? this.childList
      ..count = count ?? this.count;
  }
}