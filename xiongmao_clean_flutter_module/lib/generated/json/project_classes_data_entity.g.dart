import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/project_classes_data_entity.dart';

ProjectClassesDataEntity $ProjectClassesDataEntityFromJson(Map<String, dynamic> json) {
  final ProjectClassesDataEntity projectClassesDataEntity = ProjectClassesDataEntity();
  final List<ProjectClassesDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ProjectClassesDataList>(e) as ProjectClassesDataList).toList();
  if (list != null) {
    projectClassesDataEntity.list = list;
  }
  return projectClassesDataEntity;
}

Map<String, dynamic> $ProjectClassesDataEntityToJson(ProjectClassesDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ProjectClassesDataEntityExtension on ProjectClassesDataEntity {
  ProjectClassesDataEntity copyWith({
    List<ProjectClassesDataList>? list,
  }) {
    return ProjectClassesDataEntity()
      ..list = list ?? this.list;
  }
}

ProjectClassesDataList $ProjectClassesDataListFromJson(Map<String, dynamic> json) {
  final ProjectClassesDataList projectClassesDataList = ProjectClassesDataList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    projectClassesDataList.uuid = uuid;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    projectClassesDataList.className = className;
  }
  final String? isNoClock = jsonConvert.convert<String>(json['is_no_clock']);
  if (isNoClock != null) {
    projectClassesDataList.isNoClock = isNoClock;
  }
  final String? classType = jsonConvert.convert<String>(json['class_type']);
  if (classType != null) {
    projectClassesDataList.classType = classType;
  }
  final String? isDefaultRest = jsonConvert.convert<String>(json['is_default_rest']);
  if (isDefaultRest != null) {
    projectClassesDataList.isDefaultRest = isDefaultRest;
  }
  final String? classTypeName = jsonConvert.convert<String>(json['class_type_name']);
  if (classTypeName != null) {
    projectClassesDataList.classTypeName = classTypeName;
  }
  final String? isNoClockName = jsonConvert.convert<String>(json['is_no_clock_name']);
  if (isNoClockName != null) {
    projectClassesDataList.isNoClockName = isNoClockName;
  }
  final String? isDefaultRestName = jsonConvert.convert<String>(json['is_default_rest_name']);
  if (isDefaultRestName != null) {
    projectClassesDataList.isDefaultRestName = isDefaultRestName;
  }
  final String? classTime = jsonConvert.convert<String>(json['class_time']);
  if (classTime != null) {
    projectClassesDataList.classTime = classTime;
  }
  final String? avgLineTime = jsonConvert.convert<String>(json['avg_line_time']);
  if (avgLineTime != null) {
    projectClassesDataList.avgLineTime = avgLineTime;
  }
  final String? avgLineIsToday = jsonConvert.convert<String>(json['avg_line_is_today']);
  if (avgLineIsToday != null) {
    projectClassesDataList.avgLineIsToday = avgLineIsToday;
  }
  final String? outClassDesc = jsonConvert.convert<String>(json['out_class_desc']);
  if (outClassDesc != null) {
    projectClassesDataList.outClassDesc = outClassDesc;
  }
  final String? inClassDesc = jsonConvert.convert<String>(json['in_class_desc']);
  if (inClassDesc != null) {
    projectClassesDataList.inClassDesc = inClassDesc;
  }
  return projectClassesDataList;
}

Map<String, dynamic> $ProjectClassesDataListToJson(ProjectClassesDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['class_name'] = entity.className;
  data['is_no_clock'] = entity.isNoClock;
  data['class_type'] = entity.classType;
  data['is_default_rest'] = entity.isDefaultRest;
  data['class_type_name'] = entity.classTypeName;
  data['is_no_clock_name'] = entity.isNoClockName;
  data['is_default_rest_name'] = entity.isDefaultRestName;
  data['class_time'] = entity.classTime;
  data['avg_line_time'] = entity.avgLineTime;
  data['avg_line_is_today'] = entity.avgLineIsToday;
  data['out_class_desc'] = entity.outClassDesc;
  data['in_class_desc'] = entity.inClassDesc;
  return data;
}

extension ProjectClassesDataListExtension on ProjectClassesDataList {
  ProjectClassesDataList copyWith({
    String? uuid,
    String? className,
    String? isNoClock,
    String? classType,
    String? isDefaultRest,
    String? classTypeName,
    String? isNoClockName,
    String? isDefaultRestName,
    String? classTime,
    String? avgLineTime,
    String? avgLineIsToday,
    String? outClassDesc,
    String? inClassDesc,
  }) {
    return ProjectClassesDataList()
      ..uuid = uuid ?? this.uuid
      ..className = className ?? this.className
      ..isNoClock = isNoClock ?? this.isNoClock
      ..classType = classType ?? this.classType
      ..isDefaultRest = isDefaultRest ?? this.isDefaultRest
      ..classTypeName = classTypeName ?? this.classTypeName
      ..isNoClockName = isNoClockName ?? this.isNoClockName
      ..isDefaultRestName = isDefaultRestName ?? this.isDefaultRestName
      ..classTime = classTime ?? this.classTime
      ..avgLineTime = avgLineTime ?? this.avgLineTime
      ..avgLineIsToday = avgLineIsToday ?? this.avgLineIsToday
      ..outClassDesc = outClassDesc ?? this.outClassDesc
      ..inClassDesc = inClassDesc ?? this.inClassDesc;
  }
}