import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/holiday_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_one_entity.dart';


HolidayOneEntity $HolidayOneEntityFromJson(Map<String, dynamic> json) {
  final HolidayOneEntity holidayOneEntity = HolidayOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    holidayOneEntity.uuid = uuid;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    holidayOneEntity.projectUuid = projectUuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    holidayOneEntity.projectName = projectName;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    holidayOneEntity.startDate = startDate;
  }
  final String? startType = jsonConvert.convert<String>(json['start_type']);
  if (startType != null) {
    holidayOneEntity.startType = startType;
  }
  final String? startTypeName = jsonConvert.convert<String>(json['start_type_name']);
  if (startTypeName != null) {
    holidayOneEntity.startTypeName = startTypeName;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    holidayOneEntity.endDate = endDate;
  }
  final String? endType = jsonConvert.convert<String>(json['end_type']);
  if (endType != null) {
    holidayOneEntity.endType = endType;
  }
  final String? endTypeName = jsonConvert.convert<String>(json['end_type_name']);
  if (endTypeName != null) {
    holidayOneEntity.endTypeName = endTypeName;
  }
  final String? holidayType = jsonConvert.convert<String>(json['holiday_type']);
  if (holidayType != null) {
    holidayOneEntity.holidayType = holidayType;
  }
  final String? holidayTypeName = jsonConvert.convert<String>(json['holiday_type_name']);
  if (holidayTypeName != null) {
    holidayOneEntity.holidayTypeName = holidayTypeName;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    holidayOneEntity.reason = reason;
  }
  final String? holidayDays = jsonConvert.convert<String>(json['holiday_days']);
  if (holidayDays != null) {
    holidayOneEntity.holidayDays = holidayDays;
  }
  final String? createUserName = jsonConvert.convert<String>(json['create_user_name']);
  if (createUserName != null) {
    holidayOneEntity.createUserName = createUserName;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    holidayOneEntity.createTime = createTime;
  }
  final List<WorkOvertimeOneUserList>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkOvertimeOneUserList>(e) as WorkOvertimeOneUserList).toList();
  if (userList != null) {
    holidayOneEntity.userList = userList;
  }
  return holidayOneEntity;
}

Map<String, dynamic> $HolidayOneEntityToJson(HolidayOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_uuid'] = entity.projectUuid;
  data['project_name'] = entity.projectName;
  data['start_date'] = entity.startDate;
  data['start_type'] = entity.startType;
  data['start_type_name'] = entity.startTypeName;
  data['end_date'] = entity.endDate;
  data['end_type'] = entity.endType;
  data['end_type_name'] = entity.endTypeName;
  data['holiday_type'] = entity.holidayType;
  data['holiday_type_name'] = entity.holidayTypeName;
  data['reason'] = entity.reason;
  data['holiday_days'] = entity.holidayDays;
  data['create_user_name'] = entity.createUserName;
  data['create_time'] = entity.createTime;
  data['user_list'] = entity.userList?.map((v) => v.toJson()).toList();
  return data;
}

extension HolidayOneEntityExtension on HolidayOneEntity {
  HolidayOneEntity copyWith({
    String? uuid,
    String? projectUuid,
    String? projectName,
    String? startDate,
    String? startType,
    String? startTypeName,
    String? endDate,
    String? endType,
    String? endTypeName,
    String? holidayType,
    String? holidayTypeName,
    String? reason,
    String? holidayDays,
    String? createUserName,
    String? createTime,
    List<WorkOvertimeOneUserList>? userList,
  }) {
    return HolidayOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectUuid = projectUuid ?? this.projectUuid
      ..projectName = projectName ?? this.projectName
      ..startDate = startDate ?? this.startDate
      ..startType = startType ?? this.startType
      ..startTypeName = startTypeName ?? this.startTypeName
      ..endDate = endDate ?? this.endDate
      ..endType = endType ?? this.endType
      ..endTypeName = endTypeName ?? this.endTypeName
      ..holidayType = holidayType ?? this.holidayType
      ..holidayTypeName = holidayTypeName ?? this.holidayTypeName
      ..reason = reason ?? this.reason
      ..holidayDays = holidayDays ?? this.holidayDays
      ..createUserName = createUserName ?? this.createUserName
      ..createTime = createTime ?? this.createTime
      ..userList = userList ?? this.userList;
  }
}

HolidayOneUserList $HolidayOneUserListFromJson(Map<String, dynamic> json) {
  final HolidayOneUserList holidayOneUserList = HolidayOneUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    holidayOneUserList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    holidayOneUserList.userName = userName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    holidayOneUserList.avatar = avatar;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    holidayOneUserList.jobName = jobName;
  }
  final String? holidayHours = jsonConvert.convert<String>(json['holiday_hours']);
  if (holidayHours != null) {
    holidayOneUserList.holidayHours = holidayHours;
  }
  return holidayOneUserList;
}

Map<String, dynamic> $HolidayOneUserListToJson(HolidayOneUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['avatar'] = entity.avatar;
  data['job_name'] = entity.jobName;
  data['holiday_hours'] = entity.holidayHours;
  return data;
}

extension HolidayOneUserListExtension on HolidayOneUserList {
  HolidayOneUserList copyWith({
    String? uuid,
    String? userName,
    String? avatar,
    String? jobName,
    String? holidayHours,
  }) {
    return HolidayOneUserList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..avatar = avatar ?? this.avatar
      ..jobName = jobName ?? this.jobName
      ..holidayHours = holidayHours ?? this.holidayHours;
  }
}