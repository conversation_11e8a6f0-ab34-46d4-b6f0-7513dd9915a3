import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/custom/bean/custom_manager_entity.dart';

CustomManagerEntity $CustomManagerEntityFromJson(Map<String, dynamic> json) {
  final CustomManagerEntity customManagerEntity = CustomManagerEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    customManagerEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    customManagerEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    customManagerEntity.total = total;
  }
  final List<CustomManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CustomManagerList>(e) as CustomManagerList).toList();
  if (list != null) {
    customManagerEntity.list = list;
  }
  return customManagerEntity;
}

Map<String, dynamic> $CustomManagerEntityToJson(CustomManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CustomManagerEntityExtension on CustomManagerEntity {
  CustomManagerEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<CustomManagerList>? list,
  }) {
    return CustomManagerEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

CustomManagerList $CustomManagerListFromJson(Map<String, dynamic> json) {
  final CustomManagerList customManagerList = CustomManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    customManagerList.uuid = uuid;
  }
  final String? customFullName = jsonConvert.convert<String>(json['custom_full_name']);
  if (customFullName != null) {
    customManagerList.customFullName = customFullName;
  }
  final String? provinceName = jsonConvert.convert<String>(json['province_name']);
  if (provinceName != null) {
    customManagerList.provinceName = provinceName;
  }
  final String? cityName = jsonConvert.convert<String>(json['city_name']);
  if (cityName != null) {
    customManagerList.cityName = cityName;
  }
  final String? cityId = jsonConvert.convert<String>(json['city_id']);
  if (cityId != null) {
    customManagerList.cityId = cityId;
  }
  final String? areaId = jsonConvert.convert<String>(json['area_id']);
  if (areaId != null) {
    customManagerList.areaId = areaId;
  }
  final String? areaName = jsonConvert.convert<String>(json['area_name']);
  if (areaName != null) {
    customManagerList.areaName = areaName;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    customManagerList.address = address;
  }
  final String? contactPerson = jsonConvert.convert<String>(json['contact_person']);
  if (contactPerson != null) {
    customManagerList.contactPerson = contactPerson;
  }
  final String? contactMobile = jsonConvert.convert<String>(json['contact_mobile']);
  if (contactMobile != null) {
    customManagerList.contactMobile = contactMobile;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    customManagerList.remark = remark;
  }
  final String? projectNum = jsonConvert.convert<String>(json['project_num']);
  if (projectNum != null) {
    customManagerList.projectNum = projectNum;
  }
  final String? customName = jsonConvert.convert<String>(json['custom_name']);
  if (customName != null) {
    customManagerList.customName = customName;
  }
  final String? managerUserName = jsonConvert.convert<String>(json['manager_user_name']);
  if (managerUserName != null) {
    customManagerList.managerUserName = managerUserName;
  }
  final String? managerUserUuid = jsonConvert.convert<String>(json['manager_user_uuid']);
  if (managerUserUuid != null) {
    customManagerList.managerUserUuid = managerUserUuid;
  }
  return customManagerList;
}

Map<String, dynamic> $CustomManagerListToJson(CustomManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['custom_full_name'] = entity.customFullName;
  data['province_name'] = entity.provinceName;
  data['city_name'] = entity.cityName;
  data['city_id'] = entity.cityId;
  data['area_id'] = entity.areaId;
  data['area_name'] = entity.areaName;
  data['address'] = entity.address;
  data['contact_person'] = entity.contactPerson;
  data['contact_mobile'] = entity.contactMobile;
  data['remark'] = entity.remark;
  data['project_num'] = entity.projectNum;
  data['custom_name'] = entity.customName;
  data['manager_user_name'] = entity.managerUserName;
  data['manager_user_uuid'] = entity.managerUserUuid;
  return data;
}

extension CustomManagerListExtension on CustomManagerList {
  CustomManagerList copyWith({
    String? uuid,
    String? customFullName,
    String? provinceName,
    String? cityName,
    String? cityId,
    String? areaId,
    String? areaName,
    String? address,
    String? contactPerson,
    String? contactMobile,
    String? remark,
    String? projectNum,
    String? customName,
    String? managerUserName,
    String? managerUserUuid,
  }) {
    return CustomManagerList()
      ..uuid = uuid ?? this.uuid
      ..customFullName = customFullName ?? this.customFullName
      ..provinceName = provinceName ?? this.provinceName
      ..cityName = cityName ?? this.cityName
      ..cityId = cityId ?? this.cityId
      ..areaId = areaId ?? this.areaId
      ..areaName = areaName ?? this.areaName
      ..address = address ?? this.address
      ..contactPerson = contactPerson ?? this.contactPerson
      ..contactMobile = contactMobile ?? this.contactMobile
      ..remark = remark ?? this.remark
      ..projectNum = projectNum ?? this.projectNum
      ..customName = customName ?? this.customName
      ..managerUserName = managerUserName ?? this.managerUserName
      ..managerUserUuid = managerUserUuid ?? this.managerUserUuid;
  }
}