import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_user_entity.dart';

ContractUserEntity $ContractUserEntityFromJson(Map<String, dynamic> json) {
  final ContractUserEntity contractUserEntity = ContractUserEntity();
  final List<ContractUserList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractUserList>(e) as ContractUserList).toList();
  if (list != null) {
    contractUserEntity.list = list;
  }
  return contractUserEntity;
}

Map<String, dynamic> $ContractUserEntityToJson(ContractUserEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractUserEntityExtension on ContractUserEntity {
  ContractUserEntity copyWith({
    List<ContractUserList>? list,
  }) {
    return ContractUserEntity()
      ..list = list ?? this.list;
  }
}

ContractUserList $ContractUserListFromJson(Map<String, dynamic> json) {
  final ContractUserList contractUserList = ContractUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    contractUserList.uuid = uuid;
  }
  final String? workStartTime = jsonConvert.convert<String>(json['work_start_time']);
  if (workStartTime != null) {
    contractUserList.workStartTime = workStartTime;
  }
  final String? workEndTime = jsonConvert.convert<String>(json['work_end_time']);
  if (workEndTime != null) {
    contractUserList.workEndTime = workEndTime;
  }
  final String? createUserName = jsonConvert.convert<String>(json['create_user_name']);
  if (createUserName != null) {
    contractUserList.createUserName = createUserName;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractUserList.createTime = createTime;
  }
  final String? labelCode = jsonConvert.convert<String>(json['label_code']);
  if (labelCode != null) {
    contractUserList.labelCode = labelCode;
  }
  final String? labelName = jsonConvert.convert<String>(json['label_name']);
  if (labelName != null) {
    contractUserList.labelName = labelName;
  }
  final String? contractSignType = jsonConvert.convert<String>(json['contract_sign_type']);
  if (contractSignType != null) {
    contractUserList.contractSignType = contractSignType;
  }
  final String? contractStatus = jsonConvert.convert<String>(json['contract_status']);
  if (contractStatus != null) {
    contractUserList.contractStatus = contractStatus;
  }
  final String? contractStatusName = jsonConvert.convert<String>(json['contract_status_name']);
  if (contractStatusName != null) {
    contractUserList.contractStatusName = contractStatusName;
  }
  final String? contractSignTypeName = jsonConvert.convert<String>(json['contract_sign_type_name']);
  if (contractSignTypeName != null) {
    contractUserList.contractSignTypeName = contractSignTypeName;
  }
  final List<ContractUserListContractPicList>? contractPicList = (json['contract_pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractUserListContractPicList>(e) as ContractUserListContractPicList).toList();
  if (contractPicList != null) {
    contractUserList.contractPicList = contractPicList;
  }
  return contractUserList;
}

Map<String, dynamic> $ContractUserListToJson(ContractUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['work_start_time'] = entity.workStartTime;
  data['work_end_time'] = entity.workEndTime;
  data['create_user_name'] = entity.createUserName;
  data['create_time'] = entity.createTime;
  data['label_code'] = entity.labelCode;
  data['label_name'] = entity.labelName;
  data['contract_sign_type'] = entity.contractSignType;
  data['contract_status'] = entity.contractStatus;
  data['contract_status_name'] = entity.contractStatusName;
  data['contract_sign_type_name'] = entity.contractSignTypeName;
  data['contract_pic_list'] = entity.contractPicList?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractUserListExtension on ContractUserList {
  ContractUserList copyWith({
    String? uuid,
    String? workStartTime,
    String? workEndTime,
    String? createUserName,
    String? createTime,
    String? labelCode,
    String? labelName,
    String? contractSignType,
    String? contractStatus,
    String? contractStatusName,
    String? contractSignTypeName,
    List<ContractUserListContractPicList>? contractPicList,
  }) {
    return ContractUserList()
      ..uuid = uuid ?? this.uuid
      ..workStartTime = workStartTime ?? this.workStartTime
      ..workEndTime = workEndTime ?? this.workEndTime
      ..createUserName = createUserName ?? this.createUserName
      ..createTime = createTime ?? this.createTime
      ..labelCode = labelCode ?? this.labelCode
      ..labelName = labelName ?? this.labelName
      ..contractSignType = contractSignType ?? this.contractSignType
      ..contractStatus = contractStatus ?? this.contractStatus
      ..contractStatusName = contractStatusName ?? this.contractStatusName
      ..contractSignTypeName = contractSignTypeName ?? this.contractSignTypeName
      ..contractPicList = contractPicList ?? this.contractPicList;
  }
}

ContractUserListContractPicList $ContractUserListContractPicListFromJson(Map<String, dynamic> json) {
  final ContractUserListContractPicList contractUserListContractPicList = ContractUserListContractPicList();
  final String? sourceUrl = jsonConvert.convert<String>(json['source_url']);
  if (sourceUrl != null) {
    contractUserListContractPicList.sourceUrl = sourceUrl;
  }
  return contractUserListContractPicList;
}

Map<String, dynamic> $ContractUserListContractPicListToJson(ContractUserListContractPicList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['source_url'] = entity.sourceUrl;
  return data;
}

extension ContractUserListContractPicListExtension on ContractUserListContractPicList {
  ContractUserListContractPicList copyWith({
    String? sourceUrl,
  }) {
    return ContractUserListContractPicList()
      ..sourceUrl = sourceUrl ?? this.sourceUrl;
  }
}