import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';

PermissionEntity $PermissionEntityFromJson(Map<String, dynamic> json) {
  final PermissionEntity permissionEntity = PermissionEntity();
  final List<PermissionData>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<PermissionData>(e) as PermissionData).toList();
  if (list != null) {
    permissionEntity.list = list;
  }
  return permissionEntity;
}

Map<String, dynamic> $PermissionEntityToJson(PermissionEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension PermissionEntityExtension on PermissionEntity {
  PermissionEntity copyWith({
    List<PermissionData>? list,
  }) {
    return PermissionEntity()
      ..list = list ?? this.list;
  }
}

PermissionData $PermissionDataFromJson(Map<String, dynamic> json) {
  final PermissionData permissionData = PermissionData();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    permissionData.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    permissionData.name = name;
  }
  final String? role_desc = jsonConvert.convert<String>(json['role_desc']);
  if (role_desc != null) {
    permissionData.role_desc = role_desc;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    permissionData.isSelected = isSelected;
  }
  return permissionData;
}

Map<String, dynamic> $PermissionDataToJson(PermissionData entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['role_desc'] = entity.role_desc;
  data['isSelected'] = entity.isSelected;
  return data;
}

extension PermissionDataExtension on PermissionData {
  PermissionData copyWith({
    String? id,
    String? name,
    String? role_desc,
    bool? isSelected,
  }) {
    return PermissionData()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..role_desc = role_desc ?? this.role_desc
      ..isSelected = isSelected ?? this.isSelected;
  }
}