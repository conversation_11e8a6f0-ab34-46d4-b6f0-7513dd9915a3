import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_entity.dart';

DepartmentCompanyEntity $DepartmentCompanyEntityFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyEntity departmentCompanyEntity = DepartmentCompanyEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    departmentCompanyEntity.uuid = uuid;
  }
  final String? departmentName = jsonConvert.convert<String>(json['department_name']);
  if (departmentName != null) {
    departmentCompanyEntity.departmentName = departmentName;
  }
  final String? departmentType = jsonConvert.convert<String>(json['department_type']);
  if (departmentType != null) {
    departmentCompanyEntity.departmentType = departmentType;
  }
  final String? departmentTypeName = jsonConvert.convert<String>(json['department_type_name']);
  if (departmentTypeName != null) {
    departmentCompanyEntity.departmentTypeName = departmentTypeName;
  }
  final String? isHasChild = jsonConvert.convert<String>(json['is_has_child']);
  if (isHasChild != null) {
    departmentCompanyEntity.isHasChild = isHasChild;
  }
  final String? parentUuid = jsonConvert.convert<String>(json['parent_uuid']);
  if (parentUuid != null) {
    departmentCompanyEntity.parentUuid = parentUuid;
  }
  final String? parentDepartmentName = jsonConvert.convert<String>(json['parent_department_name']);
  if (parentDepartmentName != null) {
    departmentCompanyEntity.parentDepartmentName = parentDepartmentName;
  }
  final String? contractCompanyUuid = jsonConvert.convert<String>(json['contract_company_uuid']);
  if (contractCompanyUuid != null) {
    departmentCompanyEntity.contractCompanyUuid = contractCompanyUuid;
  }
  final String? contractCompanyName = jsonConvert.convert<String>(json['contract_company_name']);
  if (contractCompanyName != null) {
    departmentCompanyEntity.contractCompanyName = contractCompanyName;
  }
  final List<DepartmentCompanyListResponseUserList>? responseUserList = (json['response_user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<DepartmentCompanyListResponseUserList>(e) as DepartmentCompanyListResponseUserList).toList();
  if (responseUserList != null) {
    departmentCompanyEntity.responseUserList = responseUserList;
  }
  final List<DepartmentCompanyList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<DepartmentCompanyList>(e) as DepartmentCompanyList).toList();
  if (list != null) {
    departmentCompanyEntity.list = list;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    departmentCompanyEntity.isSelected = isSelected;
  }
  return departmentCompanyEntity;
}

Map<String, dynamic> $DepartmentCompanyEntityToJson(DepartmentCompanyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['department_name'] = entity.departmentName;
  data['department_type'] = entity.departmentType;
  data['department_type_name'] = entity.departmentTypeName;
  data['is_has_child'] = entity.isHasChild;
  data['parent_uuid'] = entity.parentUuid;
  data['parent_department_name'] = entity.parentDepartmentName;
  data['contract_company_uuid'] = entity.contractCompanyUuid;
  data['contract_company_name'] = entity.contractCompanyName;
  data['response_user_list'] = entity.responseUserList?.map((v) => v.toJson()).toList();
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  data['isSelected'] = entity.isSelected;
  return data;
}

extension DepartmentCompanyEntityExtension on DepartmentCompanyEntity {
  DepartmentCompanyEntity copyWith({
    String? uuid,
    String? departmentName,
    String? departmentType,
    String? departmentTypeName,
    String? isHasChild,
    String? parentUuid,
    String? parentDepartmentName,
    String? contractCompanyUuid,
    String? contractCompanyName,
    List<DepartmentCompanyListResponseUserList>? responseUserList,
    List<DepartmentCompanyList>? list,
    bool? isSelected,
  }) {
    return DepartmentCompanyEntity()
      ..uuid = uuid ?? this.uuid
      ..departmentName = departmentName ?? this.departmentName
      ..departmentType = departmentType ?? this.departmentType
      ..departmentTypeName = departmentTypeName ?? this.departmentTypeName
      ..isHasChild = isHasChild ?? this.isHasChild
      ..parentUuid = parentUuid ?? this.parentUuid
      ..parentDepartmentName = parentDepartmentName ?? this.parentDepartmentName
      ..contractCompanyUuid = contractCompanyUuid ?? this.contractCompanyUuid
      ..contractCompanyName = contractCompanyName ?? this.contractCompanyName
      ..responseUserList = responseUserList ?? this.responseUserList
      ..list = list ?? this.list
      ..isSelected = isSelected ?? this.isSelected;
  }
}

DepartmentCompanyList $DepartmentCompanyListFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyList departmentCompanyList = DepartmentCompanyList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    departmentCompanyList.uuid = uuid;
  }
  final String? departmentName = jsonConvert.convert<String>(json['department_name']);
  if (departmentName != null) {
    departmentCompanyList.departmentName = departmentName;
  }
  final String? departmentType = jsonConvert.convert<String>(json['department_type']);
  if (departmentType != null) {
    departmentCompanyList.departmentType = departmentType;
  }
  final String? departmentTypeName = jsonConvert.convert<String>(json['department_type_name']);
  if (departmentTypeName != null) {
    departmentCompanyList.departmentTypeName = departmentTypeName;
  }
  final String? isHasChild = jsonConvert.convert<String>(json['is_has_child']);
  if (isHasChild != null) {
    departmentCompanyList.isHasChild = isHasChild;
  }
  final String? contractCompanyUuid = jsonConvert.convert<String>(json['contract_company_uuid']);
  if (contractCompanyUuid != null) {
    departmentCompanyList.contractCompanyUuid = contractCompanyUuid;
  }
  final String? contractCompanyName = jsonConvert.convert<String>(json['contract_company_name']);
  if (contractCompanyName != null) {
    departmentCompanyList.contractCompanyName = contractCompanyName;
  }
  final List<DepartmentCompanyListResponseUserList>? responseUserList = (json['response_user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<DepartmentCompanyListResponseUserList>(e) as DepartmentCompanyListResponseUserList).toList();
  if (responseUserList != null) {
    departmentCompanyList.responseUserList = responseUserList;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['isSelected']);
  if (isSelected != null) {
    departmentCompanyList.isSelected = isSelected;
  }
  return departmentCompanyList;
}

Map<String, dynamic> $DepartmentCompanyListToJson(DepartmentCompanyList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['department_name'] = entity.departmentName;
  data['department_type'] = entity.departmentType;
  data['department_type_name'] = entity.departmentTypeName;
  data['is_has_child'] = entity.isHasChild;
  data['contract_company_uuid'] = entity.contractCompanyUuid;
  data['contract_company_name'] = entity.contractCompanyName;
  data['response_user_list'] = entity.responseUserList?.map((v) => v.toJson()).toList();
  data['isSelected'] = entity.isSelected;
  return data;
}

extension DepartmentCompanyListExtension on DepartmentCompanyList {
  DepartmentCompanyList copyWith({
    String? uuid,
    String? departmentName,
    String? departmentType,
    String? departmentTypeName,
    String? isHasChild,
    String? contractCompanyUuid,
    String? contractCompanyName,
    List<DepartmentCompanyListResponseUserList>? responseUserList,
    bool? isSelected,
  }) {
    return DepartmentCompanyList()
      ..uuid = uuid ?? this.uuid
      ..departmentName = departmentName ?? this.departmentName
      ..departmentType = departmentType ?? this.departmentType
      ..departmentTypeName = departmentTypeName ?? this.departmentTypeName
      ..isHasChild = isHasChild ?? this.isHasChild
      ..contractCompanyUuid = contractCompanyUuid ?? this.contractCompanyUuid
      ..contractCompanyName = contractCompanyName ?? this.contractCompanyName
      ..responseUserList = responseUserList ?? this.responseUserList
      ..isSelected = isSelected ?? this.isSelected;
  }
}

DepartmentCompanyListResponseUserList $DepartmentCompanyListResponseUserListFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyListResponseUserList departmentCompanyListResponseUserList = DepartmentCompanyListResponseUserList();
  final String? userUuid = jsonConvert.convert<String>(json['user_uuid']);
  if (userUuid != null) {
    departmentCompanyListResponseUserList.userUuid = userUuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    departmentCompanyListResponseUserList.userName = userName;
  }
  return departmentCompanyListResponseUserList;
}

Map<String, dynamic> $DepartmentCompanyListResponseUserListToJson(DepartmentCompanyListResponseUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_uuid'] = entity.userUuid;
  data['user_name'] = entity.userName;
  return data;
}

extension DepartmentCompanyListResponseUserListExtension on DepartmentCompanyListResponseUserList {
  DepartmentCompanyListResponseUserList copyWith({
    String? userUuid,
    String? userName,
  }) {
    return DepartmentCompanyListResponseUserList()
      ..userUuid = userUuid ?? this.userUuid
      ..userName = userName ?? this.userName;
  }
}