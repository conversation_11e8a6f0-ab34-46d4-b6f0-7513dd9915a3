import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_nation_entity.dart';

StaffNationEntity $StaffNationEntityFromJson(Map<String, dynamic> json) {
  final StaffNationEntity staffNationEntity = StaffNationEntity();
  final List<StaffNationList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<StaffNationList>(e) as StaffNationList).toList();
  if (list != null) {
    staffNationEntity.list = list;
  }
  return staffNationEntity;
}

Map<String, dynamic> $StaffNationEntityToJson(StaffNationEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension StaffNationEntityExtension on StaffNationEntity {
  StaffNationEntity copyWith({
    List<StaffNationList>? list,
  }) {
    return StaffNationEntity()
      ..list = list ?? this.list;
  }
}

StaffNationList $StaffNationListFromJson(Map<String, dynamic> json) {
  final StaffNationList staffNationList = StaffNationList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    staffNationList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    staffNationList.name = name;
  }
  return staffNationList;
}

Map<String, dynamic> $StaffNationListToJson(StaffNationList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  return data;
}

extension StaffNationListExtension on StaffNationList {
  StaffNationList copyWith({
    String? id,
    String? name,
  }) {
    return StaffNationList()
      ..id = id ?? this.id
      ..name = name ?? this.name;
  }
}