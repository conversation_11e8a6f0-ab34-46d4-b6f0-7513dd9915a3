import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ocr_id_card_entity.dart';

OcrIdCardEntity $OcrIdCardEntityFromJson(Map<String, dynamic> json) {
  final OcrIdCardEntity ocrIdCardEntity = OcrIdCardEntity();
  final int? imageStatus = jsonConvert.convert<int>(json['image_status']);
  if (imageStatus != null) {
    ocrIdCardEntity.imageStatus = imageStatus;
  }
  final String? imageStatusName = jsonConvert.convert<String>(json['image_status_name']);
  if (imageStatusName != null) {
    ocrIdCardEntity.imageStatusName = imageStatusName;
  }
  final int? direction = jsonConvert.convert<int>(json['direction']);
  if (direction != null) {
    ocrIdCardEntity.direction = direction;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    ocrIdCardEntity.address = address;
  }
  final String? birthday = jsonConvert.convert<String>(json['birthday']);
  if (birthday != null) {
    ocrIdCardEntity.birthday = birthday;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    ocrIdCardEntity.name = name;
  }
  final String? idNumber = jsonConvert.convert<String>(json['id_number']);
  if (idNumber != null) {
    ocrIdCardEntity.idNumber = idNumber;
  }
  final String? hometown = jsonConvert.convert<String>(json['hometown']);
  if (hometown != null) {
    ocrIdCardEntity.hometown = hometown;
  }
  final String? hometownCity = jsonConvert.convert<String>(json['hometown_city']);
  if (hometownCity != null) {
    ocrIdCardEntity.hometownCity = hometownCity;
  }
  final String? sex = jsonConvert.convert<String>(json['sex']);
  if (sex != null) {
    ocrIdCardEntity.sex = sex;
  }
  final String? nation = jsonConvert.convert<String>(json['nation']);
  if (nation != null) {
    ocrIdCardEntity.nation = nation;
  }
  final String? chineseZodiac = jsonConvert.convert<String>(json['chinese_zodiac']);
  if (chineseZodiac != null) {
    ocrIdCardEntity.chineseZodiac = chineseZodiac;
  }
  final String? zodiac = jsonConvert.convert<String>(json['zodiac']);
  if (zodiac != null) {
    ocrIdCardEntity.zodiac = zodiac;
  }
  return ocrIdCardEntity;
}

Map<String, dynamic> $OcrIdCardEntityToJson(OcrIdCardEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['image_status'] = entity.imageStatus;
  data['image_status_name'] = entity.imageStatusName;
  data['direction'] = entity.direction;
  data['address'] = entity.address;
  data['birthday'] = entity.birthday;
  data['name'] = entity.name;
  data['id_number'] = entity.idNumber;
  data['hometown'] = entity.hometown;
  data['hometown_city'] = entity.hometownCity;
  data['sex'] = entity.sex;
  data['nation'] = entity.nation;
  data['chinese_zodiac'] = entity.chineseZodiac;
  data['zodiac'] = entity.zodiac;
  return data;
}

extension OcrIdCardEntityExtension on OcrIdCardEntity {
  OcrIdCardEntity copyWith({
    int? imageStatus,
    String? imageStatusName,
    int? direction,
    String? address,
    String? birthday,
    String? name,
    String? idNumber,
    String? hometown,
    String? hometownCity,
    String? sex,
    String? nation,
    String? chineseZodiac,
    String? zodiac,
  }) {
    return OcrIdCardEntity()
      ..imageStatus = imageStatus ?? this.imageStatus
      ..imageStatusName = imageStatusName ?? this.imageStatusName
      ..direction = direction ?? this.direction
      ..address = address ?? this.address
      ..birthday = birthday ?? this.birthday
      ..name = name ?? this.name
      ..idNumber = idNumber ?? this.idNumber
      ..hometown = hometown ?? this.hometown
      ..hometownCity = hometownCity ?? this.hometownCity
      ..sex = sex ?? this.sex
      ..nation = nation ?? this.nation
      ..chineseZodiac = chineseZodiac ?? this.chineseZodiac
      ..zodiac = zodiac ?? this.zodiac;
  }
}