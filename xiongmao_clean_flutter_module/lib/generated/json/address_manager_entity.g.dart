import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/address_manager_entity.dart';

AddressManagerEntity $AddressManagerEntityFromJson(Map<String, dynamic> json) {
  final AddressManagerEntity addressManagerEntity = AddressManagerEntity();
  final List<AddressManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AddressManagerList>(e) as AddressManagerList).toList();
  if (list != null) {
    addressManagerEntity.list = list;
  }
  return addressManagerEntity;
}

Map<String, dynamic> $AddressManagerEntityToJson(AddressManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension AddressManagerEntityExtension on AddressManagerEntity {
  AddressManagerEntity copyWith({
    List<AddressManagerList>? list,
  }) {
    return AddressManagerEntity()
      ..list = list ?? this.list;
  }
}

AddressManagerList $AddressManagerListFromJson(Map<String, dynamic> json) {
  final AddressManagerList addressManagerList = AddressManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    addressManagerList.uuid = uuid;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    addressManagerList.address = address;
  }
  final String? address_desc = jsonConvert.convert<String>(json['address_desc']);
  if (address_desc != null) {
    addressManagerList.address_desc = address_desc;
  }
  final String? lnt = jsonConvert.convert<String>(json['lnt']);
  if (lnt != null) {
    addressManagerList.lnt = lnt;
  }
  final String? lat = jsonConvert.convert<String>(json['lat']);
  if (lat != null) {
    addressManagerList.lat = lat;
  }
  final bool? isSelect = jsonConvert.convert<bool>(json['isSelect']);
  if (isSelect != null) {
    addressManagerList.isSelect = isSelect;
  }
  return addressManagerList;
}

Map<String, dynamic> $AddressManagerListToJson(AddressManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['address'] = entity.address;
  data['address_desc'] = entity.address_desc;
  data['lnt'] = entity.lnt;
  data['lat'] = entity.lat;
  data['isSelect'] = entity.isSelect;
  return data;
}

extension AddressManagerListExtension on AddressManagerList {
  AddressManagerList copyWith({
    String? uuid,
    String? address,
    String? address_desc,
    String? lnt,
    String? lat,
    bool? isSelect,
  }) {
    return AddressManagerList()
      ..uuid = uuid ?? this.uuid
      ..address = address ?? this.address
      ..address_desc = address_desc ?? this.address_desc
      ..lnt = lnt ?? this.lnt
      ..lat = lat ?? this.lat
      ..isSelect = isSelect ?? this.isSelect;
  }
}