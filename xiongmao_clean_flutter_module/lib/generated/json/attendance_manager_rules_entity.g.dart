import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_manager_rules_entity.dart';

AttendanceManagerRulesEntity $AttendanceManagerRulesEntityFromJson(Map<String, dynamic> json) {
  final AttendanceManagerRulesEntity attendanceManagerRulesEntity = AttendanceManagerRulesEntity();
  final List<AttendanceManagerRulesList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<AttendanceManagerRulesList>(e) as AttendanceManagerRulesList).toList();
  if (list != null) {
    attendanceManagerRulesEntity.list = list;
  }
  return attendanceManagerRulesEntity;
}

Map<String, dynamic> $AttendanceManagerRulesEntityToJson(AttendanceManagerRulesEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension AttendanceManagerRulesEntityExtension on AttendanceManagerRulesEntity {
  AttendanceManagerRulesEntity copyWith({
    List<AttendanceManagerRulesList>? list,
  }) {
    return AttendanceManagerRulesEntity()
      ..list = list ?? this.list;
  }
}

AttendanceManagerRulesList $AttendanceManagerRulesListFromJson(Map<String, dynamic> json) {
  final AttendanceManagerRulesList attendanceManagerRulesList = AttendanceManagerRulesList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    attendanceManagerRulesList.uuid = uuid;
  }
  final String? groupName = jsonConvert.convert<String>(json['group_name']);
  if (groupName != null) {
    attendanceManagerRulesList.groupName = groupName;
  }
  final String? workDayDesc = jsonConvert.convert<String>(json['work_day_desc']);
  if (workDayDesc != null) {
    attendanceManagerRulesList.workDayDesc = workDayDesc;
  }
  final String? inClassDesc = jsonConvert.convert<String>(json['in_class_desc']);
  if (inClassDesc != null) {
    attendanceManagerRulesList.inClassDesc = inClassDesc;
  }
  final String? attendanceMethodName = jsonConvert.convert<String>(json['attendance_method_name']);
  if (attendanceMethodName != null) {
    attendanceManagerRulesList.attendanceMethodName = attendanceMethodName;
  }
  final List<String>? addressList = (json['address_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (addressList != null) {
    attendanceManagerRulesList.addressList = addressList;
  }
  return attendanceManagerRulesList;
}

Map<String, dynamic> $AttendanceManagerRulesListToJson(AttendanceManagerRulesList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['group_name'] = entity.groupName;
  data['work_day_desc'] = entity.workDayDesc;
  data['in_class_desc'] = entity.inClassDesc;
  data['attendance_method_name'] = entity.attendanceMethodName;
  data['address_list'] = entity.addressList;
  return data;
}

extension AttendanceManagerRulesListExtension on AttendanceManagerRulesList {
  AttendanceManagerRulesList copyWith({
    String? uuid,
    String? groupName,
    String? workDayDesc,
    String? inClassDesc,
    String? attendanceMethodName,
    List<String>? addressList,
  }) {
    return AttendanceManagerRulesList()
      ..uuid = uuid ?? this.uuid
      ..groupName = groupName ?? this.groupName
      ..workDayDesc = workDayDesc ?? this.workDayDesc
      ..inClassDesc = inClassDesc ?? this.inClassDesc
      ..attendanceMethodName = attendanceMethodName ?? this.attendanceMethodName
      ..addressList = addressList ?? this.addressList;
  }
}