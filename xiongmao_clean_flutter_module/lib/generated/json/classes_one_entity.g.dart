import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_one_entity.dart';

ClassesOneEntity $ClassesOneEntityFromJson(Map<String, dynamic> json) {
  final ClassesOneEntity classesOneEntity = ClassesOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    classesOneEntity.uuid = uuid;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    classesOneEntity.projectUuid = projectUuid;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    classesOneEntity.className = className;
  }
  final String? avgLineTime = jsonConvert.convert<String>(json['avg_line_time']);
  if (avgLineTime != null) {
    classesOneEntity.avgLineTime = avgLineTime;
  }
  final String? avgLineIsToday = jsonConvert.convert<String>(json['avg_line_is_today']);
  if (avgLineIsToday != null) {
    classesOneEntity.avgLineIsToday = avgLineIsToday;
  }
  final String? isNoClock = jsonConvert.convert<String>(json['is_no_clock']);
  if (isNoClock != null) {
    classesOneEntity.isNoClock = isNoClock;
  }
  final String? classType = jsonConvert.convert<String>(json['class_type']);
  if (classType != null) {
    classesOneEntity.classType = classType;
  }
  final String? isDefaultRest = jsonConvert.convert<String>(json['is_default_rest']);
  if (isDefaultRest != null) {
    classesOneEntity.isDefaultRest = isDefaultRest;
  }
  final String? workTimeLong = jsonConvert.convert<String>(json['work_time_long']);
  if (workTimeLong != null) {
    classesOneEntity.workTimeLong = workTimeLong;
  }
  final List<ClassesOneSegmentList>? segmentList = (json['segment_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ClassesOneSegmentList>(e) as ClassesOneSegmentList).toList();
  if (segmentList != null) {
    classesOneEntity.segmentList = segmentList;
  }
  return classesOneEntity;
}

Map<String, dynamic> $ClassesOneEntityToJson(ClassesOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_uuid'] = entity.projectUuid;
  data['class_name'] = entity.className;
  data['avg_line_time'] = entity.avgLineTime;
  data['avg_line_is_today'] = entity.avgLineIsToday;
  data['is_no_clock'] = entity.isNoClock;
  data['class_type'] = entity.classType;
  data['is_default_rest'] = entity.isDefaultRest;
  data['work_time_long'] = entity.workTimeLong;
  data['segment_list'] = entity.segmentList?.map((v) => v.toJson()).toList();
  return data;
}

extension ClassesOneEntityExtension on ClassesOneEntity {
  ClassesOneEntity copyWith({
    String? uuid,
    String? projectUuid,
    String? className,
    String? avgLineTime,
    String? avgLineIsToday,
    String? isNoClock,
    String? classType,
    String? isDefaultRest,
    String? workTimeLong,
    List<ClassesOneSegmentList>? segmentList,
  }) {
    return ClassesOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectUuid = projectUuid ?? this.projectUuid
      ..className = className ?? this.className
      ..avgLineTime = avgLineTime ?? this.avgLineTime
      ..avgLineIsToday = avgLineIsToday ?? this.avgLineIsToday
      ..isNoClock = isNoClock ?? this.isNoClock
      ..classType = classType ?? this.classType
      ..isDefaultRest = isDefaultRest ?? this.isDefaultRest
      ..workTimeLong = workTimeLong ?? this.workTimeLong
      ..segmentList = segmentList ?? this.segmentList;
  }
}

ClassesOneSegmentList $ClassesOneSegmentListFromJson(Map<String, dynamic> json) {
  final ClassesOneSegmentList classesOneSegmentList = ClassesOneSegmentList();
  final String? workIsToday = jsonConvert.convert<String>(json['work_is_today']);
  if (workIsToday != null) {
    classesOneSegmentList.workIsToday = workIsToday;
  }
  final String? workStartTime = jsonConvert.convert<String>(json['work_start_time']);
  if (workStartTime != null) {
    classesOneSegmentList.workStartTime = workStartTime;
  }
  final String? workEndTime = jsonConvert.convert<String>(json['work_end_time']);
  if (workEndTime != null) {
    classesOneSegmentList.workEndTime = workEndTime;
  }
  final String? startTimeIsToday = jsonConvert.convert<String>(json['start_time_is_today']);
  if (startTimeIsToday != null) {
    classesOneSegmentList.startTimeIsToday = startTimeIsToday;
  }
  final String? endTimeIsToday = jsonConvert.convert<String>(json['end_time_is_today']);
  if (endTimeIsToday != null) {
    classesOneSegmentList.endTimeIsToday = endTimeIsToday;
  }
  final String? restStartTime = jsonConvert.convert<String>(json['rest_start_time']);
  if (restStartTime != null) {
    classesOneSegmentList.restStartTime = restStartTime;
  }
  final String? restEndTime = jsonConvert.convert<String>(json['rest_end_time']);
  if (restEndTime != null) {
    classesOneSegmentList.restEndTime = restEndTime;
  }
  final String? restStartTimeIsToday = jsonConvert.convert<String>(json['rest_start_time_is_today']);
  if (restStartTimeIsToday != null) {
    classesOneSegmentList.restStartTimeIsToday = restStartTimeIsToday;
  }
  final String? restEndTimeIsToday = jsonConvert.convert<String>(json['rest_end_time_is_today']);
  if (restEndTimeIsToday != null) {
    classesOneSegmentList.restEndTimeIsToday = restEndTimeIsToday;
  }
  final String? inClassStartType = jsonConvert.convert<String>(json['in_class_start_type']);
  if (inClassStartType != null) {
    classesOneSegmentList.inClassStartType = inClassStartType;
  }
  final String? inClassEndType = jsonConvert.convert<String>(json['in_class_end_type']);
  if (inClassEndType != null) {
    classesOneSegmentList.inClassEndType = inClassEndType;
  }
  final String? outClassStartType = jsonConvert.convert<String>(json['out_class_start_type']);
  if (outClassStartType != null) {
    classesOneSegmentList.outClassStartType = outClassStartType;
  }
  final String? outClassEndType = jsonConvert.convert<String>(json['out_class_end_type']);
  if (outClassEndType != null) {
    classesOneSegmentList.outClassEndType = outClassEndType;
  }
  return classesOneSegmentList;
}

Map<String, dynamic> $ClassesOneSegmentListToJson(ClassesOneSegmentList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['work_is_today'] = entity.workIsToday;
  data['work_start_time'] = entity.workStartTime;
  data['work_end_time'] = entity.workEndTime;
  data['start_time_is_today'] = entity.startTimeIsToday;
  data['end_time_is_today'] = entity.endTimeIsToday;
  data['rest_start_time'] = entity.restStartTime;
  data['rest_end_time'] = entity.restEndTime;
  data['rest_start_time_is_today'] = entity.restStartTimeIsToday;
  data['rest_end_time_is_today'] = entity.restEndTimeIsToday;
  data['in_class_start_type'] = entity.inClassStartType;
  data['in_class_end_type'] = entity.inClassEndType;
  data['out_class_start_type'] = entity.outClassStartType;
  data['out_class_end_type'] = entity.outClassEndType;
  return data;
}

extension ClassesOneSegmentListExtension on ClassesOneSegmentList {
  ClassesOneSegmentList copyWith({
    String? workIsToday,
    String? workStartTime,
    String? workEndTime,
    String? startTimeIsToday,
    String? endTimeIsToday,
    String? restStartTime,
    String? restEndTime,
    String? restStartTimeIsToday,
    String? restEndTimeIsToday,
    String? inClassStartType,
    String? inClassEndType,
    String? outClassStartType,
    String? outClassEndType,
  }) {
    return ClassesOneSegmentList()
      ..workIsToday = workIsToday ?? this.workIsToday
      ..workStartTime = workStartTime ?? this.workStartTime
      ..workEndTime = workEndTime ?? this.workEndTime
      ..startTimeIsToday = startTimeIsToday ?? this.startTimeIsToday
      ..endTimeIsToday = endTimeIsToday ?? this.endTimeIsToday
      ..restStartTime = restStartTime ?? this.restStartTime
      ..restEndTime = restEndTime ?? this.restEndTime
      ..restStartTimeIsToday = restStartTimeIsToday ?? this.restStartTimeIsToday
      ..restEndTimeIsToday = restEndTimeIsToday ?? this.restEndTimeIsToday
      ..inClassStartType = inClassStartType ?? this.inClassStartType
      ..inClassEndType = inClassEndType ?? this.inClassEndType
      ..outClassStartType = outClassStartType ?? this.outClassStartType
      ..outClassEndType = outClassEndType ?? this.outClassEndType;
  }
}