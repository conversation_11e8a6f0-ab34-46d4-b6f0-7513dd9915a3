import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/custom_template_data_entity.dart';

CustomTemplateDataEntity $CustomTemplateDataEntityFromJson(Map<String, dynamic> json) {
  final CustomTemplateDataEntity customTemplateDataEntity = CustomTemplateDataEntity();
  final String? key = jsonConvert.convert<String>(json['key']);
  if (key != null) {
    customTemplateDataEntity.key = key;
  }
  final List<String>? value = (json['value'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (value != null) {
    customTemplateDataEntity.value = value;
  }
  final List<CustomTemplateDataAttachmentList>? attachmentList = (json['attachment_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CustomTemplateDataAttachmentList>(e) as CustomTemplateDataAttachmentList).toList();
  if (attachmentList != null) {
    customTemplateDataEntity.attachmentList = attachmentList;
  }
  final List<List<CustomTemplateDataEntity>>? tableList = (json['table_list'] as List<dynamic>?)?.map(
          (e) =>
          (e as List<dynamic>).map(
                  (e) => jsonConvert.convert<CustomTemplateDataEntity>(e) as CustomTemplateDataEntity).toList()).toList();
  if (tableList != null) {
    customTemplateDataEntity.tableList = tableList;
  }
  return customTemplateDataEntity;
}

Map<String, dynamic> $CustomTemplateDataEntityToJson(CustomTemplateDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['key'] = entity.key;
  data['value'] = entity.value;
  data['attachment_list'] = entity.attachmentList.map((v) => v.toJson()).toList();
  data['table_list'] = entity.tableList;
  return data;
}

extension CustomTemplateDataEntityExtension on CustomTemplateDataEntity {
  CustomTemplateDataEntity copyWith({
    String? key,
    List<String>? value,
    List<CustomTemplateDataAttachmentList>? attachmentList,
    List<List<CustomTemplateDataEntity>>? tableList,
  }) {
    return CustomTemplateDataEntity()
      ..key = key ?? this.key
      ..value = value ?? this.value
      ..attachmentList = attachmentList ?? this.attachmentList
      ..tableList = tableList ?? this.tableList;
  }
}

CustomTemplateDataAttachmentList $CustomTemplateDataAttachmentListFromJson(Map<String, dynamic> json) {
  final CustomTemplateDataAttachmentList customTemplateDataAttachmentList = CustomTemplateDataAttachmentList();
  final String? fileName = jsonConvert.convert<String>(json['file_name']);
  if (fileName != null) {
    customTemplateDataAttachmentList.fileName = fileName;
  }
  final String? fileUrl = jsonConvert.convert<String>(json['file_url']);
  if (fileUrl != null) {
    customTemplateDataAttachmentList.fileUrl = fileUrl;
  }
  return customTemplateDataAttachmentList;
}

Map<String, dynamic> $CustomTemplateDataAttachmentListToJson(CustomTemplateDataAttachmentList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['file_name'] = entity.fileName;
  data['file_url'] = entity.fileUrl;
  return data;
}

extension CustomTemplateDataAttachmentListExtension on CustomTemplateDataAttachmentList {
  CustomTemplateDataAttachmentList copyWith({
    String? fileName,
    String? fileUrl,
  }) {
    return CustomTemplateDataAttachmentList()
      ..fileName = fileName ?? this.fileName
      ..fileUrl = fileUrl ?? this.fileUrl;
  }
}