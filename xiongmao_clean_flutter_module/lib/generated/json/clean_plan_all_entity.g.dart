import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_all_entity.dart';

CleanPlanAllEntity $CleanPlanAllEntityFromJson(Map<String, dynamic> json) {
  final CleanPlanAllEntity cleanPlanAllEntity = CleanPlanAllEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    cleanPlanAllEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    cleanPlanAllEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    cleanPlanAllEntity.total = total;
  }
  final List<CleanPlanAllList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanPlanAllList>(e) as CleanPlanAllList).toList();
  if (list != null) {
    cleanPlanAllEntity.list = list;
  }
  return cleanPlanAllEntity;
}

Map<String, dynamic> $CleanPlanAllEntityToJson(CleanPlanAllEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CleanPlanAllEntityExtension on CleanPlanAllEntity {
  CleanPlanAllEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<CleanPlanAllList>? list,
  }) {
    return CleanPlanAllEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

CleanPlanAllList $CleanPlanAllListFromJson(Map<String, dynamic> json) {
  final CleanPlanAllList cleanPlanAllList = CleanPlanAllList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    cleanPlanAllList.uuid = uuid;
  }
  final String? planContent = jsonConvert.convert<String>(json['plan_content']);
  if (planContent != null) {
    cleanPlanAllList.planContent = planContent;
  }
  final String? cleanAreaName = jsonConvert.convert<String>(json['clean_area_name']);
  if (cleanAreaName != null) {
    cleanPlanAllList.cleanAreaName = cleanAreaName;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    cleanPlanAllList.startDate = startDate;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    cleanPlanAllList.endDate = endDate;
  }
  final String? executeUserName = jsonConvert.convert<String>(json['execute_user_name']);
  if (executeUserName != null) {
    cleanPlanAllList.executeUserName = executeUserName;
  }
  final String? planStatusName = jsonConvert.convert<String>(json['plan_status_name']);
  if (planStatusName != null) {
    cleanPlanAllList.planStatusName = planStatusName;
  }
  final String? planStatus = jsonConvert.convert<String>(json['plan_status']);
  if (planStatus != null) {
    cleanPlanAllList.planStatus = planStatus;
  }
  return cleanPlanAllList;
}

Map<String, dynamic> $CleanPlanAllListToJson(CleanPlanAllList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['plan_content'] = entity.planContent;
  data['clean_area_name'] = entity.cleanAreaName;
  data['start_date'] = entity.startDate;
  data['end_date'] = entity.endDate;
  data['execute_user_name'] = entity.executeUserName;
  data['plan_status_name'] = entity.planStatusName;
  data['plan_status'] = entity.planStatus;
  return data;
}

extension CleanPlanAllListExtension on CleanPlanAllList {
  CleanPlanAllList copyWith({
    String? uuid,
    String? planContent,
    String? cleanAreaName,
    String? startDate,
    String? endDate,
    String? executeUserName,
    String? planStatusName,
    String? planStatus,
  }) {
    return CleanPlanAllList()
      ..uuid = uuid ?? this.uuid
      ..planContent = planContent ?? this.planContent
      ..cleanAreaName = cleanAreaName ?? this.cleanAreaName
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..executeUserName = executeUserName ?? this.executeUserName
      ..planStatusName = planStatusName ?? this.planStatusName
      ..planStatus = planStatus ?? this.planStatus;
  }
}