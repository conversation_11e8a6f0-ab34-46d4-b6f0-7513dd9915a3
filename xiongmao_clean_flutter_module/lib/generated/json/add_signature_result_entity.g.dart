import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/add_signature_result_entity.dart';

AddSignatureResultEntity $AddSignatureResultEntityFromJson(Map<String, dynamic> json) {
  final AddSignatureResultEntity addSignatureResultEntity = AddSignatureResultEntity();
  final String? signatureId = jsonConvert.convert<String>(json['signature_id']);
  if (signatureId != null) {
    addSignatureResultEntity.signatureId = signatureId;
  }
  final String? sealCdnUrl = jsonConvert.convert<String>(json['seal_cdn_url']);
  if (sealCdnUrl != null) {
    addSignatureResultEntity.sealCdnUrl = sealCdnUrl;
  }
  return addSignatureResultEntity;
}

Map<String, dynamic> $AddSignatureResultEntityToJson(AddSignatureResultEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['signature_id'] = entity.signatureId;
  data['seal_cdn_url'] = entity.sealCdnUrl;
  return data;
}

extension AddSignatureResultEntityExtension on AddSignatureResultEntity {
  AddSignatureResultEntity copyWith({
    String? signatureId,
    String? sealCdnUrl,
  }) {
    return AddSignatureResultEntity()
      ..signatureId = signatureId ?? this.signatureId
      ..sealCdnUrl = sealCdnUrl ?? this.sealCdnUrl;
  }
}