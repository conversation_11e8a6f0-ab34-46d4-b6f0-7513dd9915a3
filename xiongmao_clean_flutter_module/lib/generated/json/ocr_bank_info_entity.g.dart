import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ocr_bank_info_entity.dart';

OcrBankInfoEntity $OcrBankInfoEntityFromJson(Map<String, dynamic> json) {
  final OcrBankInfoEntity ocrBankInfoEntity = OcrBankInfoEntity();
  final int? imageStatus = jsonConvert.convert<int>(json['image_status']);
  if (imageStatus != null) {
    ocrBankInfoEntity.imageStatus = imageStatus;
  }
  final String? imageStatusName = jsonConvert.convert<String>(json['image_status_name']);
  if (imageStatusName != null) {
    ocrBankInfoEntity.imageStatusName = imageStatusName;
  }
  final String? bankCardNumber = jsonConvert.convert<String>(json['bank_card_number']);
  if (bankCardNumber != null) {
    ocrBankInfoEntity.bankCardNumber = bankCardNumber;
  }
  final String? validDate = jsonConvert.convert<String>(json['valid_date']);
  if (validDate != null) {
    ocrBankInfoEntity.validDate = validDate;
  }
  final String? bankName = jsonConvert.convert<String>(json['bank_name']);
  if (bankName != null) {
    ocrBankInfoEntity.bankName = bankName;
  }
  final String? bankCode = jsonConvert.convert<String>(json['bank_code']);
  if (bankCode != null) {
    ocrBankInfoEntity.bankCode = bankCode;
  }
  return ocrBankInfoEntity;
}

Map<String, dynamic> $OcrBankInfoEntityToJson(OcrBankInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['image_status'] = entity.imageStatus;
  data['image_status_name'] = entity.imageStatusName;
  data['bank_card_number'] = entity.bankCardNumber;
  data['valid_date'] = entity.validDate;
  data['bank_name'] = entity.bankName;
  data['bank_code'] = entity.bankCode;
  return data;
}

extension OcrBankInfoEntityExtension on OcrBankInfoEntity {
  OcrBankInfoEntity copyWith({
    int? imageStatus,
    String? imageStatusName,
    String? bankCardNumber,
    String? validDate,
    String? bankName,
    String? bankCode,
  }) {
    return OcrBankInfoEntity()
      ..imageStatus = imageStatus ?? this.imageStatus
      ..imageStatusName = imageStatusName ?? this.imageStatusName
      ..bankCardNumber = bankCardNumber ?? this.bankCardNumber
      ..validDate = validDate ?? this.validDate
      ..bankName = bankName ?? this.bankName
      ..bankCode = bankCode ?? this.bankCode;
  }
}