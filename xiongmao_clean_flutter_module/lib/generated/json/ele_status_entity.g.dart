import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ele_status_entity.dart';

EleStatusEntity $EleStatusEntityFromJson(Map<String, dynamic> json) {
  final EleStatusEntity eleStatusEntity = EleStatusEntity();
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    eleStatusEntity.status = status;
  }
  final String? tips = jsonConvert.convert<String>(json['tips']);
  if (tips != null) {
    eleStatusEntity.tips = tips;
  }
  return eleStatusEntity;
}

Map<String, dynamic> $EleStatusEntityToJson(EleStatusEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['tips'] = entity.tips;
  return data;
}

extension EleStatusEntityExtension on EleStatusEntity {
  EleStatusEntity copyWith({
    int? status,
    String? tips,
  }) {
    return EleStatusEntity()
      ..status = status ?? this.status
      ..tips = tips ?? this.tips;
  }
}