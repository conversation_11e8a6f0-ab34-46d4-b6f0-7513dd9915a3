import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/bean/work_circle_entity.dart';

WorkCircleEntity $WorkCircleEntityFromJson(Map<String, dynamic> json) {
  final WorkCircleEntity workCircleEntity = WorkCircleEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    workCircleEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    workCircleEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    workCircleEntity.total = total;
  }
  final List<WorkCircleList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkCircleList>(e) as WorkCircleList).toList();
  if (list != null) {
    workCircleEntity.list = list;
  }
  final String? latestRecordUuid = jsonConvert.convert<String>(json['latest_record_uuid']);
  if (latestRecordUuid != null) {
    workCircleEntity.latestRecordUuid = latestRecordUuid;
  }
  return workCircleEntity;
}

Map<String, dynamic> $WorkCircleEntityToJson(WorkCircleEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  data['latest_record_uuid'] = entity.latestRecordUuid;
  return data;
}

extension WorkCircleEntityExtension on WorkCircleEntity {
  WorkCircleEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<WorkCircleList>? list,
    String? latestRecordUuid,
  }) {
    return WorkCircleEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list
      ..latestRecordUuid = latestRecordUuid ?? this.latestRecordUuid;
  }
}

WorkCircleList $WorkCircleListFromJson(Map<String, dynamic> json) {
  final WorkCircleList workCircleList = WorkCircleList();
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    workCircleList.userName = userName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    workCircleList.jobName = jobName;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    workCircleList.avatar = avatar;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    workCircleList.createTime = createTime;
  }
  final List<WorkCircleListList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkCircleListList>(e) as WorkCircleListList).toList();
  if (list != null) {
    workCircleList.list = list;
  }
  return workCircleList;
}

Map<String, dynamic> $WorkCircleListToJson(WorkCircleList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['user_name'] = entity.userName;
  data['job_name'] = entity.jobName;
  data['avatar'] = entity.avatar;
  data['create_time'] = entity.createTime;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension WorkCircleListExtension on WorkCircleList {
  WorkCircleList copyWith({
    String? userName,
    String? jobName,
    String? avatar,
    String? createTime,
    List<WorkCircleListList>? list,
  }) {
    return WorkCircleList()
      ..userName = userName ?? this.userName
      ..jobName = jobName ?? this.jobName
      ..avatar = avatar ?? this.avatar
      ..createTime = createTime ?? this.createTime
      ..list = list ?? this.list;
  }
}

WorkCircleListList $WorkCircleListListFromJson(Map<String, dynamic> json) {
  final WorkCircleListList workCircleListList = WorkCircleListList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workCircleListList.uuid = uuid;
  }
  final String? actType = jsonConvert.convert<String>(json['act_type']);
  if (actType != null) {
    workCircleListList.actType = actType;
  }
  final String? actTypeName = jsonConvert.convert<String>(json['act_type_name']);
  if (actTypeName != null) {
    workCircleListList.actTypeName = actTypeName;
  }
  final String? messageType = jsonConvert.convert<String>(json['message_type']);
  if (messageType != null) {
    workCircleListList.messageType = messageType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    workCircleListList.mediaUrl = mediaUrl;
  }
  final String? picThumb = jsonConvert.convert<String>(json['pic_thumb']);
  if (picThumb != null) {
    workCircleListList.picThumb = picThumb;
  }
  final String? originMediaUrl = jsonConvert.convert<String>(json['origin_media_url']);
  if (originMediaUrl != null) {
    workCircleListList.originMediaUrl = originMediaUrl;
  }
  final String? videoCoverUrl = jsonConvert.convert<String>(json['video_cover_url']);
  if (videoCoverUrl != null) {
    workCircleListList.videoCoverUrl = videoCoverUrl;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    workCircleListList.createTime = createTime;
  }
  return workCircleListList;
}

Map<String, dynamic> $WorkCircleListListToJson(WorkCircleListList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['act_type'] = entity.actType;
  data['act_type_name'] = entity.actTypeName;
  data['message_type'] = entity.messageType;
  data['media_url'] = entity.mediaUrl;
  data['pic_thumb'] = entity.picThumb;
  data['origin_media_url'] = entity.originMediaUrl;
  data['video_cover_url'] = entity.videoCoverUrl;
  data['create_time'] = entity.createTime;
  return data;
}

extension WorkCircleListListExtension on WorkCircleListList {
  WorkCircleListList copyWith({
    String? uuid,
    String? actType,
    String? actTypeName,
    String? messageType,
    String? mediaUrl,
    String? picThumb,
    String? originMediaUrl,
    String? videoCoverUrl,
    String? createTime,
  }) {
    return WorkCircleListList()
      ..uuid = uuid ?? this.uuid
      ..actType = actType ?? this.actType
      ..actTypeName = actTypeName ?? this.actTypeName
      ..messageType = messageType ?? this.messageType
      ..mediaUrl = mediaUrl ?? this.mediaUrl
      ..picThumb = picThumb ?? this.picThumb
      ..originMediaUrl = originMediaUrl ?? this.originMediaUrl
      ..videoCoverUrl = videoCoverUrl ?? this.videoCoverUrl
      ..createTime = createTime ?? this.createTime;
  }
}