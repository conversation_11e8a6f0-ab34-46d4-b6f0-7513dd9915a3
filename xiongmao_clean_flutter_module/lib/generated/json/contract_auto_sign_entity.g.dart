import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_auto_sign_entity.dart';

ContractAutoSignEntity $ContractAutoSignEntityFromJson(Map<String, dynamic> json) {
  final ContractAutoSignEntity contractAutoSignEntity = ContractAutoSignEntity();
  final String? transactionNo = jsonConvert.convert<String>(json['transactionNo']);
  if (transactionNo != null) {
    contractAutoSignEntity.transactionNo = transactionNo;
  }
  final String? authUrl = jsonConvert.convert<String>(json['authUrl']);
  if (authUrl != null) {
    contractAutoSignEntity.authUrl = authUrl;
  }
  return contractAutoSignEntity;
}

Map<String, dynamic> $ContractAutoSignEntityToJson(ContractAutoSignEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['transactionNo'] = entity.transactionNo;
  data['authUrl'] = entity.authUrl;
  return data;
}

extension ContractAutoSignEntityExtension on ContractAutoSignEntity {
  ContractAutoSignEntity copyWith({
    String? transactionNo,
    String? authUrl,
  }) {
    return ContractAutoSignEntity()
      ..transactionNo = transactionNo ?? this.transactionNo
      ..authUrl = authUrl ?? this.authUrl;
  }
}