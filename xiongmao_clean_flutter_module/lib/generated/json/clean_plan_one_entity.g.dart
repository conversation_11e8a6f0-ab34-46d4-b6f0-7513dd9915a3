import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/clean_plan_one_entity.dart';

CleanPlanOneEntity $CleanPlanOneEntityFromJson(Map<String, dynamic> json) {
  final CleanPlanOneEntity cleanPlanOneEntity = CleanPlanOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    cleanPlanOneEntity.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    cleanPlanOneEntity.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    cleanPlanOneEntity.projectShortName = projectShortName;
  }
  final String? planContent = jsonConvert.convert<String>(json['plan_content']);
  if (planContent != null) {
    cleanPlanOneEntity.planContent = planContent;
  }
  final String? cleanAreaUuid = jsonConvert.convert<String>(json['clean_area_uuid']);
  if (cleanAreaUuid != null) {
    cleanPlanOneEntity.cleanAreaUuid = cleanAreaUuid;
  }
  final String? cleanAreaName = jsonConvert.convert<String>(json['clean_area_name']);
  if (cleanAreaName != null) {
    cleanPlanOneEntity.cleanAreaName = cleanAreaName;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    cleanPlanOneEntity.startDate = startDate;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    cleanPlanOneEntity.endDate = endDate;
  }
  final String? startDateName = jsonConvert.convert<String>(json['start_date_name']);
  if (startDateName != null) {
    cleanPlanOneEntity.startDateName = startDateName;
  }
  final String? endDateName = jsonConvert.convert<String>(json['end_date_name']);
  if (endDateName != null) {
    cleanPlanOneEntity.endDateName = endDateName;
  }
  final String? executeUserUuid = jsonConvert.convert<String>(json['execute_user_uuid']);
  if (executeUserUuid != null) {
    cleanPlanOneEntity.executeUserUuid = executeUserUuid;
  }
  final String? executeUserName = jsonConvert.convert<String>(json['execute_user_name']);
  if (executeUserName != null) {
    cleanPlanOneEntity.executeUserName = executeUserName;
  }
  final String? planStatusName = jsonConvert.convert<String>(json['plan_status_name']);
  if (planStatusName != null) {
    cleanPlanOneEntity.planStatusName = planStatusName;
  }
  final String? planStatus = jsonConvert.convert<String>(json['plan_status']);
  if (planStatus != null) {
    cleanPlanOneEntity.planStatus = planStatus;
  }
  final String? loopType = jsonConvert.convert<String>(json['loop_type']);
  if (loopType != null) {
    cleanPlanOneEntity.loopType = loopType;
  }
  final List<CleanPlanOneMediaList>? mediaList = (json['media_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CleanPlanOneMediaList>(e) as CleanPlanOneMediaList).toList();
  if (mediaList != null) {
    cleanPlanOneEntity.mediaList = mediaList;
  }
  return cleanPlanOneEntity;
}

Map<String, dynamic> $CleanPlanOneEntityToJson(CleanPlanOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  data['plan_content'] = entity.planContent;
  data['clean_area_uuid'] = entity.cleanAreaUuid;
  data['clean_area_name'] = entity.cleanAreaName;
  data['start_date'] = entity.startDate;
  data['end_date'] = entity.endDate;
  data['start_date_name'] = entity.startDateName;
  data['end_date_name'] = entity.endDateName;
  data['execute_user_uuid'] = entity.executeUserUuid;
  data['execute_user_name'] = entity.executeUserName;
  data['plan_status_name'] = entity.planStatusName;
  data['plan_status'] = entity.planStatus;
  data['loop_type'] = entity.loopType;
  data['media_list'] = entity.mediaList?.map((v) => v.toJson()).toList();
  return data;
}

extension CleanPlanOneEntityExtension on CleanPlanOneEntity {
  CleanPlanOneEntity copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
    String? planContent,
    String? cleanAreaUuid,
    String? cleanAreaName,
    String? startDate,
    String? endDate,
    String? startDateName,
    String? endDateName,
    String? executeUserUuid,
    String? executeUserName,
    String? planStatusName,
    String? planStatus,
    String? loopType,
    List<CleanPlanOneMediaList>? mediaList,
  }) {
    return CleanPlanOneEntity()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..planContent = planContent ?? this.planContent
      ..cleanAreaUuid = cleanAreaUuid ?? this.cleanAreaUuid
      ..cleanAreaName = cleanAreaName ?? this.cleanAreaName
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..startDateName = startDateName ?? this.startDateName
      ..endDateName = endDateName ?? this.endDateName
      ..executeUserUuid = executeUserUuid ?? this.executeUserUuid
      ..executeUserName = executeUserName ?? this.executeUserName
      ..planStatusName = planStatusName ?? this.planStatusName
      ..planStatus = planStatus ?? this.planStatus
      ..loopType = loopType ?? this.loopType
      ..mediaList = mediaList ?? this.mediaList;
  }
}

CleanPlanOneMediaList $CleanPlanOneMediaListFromJson(Map<String, dynamic> json) {
  final CleanPlanOneMediaList cleanPlanOneMediaList = CleanPlanOneMediaList();
  final String? mediaType = jsonConvert.convert<String>(json['media_type']);
  if (mediaType != null) {
    cleanPlanOneMediaList.mediaType = mediaType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    cleanPlanOneMediaList.mediaUrl = mediaUrl;
  }
  return cleanPlanOneMediaList;
}

Map<String, dynamic> $CleanPlanOneMediaListToJson(CleanPlanOneMediaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['media_type'] = entity.mediaType;
  data['media_url'] = entity.mediaUrl;
  return data;
}

extension CleanPlanOneMediaListExtension on CleanPlanOneMediaList {
  CleanPlanOneMediaList copyWith({
    String? mediaType,
    String? mediaUrl,
  }) {
    return CleanPlanOneMediaList()
      ..mediaType = mediaType ?? this.mediaType
      ..mediaUrl = mediaUrl ?? this.mediaUrl;
  }
}