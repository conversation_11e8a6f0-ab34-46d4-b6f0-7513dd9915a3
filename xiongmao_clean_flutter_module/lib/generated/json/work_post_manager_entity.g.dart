import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';

WorkPostManagerEntity $WorkPostManagerEntityFromJson(Map<String, dynamic> json) {
  final WorkPostManagerEntity workPostManagerEntity = WorkPostManagerEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    workPostManagerEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    workPostManagerEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    workPostManagerEntity.total = total;
  }
  final List<WorkPostManagerList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkPostManagerList>(e) as WorkPostManagerList).toList();
  if (list != null) {
    workPostManagerEntity.list = list;
  }
  return workPostManagerEntity;
}

Map<String, dynamic> $WorkPostManagerEntityToJson(WorkPostManagerEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension WorkPostManagerEntityExtension on WorkPostManagerEntity {
  WorkPostManagerEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<WorkPostManagerList>? list,
  }) {
    return WorkPostManagerEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

WorkPostManagerList $WorkPostManagerListFromJson(Map<String, dynamic> json) {
  final WorkPostManagerList workPostManagerList = WorkPostManagerList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workPostManagerList.uuid = uuid;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    workPostManagerList.jobName = jobName;
  }
  final String? jobSalary = jsonConvert.convert<String>(json['job_salary']);
  if (jobSalary != null) {
    workPostManagerList.jobSalary = jobSalary;
  }
  final int? isSystem = jsonConvert.convert<int>(json['is_system']);
  if (isSystem != null) {
    workPostManagerList.isSystem = isSystem;
  }
  return workPostManagerList;
}

Map<String, dynamic> $WorkPostManagerListToJson(WorkPostManagerList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['job_name'] = entity.jobName;
  data['job_salary'] = entity.jobSalary;
  data['is_system'] = entity.isSystem;
  return data;
}

extension WorkPostManagerListExtension on WorkPostManagerList {
  WorkPostManagerList copyWith({
    String? uuid,
    String? jobName,
    String? jobSalary,
    int? isSystem,
  }) {
    return WorkPostManagerList()
      ..uuid = uuid ?? this.uuid
      ..jobName = jobName ?? this.jobName
      ..jobSalary = jobSalary ?? this.jobSalary
      ..isSystem = isSystem ?? this.isSystem;
  }
}