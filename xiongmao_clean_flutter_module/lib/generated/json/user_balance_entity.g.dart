import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';

UserBalanceEntity $UserBalanceEntityFromJson(Map<String, dynamic> json) {
  final UserBalanceEntity userBalanceEntity = UserBalanceEntity();
  final String? balance = jsonConvert.convert<String>(json['balance']);
  if (balance != null) {
    userBalanceEntity.balance = balance;
  }
  final String? totalBalance = jsonConvert.convert<String>(json['total_balance']);
  if (totalBalance != null) {
    userBalanceEntity.totalBalance = totalBalance;
  }
  final String? frozenBalance = jsonConvert.convert<String>(json['frozen_balance']);
  if (frozenBalance != null) {
    userBalanceEntity.frozenBalance = frozenBalance;
  }
  return userBalanceEntity;
}

Map<String, dynamic> $UserBalanceEntityToJson(UserBalanceEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['balance'] = entity.balance;
  data['total_balance'] = entity.totalBalance;
  data['frozen_balance'] = entity.frozenBalance;
  return data;
}

extension UserBalanceEntityExtension on UserBalanceEntity {
  UserBalanceEntity copyWith({
    String? balance,
    String? totalBalance,
    String? frozenBalance,
  }) {
    return UserBalanceEntity()
      ..balance = balance ?? this.balance
      ..totalBalance = totalBalance ?? this.totalBalance
      ..frozenBalance = frozenBalance ?? this.frozenBalance;
  }
}