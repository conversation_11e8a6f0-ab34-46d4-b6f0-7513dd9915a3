import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_auth_entity.dart';

CheckIdNumberAuthEntity $CheckIdNumberAuthEntityFromJson(Map<String, dynamic> json) {
  final CheckIdNumberAuthEntity checkIdNumberAuthEntity = CheckIdNumberAuthEntity();
  final int? isAuth = jsonConvert.convert<int>(json['is_auth']);
  if (isAuth != null) {
    checkIdNumberAuthEntity.isAuth = isAuth;
  }
  return checkIdNumberAuthEntity;
}

Map<String, dynamic> $CheckIdNumberAuthEntityToJson(CheckIdNumberAuthEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['is_auth'] = entity.isAuth;
  return data;
}

extension CheckIdNumberAuthEntityExtension on CheckIdNumberAuthEntity {
  CheckIdNumberAuthEntity copyWith({
    int? isAuth,
  }) {
    return CheckIdNumberAuthEntity()
      ..isAuth = isAuth ?? this.isAuth;
  }
}