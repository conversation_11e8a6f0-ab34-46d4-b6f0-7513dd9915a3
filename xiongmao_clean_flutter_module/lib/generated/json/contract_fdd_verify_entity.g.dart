import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_fdd_verify_entity.dart';

ContractFddVerifyEntity $ContractFddVerifyEntityFromJson(Map<String, dynamic> json) {
  final ContractFddVerifyEntity contractFddVerifyEntity = ContractFddVerifyEntity();
  final String? transactionNo = jsonConvert.convert<String>(json['transactionNo']);
  if (transactionNo != null) {
    contractFddVerifyEntity.transactionNo = transactionNo;
  }
  final String? url = jsonConvert.convert<String>(json['url']);
  if (url != null) {
    contractFddVerifyEntity.url = url;
  }
  return contractFddVerifyEntity;
}

Map<String, dynamic> $ContractFddVerifyEntityToJson(ContractFddVerifyEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['transactionNo'] = entity.transactionNo;
  data['url'] = entity.url;
  return data;
}

extension ContractFddVerifyEntityExtension on ContractFddVerifyEntity {
  ContractFddVerifyEntity copyWith({
    String? transactionNo,
    String? url,
  }) {
    return ContractFddVerifyEntity()
      ..transactionNo = transactionNo ?? this.transactionNo
      ..url = url ?? this.url;
  }
}