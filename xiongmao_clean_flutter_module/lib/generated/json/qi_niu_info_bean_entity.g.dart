import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/qi_niu_info_bean_entity.dart';

QiNiuInfoBeanEntity $QiNiuInfoBeanEntityFromJson(Map<String, dynamic> json) {
  final QiNiuInfoBeanEntity qiNiuInfoBeanEntity = QiNiuInfoBeanEntity();
  final String? bucket = jsonConvert.convert<String>(json['bucket']);
  if (bucket != null) {
    qiNiuInfoBeanEntity.bucket = bucket;
  }
  final String? token = jsonConvert.convert<String>(json['token']);
  if (token != null) {
    qiNiuInfoBeanEntity.token = token;
  }
  return qiNiuInfoBeanEntity;
}

Map<String, dynamic> $QiNiuInfoBeanEntityToJson(QiNiuInfoBeanEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['bucket'] = entity.bucket;
  data['token'] = entity.token;
  return data;
}

extension QiNiuInfoBeanEntityExtension on QiNiuInfoBeanEntity {
  QiNiuInfoBeanEntity copyWith({
    String? bucket,
    String? token,
  }) {
    return QiNiuInfoBeanEntity()
      ..bucket = bucket ?? this.bucket
      ..token = token ?? this.token;
  }
}