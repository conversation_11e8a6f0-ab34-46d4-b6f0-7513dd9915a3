import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/account_code_entity.dart';

AccountCodeEntity $AccountCodeEntityFromJson(Map<String, dynamic> json) {
  final AccountCodeEntity accountCodeEntity = AccountCodeEntity();
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    accountCodeEntity.code = code;
  }
  return accountCodeEntity;
}

Map<String, dynamic> $AccountCodeEntityToJson(AccountCodeEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['code'] = entity.code;
  return data;
}

extension AccountCodeEntityExtension on AccountCodeEntity {
  AccountCodeEntity copyWith({
    String? code,
  }) {
    return AccountCodeEntity()
      ..code = code ?? this.code;
  }
}