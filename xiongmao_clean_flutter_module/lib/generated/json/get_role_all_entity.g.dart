import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_role_all_entity.dart';

GetRoleAllEntity $GetRoleAllEntityFromJson(Map<String, dynamic> json) {
  final GetRoleAllEntity getRoleAllEntity = GetRoleAllEntity();
  final List<GetRoleAllList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<GetRoleAllList>(e) as GetRoleAllList).toList();
  if (list != null) {
    getRoleAllEntity.list = list;
  }
  return getRoleAllEntity;
}

Map<String, dynamic> $GetRoleAllEntityToJson(GetRoleAllEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension GetRoleAllEntityExtension on GetRoleAllEntity {
  GetRoleAllEntity copyWith({
    List<GetRoleAllList>? list,
  }) {
    return GetRoleAllEntity()
      ..list = list ?? this.list;
  }
}

GetRoleAllList $GetRoleAllListFromJson(Map<String, dynamic> json) {
  final GetRoleAllList getRoleAllList = GetRoleAllList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    getRoleAllList.id = id;
  }
  final String? name = jsonConvert.convert<String>(json['name']);
  if (name != null) {
    getRoleAllList.name = name;
  }
  final String? roleDesc = jsonConvert.convert<String>(json['role_desc']);
  if (roleDesc != null) {
    getRoleAllList.roleDesc = roleDesc;
  }
  return getRoleAllList;
}

Map<String, dynamic> $GetRoleAllListToJson(GetRoleAllList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['name'] = entity.name;
  data['role_desc'] = entity.roleDesc;
  return data;
}

extension GetRoleAllListExtension on GetRoleAllList {
  GetRoleAllList copyWith({
    String? id,
    String? name,
    String? roleDesc,
  }) {
    return GetRoleAllList()
      ..id = id ?? this.id
      ..name = name ?? this.name
      ..roleDesc = roleDesc ?? this.roleDesc;
  }
}