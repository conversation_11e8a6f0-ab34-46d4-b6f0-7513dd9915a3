import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/bean/project_archives_entity.dart';

ProjectArchivesEntity $ProjectArchivesEntityFromJson(Map<String, dynamic> json) {
  final ProjectArchivesEntity projectArchivesEntity = ProjectArchivesEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    projectArchivesEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    projectArchivesEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    projectArchivesEntity.total = total;
  }
  final List<ProjectArchivesList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ProjectArchivesList>(e) as ProjectArchivesList).toList();
  if (list != null) {
    projectArchivesEntity.list = list;
  }
  return projectArchivesEntity;
}

Map<String, dynamic> $ProjectArchivesEntityToJson(ProjectArchivesEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ProjectArchivesEntityExtension on ProjectArchivesEntity {
  ProjectArchivesEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ProjectArchivesList>? list,
  }) {
    return ProjectArchivesEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ProjectArchivesList $ProjectArchivesListFromJson(Map<String, dynamic> json) {
  final ProjectArchivesList projectArchivesList = ProjectArchivesList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    projectArchivesList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    projectArchivesList.userName = userName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    projectArchivesList.projectShortName = projectShortName;
  }
  final String? reasonName = jsonConvert.convert<String>(json['reason_name']);
  if (reasonName != null) {
    projectArchivesList.reasonName = reasonName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    projectArchivesList.jobName = jobName;
  }
  final String? age = jsonConvert.convert<String>(json['age']);
  if (age != null) {
    projectArchivesList.age = age;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    projectArchivesList.avatar = avatar;
  }
  final String? workAge = jsonConvert.convert<String>(json['work_age']);
  if (workAge != null) {
    projectArchivesList.workAge = workAge;
  }
  final String? idNumber = jsonConvert.convert<String>(json['id_number']);
  if (idNumber != null) {
    projectArchivesList.idNumber = idNumber;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    projectArchivesList.mobile = mobile;
  }
  final String? isHasIdCard = jsonConvert.convert<String>(json['is_has_id_card']);
  if (isHasIdCard != null) {
    projectArchivesList.isHasIdCard = isHasIdCard;
  }
  final String? isHasContract = jsonConvert.convert<String>(json['is_has_contract']);
  if (isHasContract != null) {
    projectArchivesList.isHasContract = isHasContract;
  }
  final String? isHasBankCard = jsonConvert.convert<String>(json['is_has_bank_card']);
  if (isHasBankCard != null) {
    projectArchivesList.isHasBankCard = isHasBankCard;
  }
  final String? isHasHealthyCard = jsonConvert.convert<String>(json['is_has_healthy_card']);
  if (isHasHealthyCard != null) {
    projectArchivesList.isHasHealthyCard = isHasHealthyCard;
  }
  final String? isHeadOffice = jsonConvert.convert<String>(json['is_head_office']);
  if (isHeadOffice != null) {
    projectArchivesList.isHeadOffice = isHeadOffice;
  }
  final bool? isSelected = jsonConvert.convert<bool>(json['is_selected']);
  if (isSelected != null) {
    projectArchivesList.isSelected = isSelected;
  }
  final List<String>? roleList = (json['role_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (roleList != null) {
    projectArchivesList.roleList = roleList;
  }
  return projectArchivesList;
}

Map<String, dynamic> $ProjectArchivesListToJson(ProjectArchivesList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['project_short_name'] = entity.projectShortName;
  data['reason_name'] = entity.reasonName;
  data['job_name'] = entity.jobName;
  data['age'] = entity.age;
  data['avatar'] = entity.avatar;
  data['work_age'] = entity.workAge;
  data['id_number'] = entity.idNumber;
  data['mobile'] = entity.mobile;
  data['is_has_id_card'] = entity.isHasIdCard;
  data['is_has_contract'] = entity.isHasContract;
  data['is_has_bank_card'] = entity.isHasBankCard;
  data['is_has_healthy_card'] = entity.isHasHealthyCard;
  data['is_head_office'] = entity.isHeadOffice;
  data['is_selected'] = entity.isSelected;
  data['role_list'] = entity.roleList;
  return data;
}

extension ProjectArchivesListExtension on ProjectArchivesList {
  ProjectArchivesList copyWith({
    String? uuid,
    String? userName,
    String? projectShortName,
    String? reasonName,
    String? jobName,
    String? age,
    String? avatar,
    String? workAge,
    String? idNumber,
    String? mobile,
    String? isHasIdCard,
    String? isHasContract,
    String? isHasBankCard,
    String? isHasHealthyCard,
    String? isHeadOffice,
    bool? isSelected,
    List<String>? roleList,
  }) {
    return ProjectArchivesList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..reasonName = reasonName ?? this.reasonName
      ..jobName = jobName ?? this.jobName
      ..age = age ?? this.age
      ..avatar = avatar ?? this.avatar
      ..workAge = workAge ?? this.workAge
      ..idNumber = idNumber ?? this.idNumber
      ..mobile = mobile ?? this.mobile
      ..isHasIdCard = isHasIdCard ?? this.isHasIdCard
      ..isHasContract = isHasContract ?? this.isHasContract
      ..isHasBankCard = isHasBankCard ?? this.isHasBankCard
      ..isHasHealthyCard = isHasHealthyCard ?? this.isHasHealthyCard
      ..isHeadOffice = isHeadOffice ?? this.isHeadOffice
      ..isSelected = isSelected ?? this.isSelected
      ..roleList = roleList ?? this.roleList;
  }
}