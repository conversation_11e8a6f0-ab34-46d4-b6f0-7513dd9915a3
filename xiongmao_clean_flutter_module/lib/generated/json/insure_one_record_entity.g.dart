import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_one_record_entity.dart';

InsureOneRecordEntity $InsureOneRecordEntityFromJson(Map<String, dynamic> json) {
  final InsureOneRecordEntity insureOneRecordEntity = InsureOneRecordEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    insureOneRecordEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    insureOneRecordEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    insureOneRecordEntity.total = total;
  }
  final List<InsureOneRecordList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsureOneRecordList>(e) as InsureOneRecordList).toList();
  if (list != null) {
    insureOneRecordEntity.list = list;
  }
  return insureOneRecordEntity;
}

Map<String, dynamic> $InsureOneRecordEntityToJson(InsureOneRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureOneRecordEntityExtension on InsureOneRecordEntity {
  InsureOneRecordEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<InsureOneRecordList>? list,
  }) {
    return InsureOneRecordEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

InsureOneRecordList $InsureOneRecordListFromJson(Map<String, dynamic> json) {
  final InsureOneRecordList insureOneRecordList = InsureOneRecordList();
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    insureOneRecordList.remark = remark;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    insureOneRecordList.createTime = createTime;
  }
  return insureOneRecordList;
}

Map<String, dynamic> $InsureOneRecordListToJson(InsureOneRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['remark'] = entity.remark;
  data['create_time'] = entity.createTime;
  return data;
}

extension InsureOneRecordListExtension on InsureOneRecordList {
  InsureOneRecordList copyWith({
    String? remark,
    String? createTime,
  }) {
    return InsureOneRecordList()
      ..remark = remark ?? this.remark
      ..createTime = createTime ?? this.createTime;
  }
}