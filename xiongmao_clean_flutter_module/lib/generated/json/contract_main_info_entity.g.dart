import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';

ContractMainInfoEntity $ContractMainInfoEntityFromJson(Map<String, dynamic> json) {
  final ContractMainInfoEntity contractMainInfoEntity = ContractMainInfoEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractMainInfoEntity.id = id;
  }
  final String? companyId = jsonConvert.convert<String>(json['company_id']);
  if (companyId != null) {
    contractMainInfoEntity.companyId = companyId;
  }
  final String? customerId = jsonConvert.convert<String>(json['customer_id']);
  if (customerId != null) {
    contractMainInfoEntity.customerId = customerId;
  }
  final String? customerIdCard = jsonConvert.convert<String>(json['customer_id_card']);
  if (customerIdCard != null) {
    contractMainInfoEntity.customerIdCard = customerIdCard;
  }
  final String? customerName = jsonConvert.convert<String>(json['customer_name']);
  if (customerName != null) {
    contractMainInfoEntity.customerName = customerName;
  }
  final String? customerMobile = jsonConvert.convert<String>(json['customer_mobile']);
  if (customerMobile != null) {
    contractMainInfoEntity.customerMobile = customerMobile;
  }
  final String? customerType = jsonConvert.convert<String>(json['customer_type']);
  if (customerType != null) {
    contractMainInfoEntity.customerType = customerType;
  }
  final String? customerStatus = jsonConvert.convert<String>(json['customer_status']);
  if (customerStatus != null) {
    contractMainInfoEntity.customerStatus = customerStatus;
  }
  final ContractMainInfoCert? cert = jsonConvert.convert<ContractMainInfoCert>(json['cert']);
  if (cert != null) {
    contractMainInfoEntity.cert = cert;
  }
  final String? sealCdnUrl = jsonConvert.convert<String>(json['seal_cdn_url']);
  if (sealCdnUrl != null) {
    contractMainInfoEntity.sealCdnUrl = sealCdnUrl;
  }
  final String? sealAutoSignStatus = jsonConvert.convert<String>(json['seal_auto_sign_status']);
  if (sealAutoSignStatus != null) {
    contractMainInfoEntity.sealAutoSignStatus = sealAutoSignStatus;
  }
  final ContractMainInfoSignSetting? signSetting = jsonConvert.convert<ContractMainInfoSignSetting>(json['sign_setting']);
  if (signSetting != null) {
    contractMainInfoEntity.signSetting = signSetting;
  }
  final String? contractCount = jsonConvert.convert<String>(json['contract_count']);
  if (contractCount != null) {
    contractMainInfoEntity.contractCount = contractCount;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractMainInfoEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractMainInfoEntity.updateTime = updateTime;
  }
  final String? sealNo = jsonConvert.convert<String>(json['seal_no']);
  if (sealNo != null) {
    contractMainInfoEntity.sealNo = sealNo;
  }
  return contractMainInfoEntity;
}

Map<String, dynamic> $ContractMainInfoEntityToJson(ContractMainInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['company_id'] = entity.companyId;
  data['customer_id'] = entity.customerId;
  data['customer_id_card'] = entity.customerIdCard;
  data['customer_name'] = entity.customerName;
  data['customer_mobile'] = entity.customerMobile;
  data['customer_type'] = entity.customerType;
  data['customer_status'] = entity.customerStatus;
  data['cert'] = entity.cert?.toJson();
  data['seal_cdn_url'] = entity.sealCdnUrl;
  data['seal_auto_sign_status'] = entity.sealAutoSignStatus;
  data['sign_setting'] = entity.signSetting?.toJson();
  data['contract_count'] = entity.contractCount;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  data['seal_no'] = entity.sealNo;
  return data;
}

extension ContractMainInfoEntityExtension on ContractMainInfoEntity {
  ContractMainInfoEntity copyWith({
    String? id,
    String? companyId,
    String? customerId,
    String? customerIdCard,
    String? customerName,
    String? customerMobile,
    String? customerType,
    String? customerStatus,
    ContractMainInfoCert? cert,
    String? sealCdnUrl,
    String? sealAutoSignStatus,
    ContractMainInfoSignSetting? signSetting,
    String? contractCount,
    String? createTime,
    String? updateTime,
    String? sealNo,
  }) {
    return ContractMainInfoEntity()
      ..id = id ?? this.id
      ..companyId = companyId ?? this.companyId
      ..customerId = customerId ?? this.customerId
      ..customerIdCard = customerIdCard ?? this.customerIdCard
      ..customerName = customerName ?? this.customerName
      ..customerMobile = customerMobile ?? this.customerMobile
      ..customerType = customerType ?? this.customerType
      ..customerStatus = customerStatus ?? this.customerStatus
      ..cert = cert ?? this.cert
      ..sealCdnUrl = sealCdnUrl ?? this.sealCdnUrl
      ..sealAutoSignStatus = sealAutoSignStatus ?? this.sealAutoSignStatus
      ..signSetting = signSetting ?? this.signSetting
      ..contractCount = contractCount ?? this.contractCount
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..sealNo = sealNo ?? this.sealNo;
  }
}

ContractMainInfoCert $ContractMainInfoCertFromJson(Map<String, dynamic> json) {
  final ContractMainInfoCert contractMainInfoCert = ContractMainInfoCert();
  final String? companyName = jsonConvert.convert<String>(json['company_name']);
  if (companyName != null) {
    contractMainInfoCert.companyName = companyName;
  }
  final String? organization = jsonConvert.convert<String>(json['organization']);
  if (organization != null) {
    contractMainInfoCert.organization = organization;
  }
  final String? customerName = jsonConvert.convert<String>(json['customer_name']);
  if (customerName != null) {
    contractMainInfoCert.customerName = customerName;
  }
  final String? customerMobile = jsonConvert.convert<String>(json['customer_mobile']);
  if (customerMobile != null) {
    contractMainInfoCert.customerMobile = customerMobile;
  }
  final String? customerIdCard = jsonConvert.convert<String>(json['customer_id_card']);
  if (customerIdCard != null) {
    contractMainInfoCert.customerIdCard = customerIdCard;
  }
  return contractMainInfoCert;
}

Map<String, dynamic> $ContractMainInfoCertToJson(ContractMainInfoCert entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['company_name'] = entity.companyName;
  data['organization'] = entity.organization;
  data['customer_name'] = entity.customerName;
  data['customer_mobile'] = entity.customerMobile;
  data['customer_id_card'] = entity.customerIdCard;
  return data;
}

extension ContractMainInfoCertExtension on ContractMainInfoCert {
  ContractMainInfoCert copyWith({
    String? companyName,
    String? organization,
    String? customerName,
    String? customerMobile,
    String? customerIdCard,
  }) {
    return ContractMainInfoCert()
      ..companyName = companyName ?? this.companyName
      ..organization = organization ?? this.organization
      ..customerName = customerName ?? this.customerName
      ..customerMobile = customerMobile ?? this.customerMobile
      ..customerIdCard = customerIdCard ?? this.customerIdCard;
  }
}

ContractMainInfoSignSetting $ContractMainInfoSignSettingFromJson(Map<String, dynamic> json) {
  final ContractMainInfoSignSetting contractMainInfoSignSetting = ContractMainInfoSignSetting();
  final ContractMainInfoSignSettingLabelEnabledStatus? labelEnabledStatus = jsonConvert.convert<ContractMainInfoSignSettingLabelEnabledStatus>(json['label_enabled_status']);
  if (labelEnabledStatus != null) {
    contractMainInfoSignSetting.labelEnabledStatus = labelEnabledStatus;
  }
  final ContractMainInfoSignSettingDefaultSignLabelCode? defaultSignLabelCode = jsonConvert.convert<ContractMainInfoSignSettingDefaultSignLabelCode>(json['default_sign_label_code']);
  if (defaultSignLabelCode != null) {
    contractMainInfoSignSetting.defaultSignLabelCode = defaultSignLabelCode;
  }
  return contractMainInfoSignSetting;
}

Map<String, dynamic> $ContractMainInfoSignSettingToJson(ContractMainInfoSignSetting entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['label_enabled_status'] = entity.labelEnabledStatus?.toJson();
  data['default_sign_label_code'] = entity.defaultSignLabelCode?.toJson();
  return data;
}

extension ContractMainInfoSignSettingExtension on ContractMainInfoSignSetting {
  ContractMainInfoSignSetting copyWith({
    ContractMainInfoSignSettingLabelEnabledStatus? labelEnabledStatus,
    ContractMainInfoSignSettingDefaultSignLabelCode? defaultSignLabelCode,
  }) {
    return ContractMainInfoSignSetting()
      ..labelEnabledStatus = labelEnabledStatus ?? this.labelEnabledStatus
      ..defaultSignLabelCode = defaultSignLabelCode ?? this.defaultSignLabelCode;
  }
}

ContractMainInfoSignSettingLabelEnabledStatus $ContractMainInfoSignSettingLabelEnabledStatusFromJson(Map<String, dynamic> json) {
  final ContractMainInfoSignSettingLabelEnabledStatus contractMainInfoSignSettingLabelEnabledStatus = ContractMainInfoSignSettingLabelEnabledStatus();
  final bool? entryContract = jsonConvert.convert<bool>(json['EntryContract']);
  if (entryContract != null) {
    contractMainInfoSignSettingLabelEnabledStatus.entryContract = entryContract;
  }
  final bool? separationContract = jsonConvert.convert<bool>(json['SeparationContract']);
  if (separationContract != null) {
    contractMainInfoSignSettingLabelEnabledStatus.separationContract = separationContract;
  }
  return contractMainInfoSignSettingLabelEnabledStatus;
}

Map<String, dynamic> $ContractMainInfoSignSettingLabelEnabledStatusToJson(ContractMainInfoSignSettingLabelEnabledStatus entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EntryContract'] = entity.entryContract;
  data['SeparationContract'] = entity.separationContract;
  return data;
}

extension ContractMainInfoSignSettingLabelEnabledStatusExtension on ContractMainInfoSignSettingLabelEnabledStatus {
  ContractMainInfoSignSettingLabelEnabledStatus copyWith({
    bool? entryContract,
    bool? separationContract,
  }) {
    return ContractMainInfoSignSettingLabelEnabledStatus()
      ..entryContract = entryContract ?? this.entryContract
      ..separationContract = separationContract ?? this.separationContract;
  }
}

ContractMainInfoSignSettingDefaultSignLabelCode $ContractMainInfoSignSettingDefaultSignLabelCodeFromJson(Map<String, dynamic> json) {
  final ContractMainInfoSignSettingDefaultSignLabelCode contractMainInfoSignSettingDefaultSignLabelCode = ContractMainInfoSignSettingDefaultSignLabelCode();
  final String? entrycontractRehire = jsonConvert.convert<String>(json['EntryContract_Rehire']);
  if (entrycontractRehire != null) {
    contractMainInfoSignSettingDefaultSignLabelCode.entrycontractRehire = entrycontractRehire;
  }
  final String? entrycontractService = jsonConvert.convert<String>(json['EntryContract_Service']);
  if (entrycontractService != null) {
    contractMainInfoSignSettingDefaultSignLabelCode.entrycontractService = entrycontractService;
  }
  final String? separationcontractAgreement = jsonConvert.convert<String>(json['SeparationContract_Agreement']);
  if (separationcontractAgreement != null) {
    contractMainInfoSignSettingDefaultSignLabelCode.separationcontractAgreement = separationcontractAgreement;
  }
  return contractMainInfoSignSettingDefaultSignLabelCode;
}

Map<String, dynamic> $ContractMainInfoSignSettingDefaultSignLabelCodeToJson(ContractMainInfoSignSettingDefaultSignLabelCode entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['EntryContract_Rehire'] = entity.entrycontractRehire;
  data['EntryContract_Service'] = entity.entrycontractService;
  data['SeparationContract_Agreement'] = entity.separationcontractAgreement;
  return data;
}

extension ContractMainInfoSignSettingDefaultSignLabelCodeExtension on ContractMainInfoSignSettingDefaultSignLabelCode {
  ContractMainInfoSignSettingDefaultSignLabelCode copyWith({
    String? entrycontractRehire,
    String? entrycontractService,
    String? separationcontractAgreement,
  }) {
    return ContractMainInfoSignSettingDefaultSignLabelCode()
      ..entrycontractRehire = entrycontractRehire ?? this.entrycontractRehire
      ..entrycontractService = entrycontractService ?? this.entrycontractService
      ..separationcontractAgreement = separationcontractAgreement ?? this.separationcontractAgreement;
  }
}