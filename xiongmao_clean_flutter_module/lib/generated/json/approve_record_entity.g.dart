import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_record_entity.dart';

ApproveRecordEntity $ApproveRecordEntityFromJson(Map<String, dynamic> json) {
  final ApproveRecordEntity approveRecordEntity = ApproveRecordEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    approveRecordEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    approveRecordEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    approveRecordEntity.total = total;
  }
  final List<ApproveRecordList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveRecordList>(e) as ApproveRecordList).toList();
  if (list != null) {
    approveRecordEntity.list = list;
  }
  return approveRecordEntity;
}

Map<String, dynamic> $ApproveRecordEntityToJson(ApproveRecordEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveRecordEntityExtension on ApproveRecordEntity {
  ApproveRecordEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<ApproveRecordList>? list,
  }) {
    return ApproveRecordEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

ApproveRecordList $ApproveRecordListFromJson(Map<String, dynamic> json) {
  final ApproveRecordList approveRecordList = ApproveRecordList();
  final String? applicationTitle = jsonConvert.convert<String>(json['application_title']);
  if (applicationTitle != null) {
    approveRecordList.applicationTitle = applicationTitle;
  }
  final String? applicationNo = jsonConvert.convert<String>(json['application_no']);
  if (applicationNo != null) {
    approveRecordList.applicationNo = applicationNo;
  }
  final String? applicationType = jsonConvert.convert<String>(json['application_type']);
  if (applicationType != null) {
    approveRecordList.applicationType = applicationType;
  }
  final String? applicationStatus = jsonConvert.convert<String>(json['application_status']);
  if (applicationStatus != null) {
    approveRecordList.applicationStatus = applicationStatus;
  }
  final String? applicationStatusName = jsonConvert.convert<String>(json['application_status_name']);
  if (applicationStatusName != null) {
    approveRecordList.applicationStatusName = applicationStatusName;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    approveRecordList.createTime = createTime;
  }
  final List<ApproveRecordListSummaryList>? summaryList = (json['summary_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveRecordListSummaryList>(e) as ApproveRecordListSummaryList).toList();
  if (summaryList != null) {
    approveRecordList.summaryList = summaryList;
  }
  final int? taskId = jsonConvert.convert<int>(json['task_id']);
  if (taskId != null) {
    approveRecordList.taskId = taskId;
  }
  final String? readStatus = jsonConvert.convert<String>(json['read_status']);
  if (readStatus != null) {
    approveRecordList.readStatus = readStatus;
  }
  final String? templateUuid = jsonConvert.convert<String>(json['template_uuid']);
  if (templateUuid != null) {
    approveRecordList.templateUuid = templateUuid;
  }
  final String? templateType = jsonConvert.convert<String>(json['template_type']);
  if (templateType != null) {
    approveRecordList.templateType = templateType;
  }
  final String? isSystem = jsonConvert.convert<String>(json['is_system']);
  if (isSystem != null) {
    approveRecordList.isSystem = isSystem;
  }
  final String? templateName = jsonConvert.convert<String>(json['template_name']);
  if (templateName != null) {
    approveRecordList.templateName = templateName;
  }
  return approveRecordList;
}

Map<String, dynamic> $ApproveRecordListToJson(ApproveRecordList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['application_title'] = entity.applicationTitle;
  data['application_no'] = entity.applicationNo;
  data['application_type'] = entity.applicationType;
  data['application_status'] = entity.applicationStatus;
  data['application_status_name'] = entity.applicationStatusName;
  data['create_time'] = entity.createTime;
  data['summary_list'] = entity.summaryList?.map((v) => v.toJson()).toList();
  data['task_id'] = entity.taskId;
  data['read_status'] = entity.readStatus;
  data['template_uuid'] = entity.templateUuid;
  data['template_type'] = entity.templateType;
  data['is_system'] = entity.isSystem;
  data['template_name'] = entity.templateName;
  return data;
}

extension ApproveRecordListExtension on ApproveRecordList {
  ApproveRecordList copyWith({
    String? applicationTitle,
    String? applicationNo,
    String? applicationType,
    String? applicationStatus,
    String? applicationStatusName,
    String? createTime,
    List<ApproveRecordListSummaryList>? summaryList,
    int? taskId,
    String? readStatus,
    String? templateUuid,
    String? templateType,
    String? isSystem,
    String? templateName,
  }) {
    return ApproveRecordList()
      ..applicationTitle = applicationTitle ?? this.applicationTitle
      ..applicationNo = applicationNo ?? this.applicationNo
      ..applicationType = applicationType ?? this.applicationType
      ..applicationStatus = applicationStatus ?? this.applicationStatus
      ..applicationStatusName = applicationStatusName ?? this.applicationStatusName
      ..createTime = createTime ?? this.createTime
      ..summaryList = summaryList ?? this.summaryList
      ..taskId = taskId ?? this.taskId
      ..readStatus = readStatus ?? this.readStatus
      ..templateUuid = templateUuid ?? this.templateUuid
      ..templateType = templateType ?? this.templateType
      ..isSystem = isSystem ?? this.isSystem
      ..templateName = templateName ?? this.templateName;
  }
}

ApproveRecordListSummaryList $ApproveRecordListSummaryListFromJson(Map<String, dynamic> json) {
  final ApproveRecordListSummaryList approveRecordListSummaryList = ApproveRecordListSummaryList();
  final String? item = jsonConvert.convert<String>(json['item']);
  if (item != null) {
    approveRecordListSummaryList.item = item;
  }
  final String? content = jsonConvert.convert<String>(json['content']);
  if (content != null) {
    approveRecordListSummaryList.content = content;
  }
  return approveRecordListSummaryList;
}

Map<String, dynamic> $ApproveRecordListSummaryListToJson(ApproveRecordListSummaryList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['item'] = entity.item;
  data['content'] = entity.content;
  return data;
}

extension ApproveRecordListSummaryListExtension on ApproveRecordListSummaryList {
  ApproveRecordListSummaryList copyWith({
    String? item,
    String? content,
  }) {
    return ApproveRecordListSummaryList()
      ..item = item ?? this.item
      ..content = content ?? this.content;
  }
}