import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';

WorkRulesConfigEntity $WorkRulesConfigEntityFromJson(Map<String, dynamic> json) {
  final WorkRulesConfigEntity workRulesConfigEntity = WorkRulesConfigEntity();
  final String? maxAge = jsonConvert.convert<String>(json['max_age']);
  if (maxAge != null) {
    workRulesConfigEntity.maxAge = maxAge;
  }
  final String? isEntryBeforeToday = jsonConvert.convert<String>(json['is_entry_before_today']);
  if (isEntryBeforeToday != null) {
    workRulesConfigEntity.isEntryBeforeToday = isEntryBeforeToday;
  }
  final String? isUploadNotGuilty = jsonConvert.convert<String>(json['is_upload_not_guilty']);
  if (isUploadNotGuilty != null) {
    workRulesConfigEntity.isUploadNotGuilty = isUploadNotGuilty;
  }
  final String? isEntrySign = jsonConvert.convert<String>(json['is_entry_sign']);
  if (isEntrySign != null) {
    workRulesConfigEntity.isEntrySign = isEntrySign;
  }
  final String? payrollDate = jsonConvert.convert<String>(json['payroll_date']);
  if (payrollDate != null) {
    workRulesConfigEntity.payrollDate = payrollDate;
  }
  final String? clockInRange = jsonConvert.convert<String>(json['clock_in_range']);
  if (clockInRange != null) {
    workRulesConfigEntity.clockInRange = clockInRange;
  }
  final String? isSeeKq = jsonConvert.convert<String>(json['is_see_kq']);
  if (isSeeKq != null) {
    workRulesConfigEntity.isSeeKq = isSeeKq;
  }
  final String? isSeePhoto = jsonConvert.convert<String>(json['is_see_photo']);
  if (isSeePhoto != null) {
    workRulesConfigEntity.isSeePhoto = isSeePhoto;
  }
  final String? isMinimalism = jsonConvert.convert<String>(json['is_minimalism']);
  if (isMinimalism != null) {
    workRulesConfigEntity.isMinimalism = isMinimalism;
  }
  final String? scheduleDate = jsonConvert.convert<String>(json['schedule_date']);
  if (scheduleDate != null) {
    workRulesConfigEntity.scheduleDate = scheduleDate;
  }
  final String? isSavePhotoMobile = jsonConvert.convert<String>(json['is_save_photo_mobile']);
  if (isSavePhotoMobile != null) {
    workRulesConfigEntity.isSavePhotoMobile = isSavePhotoMobile;
  }
  final String? isOpenWorkPhoto = jsonConvert.convert<String>(json['is_open_work_photo']);
  if (isOpenWorkPhoto != null) {
    workRulesConfigEntity.isOpenWorkPhoto = isOpenWorkPhoto;
  }
  final String? isDkConfirm = jsonConvert.convert<String>(json['is_dk_confirm']);
  if (isDkConfirm != null) {
    workRulesConfigEntity.isDkConfirm = isDkConfirm;
  }
  final String? headOfficeMode = jsonConvert.convert<String>(json['head_office_mode']);
  if (headOfficeMode != null) {
    workRulesConfigEntity.headOfficeMode = headOfficeMode;
  }
  final List<WorkRulesConfigProjectList>? projectList = (json['project_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkRulesConfigProjectList>(e) as WorkRulesConfigProjectList).toList();
  if (projectList != null) {
    workRulesConfigEntity.projectList = projectList;
  }
  final List<WorkRulesConfigRoleAccess>? roleAccess = (json['role_access'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkRulesConfigRoleAccess>(e) as WorkRulesConfigRoleAccess).toList();
  if (roleAccess != null) {
    workRulesConfigEntity.roleAccess = roleAccess;
  }
  return workRulesConfigEntity;
}

Map<String, dynamic> $WorkRulesConfigEntityToJson(WorkRulesConfigEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['max_age'] = entity.maxAge;
  data['is_entry_before_today'] = entity.isEntryBeforeToday;
  data['is_upload_not_guilty'] = entity.isUploadNotGuilty;
  data['is_entry_sign'] = entity.isEntrySign;
  data['payroll_date'] = entity.payrollDate;
  data['clock_in_range'] = entity.clockInRange;
  data['is_see_kq'] = entity.isSeeKq;
  data['is_see_photo'] = entity.isSeePhoto;
  data['is_minimalism'] = entity.isMinimalism;
  data['schedule_date'] = entity.scheduleDate;
  data['is_save_photo_mobile'] = entity.isSavePhotoMobile;
  data['is_open_work_photo'] = entity.isOpenWorkPhoto;
  data['is_dk_confirm'] = entity.isDkConfirm;
  data['head_office_mode'] = entity.headOfficeMode;
  data['project_list'] = entity.projectList?.map((v) => v.toJson()).toList();
  data['role_access'] = entity.roleAccess?.map((v) => v.toJson()).toList();
  return data;
}

extension WorkRulesConfigEntityExtension on WorkRulesConfigEntity {
  WorkRulesConfigEntity copyWith({
    String? maxAge,
    String? isEntryBeforeToday,
    String? isUploadNotGuilty,
    String? isEntrySign,
    String? payrollDate,
    String? clockInRange,
    String? isSeeKq,
    String? isSeePhoto,
    String? isMinimalism,
    String? scheduleDate,
    String? isSavePhotoMobile,
    String? isOpenWorkPhoto,
    String? isDkConfirm,
    String? headOfficeMode,
    List<WorkRulesConfigProjectList>? projectList,
    List<WorkRulesConfigRoleAccess>? roleAccess,
  }) {
    return WorkRulesConfigEntity()
      ..maxAge = maxAge ?? this.maxAge
      ..isEntryBeforeToday = isEntryBeforeToday ?? this.isEntryBeforeToday
      ..isUploadNotGuilty = isUploadNotGuilty ?? this.isUploadNotGuilty
      ..isEntrySign = isEntrySign ?? this.isEntrySign
      ..payrollDate = payrollDate ?? this.payrollDate
      ..clockInRange = clockInRange ?? this.clockInRange
      ..isSeeKq = isSeeKq ?? this.isSeeKq
      ..isSeePhoto = isSeePhoto ?? this.isSeePhoto
      ..isMinimalism = isMinimalism ?? this.isMinimalism
      ..scheduleDate = scheduleDate ?? this.scheduleDate
      ..isSavePhotoMobile = isSavePhotoMobile ?? this.isSavePhotoMobile
      ..isOpenWorkPhoto = isOpenWorkPhoto ?? this.isOpenWorkPhoto
      ..isDkConfirm = isDkConfirm ?? this.isDkConfirm
      ..headOfficeMode = headOfficeMode ?? this.headOfficeMode
      ..projectList = projectList ?? this.projectList
      ..roleAccess = roleAccess ?? this.roleAccess;
  }
}

WorkRulesConfigProjectList $WorkRulesConfigProjectListFromJson(Map<String, dynamic> json) {
  final WorkRulesConfigProjectList workRulesConfigProjectList = WorkRulesConfigProjectList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workRulesConfigProjectList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    workRulesConfigProjectList.projectName = projectName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    workRulesConfigProjectList.projectShortName = projectShortName;
  }
  return workRulesConfigProjectList;
}

Map<String, dynamic> $WorkRulesConfigProjectListToJson(WorkRulesConfigProjectList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_short_name'] = entity.projectShortName;
  return data;
}

extension WorkRulesConfigProjectListExtension on WorkRulesConfigProjectList {
  WorkRulesConfigProjectList copyWith({
    String? uuid,
    String? projectName,
    String? projectShortName,
  }) {
    return WorkRulesConfigProjectList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectShortName = projectShortName ?? this.projectShortName;
  }
}

WorkRulesConfigRoleAccess $WorkRulesConfigRoleAccessFromJson(Map<String, dynamic> json) {
  final WorkRulesConfigRoleAccess workRulesConfigRoleAccess = WorkRulesConfigRoleAccess();
  final String? roleId = jsonConvert.convert<String>(json['role_id']);
  if (roleId != null) {
    workRulesConfigRoleAccess.roleId = roleId;
  }
  final String? accessType = jsonConvert.convert<String>(json['access_type']);
  if (accessType != null) {
    workRulesConfigRoleAccess.accessType = accessType;
  }
  return workRulesConfigRoleAccess;
}

Map<String, dynamic> $WorkRulesConfigRoleAccessToJson(WorkRulesConfigRoleAccess entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['role_id'] = entity.roleId;
  data['access_type'] = entity.accessType;
  return data;
}

extension WorkRulesConfigRoleAccessExtension on WorkRulesConfigRoleAccess {
  WorkRulesConfigRoleAccess copyWith({
    String? roleId,
    String? accessType,
  }) {
    return WorkRulesConfigRoleAccess()
      ..roleId = roleId ?? this.roleId
      ..accessType = accessType ?? this.accessType;
  }
}