import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';

ApproveDetailEntity $ApproveDetailEntityFromJson(Map<String, dynamic> json) {
  final ApproveDetailEntity approveDetailEntity = ApproveDetailEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveDetailEntity.uuid = uuid;
  }
  final String? templateName = jsonConvert.convert<String>(json['template_name']);
  if (templateName != null) {
    approveDetailEntity.templateName = templateName;
  }
  final List<ApproveDetailList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveDetailList>(e) as ApproveDetailList).toList();
  if (list != null) {
    approveDetailEntity.list = list;
  }
  return approveDetailEntity;
}

Map<String, dynamic> $ApproveDetailEntityToJson(ApproveDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['template_name'] = entity.templateName;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ApproveDetailEntityExtension on ApproveDetailEntity {
  ApproveDetailEntity copyWith({
    String? uuid,
    String? templateName,
    List<ApproveDetailList>? list,
  }) {
    return ApproveDetailEntity()
      ..uuid = uuid ?? this.uuid
      ..templateName = templateName ?? this.templateName
      ..list = list ?? this.list;
  }
}

ApproveDetailList $ApproveDetailListFromJson(Map<String, dynamic> json) {
  final ApproveDetailList approveDetailList = ApproveDetailList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveDetailList.uuid = uuid;
  }
  final String? nodeName = jsonConvert.convert<String>(json['node_name']);
  if (nodeName != null) {
    approveDetailList.nodeName = nodeName;
  }
  final String? subNodeName = jsonConvert.convert<String>(json['sub_node_name']);
  if (subNodeName != null) {
    approveDetailList.subNodeName = subNodeName;
  }
  final String? nodeType = jsonConvert.convert<String>(json['node_type']);
  if (nodeType != null) {
    approveDetailList.nodeType = nodeType;
  }
  final String? approverType = jsonConvert.convert<String>(json['approver_type']);
  if (approverType != null) {
    approveDetailList.approverType = approverType;
  }
  final String? isCosigned = jsonConvert.convert<String>(json['is_cosigned']);
  if (isCosigned != null) {
    approveDetailList.isCosigned = isCosigned;
  }
  final String? superiorsLevel = jsonConvert.convert<String>(json['superiors_level']);
  if (superiorsLevel != null) {
    approveDetailList.superiorsLevel = superiorsLevel;
  }
  final String? superiorsLevelName = jsonConvert.convert<String>(json['superiors_level_name']);
  if (superiorsLevelName != null) {
    approveDetailList.superiorsLevelName = superiorsLevelName;
  }
  final String? roleId = jsonConvert.convert<String>(json['role_id']);
  if (roleId != null) {
    approveDetailList.roleId = roleId;
  }
  final String? roleName = jsonConvert.convert<String>(json['role_name']);
  if (roleName != null) {
    approveDetailList.roleName = roleName;
  }
  final List<ApproveDetailListUserList>? userList = (json['user_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ApproveDetailListUserList>(e) as ApproveDetailListUserList).toList();
  if (userList != null) {
    approveDetailList.userList = userList;
  }
  final List<String>? userIdList = (json['user_id_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (userIdList != null) {
    approveDetailList.userIdList = userIdList;
  }
  return approveDetailList;
}

Map<String, dynamic> $ApproveDetailListToJson(ApproveDetailList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['node_name'] = entity.nodeName;
  data['sub_node_name'] = entity.subNodeName;
  data['node_type'] = entity.nodeType;
  data['approver_type'] = entity.approverType;
  data['is_cosigned'] = entity.isCosigned;
  data['superiors_level'] = entity.superiorsLevel;
  data['superiors_level_name'] = entity.superiorsLevelName;
  data['role_id'] = entity.roleId;
  data['role_name'] = entity.roleName;
  data['user_list'] = entity.userList?.map((v) => v.toJson()).toList();
  data['user_id_list'] = entity.userIdList;
  return data;
}

extension ApproveDetailListExtension on ApproveDetailList {
  ApproveDetailList copyWith({
    String? uuid,
    String? nodeName,
    String? subNodeName,
    String? nodeType,
    String? approverType,
    String? isCosigned,
    String? superiorsLevel,
    String? superiorsLevelName,
    String? roleId,
    String? roleName,
    List<ApproveDetailListUserList>? userList,
    List<String>? userIdList,
  }) {
    return ApproveDetailList()
      ..uuid = uuid ?? this.uuid
      ..nodeName = nodeName ?? this.nodeName
      ..subNodeName = subNodeName ?? this.subNodeName
      ..nodeType = nodeType ?? this.nodeType
      ..approverType = approverType ?? this.approverType
      ..isCosigned = isCosigned ?? this.isCosigned
      ..superiorsLevel = superiorsLevel ?? this.superiorsLevel
      ..superiorsLevelName = superiorsLevelName ?? this.superiorsLevelName
      ..roleId = roleId ?? this.roleId
      ..roleName = roleName ?? this.roleName
      ..userList = userList ?? this.userList
      ..userIdList = userIdList ?? this.userIdList;
  }
}

ApproveDetailListUserList $ApproveDetailListUserListFromJson(Map<String, dynamic> json) {
  final ApproveDetailListUserList approveDetailListUserList = ApproveDetailListUserList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    approveDetailListUserList.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    approveDetailListUserList.userName = userName;
  }
  return approveDetailListUserList;
}

Map<String, dynamic> $ApproveDetailListUserListToJson(ApproveDetailListUserList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  return data;
}

extension ApproveDetailListUserListExtension on ApproveDetailListUserList {
  ApproveDetailListUserList copyWith({
    String? uuid,
    String? userName,
  }) {
    return ApproveDetailListUserList()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName;
  }
}