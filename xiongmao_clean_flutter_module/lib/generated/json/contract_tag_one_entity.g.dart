import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import '../../../../widgets/select_tab/select_tab_data.dart';


ContractTagOneEntity $ContractTagOneEntityFromJson(Map<String, dynamic> json) {
  final ContractTagOneEntity contractTagOneEntity = ContractTagOneEntity();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractTagOneEntity.id = id;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    contractTagOneEntity.pid = pid;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    contractTagOneEntity.code = code;
  }
  final String? labelName = jsonConvert.convert<String>(json['label_name']);
  if (labelName != null) {
    contractTagOneEntity.labelName = labelName;
  }
  final String? isDelete = jsonConvert.convert<String>(json['is_delete']);
  if (isDelete != null) {
    contractTagOneEntity.isDelete = isDelete;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractTagOneEntity.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractTagOneEntity.updateTime = updateTime;
  }
  final List<ContractTagOneSubLabelList>? subLabelList = (json['sub_label_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractTagOneSubLabelList>(e) as ContractTagOneSubLabelList).toList();
  if (subLabelList != null) {
    contractTagOneEntity.subLabelList = subLabelList;
  }
  return contractTagOneEntity;
}

Map<String, dynamic> $ContractTagOneEntityToJson(ContractTagOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['pid'] = entity.pid;
  data['code'] = entity.code;
  data['label_name'] = entity.labelName;
  data['is_delete'] = entity.isDelete;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  data['sub_label_list'] = entity.subLabelList?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractTagOneEntityExtension on ContractTagOneEntity {
  ContractTagOneEntity copyWith({
    String? id,
    String? pid,
    String? code,
    String? labelName,
    String? isDelete,
    String? createTime,
    String? updateTime,
    List<ContractTagOneSubLabelList>? subLabelList,
  }) {
    return ContractTagOneEntity()
      ..id = id ?? this.id
      ..pid = pid ?? this.pid
      ..code = code ?? this.code
      ..labelName = labelName ?? this.labelName
      ..isDelete = isDelete ?? this.isDelete
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime
      ..subLabelList = subLabelList ?? this.subLabelList;
  }
}

ContractTagOneSubLabelList $ContractTagOneSubLabelListFromJson(Map<String, dynamic> json) {
  final ContractTagOneSubLabelList contractTagOneSubLabelList = ContractTagOneSubLabelList();
  final String? id = jsonConvert.convert<String>(json['id']);
  if (id != null) {
    contractTagOneSubLabelList.id = id;
  }
  final String? pid = jsonConvert.convert<String>(json['pid']);
  if (pid != null) {
    contractTagOneSubLabelList.pid = pid;
  }
  final String? code = jsonConvert.convert<String>(json['code']);
  if (code != null) {
    contractTagOneSubLabelList.code = code;
  }
  final String? labelName = jsonConvert.convert<String>(json['label_name']);
  if (labelName != null) {
    contractTagOneSubLabelList.labelName = labelName;
  }
  final String? isDelete = jsonConvert.convert<String>(json['is_delete']);
  if (isDelete != null) {
    contractTagOneSubLabelList.isDelete = isDelete;
  }
  final String? createTime = jsonConvert.convert<String>(json['create_time']);
  if (createTime != null) {
    contractTagOneSubLabelList.createTime = createTime;
  }
  final String? updateTime = jsonConvert.convert<String>(json['update_time']);
  if (updateTime != null) {
    contractTagOneSubLabelList.updateTime = updateTime;
  }
  return contractTagOneSubLabelList;
}

Map<String, dynamic> $ContractTagOneSubLabelListToJson(ContractTagOneSubLabelList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['id'] = entity.id;
  data['pid'] = entity.pid;
  data['code'] = entity.code;
  data['label_name'] = entity.labelName;
  data['is_delete'] = entity.isDelete;
  data['create_time'] = entity.createTime;
  data['update_time'] = entity.updateTime;
  return data;
}

extension ContractTagOneSubLabelListExtension on ContractTagOneSubLabelList {
  ContractTagOneSubLabelList copyWith({
    String? id,
    String? pid,
    String? code,
    String? labelName,
    String? isDelete,
    String? createTime,
    String? updateTime,
  }) {
    return ContractTagOneSubLabelList()
      ..id = id ?? this.id
      ..pid = pid ?? this.pid
      ..code = code ?? this.code
      ..labelName = labelName ?? this.labelName
      ..isDelete = isDelete ?? this.isDelete
      ..createTime = createTime ?? this.createTime
      ..updateTime = updateTime ?? this.updateTime;
  }
}