import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_scheme_data_entity.dart';

InsureSchemeDataEntity $InsureSchemeDataEntityFromJson(Map<String, dynamic> json) {
  final InsureSchemeDataEntity insureSchemeDataEntity = InsureSchemeDataEntity();
  final List<InsureSchemeDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsureSchemeDataList>(e) as InsureSchemeDataList).toList();
  if (list != null) {
    insureSchemeDataEntity.list = list;
  }
  return insureSchemeDataEntity;
}

Map<String, dynamic> $InsureSchemeDataEntityToJson(InsureSchemeDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureSchemeDataEntityExtension on InsureSchemeDataEntity {
  InsureSchemeDataEntity copyWith({
    List<InsureSchemeDataList>? list,
  }) {
    return InsureSchemeDataEntity()
      ..list = list ?? this.list;
  }
}

InsureSchemeDataList $InsureSchemeDataListFromJson(Map<String, dynamic> json) {
  final InsureSchemeDataList insureSchemeDataList = InsureSchemeDataList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    insureSchemeDataList.uuid = uuid;
  }
  final String? productName = jsonConvert.convert<String>(json['product_name']);
  if (productName != null) {
    insureSchemeDataList.productName = productName;
  }
  final String? insuranceCompany = jsonConvert.convert<String>(json['insurance_company']);
  if (insuranceCompany != null) {
    insureSchemeDataList.insuranceCompany = insuranceCompany;
  }
  final String? insuranceNo = jsonConvert.convert<String>(json['insurance_no']);
  if (insuranceNo != null) {
    insureSchemeDataList.insuranceNo = insuranceNo;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    insureSchemeDataList.introduction = introduction;
  }
  final String? coverUrl = jsonConvert.convert<String>(json['cover_url']);
  if (coverUrl != null) {
    insureSchemeDataList.coverUrl = coverUrl;
  }
  final String? descriptionUrl = jsonConvert.convert<String>(json['description_url']);
  if (descriptionUrl != null) {
    insureSchemeDataList.descriptionUrl = descriptionUrl;
  }
  final List<InsureSchemeDataListSkuList>? skuList = (json['sku_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<InsureSchemeDataListSkuList>(e) as InsureSchemeDataListSkuList).toList();
  if (skuList != null) {
    insureSchemeDataList.skuList = skuList;
  }
  return insureSchemeDataList;
}

Map<String, dynamic> $InsureSchemeDataListToJson(InsureSchemeDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['product_name'] = entity.productName;
  data['insurance_company'] = entity.insuranceCompany;
  data['insurance_no'] = entity.insuranceNo;
  data['introduction'] = entity.introduction;
  data['cover_url'] = entity.coverUrl;
  data['description_url'] = entity.descriptionUrl;
  data['sku_list'] = entity.skuList?.map((v) => v.toJson()).toList();
  return data;
}

extension InsureSchemeDataListExtension on InsureSchemeDataList {
  InsureSchemeDataList copyWith({
    String? uuid,
    String? productName,
    String? insuranceCompany,
    String? insuranceNo,
    String? introduction,
    String? coverUrl,
    String? descriptionUrl,
    List<InsureSchemeDataListSkuList>? skuList,
  }) {
    return InsureSchemeDataList()
      ..uuid = uuid ?? this.uuid
      ..productName = productName ?? this.productName
      ..insuranceCompany = insuranceCompany ?? this.insuranceCompany
      ..insuranceNo = insuranceNo ?? this.insuranceNo
      ..introduction = introduction ?? this.introduction
      ..coverUrl = coverUrl ?? this.coverUrl
      ..descriptionUrl = descriptionUrl ?? this.descriptionUrl
      ..skuList = skuList ?? this.skuList;
  }
}

InsureSchemeDataListSkuList $InsureSchemeDataListSkuListFromJson(Map<String, dynamic> json) {
  final InsureSchemeDataListSkuList insureSchemeDataListSkuList = InsureSchemeDataListSkuList();
  final String? unit = jsonConvert.convert<String>(json['unit']);
  if (unit != null) {
    insureSchemeDataListSkuList.unit = unit;
  }
  final String? num = jsonConvert.convert<String>(json['num']);
  if (num != null) {
    insureSchemeDataListSkuList.num = num;
  }
  final String? skuType = jsonConvert.convert<String>(json['sku_type']);
  if (skuType != null) {
    insureSchemeDataListSkuList.skuType = skuType;
  }
  final String? money = jsonConvert.convert<String>(json['money']);
  if (money != null) {
    insureSchemeDataListSkuList.money = money;
  }
  return insureSchemeDataListSkuList;
}

Map<String, dynamic> $InsureSchemeDataListSkuListToJson(InsureSchemeDataListSkuList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['unit'] = entity.unit;
  data['num'] = entity.num;
  data['sku_type'] = entity.skuType;
  data['money'] = entity.money;
  return data;
}

extension InsureSchemeDataListSkuListExtension on InsureSchemeDataListSkuList {
  InsureSchemeDataListSkuList copyWith({
    String? unit,
    String? num,
    String? skuType,
    String? money,
  }) {
    return InsureSchemeDataListSkuList()
      ..unit = unit ?? this.unit
      ..num = num ?? this.num
      ..skuType = skuType ?? this.skuType
      ..money = money ?? this.money;
  }
}