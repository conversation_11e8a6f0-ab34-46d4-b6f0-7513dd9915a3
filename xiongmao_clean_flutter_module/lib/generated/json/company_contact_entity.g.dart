import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';

CompanyContactEntity $CompanyContactEntityFromJson(Map<String, dynamic> json) {
  final CompanyContactEntity companyContactEntity = CompanyContactEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    companyContactEntity.uuid = uuid;
  }
  final String? user_name = jsonConvert.convert<String>(json['user_name']);
  if (user_name != null) {
    companyContactEntity.user_name = user_name;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    companyContactEntity.mobile = mobile;
  }
  final String? role_id = jsonConvert.convert<String>(json['role_id']);
  if (role_id != null) {
    companyContactEntity.role_id = role_id;
  }
  final String? role_name = jsonConvert.convert<String>(json['role_name']);
  if (role_name != null) {
    companyContactEntity.role_name = role_name;
  }
  final String? is_super_admin = jsonConvert.convert<String>(json['is_super_admin']);
  if (is_super_admin != null) {
    companyContactEntity.is_super_admin = is_super_admin;
  }
  final String? department_uuid = jsonConvert.convert<String>(json['department_uuid']);
  if (department_uuid != null) {
    companyContactEntity.department_uuid = department_uuid;
  }
  final String? department_name = jsonConvert.convert<String>(json['department_name']);
  if (department_name != null) {
    companyContactEntity.department_name = department_name;
  }
  return companyContactEntity;
}

Map<String, dynamic> $CompanyContactEntityToJson(CompanyContactEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.user_name;
  data['mobile'] = entity.mobile;
  data['role_id'] = entity.role_id;
  data['role_name'] = entity.role_name;
  data['is_super_admin'] = entity.is_super_admin;
  data['department_uuid'] = entity.department_uuid;
  data['department_name'] = entity.department_name;
  return data;
}

extension CompanyContactEntityExtension on CompanyContactEntity {
  CompanyContactEntity copyWith({
    String? uuid,
    String? user_name,
    String? mobile,
    String? role_id,
    String? role_name,
    String? is_super_admin,
    String? department_uuid,
    String? department_name,
  }) {
    return CompanyContactEntity()
      ..uuid = uuid ?? this.uuid
      ..user_name = user_name ?? this.user_name
      ..mobile = mobile ?? this.mobile
      ..role_id = role_id ?? this.role_id
      ..role_name = role_name ?? this.role_name
      ..is_super_admin = is_super_admin ?? this.is_super_admin
      ..department_uuid = department_uuid ?? this.department_uuid
      ..department_name = department_name ?? this.department_name;
  }
}