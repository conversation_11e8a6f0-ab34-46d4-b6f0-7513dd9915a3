import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';

BaseUuidEntity $BaseUuidEntityFromJson(Map<String, dynamic> json) {
  final BaseUuidEntity baseUuidEntity = BaseUuidEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    baseUuidEntity.uuid = uuid;
  }
  return baseUuidEntity;
}

Map<String, dynamic> $BaseUuidEntityToJson(BaseUuidEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  return data;
}

extension BaseUuidEntityExtension on BaseUuidEntity {
  BaseUuidEntity copyWith({
    String? uuid,
  }) {
    return BaseUuidEntity()
      ..uuid = uuid ?? this.uuid;
  }
}