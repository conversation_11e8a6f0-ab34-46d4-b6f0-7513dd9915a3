import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_info_entity.dart';

ContractInfoEntity $ContractInfoEntityFromJson(Map<String, dynamic> json) {
  final ContractInfoEntity contractInfoEntity = ContractInfoEntity();
  final List<ContractInfoList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<ContractInfoList>(e) as ContractInfoList).toList();
  if (list != null) {
    contractInfoEntity.list = list;
  }
  return contractInfoEntity;
}

Map<String, dynamic> $ContractInfoEntityToJson(ContractInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension ContractInfoEntityExtension on ContractInfoEntity {
  ContractInfoEntity copyWith({
    List<ContractInfoList>? list,
  }) {
    return ContractInfoEntity()
      ..list = list ?? this.list;
  }
}

ContractInfoList $ContractInfoListFromJson(Map<String, dynamic> json) {
  final ContractInfoList contractInfoList = ContractInfoList();
  final String? number = jsonConvert.convert<String>(json['number']);
  if (number != null) {
    contractInfoList.number = number;
  }
  final String? unitPrice = jsonConvert.convert<String>(json['unit_price']);
  if (unitPrice != null) {
    contractInfoList.unitPrice = unitPrice;
  }
  final String? price = jsonConvert.convert<String>(json['price']);
  if (price != null) {
    contractInfoList.price = price;
  }
  final String? amount = jsonConvert.convert<String>(json['amount']);
  if (amount != null) {
    contractInfoList.amount = amount;
  }
  final String? isDefault = jsonConvert.convert<String>(json['is_default']);
  if (isDefault != null) {
    contractInfoList.isDefault = isDefault;
  }
  return contractInfoList;
}

Map<String, dynamic> $ContractInfoListToJson(ContractInfoList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['number'] = entity.number;
  data['unit_price'] = entity.unitPrice;
  data['price'] = entity.price;
  data['amount'] = entity.amount;
  data['is_default'] = entity.isDefault;
  return data;
}

extension ContractInfoListExtension on ContractInfoList {
  ContractInfoList copyWith({
    String? number,
    String? unitPrice,
    String? price,
    String? amount,
    String? isDefault,
  }) {
    return ContractInfoList()
      ..number = number ?? this.number
      ..unitPrice = unitPrice ?? this.unitPrice
      ..price = price ?? this.price
      ..amount = amount ?? this.amount
      ..isDefault = isDefault ?? this.isDefault;
  }
}