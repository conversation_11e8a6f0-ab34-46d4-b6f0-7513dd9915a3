import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_contract_entity.dart';

DepartmentCompanyContractEntity $DepartmentCompanyContractEntityFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyContractEntity departmentCompanyContractEntity = DepartmentCompanyContractEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    departmentCompanyContractEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    departmentCompanyContractEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    departmentCompanyContractEntity.total = total;
  }
  final List<DepartmentCompanyContractList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<DepartmentCompanyContractList>(e) as DepartmentCompanyContractList).toList();
  if (list != null) {
    departmentCompanyContractEntity.list = list;
  }
  return departmentCompanyContractEntity;
}

Map<String, dynamic> $DepartmentCompanyContractEntityToJson(DepartmentCompanyContractEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension DepartmentCompanyContractEntityExtension on DepartmentCompanyContractEntity {
  DepartmentCompanyContractEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<DepartmentCompanyContractList>? list,
  }) {
    return DepartmentCompanyContractEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

DepartmentCompanyContractList $DepartmentCompanyContractListFromJson(Map<String, dynamic> json) {
  final DepartmentCompanyContractList departmentCompanyContractList = DepartmentCompanyContractList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    departmentCompanyContractList.uuid = uuid;
  }
  final String? companyName = jsonConvert.convert<String>(json['company_name']);
  if (companyName != null) {
    departmentCompanyContractList.companyName = companyName;
  }
  final String? creditCode = jsonConvert.convert<String>(json['credit_code']);
  if (creditCode != null) {
    departmentCompanyContractList.creditCode = creditCode;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    departmentCompanyContractList.mobile = mobile;
  }
  final String? businessLicense = jsonConvert.convert<String>(json['business_license']);
  if (businessLicense != null) {
    departmentCompanyContractList.businessLicense = businessLicense;
  }
  final String? address = jsonConvert.convert<String>(json['address']);
  if (address != null) {
    departmentCompanyContractList.address = address;
  }
  final String? bankNo = jsonConvert.convert<String>(json['bank_no']);
  if (bankNo != null) {
    departmentCompanyContractList.bankNo = bankNo;
  }
  final String? bank = jsonConvert.convert<String>(json['bank']);
  if (bank != null) {
    departmentCompanyContractList.bank = bank;
  }
  final String? auditStatus = jsonConvert.convert<String>(json['audit_status']);
  if (auditStatus != null) {
    departmentCompanyContractList.auditStatus = auditStatus;
  }
  final String? auditStatusName = jsonConvert.convert<String>(json['audit_status_name']);
  if (auditStatusName != null) {
    departmentCompanyContractList.auditStatusName = auditStatusName;
  }
  final String? auditSuggest = jsonConvert.convert<String>(json['audit_suggest']);
  if (auditSuggest != null) {
    departmentCompanyContractList.auditSuggest = auditSuggest;
  }
  final String? customerId = jsonConvert.convert<String>(json['customer_id']);
  if (customerId != null) {
    departmentCompanyContractList.customerId = customerId;
  }
  final String? sealCdnUrl = jsonConvert.convert<String>(json['seal_cdn_url']);
  if (sealCdnUrl != null) {
    departmentCompanyContractList.sealCdnUrl = sealCdnUrl;
  }
  return departmentCompanyContractList;
}

Map<String, dynamic> $DepartmentCompanyContractListToJson(DepartmentCompanyContractList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['company_name'] = entity.companyName;
  data['credit_code'] = entity.creditCode;
  data['mobile'] = entity.mobile;
  data['business_license'] = entity.businessLicense;
  data['address'] = entity.address;
  data['bank_no'] = entity.bankNo;
  data['bank'] = entity.bank;
  data['audit_status'] = entity.auditStatus;
  data['audit_status_name'] = entity.auditStatusName;
  data['audit_suggest'] = entity.auditSuggest;
  data['customer_id'] = entity.customerId;
  data['seal_cdn_url'] = entity.sealCdnUrl;
  return data;
}

extension DepartmentCompanyContractListExtension on DepartmentCompanyContractList {
  DepartmentCompanyContractList copyWith({
    String? uuid,
    String? companyName,
    String? creditCode,
    String? mobile,
    String? businessLicense,
    String? address,
    String? bankNo,
    String? bank,
    String? auditStatus,
    String? auditStatusName,
    String? auditSuggest,
    String? customerId,
    String? sealCdnUrl,
  }) {
    return DepartmentCompanyContractList()
      ..uuid = uuid ?? this.uuid
      ..companyName = companyName ?? this.companyName
      ..creditCode = creditCode ?? this.creditCode
      ..mobile = mobile ?? this.mobile
      ..businessLicense = businessLicense ?? this.businessLicense
      ..address = address ?? this.address
      ..bankNo = bankNo ?? this.bankNo
      ..bank = bank ?? this.bank
      ..auditStatus = auditStatus ?? this.auditStatus
      ..auditStatusName = auditStatusName ?? this.auditStatusName
      ..auditSuggest = auditSuggest ?? this.auditSuggest
      ..customerId = customerId ?? this.customerId
      ..sealCdnUrl = sealCdnUrl ?? this.sealCdnUrl;
  }
}