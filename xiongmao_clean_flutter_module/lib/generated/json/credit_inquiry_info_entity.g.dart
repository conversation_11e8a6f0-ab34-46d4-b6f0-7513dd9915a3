import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/credit_inquiry/bean/credit_inquiry_info_entity.dart';

CreditInquiryInfoEntity $CreditInquiryInfoEntityFromJson(Map<String, dynamic> json) {
  final CreditInquiryInfoEntity creditInquiryInfoEntity = CreditInquiryInfoEntity();
  final String? status = jsonConvert.convert<String>(json['status']);
  if (status != null) {
    creditInquiryInfoEntity.status = status;
  }
  final List<CreditInquiryInfoList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<CreditInquiryInfoList>(e) as CreditInquiryInfoList).toList();
  if (list != null) {
    creditInquiryInfoEntity.list = list;
  }
  return creditInquiryInfoEntity;
}

Map<String, dynamic> $CreditInquiryInfoEntityToJson(CreditInquiryInfoEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['status'] = entity.status;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension CreditInquiryInfoEntityExtension on CreditInquiryInfoEntity {
  CreditInquiryInfoEntity copyWith({
    String? status,
    List<CreditInquiryInfoList>? list,
  }) {
    return CreditInquiryInfoEntity()
      ..status = status ?? this.status
      ..list = list ?? this.list;
  }
}

CreditInquiryInfoList $CreditInquiryInfoListFromJson(Map<String, dynamic> json) {
  final CreditInquiryInfoList creditInquiryInfoList = CreditInquiryInfoList();
  final String? item = jsonConvert.convert<String>(json['item']);
  if (item != null) {
    creditInquiryInfoList.item = item;
  }
  final int? creditType = jsonConvert.convert<int>(json['credit_type']);
  if (creditType != null) {
    creditInquiryInfoList.creditType = creditType;
  }
  final int? status = jsonConvert.convert<int>(json['status']);
  if (status != null) {
    creditInquiryInfoList.status = status;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    creditInquiryInfoList.title = title;
  }
  final String? message = jsonConvert.convert<String>(json['message']);
  if (message != null) {
    creditInquiryInfoList.message = message;
  }
  final String? imgUrl = jsonConvert.convert<String>(json['img_url']);
  if (imgUrl != null) {
    creditInquiryInfoList.imgUrl = imgUrl;
  }
  final String? searchTime = jsonConvert.convert<String>(json['search_time']);
  if (searchTime != null) {
    creditInquiryInfoList.searchTime = searchTime;
  }
  final String? explain = jsonConvert.convert<String>(json['explain']);
  if (explain != null) {
    creditInquiryInfoList.explain = explain;
  }
  return creditInquiryInfoList;
}

Map<String, dynamic> $CreditInquiryInfoListToJson(CreditInquiryInfoList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['item'] = entity.item;
  data['credit_type'] = entity.creditType;
  data['status'] = entity.status;
  data['title'] = entity.title;
  data['message'] = entity.message;
  data['img_url'] = entity.imgUrl;
  data['search_time'] = entity.searchTime;
  data['explain'] = entity.explain;
  return data;
}

extension CreditInquiryInfoListExtension on CreditInquiryInfoList {
  CreditInquiryInfoList copyWith({
    String? item,
    int? creditType,
    int? status,
    String? title,
    String? message,
    String? imgUrl,
    String? searchTime,
    String? explain,
  }) {
    return CreditInquiryInfoList()
      ..item = item ?? this.item
      ..creditType = creditType ?? this.creditType
      ..status = status ?? this.status
      ..title = title ?? this.title
      ..message = message ?? this.message
      ..imgUrl = imgUrl ?? this.imgUrl
      ..searchTime = searchTime ?? this.searchTime
      ..explain = explain ?? this.explain;
  }
}