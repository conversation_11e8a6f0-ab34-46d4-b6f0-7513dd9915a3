import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_day_entity.dart';

MyAttendanceDayEntity $MyAttendanceDayEntityFromJson(Map<String, dynamic> json) {
  final MyAttendanceDayEntity myAttendanceDayEntity = MyAttendanceDayEntity();
  final List<MyAttendanceDayList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MyAttendanceDayList>(e) as MyAttendanceDayList).toList();
  if (list != null) {
    myAttendanceDayEntity.list = list;
  }
  final String? className = jsonConvert.convert<String>(json['class_name']);
  if (className != null) {
    myAttendanceDayEntity.className = className;
  }
  final String? dayType = jsonConvert.convert<String>(json['day_type']);
  if (dayType != null) {
    myAttendanceDayEntity.dayType = dayType;
  }
  final String? classType = jsonConvert.convert<String>(json['class_type']);
  if (classType != null) {
    myAttendanceDayEntity.classType = classType;
  }
  final String? classUuid = jsonConvert.convert<String>(json['class_uuid']);
  if (classUuid != null) {
    myAttendanceDayEntity.classUuid = classUuid;
  }
  final String? scheduleUuid = jsonConvert.convert<String>(json['schedule_uuid']);
  if (scheduleUuid != null) {
    myAttendanceDayEntity.scheduleUuid = scheduleUuid;
  }
  final List<MyAttendanceDayHolidayList>? holidayList = (json['holiday_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MyAttendanceDayHolidayList>(e) as MyAttendanceDayHolidayList).toList();
  if (holidayList != null) {
    myAttendanceDayEntity.holidayList = holidayList;
  }
  final List<MyAttendanceDayOvertimeList>? overtimeList = (json['overtime_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MyAttendanceDayOvertimeList>(e) as MyAttendanceDayOvertimeList).toList();
  if (overtimeList != null) {
    myAttendanceDayEntity.overtimeList = overtimeList;
  }
  final List<MyAttendanceDayOperateLogList>? operateLogList = (json['operate_log_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<MyAttendanceDayOperateLogList>(e) as MyAttendanceDayOperateLogList).toList();
  if (operateLogList != null) {
    myAttendanceDayEntity.operateLogList = operateLogList;
  }
  return myAttendanceDayEntity;
}

Map<String, dynamic> $MyAttendanceDayEntityToJson(MyAttendanceDayEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  data['class_name'] = entity.className;
  data['day_type'] = entity.dayType;
  data['class_type'] = entity.classType;
  data['class_uuid'] = entity.classUuid;
  data['schedule_uuid'] = entity.scheduleUuid;
  data['holiday_list'] = entity.holidayList?.map((v) => v.toJson()).toList();
  data['overtime_list'] = entity.overtimeList?.map((v) => v.toJson()).toList();
  data['operate_log_list'] = entity.operateLogList?.map((v) => v.toJson()).toList();
  return data;
}

extension MyAttendanceDayEntityExtension on MyAttendanceDayEntity {
  MyAttendanceDayEntity copyWith({
    List<MyAttendanceDayList>? list,
    String? className,
    String? dayType,
    String? classType,
    String? classUuid,
    String? scheduleUuid,
    List<MyAttendanceDayHolidayList>? holidayList,
    List<MyAttendanceDayOvertimeList>? overtimeList,
    List<MyAttendanceDayOperateLogList>? operateLogList,
  }) {
    return MyAttendanceDayEntity()
      ..list = list ?? this.list
      ..className = className ?? this.className
      ..dayType = dayType ?? this.dayType
      ..classType = classType ?? this.classType
      ..classUuid = classUuid ?? this.classUuid
      ..scheduleUuid = scheduleUuid ?? this.scheduleUuid
      ..holidayList = holidayList ?? this.holidayList
      ..overtimeList = overtimeList ?? this.overtimeList
      ..operateLogList = operateLogList ?? this.operateLogList;
  }
}

MyAttendanceDayList $MyAttendanceDayListFromJson(Map<String, dynamic> json) {
  final MyAttendanceDayList myAttendanceDayList = MyAttendanceDayList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    myAttendanceDayList.uuid = uuid;
  }
  final String? inTitle = jsonConvert.convert<String>(json['in_title']);
  if (inTitle != null) {
    myAttendanceDayList.inTitle = inTitle;
  }
  final String? inStatus = jsonConvert.convert<String>(json['in_status']);
  if (inStatus != null) {
    myAttendanceDayList.inStatus = inStatus;
  }
  final String? inStatusName = jsonConvert.convert<String>(json['in_status_name']);
  if (inStatusName != null) {
    myAttendanceDayList.inStatusName = inStatusName;
  }
  final String? inClockTime = jsonConvert.convert<String>(json['in_clock_time']);
  if (inClockTime != null) {
    myAttendanceDayList.inClockTime = inClockTime;
  }
  final String? inShouldClockTime = jsonConvert.convert<String>(json['in_should_clock_time']);
  if (inShouldClockTime != null) {
    myAttendanceDayList.inShouldClockTime = inShouldClockTime;
  }
  final String? inActualClockTime = jsonConvert.convert<String>(json['in_actual_clock_time']);
  if (inActualClockTime != null) {
    myAttendanceDayList.inActualClockTime = inActualClockTime;
  }
  final String? inIsClock = jsonConvert.convert<String>(json['in_is_clock']);
  if (inIsClock != null) {
    myAttendanceDayList.inIsClock = inIsClock;
  }
  final String? inClockTypeName = jsonConvert.convert<String>(json['in_clock_type_name']);
  if (inClockTypeName != null) {
    myAttendanceDayList.inClockTypeName = inClockTypeName;
  }
  final String? inMediaUrl = jsonConvert.convert<String>(json['in_media_url']);
  if (inMediaUrl != null) {
    myAttendanceDayList.inMediaUrl = inMediaUrl;
  }
  final String? inPicThumb = jsonConvert.convert<String>(json['in_pic_thumb']);
  if (inPicThumb != null) {
    myAttendanceDayList.inPicThumb = inPicThumb;
  }
  final String? inVideoCoverUrl = jsonConvert.convert<String>(json['in_video_cover_url']);
  if (inVideoCoverUrl != null) {
    myAttendanceDayList.inVideoCoverUrl = inVideoCoverUrl;
  }
  final String? inOriginMediaUrl = jsonConvert.convert<String>(json['in_origin_media_url']);
  if (inOriginMediaUrl != null) {
    myAttendanceDayList.inOriginMediaUrl = inOriginMediaUrl;
  }
  final String? inMessageType = jsonConvert.convert<String>(json['in_message_type']);
  if (inMessageType != null) {
    myAttendanceDayList.inMessageType = inMessageType;
  }
  final String? inMediaType = jsonConvert.convert<String>(json['in_media_type']);
  if (inMediaType != null) {
    myAttendanceDayList.inMediaType = inMediaType;
  }
  final String? inOperateName = jsonConvert.convert<String>(json['in_operate_name']);
  if (inOperateName != null) {
    myAttendanceDayList.inOperateName = inOperateName;
  }
  final String? inIsCurrentDay = jsonConvert.convert<String>(json['in_is_current_day']);
  if (inIsCurrentDay != null) {
    myAttendanceDayList.inIsCurrentDay = inIsCurrentDay;
  }
  final String? outTitle = jsonConvert.convert<String>(json['out_title']);
  if (outTitle != null) {
    myAttendanceDayList.outTitle = outTitle;
  }
  final String? outStatus = jsonConvert.convert<String>(json['out_status']);
  if (outStatus != null) {
    myAttendanceDayList.outStatus = outStatus;
  }
  final String? outStatusName = jsonConvert.convert<String>(json['out_status_name']);
  if (outStatusName != null) {
    myAttendanceDayList.outStatusName = outStatusName;
  }
  final String? outClockTime = jsonConvert.convert<String>(json['out_clock_time']);
  if (outClockTime != null) {
    myAttendanceDayList.outClockTime = outClockTime;
  }
  final String? outShouldClockTime = jsonConvert.convert<String>(json['out_should_clock_time']);
  if (outShouldClockTime != null) {
    myAttendanceDayList.outShouldClockTime = outShouldClockTime;
  }
  final String? outActualClockTime = jsonConvert.convert<String>(json['out_actual_clock_time']);
  if (outActualClockTime != null) {
    myAttendanceDayList.outActualClockTime = outActualClockTime;
  }
  final String? outIsClock = jsonConvert.convert<String>(json['out_is_clock']);
  if (outIsClock != null) {
    myAttendanceDayList.outIsClock = outIsClock;
  }
  final String? outClockTypeName = jsonConvert.convert<String>(json['out_clock_type_name']);
  if (outClockTypeName != null) {
    myAttendanceDayList.outClockTypeName = outClockTypeName;
  }
  final String? outMediaUrl = jsonConvert.convert<String>(json['out_media_url']);
  if (outMediaUrl != null) {
    myAttendanceDayList.outMediaUrl = outMediaUrl;
  }
  final String? outPicThumb = jsonConvert.convert<String>(json['out_pic_thumb']);
  if (outPicThumb != null) {
    myAttendanceDayList.outPicThumb = outPicThumb;
  }
  final String? outVideoCoverUrl = jsonConvert.convert<String>(json['out_video_cover_url']);
  if (outVideoCoverUrl != null) {
    myAttendanceDayList.outVideoCoverUrl = outVideoCoverUrl;
  }
  final String? outOriginMediaUrl = jsonConvert.convert<String>(json['out_origin_media_url']);
  if (outOriginMediaUrl != null) {
    myAttendanceDayList.outOriginMediaUrl = outOriginMediaUrl;
  }
  final String? outMessageType = jsonConvert.convert<String>(json['out_message_type']);
  if (outMessageType != null) {
    myAttendanceDayList.outMessageType = outMessageType;
  }
  final String? outMediaType = jsonConvert.convert<String>(json['out_media_type']);
  if (outMediaType != null) {
    myAttendanceDayList.outMediaType = outMediaType;
  }
  final String? outOperateName = jsonConvert.convert<String>(json['out_operate_name']);
  if (outOperateName != null) {
    myAttendanceDayList.outOperateName = outOperateName;
  }
  final String? outIsCurrentDay = jsonConvert.convert<String>(json['out_is_current_day']);
  if (outIsCurrentDay != null) {
    myAttendanceDayList.outIsCurrentDay = outIsCurrentDay;
  }
  return myAttendanceDayList;
}

Map<String, dynamic> $MyAttendanceDayListToJson(MyAttendanceDayList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['in_title'] = entity.inTitle;
  data['in_status'] = entity.inStatus;
  data['in_status_name'] = entity.inStatusName;
  data['in_clock_time'] = entity.inClockTime;
  data['in_should_clock_time'] = entity.inShouldClockTime;
  data['in_actual_clock_time'] = entity.inActualClockTime;
  data['in_is_clock'] = entity.inIsClock;
  data['in_clock_type_name'] = entity.inClockTypeName;
  data['in_media_url'] = entity.inMediaUrl;
  data['in_pic_thumb'] = entity.inPicThumb;
  data['in_video_cover_url'] = entity.inVideoCoverUrl;
  data['in_origin_media_url'] = entity.inOriginMediaUrl;
  data['in_message_type'] = entity.inMessageType;
  data['in_media_type'] = entity.inMediaType;
  data['in_operate_name'] = entity.inOperateName;
  data['in_is_current_day'] = entity.inIsCurrentDay;
  data['out_title'] = entity.outTitle;
  data['out_status'] = entity.outStatus;
  data['out_status_name'] = entity.outStatusName;
  data['out_clock_time'] = entity.outClockTime;
  data['out_should_clock_time'] = entity.outShouldClockTime;
  data['out_actual_clock_time'] = entity.outActualClockTime;
  data['out_is_clock'] = entity.outIsClock;
  data['out_clock_type_name'] = entity.outClockTypeName;
  data['out_media_url'] = entity.outMediaUrl;
  data['out_pic_thumb'] = entity.outPicThumb;
  data['out_video_cover_url'] = entity.outVideoCoverUrl;
  data['out_origin_media_url'] = entity.outOriginMediaUrl;
  data['out_message_type'] = entity.outMessageType;
  data['out_media_type'] = entity.outMediaType;
  data['out_operate_name'] = entity.outOperateName;
  data['out_is_current_day'] = entity.outIsCurrentDay;
  return data;
}

extension MyAttendanceDayListExtension on MyAttendanceDayList {
  MyAttendanceDayList copyWith({
    String? uuid,
    String? inTitle,
    String? inStatus,
    String? inStatusName,
    String? inClockTime,
    String? inShouldClockTime,
    String? inActualClockTime,
    String? inIsClock,
    String? inClockTypeName,
    String? inMediaUrl,
    String? inPicThumb,
    String? inVideoCoverUrl,
    String? inOriginMediaUrl,
    String? inMessageType,
    String? inMediaType,
    String? inOperateName,
    String? inIsCurrentDay,
    String? outTitle,
    String? outStatus,
    String? outStatusName,
    String? outClockTime,
    String? outShouldClockTime,
    String? outActualClockTime,
    String? outIsClock,
    String? outClockTypeName,
    String? outMediaUrl,
    String? outPicThumb,
    String? outVideoCoverUrl,
    String? outOriginMediaUrl,
    String? outMessageType,
    String? outMediaType,
    String? outOperateName,
    String? outIsCurrentDay,
  }) {
    return MyAttendanceDayList()
      ..uuid = uuid ?? this.uuid
      ..inTitle = inTitle ?? this.inTitle
      ..inStatus = inStatus ?? this.inStatus
      ..inStatusName = inStatusName ?? this.inStatusName
      ..inClockTime = inClockTime ?? this.inClockTime
      ..inShouldClockTime = inShouldClockTime ?? this.inShouldClockTime
      ..inActualClockTime = inActualClockTime ?? this.inActualClockTime
      ..inIsClock = inIsClock ?? this.inIsClock
      ..inClockTypeName = inClockTypeName ?? this.inClockTypeName
      ..inMediaUrl = inMediaUrl ?? this.inMediaUrl
      ..inPicThumb = inPicThumb ?? this.inPicThumb
      ..inVideoCoverUrl = inVideoCoverUrl ?? this.inVideoCoverUrl
      ..inOriginMediaUrl = inOriginMediaUrl ?? this.inOriginMediaUrl
      ..inMessageType = inMessageType ?? this.inMessageType
      ..inMediaType = inMediaType ?? this.inMediaType
      ..inOperateName = inOperateName ?? this.inOperateName
      ..inIsCurrentDay = inIsCurrentDay ?? this.inIsCurrentDay
      ..outTitle = outTitle ?? this.outTitle
      ..outStatus = outStatus ?? this.outStatus
      ..outStatusName = outStatusName ?? this.outStatusName
      ..outClockTime = outClockTime ?? this.outClockTime
      ..outShouldClockTime = outShouldClockTime ?? this.outShouldClockTime
      ..outActualClockTime = outActualClockTime ?? this.outActualClockTime
      ..outIsClock = outIsClock ?? this.outIsClock
      ..outClockTypeName = outClockTypeName ?? this.outClockTypeName
      ..outMediaUrl = outMediaUrl ?? this.outMediaUrl
      ..outPicThumb = outPicThumb ?? this.outPicThumb
      ..outVideoCoverUrl = outVideoCoverUrl ?? this.outVideoCoverUrl
      ..outOriginMediaUrl = outOriginMediaUrl ?? this.outOriginMediaUrl
      ..outMessageType = outMessageType ?? this.outMessageType
      ..outMediaType = outMediaType ?? this.outMediaType
      ..outOperateName = outOperateName ?? this.outOperateName
      ..outIsCurrentDay = outIsCurrentDay ?? this.outIsCurrentDay;
  }
}

MyAttendanceDayHolidayList $MyAttendanceDayHolidayListFromJson(Map<String, dynamic> json) {
  final MyAttendanceDayHolidayList myAttendanceDayHolidayList = MyAttendanceDayHolidayList();
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    myAttendanceDayHolidayList.introduction = introduction;
  }
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    myAttendanceDayHolidayList.uuid = uuid;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    myAttendanceDayHolidayList.startDate = startDate;
  }
  final String? startType = jsonConvert.convert<String>(json['start_type']);
  if (startType != null) {
    myAttendanceDayHolidayList.startType = startType;
  }
  final String? startTypeName = jsonConvert.convert<String>(json['start_type_name']);
  if (startTypeName != null) {
    myAttendanceDayHolidayList.startTypeName = startTypeName;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    myAttendanceDayHolidayList.endDate = endDate;
  }
  final String? endType = jsonConvert.convert<String>(json['end_type']);
  if (endType != null) {
    myAttendanceDayHolidayList.endType = endType;
  }
  final String? endTypeName = jsonConvert.convert<String>(json['end_type_name']);
  if (endTypeName != null) {
    myAttendanceDayHolidayList.endTypeName = endTypeName;
  }
  final String? holidayType = jsonConvert.convert<String>(json['holiday_type']);
  if (holidayType != null) {
    myAttendanceDayHolidayList.holidayType = holidayType;
  }
  final String? holidayTypeName = jsonConvert.convert<String>(json['holiday_type_name']);
  if (holidayTypeName != null) {
    myAttendanceDayHolidayList.holidayTypeName = holidayTypeName;
  }
  final String? timeLong = jsonConvert.convert<String>(json['time_long']);
  if (timeLong != null) {
    myAttendanceDayHolidayList.timeLong = timeLong;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    myAttendanceDayHolidayList.reason = reason;
  }
  return myAttendanceDayHolidayList;
}

Map<String, dynamic> $MyAttendanceDayHolidayListToJson(MyAttendanceDayHolidayList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['introduction'] = entity.introduction;
  data['uuid'] = entity.uuid;
  data['start_date'] = entity.startDate;
  data['start_type'] = entity.startType;
  data['start_type_name'] = entity.startTypeName;
  data['end_date'] = entity.endDate;
  data['end_type'] = entity.endType;
  data['end_type_name'] = entity.endTypeName;
  data['holiday_type'] = entity.holidayType;
  data['holiday_type_name'] = entity.holidayTypeName;
  data['time_long'] = entity.timeLong;
  data['reason'] = entity.reason;
  return data;
}

extension MyAttendanceDayHolidayListExtension on MyAttendanceDayHolidayList {
  MyAttendanceDayHolidayList copyWith({
    String? introduction,
    String? uuid,
    String? startDate,
    String? startType,
    String? startTypeName,
    String? endDate,
    String? endType,
    String? endTypeName,
    String? holidayType,
    String? holidayTypeName,
    String? timeLong,
    String? reason,
  }) {
    return MyAttendanceDayHolidayList()
      ..introduction = introduction ?? this.introduction
      ..uuid = uuid ?? this.uuid
      ..startDate = startDate ?? this.startDate
      ..startType = startType ?? this.startType
      ..startTypeName = startTypeName ?? this.startTypeName
      ..endDate = endDate ?? this.endDate
      ..endType = endType ?? this.endType
      ..endTypeName = endTypeName ?? this.endTypeName
      ..holidayType = holidayType ?? this.holidayType
      ..holidayTypeName = holidayTypeName ?? this.holidayTypeName
      ..timeLong = timeLong ?? this.timeLong
      ..reason = reason ?? this.reason;
  }
}

MyAttendanceDayOvertimeList $MyAttendanceDayOvertimeListFromJson(Map<String, dynamic> json) {
  final MyAttendanceDayOvertimeList myAttendanceDayOvertimeList = MyAttendanceDayOvertimeList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    myAttendanceDayOvertimeList.uuid = uuid;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    myAttendanceDayOvertimeList.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    myAttendanceDayOvertimeList.endTime = endTime;
  }
  final String? overtimeLong = jsonConvert.convert<String>(json['overtime_long']);
  if (overtimeLong != null) {
    myAttendanceDayOvertimeList.overtimeLong = overtimeLong;
  }
  final String? overtimeDate = jsonConvert.convert<String>(json['overtime_date']);
  if (overtimeDate != null) {
    myAttendanceDayOvertimeList.overtimeDate = overtimeDate;
  }
  final String? overtimeType = jsonConvert.convert<String>(json['overtime_type']);
  if (overtimeType != null) {
    myAttendanceDayOvertimeList.overtimeType = overtimeType;
  }
  final String? overtimeTypeName = jsonConvert.convert<String>(json['overtime_type_name']);
  if (overtimeTypeName != null) {
    myAttendanceDayOvertimeList.overtimeTypeName = overtimeTypeName;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    myAttendanceDayOvertimeList.reason = reason;
  }
  return myAttendanceDayOvertimeList;
}

Map<String, dynamic> $MyAttendanceDayOvertimeListToJson(MyAttendanceDayOvertimeList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['overtime_long'] = entity.overtimeLong;
  data['overtime_date'] = entity.overtimeDate;
  data['overtime_type'] = entity.overtimeType;
  data['overtime_type_name'] = entity.overtimeTypeName;
  data['reason'] = entity.reason;
  return data;
}

extension MyAttendanceDayOvertimeListExtension on MyAttendanceDayOvertimeList {
  MyAttendanceDayOvertimeList copyWith({
    String? uuid,
    String? startTime,
    String? endTime,
    String? overtimeLong,
    String? overtimeDate,
    String? overtimeType,
    String? overtimeTypeName,
    String? reason,
  }) {
    return MyAttendanceDayOvertimeList()
      ..uuid = uuid ?? this.uuid
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..overtimeLong = overtimeLong ?? this.overtimeLong
      ..overtimeDate = overtimeDate ?? this.overtimeDate
      ..overtimeType = overtimeType ?? this.overtimeType
      ..overtimeTypeName = overtimeTypeName ?? this.overtimeTypeName
      ..reason = reason ?? this.reason;
  }
}

MyAttendanceDayOperateLogList $MyAttendanceDayOperateLogListFromJson(Map<String, dynamic> json) {
  final MyAttendanceDayOperateLogList myAttendanceDayOperateLogList = MyAttendanceDayOperateLogList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    myAttendanceDayOperateLogList.uuid = uuid;
  }
  final String? segmentUuid = jsonConvert.convert<String>(json['segment_uuid']);
  if (segmentUuid != null) {
    myAttendanceDayOperateLogList.segmentUuid = segmentUuid;
  }
  final String? title = jsonConvert.convert<String>(json['title']);
  if (title != null) {
    myAttendanceDayOperateLogList.title = title;
  }
  final String? sbStatus = jsonConvert.convert<String>(json['sb_status']);
  if (sbStatus != null) {
    myAttendanceDayOperateLogList.sbStatus = sbStatus;
  }
  final String? sbStatusName = jsonConvert.convert<String>(json['sb_status_name']);
  if (sbStatusName != null) {
    myAttendanceDayOperateLogList.sbStatusName = sbStatusName;
  }
  final String? cdTimeLong = jsonConvert.convert<String>(json['cd_time_long']);
  if (cdTimeLong != null) {
    myAttendanceDayOperateLogList.cdTimeLong = cdTimeLong;
  }
  final String? xbStatus = jsonConvert.convert<String>(json['xb_status']);
  if (xbStatus != null) {
    myAttendanceDayOperateLogList.xbStatus = xbStatus;
  }
  final String? xbStatusName = jsonConvert.convert<String>(json['xb_status_name']);
  if (xbStatusName != null) {
    myAttendanceDayOperateLogList.xbStatusName = xbStatusName;
  }
  final String? ztTimeLong = jsonConvert.convert<String>(json['zt_time_long']);
  if (ztTimeLong != null) {
    myAttendanceDayOperateLogList.ztTimeLong = ztTimeLong;
  }
  return myAttendanceDayOperateLogList;
}

Map<String, dynamic> $MyAttendanceDayOperateLogListToJson(MyAttendanceDayOperateLogList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['segment_uuid'] = entity.segmentUuid;
  data['title'] = entity.title;
  data['sb_status'] = entity.sbStatus;
  data['sb_status_name'] = entity.sbStatusName;
  data['cd_time_long'] = entity.cdTimeLong;
  data['xb_status'] = entity.xbStatus;
  data['xb_status_name'] = entity.xbStatusName;
  data['zt_time_long'] = entity.ztTimeLong;
  return data;
}

extension MyAttendanceDayOperateLogListExtension on MyAttendanceDayOperateLogList {
  MyAttendanceDayOperateLogList copyWith({
    String? uuid,
    String? segmentUuid,
    String? title,
    String? sbStatus,
    String? sbStatusName,
    String? cdTimeLong,
    String? xbStatus,
    String? xbStatusName,
    String? ztTimeLong,
  }) {
    return MyAttendanceDayOperateLogList()
      ..uuid = uuid ?? this.uuid
      ..segmentUuid = segmentUuid ?? this.segmentUuid
      ..title = title ?? this.title
      ..sbStatus = sbStatus ?? this.sbStatus
      ..sbStatusName = sbStatusName ?? this.sbStatusName
      ..cdTimeLong = cdTimeLong ?? this.cdTimeLong
      ..xbStatus = xbStatus ?? this.xbStatus
      ..xbStatusName = xbStatusName ?? this.xbStatusName
      ..ztTimeLong = ztTimeLong ?? this.ztTimeLong;
  }
}