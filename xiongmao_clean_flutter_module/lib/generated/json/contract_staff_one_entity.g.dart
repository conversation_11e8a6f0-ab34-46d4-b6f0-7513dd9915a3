import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_staff_one_entity.dart';

ContractStaffOneEntity $ContractStaffOneEntityFromJson(Map<String, dynamic> json) {
  final ContractStaffOneEntity contractStaffOneEntity = ContractStaffOneEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    contractStaffOneEntity.uuid = uuid;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    contractStaffOneEntity.startTime = startTime;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    contractStaffOneEntity.endTime = endTime;
  }
  final String? subLabelCode = jsonConvert.convert<String>(json['sub_label_code']);
  if (subLabelCode != null) {
    contractStaffOneEntity.subLabelCode = subLabelCode;
  }
  final String? contractType = jsonConvert.convert<String>(json['contract_type']);
  if (contractType != null) {
    contractStaffOneEntity.contractType = contractType;
  }
  final String? leftReason = jsonConvert.convert<String>(json['left_reason']);
  if (leftReason != null) {
    contractStaffOneEntity.leftReason = leftReason;
  }
  final String? leftReasonName = jsonConvert.convert<String>(json['left_reason_name']);
  if (leftReasonName != null) {
    contractStaffOneEntity.leftReasonName = leftReasonName;
  }
  final String? remark = jsonConvert.convert<String>(json['remark']);
  if (remark != null) {
    contractStaffOneEntity.remark = remark;
  }
  final List<String>? contractPicList = (json['contract_pic_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<String>(e) as String).toList();
  if (contractPicList != null) {
    contractStaffOneEntity.contractPicList = contractPicList;
  }
  return contractStaffOneEntity;
}

Map<String, dynamic> $ContractStaffOneEntityToJson(ContractStaffOneEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['start_time'] = entity.startTime;
  data['end_time'] = entity.endTime;
  data['sub_label_code'] = entity.subLabelCode;
  data['contract_type'] = entity.contractType;
  data['left_reason'] = entity.leftReason;
  data['left_reason_name'] = entity.leftReasonName;
  data['remark'] = entity.remark;
  data['contract_pic_list'] = entity.contractPicList;
  return data;
}

extension ContractStaffOneEntityExtension on ContractStaffOneEntity {
  ContractStaffOneEntity copyWith({
    String? uuid,
    String? startTime,
    String? endTime,
    String? subLabelCode,
    String? contractType,
    String? leftReason,
    String? leftReasonName,
    String? remark,
    List<String>? contractPicList,
  }) {
    return ContractStaffOneEntity()
      ..uuid = uuid ?? this.uuid
      ..startTime = startTime ?? this.startTime
      ..endTime = endTime ?? this.endTime
      ..subLabelCode = subLabelCode ?? this.subLabelCode
      ..contractType = contractType ?? this.contractType
      ..leftReason = leftReason ?? this.leftReason
      ..leftReasonName = leftReasonName ?? this.leftReasonName
      ..remark = remark ?? this.remark
      ..contractPicList = contractPicList ?? this.contractPicList;
  }
}