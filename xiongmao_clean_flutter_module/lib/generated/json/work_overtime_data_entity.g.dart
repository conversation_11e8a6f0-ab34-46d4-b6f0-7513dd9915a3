import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_data_entity.dart';

WorkOvertimeDataEntity $WorkOvertimeDataEntityFromJson(Map<String, dynamic> json) {
  final WorkOvertimeDataEntity workOvertimeDataEntity = WorkOvertimeDataEntity();
  final int? page = jsonConvert.convert<int>(json['page']);
  if (page != null) {
    workOvertimeDataEntity.page = page;
  }
  final int? size = jsonConvert.convert<int>(json['size']);
  if (size != null) {
    workOvertimeDataEntity.size = size;
  }
  final int? total = jsonConvert.convert<int>(json['total']);
  if (total != null) {
    workOvertimeDataEntity.total = total;
  }
  final List<WorkOvertimeDataList>? list = (json['list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<WorkOvertimeDataList>(e) as WorkOvertimeDataList).toList();
  if (list != null) {
    workOvertimeDataEntity.list = list;
  }
  return workOvertimeDataEntity;
}

Map<String, dynamic> $WorkOvertimeDataEntityToJson(WorkOvertimeDataEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['page'] = entity.page;
  data['size'] = entity.size;
  data['total'] = entity.total;
  data['list'] = entity.list?.map((v) => v.toJson()).toList();
  return data;
}

extension WorkOvertimeDataEntityExtension on WorkOvertimeDataEntity {
  WorkOvertimeDataEntity copyWith({
    int? page,
    int? size,
    int? total,
    List<WorkOvertimeDataList>? list,
  }) {
    return WorkOvertimeDataEntity()
      ..page = page ?? this.page
      ..size = size ?? this.size
      ..total = total ?? this.total
      ..list = list ?? this.list;
  }
}

WorkOvertimeDataList $WorkOvertimeDataListFromJson(Map<String, dynamic> json) {
  final WorkOvertimeDataList workOvertimeDataList = WorkOvertimeDataList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    workOvertimeDataList.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    workOvertimeDataList.projectName = projectName;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    workOvertimeDataList.projectUuid = projectUuid;
  }
  final String? startTime = jsonConvert.convert<String>(json['start_time']);
  if (startTime != null) {
    workOvertimeDataList.startTime = startTime;
  }
  final String? startTypeName = jsonConvert.convert<String>(json['start_type_name']);
  if (startTypeName != null) {
    workOvertimeDataList.startTypeName = startTypeName;
  }
  final String? endTime = jsonConvert.convert<String>(json['end_time']);
  if (endTime != null) {
    workOvertimeDataList.endTime = endTime;
  }
  final String? endTypeName = jsonConvert.convert<String>(json['end_type_name']);
  if (endTypeName != null) {
    workOvertimeDataList.endTypeName = endTypeName;
  }
  final String? overtimeLong = jsonConvert.convert<String>(json['overtime_long']);
  if (overtimeLong != null) {
    workOvertimeDataList.overtimeLong = overtimeLong;
  }
  final String? overtimeDate = jsonConvert.convert<String>(json['overtime_date']);
  if (overtimeDate != null) {
    workOvertimeDataList.overtimeDate = overtimeDate;
  }
  final String? overtimeType = jsonConvert.convert<String>(json['overtime_type']);
  if (overtimeType != null) {
    workOvertimeDataList.overtimeType = overtimeType;
  }
  final String? overtimeTypeName = jsonConvert.convert<String>(json['overtime_type_name']);
  if (overtimeTypeName != null) {
    workOvertimeDataList.overtimeTypeName = overtimeTypeName;
  }
  final String? reason = jsonConvert.convert<String>(json['reason']);
  if (reason != null) {
    workOvertimeDataList.reason = reason;
  }
  final String? introduction = jsonConvert.convert<String>(json['introduction']);
  if (introduction != null) {
    workOvertimeDataList.introduction = introduction;
  }
  return workOvertimeDataList;
}

Map<String, dynamic> $WorkOvertimeDataListToJson(WorkOvertimeDataList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_uuid'] = entity.projectUuid;
  data['start_time'] = entity.startTime;
  data['start_type_name'] = entity.startTypeName;
  data['end_time'] = entity.endTime;
  data['end_type_name'] = entity.endTypeName;
  data['overtime_long'] = entity.overtimeLong;
  data['overtime_date'] = entity.overtimeDate;
  data['overtime_type'] = entity.overtimeType;
  data['overtime_type_name'] = entity.overtimeTypeName;
  data['reason'] = entity.reason;
  data['introduction'] = entity.introduction;
  return data;
}

extension WorkOvertimeDataListExtension on WorkOvertimeDataList {
  WorkOvertimeDataList copyWith({
    String? uuid,
    String? projectName,
    String? projectUuid,
    String? startTime,
    String? startTypeName,
    String? endTime,
    String? endTypeName,
    String? overtimeLong,
    String? overtimeDate,
    String? overtimeType,
    String? overtimeTypeName,
    String? reason,
    String? introduction,
  }) {
    return WorkOvertimeDataList()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectUuid = projectUuid ?? this.projectUuid
      ..startTime = startTime ?? this.startTime
      ..startTypeName = startTypeName ?? this.startTypeName
      ..endTime = endTime ?? this.endTime
      ..endTypeName = endTypeName ?? this.endTypeName
      ..overtimeLong = overtimeLong ?? this.overtimeLong
      ..overtimeDate = overtimeDate ?? this.overtimeDate
      ..overtimeType = overtimeType ?? this.overtimeType
      ..overtimeTypeName = overtimeTypeName ?? this.overtimeTypeName
      ..reason = reason ?? this.reason
      ..introduction = introduction ?? this.introduction;
  }
}