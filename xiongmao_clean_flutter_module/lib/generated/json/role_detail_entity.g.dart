import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/generated/role_detail_entity.dart';

RoleDetailEntity $RoleDetailEntityFromJson(Map<String, dynamic> json) {
  final RoleDetailEntity roleDetailEntity = RoleDetailEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    roleDetailEntity.uuid = uuid;
  }
  final String? userName = jsonConvert.convert<String>(json['user_name']);
  if (userName != null) {
    roleDetailEntity.userName = userName;
  }
  final String? jobName = jsonConvert.convert<String>(json['job_name']);
  if (jobName != null) {
    roleDetailEntity.jobName = jobName;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    roleDetailEntity.projectShortName = projectShortName;
  }
  final String? age = jsonConvert.convert<String>(json['age']);
  if (age != null) {
    roleDetailEntity.age = age;
  }
  final String? avatar = jsonConvert.convert<String>(json['avatar']);
  if (avatar != null) {
    roleDetailEntity.avatar = avatar;
  }
  final String? workAge = jsonConvert.convert<String>(json['work_age']);
  if (workAge != null) {
    roleDetailEntity.workAge = workAge;
  }
  final String? mobile = jsonConvert.convert<String>(json['mobile']);
  if (mobile != null) {
    roleDetailEntity.mobile = mobile;
  }
  final String? isHasIdCard = jsonConvert.convert<String>(json['is_has_id_card']);
  if (isHasIdCard != null) {
    roleDetailEntity.isHasIdCard = isHasIdCard;
  }
  final String? isHasContract = jsonConvert.convert<String>(json['is_has_contract']);
  if (isHasContract != null) {
    roleDetailEntity.isHasContract = isHasContract;
  }
  final String? isHasBankCard = jsonConvert.convert<String>(json['is_has_bank_card']);
  if (isHasBankCard != null) {
    roleDetailEntity.isHasBankCard = isHasBankCard;
  }
  final String? isHasHealthyCard = jsonConvert.convert<String>(json['is_has_healthy_card']);
  if (isHasHealthyCard != null) {
    roleDetailEntity.isHasHealthyCard = isHasHealthyCard;
  }
  final String? isHeadOffice = jsonConvert.convert<String>(json['is_head_office']);
  if (isHeadOffice != null) {
    roleDetailEntity.isHeadOffice = isHeadOffice;
  }
  return roleDetailEntity;
}

Map<String, dynamic> $RoleDetailEntityToJson(RoleDetailEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['user_name'] = entity.userName;
  data['job_name'] = entity.jobName;
  data['project_short_name'] = entity.projectShortName;
  data['age'] = entity.age;
  data['avatar'] = entity.avatar;
  data['work_age'] = entity.workAge;
  data['mobile'] = entity.mobile;
  data['is_has_id_card'] = entity.isHasIdCard;
  data['is_has_contract'] = entity.isHasContract;
  data['is_has_bank_card'] = entity.isHasBankCard;
  data['is_has_healthy_card'] = entity.isHasHealthyCard;
  data['is_head_office'] = entity.isHeadOffice;
  return data;
}

extension RoleDetailEntityExtension on RoleDetailEntity {
  RoleDetailEntity copyWith({
    String? uuid,
    String? userName,
    String? jobName,
    String? projectShortName,
    String? age,
    String? avatar,
    String? workAge,
    String? mobile,
    String? isHasIdCard,
    String? isHasContract,
    String? isHasBankCard,
    String? isHasHealthyCard,
    String? isHeadOffice,
  }) {
    return RoleDetailEntity()
      ..uuid = uuid ?? this.uuid
      ..userName = userName ?? this.userName
      ..jobName = jobName ?? this.jobName
      ..projectShortName = projectShortName ?? this.projectShortName
      ..age = age ?? this.age
      ..avatar = avatar ?? this.avatar
      ..workAge = workAge ?? this.workAge
      ..mobile = mobile ?? this.mobile
      ..isHasIdCard = isHasIdCard ?? this.isHasIdCard
      ..isHasContract = isHasContract ?? this.isHasContract
      ..isHasBankCard = isHasBankCard ?? this.isHasBankCard
      ..isHasHealthyCard = isHasHealthyCard ?? this.isHasHealthyCard
      ..isHeadOffice = isHeadOffice ?? this.isHeadOffice;
  }
}