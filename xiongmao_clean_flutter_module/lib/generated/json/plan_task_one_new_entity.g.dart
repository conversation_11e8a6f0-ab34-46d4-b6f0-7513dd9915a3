import 'package:xiongmao_clean_flutter_module/generated/json/base/json_convert_content.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/plan_task_one_new_entity.dart';

PlanTaskOneNewEntity $PlanTaskOneNewEntityFromJson(Map<String, dynamic> json) {
  final PlanTaskOneNewEntity planTaskOneNewEntity = PlanTaskOneNewEntity();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    planTaskOneNewEntity.uuid = uuid;
  }
  final String? projectName = jsonConvert.convert<String>(json['project_name']);
  if (projectName != null) {
    planTaskOneNewEntity.projectName = projectName;
  }
  final String? projectUuid = jsonConvert.convert<String>(json['project_uuid']);
  if (projectUuid != null) {
    planTaskOneNewEntity.projectUuid = projectUuid;
  }
  final String? projectShortName = jsonConvert.convert<String>(json['project_short_name']);
  if (projectShortName != null) {
    planTaskOneNewEntity.projectShortName = projectShortName;
  }
  final String? areaName = jsonConvert.convert<String>(json['area_name']);
  if (areaName != null) {
    planTaskOneNewEntity.areaName = areaName;
  }
  final String? workContent = jsonConvert.convert<String>(json['work_content']);
  if (workContent != null) {
    planTaskOneNewEntity.workContent = workContent;
  }
  final String? startDate = jsonConvert.convert<String>(json['start_date']);
  if (startDate != null) {
    planTaskOneNewEntity.startDate = startDate;
  }
  final String? endDate = jsonConvert.convert<String>(json['end_date']);
  if (endDate != null) {
    planTaskOneNewEntity.endDate = endDate;
  }
  final String? executeUserUuid = jsonConvert.convert<String>(json['execute_user_uuid']);
  if (executeUserUuid != null) {
    planTaskOneNewEntity.executeUserUuid = executeUserUuid;
  }
  final String? executeUserName = jsonConvert.convert<String>(json['execute_user_name']);
  if (executeUserName != null) {
    planTaskOneNewEntity.executeUserName = executeUserName;
  }
  final String? taskType = jsonConvert.convert<String>(json['task_type']);
  if (taskType != null) {
    planTaskOneNewEntity.taskType = taskType;
  }
  final String? sourceType = jsonConvert.convert<String>(json['source_type']);
  if (sourceType != null) {
    planTaskOneNewEntity.sourceType = sourceType;
  }
  final String? finishedTime = jsonConvert.convert<String>(json['finished_time']);
  if (finishedTime != null) {
    planTaskOneNewEntity.finishedTime = finishedTime;
  }
  final String? taskStatus = jsonConvert.convert<String>(json['task_status']);
  if (taskStatus != null) {
    planTaskOneNewEntity.taskStatus = taskStatus;
  }
  final String? taskStatusName = jsonConvert.convert<String>(json['task_status_name']);
  if (taskStatusName != null) {
    planTaskOneNewEntity.taskStatusName = taskStatusName;
  }
  final String? isSelf = jsonConvert.convert<String>(json['is_self']);
  if (isSelf != null) {
    planTaskOneNewEntity.isSelf = isSelf;
  }
  final String? areaUuid = jsonConvert.convert<String>(json['area_uuid']);
  if (areaUuid != null) {
    planTaskOneNewEntity.areaUuid = areaUuid;
  }
  final List<PlanTaskOneNewMediaList>? mediaList = (json['media_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<PlanTaskOneNewMediaList>(e) as PlanTaskOneNewMediaList).toList();
  if (mediaList != null) {
    planTaskOneNewEntity.mediaList = mediaList;
  }
  final List<PlanTaskOneNewDealMediaList>? dealMediaList = (json['deal_media_list'] as List<dynamic>?)?.map(
          (e) => jsonConvert.convert<PlanTaskOneNewDealMediaList>(e) as PlanTaskOneNewDealMediaList).toList();
  if (dealMediaList != null) {
    planTaskOneNewEntity.dealMediaList = dealMediaList;
  }
  return planTaskOneNewEntity;
}

Map<String, dynamic> $PlanTaskOneNewEntityToJson(PlanTaskOneNewEntity entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['project_name'] = entity.projectName;
  data['project_uuid'] = entity.projectUuid;
  data['project_short_name'] = entity.projectShortName;
  data['area_name'] = entity.areaName;
  data['work_content'] = entity.workContent;
  data['start_date'] = entity.startDate;
  data['end_date'] = entity.endDate;
  data['execute_user_uuid'] = entity.executeUserUuid;
  data['execute_user_name'] = entity.executeUserName;
  data['task_type'] = entity.taskType;
  data['source_type'] = entity.sourceType;
  data['finished_time'] = entity.finishedTime;
  data['task_status'] = entity.taskStatus;
  data['task_status_name'] = entity.taskStatusName;
  data['is_self'] = entity.isSelf;
  data['area_uuid'] = entity.areaUuid;
  data['media_list'] = entity.mediaList?.map((v) => v.toJson()).toList();
  data['deal_media_list'] = entity.dealMediaList?.map((v) => v.toJson()).toList();
  return data;
}

extension PlanTaskOneNewEntityExtension on PlanTaskOneNewEntity {
  PlanTaskOneNewEntity copyWith({
    String? uuid,
    String? projectName,
    String? projectUuid,
    String? projectShortName,
    String? areaName,
    String? workContent,
    String? startDate,
    String? endDate,
    String? executeUserUuid,
    String? executeUserName,
    String? taskType,
    String? sourceType,
    String? finishedTime,
    String? taskStatus,
    String? taskStatusName,
    String? isSelf,
    String? areaUuid,
    List<PlanTaskOneNewMediaList>? mediaList,
    List<PlanTaskOneNewDealMediaList>? dealMediaList,
  }) {
    return PlanTaskOneNewEntity()
      ..uuid = uuid ?? this.uuid
      ..projectName = projectName ?? this.projectName
      ..projectUuid = projectUuid ?? this.projectUuid
      ..projectShortName = projectShortName ?? this.projectShortName
      ..areaName = areaName ?? this.areaName
      ..workContent = workContent ?? this.workContent
      ..startDate = startDate ?? this.startDate
      ..endDate = endDate ?? this.endDate
      ..executeUserUuid = executeUserUuid ?? this.executeUserUuid
      ..executeUserName = executeUserName ?? this.executeUserName
      ..taskType = taskType ?? this.taskType
      ..sourceType = sourceType ?? this.sourceType
      ..finishedTime = finishedTime ?? this.finishedTime
      ..taskStatus = taskStatus ?? this.taskStatus
      ..taskStatusName = taskStatusName ?? this.taskStatusName
      ..isSelf = isSelf ?? this.isSelf
      ..areaUuid = areaUuid ?? this.areaUuid
      ..mediaList = mediaList ?? this.mediaList
      ..dealMediaList = dealMediaList ?? this.dealMediaList;
  }
}

PlanTaskOneNewMediaList $PlanTaskOneNewMediaListFromJson(Map<String, dynamic> json) {
  final PlanTaskOneNewMediaList planTaskOneNewMediaList = PlanTaskOneNewMediaList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    planTaskOneNewMediaList.uuid = uuid;
  }
  final String? mediaType = jsonConvert.convert<String>(json['media_type']);
  if (mediaType != null) {
    planTaskOneNewMediaList.mediaType = mediaType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    planTaskOneNewMediaList.mediaUrl = mediaUrl;
  }
  final String? originMediaUrl = jsonConvert.convert<String>(json['origin_media_url']);
  if (originMediaUrl != null) {
    planTaskOneNewMediaList.originMediaUrl = originMediaUrl;
  }
  final String? videoCoverUrl = jsonConvert.convert<String>(json['video_cover_url']);
  if (videoCoverUrl != null) {
    planTaskOneNewMediaList.videoCoverUrl = videoCoverUrl;
  }
  final String? picThumb = jsonConvert.convert<String>(json['pic_thumb']);
  if (picThumb != null) {
    planTaskOneNewMediaList.picThumb = picThumb;
  }
  return planTaskOneNewMediaList;
}

Map<String, dynamic> $PlanTaskOneNewMediaListToJson(PlanTaskOneNewMediaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['media_type'] = entity.mediaType;
  data['media_url'] = entity.mediaUrl;
  data['origin_media_url'] = entity.originMediaUrl;
  data['video_cover_url'] = entity.videoCoverUrl;
  data['pic_thumb'] = entity.picThumb;
  return data;
}

extension PlanTaskOneNewMediaListExtension on PlanTaskOneNewMediaList {
  PlanTaskOneNewMediaList copyWith({
    String? uuid,
    String? mediaType,
    String? mediaUrl,
    String? originMediaUrl,
    String? videoCoverUrl,
    String? picThumb,
  }) {
    return PlanTaskOneNewMediaList()
      ..uuid = uuid ?? this.uuid
      ..mediaType = mediaType ?? this.mediaType
      ..mediaUrl = mediaUrl ?? this.mediaUrl
      ..originMediaUrl = originMediaUrl ?? this.originMediaUrl
      ..videoCoverUrl = videoCoverUrl ?? this.videoCoverUrl
      ..picThumb = picThumb ?? this.picThumb;
  }
}

PlanTaskOneNewDealMediaList $PlanTaskOneNewDealMediaListFromJson(Map<String, dynamic> json) {
  final PlanTaskOneNewDealMediaList planTaskOneNewDealMediaList = PlanTaskOneNewDealMediaList();
  final String? uuid = jsonConvert.convert<String>(json['uuid']);
  if (uuid != null) {
    planTaskOneNewDealMediaList.uuid = uuid;
  }
  final String? mediaType = jsonConvert.convert<String>(json['media_type']);
  if (mediaType != null) {
    planTaskOneNewDealMediaList.mediaType = mediaType;
  }
  final String? mediaUrl = jsonConvert.convert<String>(json['media_url']);
  if (mediaUrl != null) {
    planTaskOneNewDealMediaList.mediaUrl = mediaUrl;
  }
  final String? originMediaUrl = jsonConvert.convert<String>(json['origin_media_url']);
  if (originMediaUrl != null) {
    planTaskOneNewDealMediaList.originMediaUrl = originMediaUrl;
  }
  final String? videoCoverUrl = jsonConvert.convert<String>(json['video_cover_url']);
  if (videoCoverUrl != null) {
    planTaskOneNewDealMediaList.videoCoverUrl = videoCoverUrl;
  }
  final String? picThumb = jsonConvert.convert<String>(json['pic_thumb']);
  if (picThumb != null) {
    planTaskOneNewDealMediaList.picThumb = picThumb;
  }
  return planTaskOneNewDealMediaList;
}

Map<String, dynamic> $PlanTaskOneNewDealMediaListToJson(PlanTaskOneNewDealMediaList entity) {
  final Map<String, dynamic> data = <String, dynamic>{};
  data['uuid'] = entity.uuid;
  data['media_type'] = entity.mediaType;
  data['media_url'] = entity.mediaUrl;
  data['origin_media_url'] = entity.originMediaUrl;
  data['video_cover_url'] = entity.videoCoverUrl;
  data['pic_thumb'] = entity.picThumb;
  return data;
}

extension PlanTaskOneNewDealMediaListExtension on PlanTaskOneNewDealMediaList {
  PlanTaskOneNewDealMediaList copyWith({
    String? uuid,
    String? mediaType,
    String? mediaUrl,
    String? originMediaUrl,
    String? videoCoverUrl,
    String? picThumb,
  }) {
    return PlanTaskOneNewDealMediaList()
      ..uuid = uuid ?? this.uuid
      ..mediaType = mediaType ?? this.mediaType
      ..mediaUrl = mediaUrl ?? this.mediaUrl
      ..originMediaUrl = originMediaUrl ?? this.originMediaUrl
      ..videoCoverUrl = videoCoverUrl ?? this.videoCoverUrl
      ..picThumb = picThumb ?? this.picThumb;
  }
}