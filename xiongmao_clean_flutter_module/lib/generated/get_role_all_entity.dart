import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/get_role_all_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/get_role_all_entity.g.dart';

@JsonSerializable()
class GetRoleAllEntity {
	List<GetRoleAllList>? list = [];

	GetRoleAllEntity();

	factory GetRoleAllEntity.fromJson(Map<String, dynamic> json) => $GetRoleAllEntityFromJson(json);

	Map<String, dynamic> toJson() => $GetRoleAllEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class GetRoleAllList {
	String? id = '';
	String? name = '';
	@JSONField(name: "role_desc")
	String? roleDesc = '';

	GetRoleAllList();

	factory GetRoleAllList.fromJson(Map<String, dynamic> json) => $GetRoleAllListFromJson(json);

	Map<String, dynamic> toJson() => $GetRoleAllListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}