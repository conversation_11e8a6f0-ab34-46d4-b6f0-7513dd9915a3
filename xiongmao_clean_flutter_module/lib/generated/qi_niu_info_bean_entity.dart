import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/qi_niu_info_bean_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/qi_niu_info_bean_entity.g.dart';

@JsonSerializable()
class QiNiuInfoBeanEntity {
	String? bucket;
	String? token;

	QiNiuInfoBeanEntity();

	factory QiNiuInfoBeanEntity.fromJson(Map<String, dynamic> json) => $QiNiuInfoBeanEntityFromJson(json);

	Map<String, dynamic> toJson() => $QiNiuInfoBeanEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}