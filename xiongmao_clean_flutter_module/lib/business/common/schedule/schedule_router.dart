import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/holiday_add_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/holiday_one_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/holiday_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/schedule_export_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/schedule_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/schedule_reset_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/work_overtime_add_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/work_overtime_one_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/page/work_overtime_page.dart';

import '../../../net/http_config.dart';

/// 排班表
const schedulePage = "SchedulePage";

/// 导出排除表
const scheduleExportPage = "ScheduleExportPage";

/// 排班表重置
const scheduleResetPage = "ScheduleResetPage";

/// 加班
const workOvertimePage = "WorkOvertimePage";

///添加、编辑加班记录
const workOvertimeAddPage = "WorkOvertimeAddPage";

///加班详情
const workOvertimeOnePage = "WorkOvertimeOnePage";

///请假日期
const holidayPage = "HolidayPage";

///添加、编辑加班记录
const holidayAddPage = "HolidayAddPage";

///加班详情
const holidayOnePage = "HolidayOnePage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> scheduleRouterMap = {
  schedulePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '全部项目';
          return SchedulePage(
            projectName: projectName,
            projectUuid: projectUuid,
          );
        });
  },

  ///排班表重置
  scheduleResetPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '';
          String resetDate = map['reset_date'] ?? '';
          return ScheduleResetPage(
            projectName: projectName,
            projectUuid: projectUuid,
            resetDate: resetDate,
          );
        });
  },

  ///排班表导出
  scheduleExportPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '';
          String exportDate = map['export_date'] ?? '';
          return ScheduleExportPage(
            projectName: projectName,
            projectUuid: projectUuid,
            exportDate: exportDate,
          );
        });
  },

  ///加班
  workOvertimePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '全部项目';
          return WorkOvertimePage(
            projectName: projectName,
            projectUuid: projectUuid,
          );
        });
  },

  ///添加、编辑加班记录
  workOvertimeAddPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid']; //这里针对修改的情况下，才有的uuid
          String? user_uuid = map['user_uuid']; //用户的user_uuid
          String? user_name = map['user_name']; //用户的user_name
          String? user_avatar = map['user_avatar']; //用户的user_avatar
          String? current_date = map['current_date']; //用户的user_avatar
          String? user_project_name = map['user_project_name']; //用户的user_avatar
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '全部项目';
          return WorkOvertimeAddPage(
            uuid: uuid,
            user_uuid: user_uuid,
            user_name: user_name,
            user_avatar: user_avatar,
            current_date: current_date,
            user_project_name: user_project_name,
            projectName: projectName,
            projectUuid: projectUuid,
          );
        });
  },

  ///加班详情
  workOvertimeOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          return WorkOvertimeOnePage(
            uuid: uuid,
          );
        });
  },

  ///请假列表
  holidayPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '全部项目';
          return HolidayPage(
            projectName: projectName,
            projectUuid: projectUuid,
          );
        });
  },

  ///请假详情
  holidayAddPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? user_uuid = map['user_uuid']; //用户的user_uuid
          String? user_name = map['user_name']; //用户的user_name
          String? user_avatar = map['user_avatar']; //用户的user_avatar
          String? current_date = map['current_date']; //用户的user_avatar
          String? user_project_name = map['user_project_name']; //用户的user_avatar
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '全部项目';
          return HolidayAddPage(
            uuid: uuid,
            user_uuid: user_uuid,
            user_name: user_name,
            user_avatar: user_avatar,
            current_date: current_date,
            user_project_name: user_project_name,
            projectName: projectName,
            projectUuid: projectUuid,
          );
        });
  },

  ///新增、编辑请假
  holidayOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];

          return HolidayOnePage(
            uuid: uuid,
          );
        });
  },
};
