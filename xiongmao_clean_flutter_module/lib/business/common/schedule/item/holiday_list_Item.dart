import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/holiday_data_entity.dart';
import '../bean/work_overtime_data_entity.dart';

class HolidayListItem extends StatelessWidget {
  final HolidayDataList data;

  final Function(int) onClick;

  HolidayListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick(3);
      },
      child: Container(
        margin: EdgeInsets.only(left: 10, right: 10, top: 10),
        padding: EdgeInsets.symmetric(vertical: 12, horizontal: 12),
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///顶部
            CommonUtils.getSimpleText('${data.introduction}', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            Row(
              children: [
                CommonUtils.getSimpleText('所属项目：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.projectName ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('请假时段：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText('${data.startDate?.replaceAll("-", '.')}(${data.startTypeName}) ~ ${data.endDate?.replaceAll("-", ".")}(${data.endTypeName})', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('请假类型：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.holidayTypeName ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('请假原因：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText((!TextUtil.isEmpty(data.reason) ? data.reason : '-') ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                    onTap: () {
                      onClick(1);
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('编辑', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      onClick(2);
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('详情', 16, Colours.base_primary, textAlign: TextAlign.center),
                  onTap: () {
                    onClick(3);
                  },
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
