import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../jigsaw_puzzle/controller/jigasw_puzzle_controller.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/holiday_controller.dart';
import '../controller/work_overtime_controller.dart';

class HolidayStaffListItem extends StatelessWidget {
  final WorkOvertimeOneUserList data;

  final Function onClick;

  bool? hideDelBut = true;

  HolidayStaffListItem({
    required this.data,
    required this.onClick,
    this.hideDelBut,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        top: 12,
      ),
      child: Row(
        children: [
          CustomAvatarView(
            name: data.userName,
            avatarUrl: data.avatar,
          ),
          Gaps.hGap6,
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.baseline, // 设置交叉轴对齐方式为基线对齐
                textBaseline: TextBaseline.alphabetic, // 指定基线类型为字母基线
                children: [
                  CommonUtils.getSimpleText(
                    data.userName,
                    14,
                    Colours.base_primary_text_title,
                    fontWeight: FontWeight.bold,
                  ),
                  Gaps.hGap4,
                  CommonUtils.getSimpleText(
                    data.projectName,
                    12,
                    Colours.base_primary_jv,
                  )
                ],
              ),
              CommonUtils.getSimpleText(
                '${data.customStatusName}',
                14,
                Colours.base_primary_text_caption,
              ),
            ],
          )),
          Visibility(
            visible: hideDelBut == false,
            child: InkWell(
              child: const LoadAssetImage(
                'common/icon_approve_delete',
                height: 20,
                width: 20,
              ),
              onTap: () {
                onClick();
              },
            ),
          )
        ],
      ),
    );
  }
}
