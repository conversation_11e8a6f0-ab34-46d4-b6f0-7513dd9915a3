import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/schedule_data_entity.dart';

class ScheduleListItem extends StatelessWidget {
  final ScheduleDataListScheduleList data;

  int position;

  ScheduleListItem({required this.data, required this.position});

  @override
  Widget build(BuildContext context) {
    return Container(
      alignment: Alignment.center,
      width: 20,
      height: 20,
      decoration: BoxDecoration(
        color: Colours.base_primary,
        borderRadius: BorderRadius.circular(6),
      ),
      child: CommonUtils.getSimpleText(data.className, 14, Colours.white, height: 1, textAlign: TextAlign.center, fontWeight: FontWeight.bold, softWrap: false, maxLines: 2),
    );
  }
}
