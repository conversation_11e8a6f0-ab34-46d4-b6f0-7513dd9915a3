import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/work_overtime_data_entity.dart';

class WorkOvertimeListItem extends StatelessWidget {
  final WorkOvertimeDataList data;

  final Function(int) onClick;

  WorkOvertimeListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick(3);
      },
      child: Container(
        margin: const EdgeInsets.only(left: 10, right: 10, top: 10),
        padding: const EdgeInsets.only(left: 14, right: 14, top: 16, bottom: 16),
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///顶部
            CommonUtils.getSimpleText('${data.introduction}', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold, textAlign: TextAlign.left),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            Row(
              children: [
                CommonUtils.getSimpleText('所属项目：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.projectName ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('加班日期：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.overtimeDate ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,

            Row(
              children: [
                CommonUtils.getSimpleText('加班类型：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.overtimeTypeName ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('加班时段：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText('${data.startTime?.replaceAll("-", '.')}~${data.endTime?.replaceAll("-", ".")}', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('加班时长：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText('${CommonUtils.formatDecimal(data.overtimeLong) ?? '-'} 小时', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              children: [
                CommonUtils.getSimpleText('加班原因：', 14, Colours.base_primary_text_hint),
                Expanded(child: CommonUtils.getSimpleText(data.reason ?? '-', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                    onTap: () {
                      onClick(1);
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                  child: InkWell(
                    child: CommonUtils.getSimpleText('编辑', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      onClick(2);
                    },
                  ),
                ),
                Gaps.vLine,
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('详情', 16, Colours.base_primary, textAlign: TextAlign.center),
                  onTap: () {
                    onClick(3);
                  },
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
