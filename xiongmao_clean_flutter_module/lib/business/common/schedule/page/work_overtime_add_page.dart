import 'dart:async';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../approve/bean/approve_detail_entity.dart';
import '../../../../generated/role_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/schedule_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../item/schedule_list_Item.dart';
import '../item/work_overtime_list_Item.dart';
import '../item/work_overtime_staff_list_Item.dart';
import '../iview/schedule_iview.dart';
import '../iview/work_overtime_iview.dart';
import '../persenter/schedule_persenter.dart';
import '../persenter/work_overtime_persenter.dart';

class WorkOvertimeAddPage extends StatefulWidget {
  String? uuid;
  String? user_uuid;
  String? user_name;
  String? user_avatar;
  String? current_date;
  String? user_project_name;
  String projectUuid;
  String projectName;

  WorkOvertimeAddPage({super.key, this.uuid, this.user_uuid, this.user_name, this.user_avatar, this.current_date, this.user_project_name, required this.projectUuid, required this.projectName});

  @override
  _WorkOvertimeAddPageState createState() => _WorkOvertimeAddPageState();
}

class _WorkOvertimeAddPageState extends State<WorkOvertimeAddPage> with BasePageMixin<WorkOvertimeAddPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkOvertimeAddPage> implements WorkOvertimeIView {
  WorkOvertimePresenter? _presenter;

  final WorkOvertimeController _controller = WorkOvertimeController();

  String title = '添加加班记录';

  late TextEditingController textEditingController;

  late TextEditingController remarkEditingController;

  VoidCallback? selectOvertimeProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;

    if (!TextUtil.isEmpty(widget.uuid)) {
      title = '编辑加班记录';
      _presenter?.getWorkOverTimeOne(widget.uuid);
    } else {
      if (!TextUtil.isEmpty(widget.user_uuid)) {
        //设置当前日期
        if (!TextUtil.isEmpty(widget.current_date)) {
          _controller.workOverTimeBelongDateTime = DateTime.parse(widget.current_date!);
        } else {
          _controller.workOverTimeBelongDateTime = DateTime.now();
        }
      } else {
        _controller.workOverTimeBelongDateTime = DateTime.now();
      }
      _controller.workOverTimeBelongDate.value = DateUtil.formatDate(_controller.workOverTimeBelongDateTime, format: 'yyyy/MM/dd');
    }

    if (!TextUtil.isEmpty(widget.user_uuid)) {
      _controller.workOverTimeUserList.clear();
      //查看是否从原生过来 直接给客户增加内容
      WorkOvertimeOneUserList data = WorkOvertimeOneUserList();
      data.uuid = widget.user_uuid;
      data.userName = widget.user_name;
      data.avatar = widget.user_avatar;
      data.projectName = widget.user_project_name;
      data.customStatusName = '${_controller.workOverTimeStyle.value}加班：${(TextUtil.isEmpty(_controller.workOverTimeLong.value) ? '0' : _controller.workOverTimeLong.value)} 小时';
      _controller.workOverTimeUserList.add(data);
    }

    selectOvertimeProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
      });
    });

    // 初始化TextEditingController
    textEditingController = TextEditingController(text: _controller.workOverTimeLong.value);

    remarkEditingController = TextEditingController(text: _controller.workOverTimeRemark.value);

    // 监听Rx变量的变化
    ever<String>(_controller.workOverTimeLong, (String newValue) {
      textEditingController.text = newValue;
    });

    // 监听Rx变量的变化
    ever<String>(_controller.workOverTimeRemark, (String newValue) {
      remarkEditingController.text = newValue;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: title,
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: WillPopScope(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Obx(() => BrnTextSelectFormItem(
                      title: "所属项目",
                      isRequire: true,
                      isEdit: false,
                      value: _controller.projectName.value,
                      onTap: () {

                      },
                    )),
                Gaps.line,
                Obx(() => BrnTextSelectFormItem(
                      title: "加班日期",
                      isRequire: true,
                      value: _controller.workOverTimeBelongDate.value,
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          initialDateTime: _controller.workOverTimeBelongDateTime,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.date,
                          dateFormat: 'yyyy年,MMMM月,dd日',
                          onConfirm: (dateTime, list) {
                            _controller.workOverTimeBelongDate.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd');
                            _controller.workOverTimeBelongDateTime = dateTime;
                          },
                        );
                      },
                    )),
                Gaps.line,

                Obx(() => BrnRadioInputFormItem(
                      title: "加班类型",
                      options: const [
                        "休息日",
                        "节假日",
                        "工作日",
                      ],
                      value: _controller.workOverTimeStyle.value,
                      onChanged: (oldValue, newValue) {
                        _controller.workOverTimeStyle.value = newValue ?? '';
                        _controller.updateUserList();
                      },
                    )),
                Gaps.line,
                Obx(() => BrnTextSelectFormItem(
                      title: "加班开始时间",
                      value: _controller.workOverTimeStartTime.value,
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          initialDateTime: (_controller.workOverTimeStartDate == null) ? _controller.workOverTimeBelongDateTime : _controller.workOverTimeStartDate,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.datetime,
                          dateFormat: 'yyyy年,MMMM月,dd日,HH时:mm分',
                          onConfirm: (dateTime, list) {
                            _controller.workOverTimeStartDate = dateTime;
                            _controller.workOverTimeStartTime.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd HH:mm');
                            _controller.calculateHours(context);
                          },
                        );
                      },
                    )),
                Gaps.line,
                Obx(() => BrnTextSelectFormItem(
                      title: "加班结束时间",
                      value: _controller.workOverTimeEndTime.value,
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          initialDateTime: (_controller.workOverTimeEndDate == null) ? _controller.workOverTimeBelongDateTime : _controller.workOverTimeEndDate,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.datetime,
                          dateFormat: 'yyyy年,MMMM月,dd日,HH时:mm分',
                          onConfirm: (dateTime, list) {
                            _controller.workOverTimeEndTime.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd HH:mm');
                            _controller.workOverTimeEndDate = dateTime;
                            _controller.calculateHours(context);
                          },
                        );
                      },
                    )),
                Gaps.line,
                BrnTextInputFormItem(
                  themeData: BrnFormItemConfig(),
                  controller: textEditingController,
                  title: "加班时长",
                  unit: "小时",
                  hint: "请输入",
                  maxCharCount: 4,
                  inputType: BrnInputType.number,
                  isRequire: true,
                  onChanged: (newValue) {
                    _controller.workOverTimeLong.value = newValue;
                    _controller.updateUserList();
                  },
                ),
                Gaps.line,

                BrnTextInputFormItem(
                  themeData: BrnFormItemConfig(),
                  controller: remarkEditingController,
                  title: "加班原因",
                  hint: "请输入加班原因",
                  isRequire: false,
                  onChanged: (newValue) {
                    _controller.workOverTimeRemark.value = newValue;
                  },
                ),

                ///加班员工
                Container(
                  margin: EdgeInsets.only(top: 10, bottom: 50),
                  color: Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(top: 20, bottom: 20, left: 16, right: 16),
                        child: Row(
                          children: [
                            Expanded(
                                child: Row(
                              children: [
                                const Padding(
                                  padding: EdgeInsets.only(top: 2),
                                  child: LoadAssetImage(
                                    'common/icon_work_overtime',
                                    width: 20,
                                    height: 20,
                                  ),
                                ),
                                CommonUtils.getSimpleText('加班员工', 14, Colours.base_primary_text_caption),
                              ],
                            )),
                            InkWell(
                              child: CommonUtils.getSimpleText('添加员工', 14, Colours.base_primary, fontWeight: FontWeight.bold),
                              onTap: () {
                                BoostNavigator.instance.push('selectPersonnelPage', arguments: {
                                  'title': '选择加班人员',
                                  'channel': 1,
                                  'status': '1',
                                  'multiple': true,
                                  'contract_start_date': '${_controller.workOverTimeBelongDateTime?.year}-${CommonUtils.formatToTwoDigits(_controller.workOverTimeBelongDateTime?.month ?? 0)}-${CommonUtils.formatToTwoDigits(_controller.workOverTimeBelongDateTime?.day ?? 0)}',
                                  'contract_end_date': '${_controller.workOverTimeBelongDateTime?.year}-${CommonUtils.formatToTwoDigits(_controller.workOverTimeBelongDateTime?.month ?? 0)}-${CommonUtils.formatToTwoDigits(_controller.workOverTimeBelongDateTime?.day ?? 0)}',
                                  'uuids': json.encode(
                                    _controller.workOverTimeUserList.value.map((e) => e.uuid).toList() ?? [],
                                  ),
                                }).then((value) {
                                  if (value != null) {
                                    List<ProjectArchivesList> items = value as List<ProjectArchivesList>;
                                    print('拿到的数据---$items}');
                                    for (ProjectArchivesList item in items) {
                                      // 检查列表中是否已经存在相同的 uuid
                                      bool exists = _controller.workOverTimeUserList.any((data) => data.uuid == item.uuid);

                                      if (!exists) {
                                        WorkOvertimeOneUserList data = WorkOvertimeOneUserList();
                                        data.uuid = item.uuid;
                                        data.userName = item.userName;
                                        data.avatar = item.avatar;
                                        data.customStatusName = '${_controller.workOverTimeStyle.value}加班：${(TextUtil.isEmpty(_controller.workOverTimeLong.value) ? '0' : _controller.workOverTimeLong.value)} 小时';
                                        data.projectName = item.projectShortName;

                                        // 添加到用户列表
                                        _controller.workOverTimeUserList.add(data);
                                      }
                                    }
                                  }
                                });
                              },
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: _controller.workOverTimeUserList.isNotEmpty,
                        child: Gaps.line,
                      ),
                      Obx(() => ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(top: 10, left: 16, right: 16, bottom: 10),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.workOverTimeUserList.length,
                            // Example item count
                            itemBuilder: (context, index) {
                              return WorkOvertimeStaffListItem(
                                hideDelBut: false,
                                data: _controller.workOverTimeUserList[index],
                                onClick: () {
                                  _controller.deleteUserList(_controller.workOverTimeUserList[index]);
                                },
                              );
                            },
                          )),
                    ],
                  ),
                )
              ],
            ),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '提交',
          onTap: () {
            if (TextUtil.isEmpty(_controller.workOverTimeBelongDate.value)) {
              BrnToast.show('请选择加班日期', context);
              return;
            }

            if (TextUtil.isEmpty(_controller.workOverTimeLong.value)) {
              BrnToast.show('请输入加班时长', context);
              return;
            }

            //判断条件1 加班开始/结束时间，不能只填写1个 做对比
            if (_controller.workOverTimeStartTime.value.isNotEmpty || _controller.workOverTimeEndTime.value.isNotEmpty) {
              if (_controller.workOverTimeStartTime.value.isEmpty) {
                BrnToast.show('加班开始/结束时间，不能只填写1个', context);
                return;
              } else if (_controller.workOverTimeEndTime.value.isEmpty) {
                BrnToast.show('加班开始/结束时间，不能只填写1个', context);
                return;
              }
            }
            //判断条件2 加班结束时间不能早于加班开始时间 做对比
            if (_controller.workOverTimeStartDate != null && _controller.workOverTimeEndDate != null) {
              if (_controller.workOverTimeEndDate!.isBefore(_controller.workOverTimeStartDate!)) {
                // 如果结束时间早于开始时间
                BrnToast.show('加班结束时间不能早于开始时间', context);
                return;
              }
            }
            //判断条件3 加班开始时间、结束时间，必须在加班日期的前后一天之内（如加班日期是2号，开始时间结束时间必须在1号2号3号之内），否则提示：加班开始/结束时间，必须在加班日期当天及前后1天之内。
            if (_controller.workOverTimeBelongDateTime != null && _controller.workOverTimeStartDate != null && _controller.workOverTimeEndDate != null) {
              // 获取加班日期的前一天和后一天
              DateTime startBoundary = _controller.workOverTimeBelongDateTime!.subtract(const Duration(days: 1));
              DateTime endBoundary = _controller.workOverTimeBelongDateTime!.add(const Duration(days: 1));

              // 判断开始时间和结束时间是否在有效范围内
              if (_controller.workOverTimeStartDate!.isBefore(startBoundary) || _controller.workOverTimeStartDate!.isAfter(endBoundary)) {
                BrnToast.show('加班开始时间必须在加班日期当天及前后1天之内', context);
                return;
              }

              if (_controller.workOverTimeEndDate!.isBefore(startBoundary) || _controller.workOverTimeEndDate!.isAfter(endBoundary)) {
                BrnToast.show('加班结束时间必须在加班日期当天及前后1天之内', context);
                return;
              }

              // 可选：检查开始时间是否在结束时间之后
              if (_controller.workOverTimeStartDate!.isAfter(_controller.workOverTimeEndDate!)) {
                BrnToast.show('加班开始时间不能晚于结束时间', context);
                return;
              }
            }

            if (!TextUtil.isEmpty(_controller.workOverTimeLong.value)) {
              if (_controller.workOverTimeLong.value.contains('.')) {
                if (double.parse(_controller.workOverTimeLong.value) >= 24.00) {
                  BrnToast.show('加班时长过长，超过了24小时，请拆成多段分段提交', context);
                  return;
                }
              } else {
                if (int.parse(_controller.workOverTimeLong.value) >= 24) {
                  BrnToast.show('加班时长过长，超过了24小时，请拆成多段分段提交', context);
                  return;
                }
              }
            }

            _presenter?.saveWorkOverTime(widget.uuid);
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkOvertimePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onStatus() {
    BrnToast.show('保存成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
    textEditingController.dispose(); // 记得释放资源
    remarkEditingController.dispose(); // 记得释放资源
    selectOvertimeProjectListener?.call();
  }
}
