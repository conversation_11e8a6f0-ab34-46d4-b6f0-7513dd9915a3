import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/schedule/controller/holiday_controller.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/net/net.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/work_overtime_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../item/holiday_staff_list_Item.dart';
import '../item/schedule_list_Item.dart';
import '../item/work_overtime_list_Item.dart';
import '../item/work_overtime_staff_list_Item.dart';
import '../iview/holiday_iview.dart';
import '../iview/schedule_iview.dart';
import '../iview/work_overtime_iview.dart';
import '../persenter/holiday_persenter.dart';
import '../persenter/schedule_persenter.dart';
import '../persenter/work_overtime_persenter.dart';

class HolidayOnePage extends StatefulWidget {
  String? uuid;

  HolidayOnePage({super.key, this.uuid});

  @override
  _HolidayOnePageState createState() => _HolidayOnePageState();
}

class _HolidayOnePageState extends State<HolidayOnePage> with BasePageMixin<HolidayOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<HolidayOnePage> implements HolidayIView {
  HolidayPresenter? _presenter;

  final HolidayController _controller = HolidayController();

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.getWorkHolidayOne(widget.uuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '请假详情',
        centerSubTitle: _controller.projectName.value ?? '',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: WillPopScope(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('所属项目', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.projectName.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('请假开始', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workHolidayStartShowTime.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('请假结束', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workHolidayEndShowTime.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('请假类型', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workHolidayStyle.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('请假原因', 14, Colours.base_primary_text_hint)),
                            Obx(() => Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(_controller.workHolidayRemark.value)) ? "-" : _controller.workHolidayRemark.value, 14, Colours.base_primary_text_title, textAlign: TextAlign.end))),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('创建人', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workHolidayOne.value.createUserName, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('创建时间', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workHolidayOne.value.createTime, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 20, bottom: 50),
                  color: Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                        child: Row(
                          children: [
                            const LoadAssetImage(
                              'common/icon_work_overtime',
                              width: 20,
                              height: 20,
                            ),
                            CommonUtils.getSimpleText('请假员工', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Obx(() => ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(
                              top: 0,
                              left: 16,
                              right: 16,
                              bottom: 10,
                            ),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.workHolidayUserList.length,
                            // Example item count
                            itemBuilder: (context, index) {
                              return HolidayStaffListItem(
                                hideDelBut: true,
                                data: _controller.workHolidayUserList[index],
                                onClick: () {},
                              );
                            },
                          )),
                    ],
                  ),
                )
              ],
            ),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: Row(
          children: [
            InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
                decoration: BoxDecoration(
                  color: Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('删除', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                DialogManager.showConfirmDialog(
                    context: context,
                    title: '删除',
                    cancel: '取消',
                    confirm: '删除',
                    message: '是否确认删除？',
                    onConfirm: () {
                      _presenter?.deleteWorkHolidayRecord(widget.uuid);
                    },
                    onCancel: () {});
              },
            ),
            Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('编辑', 15, Colours.white, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                BoostNavigator.instance.push('HolidayAddPage', arguments: {'uuid': widget.uuid ?? ''}).then((value) => _presenter?.getWorkHolidayOne(widget.uuid));
              },
            ))
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = HolidayPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onStatus() {
    BoostNavigator.instance.pop();
  }
}
