import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/work_overtime_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../item/schedule_list_Item.dart';
import '../item/work_overtime_list_Item.dart';
import '../item/work_overtime_staff_list_Item.dart';
import '../iview/schedule_iview.dart';
import '../iview/work_overtime_iview.dart';
import '../persenter/schedule_persenter.dart';
import '../persenter/work_overtime_persenter.dart';

class WorkOvertimeOnePage extends StatefulWidget {
  String? uuid;

  WorkOvertimeOnePage({super.key, this.uuid});

  @override
  _WorkOvertimeOnePageState createState() => _WorkOvertimeOnePageState();
}

class _WorkOvertimeOnePageState extends State<WorkOvertimeOnePage> with BasePageMixin<WorkOvertimeOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkOvertimeOnePage> implements WorkOvertimeIView {
  WorkOvertimePresenter? _presenter;

  final WorkOvertimeController _controller = WorkOvertimeController();

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.getWorkOverTimeOne(widget.uuid);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '加班详情',
        centerSubTitle: _controller.projectName.value ?? '',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: WillPopScope(
          child: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('所属项目', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.projectName.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班日期', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeBelongDate.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班类型', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeStyle.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班开始时间', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeStartTime.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班结束时间', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeEndTime.value, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班时长', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText('${CommonUtils.formatDecimal(_controller.workOverTimeLong.value)} 小时', 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 14, bottom: 14),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('加班原因', 14, Colours.base_primary_text_hint)),
                            Obx(() => Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(_controller.workOverTimeRemark.value)) ? "-" : _controller.workOverTimeRemark.value, 14, Colours.base_primary_text_title, textAlign: TextAlign.end))),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 20,
                          bottom: 20,
                        ),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('创建人', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeOne.value.createUserName, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 16,
                          right: 16,
                          top: 20,
                          bottom: 20,
                        ),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('创建时间', 14, Colours.base_primary_text_hint)),
                            Obx(() => CommonUtils.getSimpleText(_controller.workOverTimeOne.value.createTime, 14, Colours.base_primary_text_title)),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 10, bottom: 50),
                  color: Colors.white,
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 16, top: 16),
                        child: Row(
                          children: [
                            const LoadAssetImage(
                              'common/icon_work_overtime',
                              width: 20,
                              height: 20,
                            ),
                            CommonUtils.getSimpleText('加班员工', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                          ],
                        ),
                      ),
                      Gaps.line,
                      Obx(() => ListView.builder(
                            shrinkWrap: true,
                            padding: const EdgeInsets.only(
                              top: 0,
                              left: 16,
                              right: 16,
                              bottom: 10,
                            ),
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.workOverTimeUserList.length,
                            // Example item count
                            itemBuilder: (context, index) {
                              return WorkOvertimeStaffListItem(
                                hideDelBut: true,
                                data: _controller.workOverTimeUserList[index],
                                onClick: () {},
                              );
                            },
                          )),
                    ],
                  ),
                )
              ],
            ),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: Row(
          children: [
            InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
                decoration: BoxDecoration(
                  color: Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('删除', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                DialogManager.showConfirmDialog(
                    context: context,
                    title: '删除',
                    cancel: '取消',
                    confirm: '删除',
                    message: '是否确认删除？',
                    onConfirm: () {
                      _presenter?.deleteWorkOverTimeRecord(widget.uuid);
                    },
                    onCancel: () {});
              },
            ),
            Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('编辑', 15, Colours.white, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                BoostNavigator.instance.push('WorkOvertimeAddPage', arguments: {'uuid': widget.uuid ?? ''}).then((value) => _presenter?.getWorkOverTimeOne(widget.uuid));
              },
            ))
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkOvertimePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onStatus() {
    BoostNavigator.instance.pop();
  }
}
