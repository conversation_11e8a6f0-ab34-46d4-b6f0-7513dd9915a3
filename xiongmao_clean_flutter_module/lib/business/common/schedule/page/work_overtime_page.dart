import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_search_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/work_overtime_data_entity.dart';
import '../controller/schedule_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../controller/work_overtime_page_controller.dart';
import '../item/schedule_list_Item.dart';
import '../item/work_overtime_list_Item.dart';
import '../iview/schedule_iview.dart';
import '../iview/work_overtime_iview.dart';
import '../persenter/schedule_persenter.dart';
import '../persenter/work_overtime_page_persenter.dart';
import '../persenter/work_overtime_persenter.dart';

class WorkOvertimePage extends StatefulWidget {
  String projectUuid;
  String projectName;

  WorkOvertimePage({super.key, required this.projectUuid, required this.projectName});

  @override
  _WorkOvertimePageState createState() => _WorkOvertimePageState();
}

class _WorkOvertimePageState extends State<WorkOvertimePage> with BasePageMixin<WorkOvertimePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkOvertimePage> implements WorkOvertimeIView {
  WorkOvertimeManagerPagePresenter? _presenter;

  final WorkOvertimePageController _controller = WorkOvertimePageController();

  VoidCallback? addProjectListener;

  DateTime currentMonth = DateTime.now();

  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;

    if ((httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID)) {
      _controller.projectUuid.value = httpConfig.project_uuid;
      _controller.projectName.value = httpConfig.project_name;
    }
    // 格式化年份和月份
    _controller.searchMonth.value = getFormattedDate(currentMonth);

    _onRefresh();

    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
        _onRefresh();

        ///让去跳转
        if (_controller.isClickAdd.value) {
          BoostNavigator.instance.push('WorkOvertimeAddPage', arguments: {
            'project_uuid': _controller.projectUuid.value,
            'project_name': _controller.projectName.value,
          }).then((value) => _onRefresh());
        }
      });
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '加班记录',
            centerSubTitle: _controller.projectName.value ?? '',
            onBack: () {
              BoostNavigator.instance.pop();
            },
            actionWidget: Row(
              children: [
                InkWell(
                  onTap: () {
                    _controller.isClickAdd.value = false;
                    BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                      "method": "goto_show_project_dialog",
                      "isChangeAppProject": false,
                      'isNeedAll': true,
                      'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                      'project_uuid': _controller.projectUuid.value,
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 10, right: 10),
                    child: LoadAssetImage(
                      "icon_change",
                      width: 20,
                      height: 20,
                    ),
                  ),
                ),
              ],
            ),
          ),
          body: WillPopScope(
              child: Column(
                children: [
                  Container(
                    color: Colors.white,
                    padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    child: Row(
                      children: [
                        Expanded(
                            child: CustomSelectedArrowView(
                          dateText: _controller.searchMonth.value,
                          onDateTextPressed: () {
                            BrnDatePicker.showDatePicker(
                              themeData: BrnPickerConfig(
                                pickerHeight: 300,
                              ),
                              context,
                              initialDateTime: currentMonth,
                              pickerTitleConfig: BrnPickerTitleConfig.Default,
                              pickerMode: BrnDateTimePickerMode.date,
                              dateFormat: 'yyyy年,MMMM月',
                              onConfirm: (dateTime, list) {
                                _controller.searchMonth.value = DateUtil.formatDate(dateTime, format: 'yyyy年MM月');
                                currentMonth = dateTime;
                                _onRefresh();
                              },
                            );
                          },
                          onNextDayPressed: () {
                            _nextMonth();
                          },
                          onPreviousDayPressed: () {
                            _previousMonth();
                          },
                        )),
                        Gaps.hGap10,
                        Expanded(
                          child: CustomSearchView(
                            hint: '输入姓名找人',
                            onTextChanged: (text) {
                              _controller.searchKeyword.value = text;
                              _onRefresh();
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                  Expanded(
                      child: MyRefreshListView(
                    itemCount: _controller.list.length,
                    onRefresh: _onRefresh,
                    loadMore: _loadMore,
                    padding: const EdgeInsets.all(0.0),
                    hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                    itemBuilder: (_, index) {
                      return WorkOvertimeListItem(
                        data: _controller.list[index],
                        onClick: (position) {
                          switch (position) {
                            case 1: //删除
                              DialogManager.showConfirmDialog(
                                  context: context,
                                  title: '删除',
                                  cancel: '取消',
                                  confirm: '删除',
                                  message: '是否确认删除？',
                                  onConfirm: () {
                                    _presenter?.deleteWorkOverTimeRecord(_controller.list[index].uuid);
                                  },
                                  onCancel: () {});
                              break;
                            case 2: //编辑
                              BoostNavigator.instance.push('WorkOvertimeAddPage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                              break;
                            case 3: //详情
                              BoostNavigator.instance.push('WorkOvertimeOnePage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                              break;
                          }
                        },
                      );
                    },
                  )),
                ],
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: BrnBigMainButton(
              themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
              title: '+ 添加',
              onTap: () {
                if (_controller.projectName.value == '全部项目') {
                  _controller.isClickAdd.value = true;
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false, 'project_uuid': _controller.projectUuid.value});
                } else {
                  BoostNavigator.instance.push('WorkOvertimeAddPage', arguments: {
                    'project_uuid': _controller.projectUuid.value,
                    'project_name': _controller.projectName.value,
                  }).then((value) => _onRefresh());
                }
                // BoostNavigator.instance.push('WorkOvertimeAddPage').then((value) => _onRefresh());
              },
            ),
          ),
        ));
  }

  void _previousMonth() {
    currentMonth = DateTime(currentMonth.year, currentMonth.month - 1);
    _controller.searchMonth.value = getFormattedDate(currentMonth);
    _onRefresh();
  }

  void _nextMonth() {
    currentMonth = DateTime(currentMonth.year, currentMonth.month + 1);
    _controller.searchMonth.value = getFormattedDate(currentMonth);
    _onRefresh();
  }

  String getFormattedDate(DateTime date) {
    return "${date.year.toString()}年${(date.month.toString().padLeft(2, '0'))}月";
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkOvertimeManagerPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onStatus() {
    _onRefresh();
  }

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }
}
