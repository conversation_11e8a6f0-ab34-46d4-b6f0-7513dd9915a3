import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/holiday_data_entity.dart';
import '../bean/holiday_one_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/holiday_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../iview/holiday_iview.dart';
import '../iview/work_overtime_iview.dart';

/// 请假
class HolidayPresenter extends BasePagePresenter<HolidayIView> with WidgetsBindingObserver {
  HolidayController controller;

  HolidayPresenter(this.controller);

  ///删除某条请假记录
  Future<dynamic> deleteWorkHolidayRecord(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.HOLIDAY_DELETE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取请假的详情
  Future<dynamic> getWorkHolidayOne(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<HolidayOneEntity>(Method.get, url: HttpApi.HOLIDAY_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateHolidayOne(data);
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///新建、编辑某条数据
  Future<dynamic> saveWorkHoliday(String? uuid) {
    var params = <String, String>{};
    params["project_uuid"] = controller.projectUuid.value;
    params["start_date"] = controller.workHolidayStartTime.value;
    params["start_type"] = controller.workHolidayStartTimeType.value;
    params["end_date"] = controller.workHolidayEndTime.value;
    params["end_type"] = controller.workHolidayEndTimeType.value;
    params["reason"] = controller.workHolidayRemark.value;
    params["user_uuid_list"] = controller.getSelectedUserUuids(); //请假人员
    if (controller.workHolidayStyle.value == '病假') {
      params["holiday_type"] = '1';
    } else {
      params["holiday_type"] = '2';
    }
    //请假UUID 如果没，那么就是新建
    if (!TextUtil.isEmpty(uuid)) {
      params["uuid"] = '$uuid';
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.HOLIDAY_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
