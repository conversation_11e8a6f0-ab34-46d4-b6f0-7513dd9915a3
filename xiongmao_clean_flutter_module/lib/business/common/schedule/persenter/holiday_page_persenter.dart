import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/holiday_data_entity.dart';
import '../bean/holiday_one_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/holiday_controller.dart';
import '../controller/holiday_page_controller.dart';
import '../controller/work_overtime_controller.dart';
import '../iview/holiday_iview.dart';
import '../iview/work_overtime_iview.dart';

/// 请假
class HolidayPagePresenter extends BasePagePresenter<HolidayIView> with WidgetsBindingObserver {
  HolidayPageController controller;

  HolidayPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getWorkListManager();
  }

  void loadMore() {
    _page++;
    getWorkListManager();
  }

  //获取岗位列表
  Future<dynamic> getWorkListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["project_uuid"] = controller.projectUuid.value;
    params["search_month"] = controller.searchMonth.value.replaceAll("年", "-").replaceAll("月", "");
    params["keyword"] = controller.searchKeyword.value;
    return requestNetwork<HolidayDataEntity>(Method.get, url: HttpApi.HOLIDAY_ALL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///删除某条请假记录
  Future<dynamic> deleteWorkHolidayRecord(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.HOLIDAY_DELETE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
