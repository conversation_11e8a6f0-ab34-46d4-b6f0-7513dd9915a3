import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/work_overtime_controller.dart';
import '../iview/work_overtime_iview.dart';

/// 加班列表
class WorkOvertimePresenter extends BasePagePresenter<WorkOvertimeIView> with WidgetsBindingObserver {
  WorkOvertimeController controller;

  WorkOvertimePresenter(this.controller);

  ///删除某条加班记录
  Future<dynamic> deleteWorkOverTimeRecord(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.DELETE_WORK_OVERTIME, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取加班的详情
  Future<dynamic> getWorkOverTimeOne(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<WorkOvertimeOneEntity>(Method.get, url: HttpApi.GET_WORK_TIME_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateOverTimeOne(data);
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///新建、编辑某条数据
  Future<dynamic> saveWorkOverTime(String? uuid) {
    var params = <String, String>{};
    params["project_uuid"] = controller.projectUuid.value;
    if (!TextUtil.isEmpty(controller.workOverTimeStartTime.value)) {
      params["start_time"] = controller.workOverTimeStartTime.value;
    }
    if (!TextUtil.isEmpty(controller.workOverTimeEndTime.value)) {
      params["end_time"] = controller.workOverTimeEndTime.value;
    }
    params["overtime_long"] = controller.workOverTimeLong.value;
    params["overtime_date"] = controller.workOverTimeBelongDate.value;
    params["reason"] = controller.workOverTimeRemark.value;
    params["user_uuid_list"] = controller.getSelectedUserUuids(); //加班人员
    if (controller.workOverTimeStyle.value == '工作日') {
      params["overtime_type"] = '1';
    } else if (controller.workOverTimeStyle.value == '休息日') {
      params["overtime_type"] = '2';
    } else {
      params["overtime_type"] = '3';
    }
    //加班UUID 如果没，那么就是新建
    if (!TextUtil.isEmpty(uuid)) {
      params["uuid"] = '$uuid';
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.WORK_OVERTIME_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
