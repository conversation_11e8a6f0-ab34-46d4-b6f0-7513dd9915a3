import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';
import '../controller/work_overtime_controller.dart';
import '../controller/work_overtime_page_controller.dart';
import '../iview/work_overtime_iview.dart';

/// 加班列表
class WorkOvertimeManagerPagePresenter extends BasePagePresenter<WorkOvertimeIView> with WidgetsBindingObserver {
  WorkOvertimePageController controller;

  WorkOvertimeManagerPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getWorkListManager();
  }

  void loadMore() {
    _page++;
    getWorkListManager();
  }

  //获取岗位列表
  Future<dynamic> getWorkListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["project_uuid"] = controller.projectUuid.value;
    params["search_month"] = controller.searchMonth.value.replaceAll("年", "-").replaceAll("月", "");
    params["keyword"] = controller.searchKeyword.value;
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<WorkOvertimeDataEntity>(Method.get, url: HttpApi.GET_WORKOVERTIME_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///删除某条加班记录
  Future<dynamic> deleteWorkOverTimeRecord(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.DELETE_WORK_OVERTIME, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.onStatus();
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
