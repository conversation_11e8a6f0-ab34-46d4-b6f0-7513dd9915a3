import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../../../util/common_utils.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/holiday_data_entity.dart';
import '../bean/holiday_one_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';

class HolidayController extends GetxController {
  var projectName = ''.obs;
  var projectUuid = ''.obs;

  ///****************************新增、编辑请假记录的参数属性********************************
  //请假开启日期
  var workHolidayStartTime = ''.obs;
  var workHolidayStartShowTime = ''.obs;
  var workHolidayStartTimeType = '1'.obs; //1上午 2下午

  //请假结束日期
  var workHolidayEndTime = ''.obs;
  var workHolidayEndShowTime = ''.obs;
  var workHolidayEndTimeType = '2'.obs; //1上午 2下午

  //请假类型
  var workHolidayStyle = '事假'.obs;

  //备注
  var workHolidayRemark = ''.obs;

  //请假人员
  var workHolidayUserList = <WorkOvertimeOneUserList>[].obs;

  void deleteUserList(WorkOvertimeOneUserList data) {
    workHolidayUserList.remove(data);
    workHolidayUserList.value = workHolidayUserList.toList();
  }

  //获取请假人员的uuid
  String getSelectedUserUuids() {
    return workHolidayUserList.value.map((item) => item.uuid).join(',');
  }

  ///****************************新增、编辑请假记录的参数属性********************************

  ///****************************详情的参数属性********************************

  ///请假记录详情
  var workHolidayOne = HolidayOneEntity().obs;

  void updateHolidayOne(HolidayOneEntity data) {
    workHolidayOne.value = data;

    ///赋值
    projectUuid.value = data.projectUuid ?? ''; //项目id
    projectName.value = data.projectName ?? ''; //项目名称
    workHolidayStartTime.value = data.startDate ?? ''; //开始 时间
    workHolidayStartShowTime.value = '${data.startDate ?? ''} ${('1' == data.startType) ? '上午' : '下午'}'; //开始 时间
    workHolidayStartTimeType.value = data.startType ?? '1'; // 1是上午 2是下午
    workHolidayEndTime.value = data.endDate ?? ''; //结束时间
    workHolidayEndShowTime.value = '${data.endDate ?? ''} ${('1' == data.endType) ? '上午' : '下午'}'; //结束时间
    workHolidayEndTimeType.value = data.endType ?? '1'; // 1是上午 2是下午
    workHolidayStyle.value = data.holidayTypeName ?? '事假'; //请假类型
    workHolidayRemark.value = data.reason ?? ''; //备注
    workHolidayUserList.value = data.userList ?? []; //请假员工

    updateUserList();
  }

  /// 把所有的User根据当前的状态修改
  void updateUserList() {
    for (var item in workHolidayUserList.value) {
      item.customStatusName = workHolidayStyle.value;
    }
    workHolidayUserList.refresh();
  }

  ///优化需求 自动获取请假开始的下一天
  void getNextDay() {
    if (!TextUtil.isEmpty(workHolidayEndTime.value)) {
      return;
    }
    // 解析字符串为 DateTime
    DateTime nextDate = DateTime.parse(workHolidayStartTime.value);

    // 获取年月日
    int year = nextDate.year;
    int month = nextDate.month;
    int day = nextDate.day;

    workHolidayEndTimeType.value = '2';
    workHolidayEndTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)}';
    workHolidayEndShowTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)} 下午';
  }

  ///优化需求 获取当天下一天的时间
  void getTheDay() {
    // 解析字符串为 DateTime
    DateTime now = DateTime.now();

    // 获取下一天
    DateTime nextDay = now.add(Duration(days: 1));

    // 提取年、月、日
    int year = nextDay.year;
    int month = nextDay.month;
    int day = nextDay.day;

    workHolidayStartTimeType.value = '1';
    workHolidayStartTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)}';
    workHolidayStartShowTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)} 上午';

    getNextDay();
  }

  ///优化需求 获取当天下一天的时间
  void getCurrentDay(String? current_date) {
    if (TextUtil.isEmpty(current_date)) {
      return;
    }
    // 解析字符串为 DateTime
    DateTime now = DateTime.parse(current_date!);

    // 提取年、月、日
    int year = now.year;
    int month = now.month;
    int day = now.day;

    workHolidayStartTimeType.value = '1';
    workHolidayStartTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)}';
    workHolidayStartShowTime.value = '$year-${CommonUtils.formatToTwoDigits(month)}-${CommonUtils.formatToTwoDigits(day)} 上午';

    getNextDay();
  }

  ///****************************详情的参数属性********************************
}
