import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/holiday_data_entity.dart';
import '../bean/holiday_one_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';

class HolidayPageController extends GetxController {
  var projectName = '全部项目'.obs;
  var projectUuid = ''.obs;

  ///是否去添加
  var isClickAdd = false.obs;
  ///****************************列表上的参数属性********************************
  ///当前搜索的关键字
  var searchKeyword = ''.obs;

  ///当前搜索的月份
  var searchMonth = ''.obs;

  var list = <HolidayDataList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<HolidayDataList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<HolidayDataList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///****************************列表上的参数属性********************************


}
