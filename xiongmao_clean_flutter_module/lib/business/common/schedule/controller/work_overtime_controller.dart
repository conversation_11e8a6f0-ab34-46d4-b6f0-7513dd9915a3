import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/work_overtime_data_entity.dart';
import '../bean/work_overtime_one_entity.dart';

class WorkOvertimeController extends GetxController {
  var projectName = ''.obs;
  var projectUuid = ''.obs;

  ///****************************新增、编辑加班记录的参数属性********************************
  //加班开启日期
  var workOverTimeStartTime = ''.obs;
  DateTime? workOverTimeStartDate;

  //加班结束日期
  var workOverTimeEndTime = ''.obs;
  DateTime? workOverTimeEndDate;

  //加班日期
  var workOverTimeBelongDate = ''.obs;
  DateTime? workOverTimeBelongDateTime;

  //加班时长
  var workOverTimeLong = ''.obs;

  //加班类型
  var workOverTimeStyle = '休息日'.obs;

  //备注
  var workOverTimeRemark = ''.obs;

  //加班人员
  var workOverTimeUserList = <WorkOvertimeOneUserList>[].obs;

  void deleteUserList(WorkOvertimeOneUserList data) {
    workOverTimeUserList.remove(data);
    workOverTimeUserList.value = workOverTimeUserList.toList();
  }

  //获取加班人员的uuid
  String getSelectedUserUuids() {
    return workOverTimeUserList.value.map((item) => item.uuid).join(',');
  }

  // 计算出相差的时间
  void calculateHours(BuildContext context) {
    if (workOverTimeStartDate != null && workOverTimeEndDate != null) {
      Duration difference = workOverTimeEndDate!.difference(workOverTimeStartDate!);

      // 将总秒数转换为小时，保留小数
      double hoursDifference = difference.inSeconds / 3600;

      if (hoursDifference < 0) {
        // 如果时间差为负数，显示 Toast 提示
        BrnToast.show('加班结束时间不能早于开始时间', context);
      } else {
        // 保留1位小数并赋值
        workOverTimeLong.value = hoursDifference.toStringAsFixed(2);
        print('计算完事的时间 $hoursDifference');
      }
    }
    updateUserList();
  }

  ///****************************新增、编辑加班记录的参数属性********************************

  ///****************************详情的参数属性********************************

  ///加班记录详情
  var workOverTimeOne = WorkOvertimeOneEntity().obs;

  void updateOverTimeOne(WorkOvertimeOneEntity data) {
    workOverTimeOne.value = data;

    ///赋值
    projectUuid.value = data.projectUuid ?? ''; //项目id
    projectName.value = data.projectName ?? ''; //项目名称

    workOverTimeStartTime.value = data.startTime ?? '';
    if (!TextUtil.isEmpty(data.startTime)) {
      workOverTimeStartDate = DateTime.parse(data.startTime ?? '');
    }
    workOverTimeEndTime.value = data.endTime ?? '';
    if (!TextUtil.isEmpty(data.endTime)) {
      workOverTimeEndDate = DateTime.parse(data.endTime ?? '');
    }
    workOverTimeBelongDate.value = data.overtimeDate ?? '';
    workOverTimeLong.value = data.overtimeLong ?? '';
    if (data.overtimeType == '1') {
      workOverTimeStyle.value = '工作日';
    } else if (data.overtimeType == '2') {
      workOverTimeStyle.value = '休息日';
    } else {
      workOverTimeStyle.value = '休息日';
    }
    workOverTimeRemark.value = data.reason ?? '';
    workOverTimeUserList.value = data.userList ?? [];
    updateUserList();
  }

  /// 把所有的User根据当前的状态修改
  void updateUserList() {
    for (var item in workOverTimeUserList.value) {
      item.customStatusName = '${workOverTimeStyle.value}加班：${(TextUtil.isEmpty(workOverTimeLong.value) ? '0' : workOverTimeLong.value)} 小时';
    }
    workOverTimeUserList.refresh();
  }

  ///****************************详情的参数属性********************************
}
