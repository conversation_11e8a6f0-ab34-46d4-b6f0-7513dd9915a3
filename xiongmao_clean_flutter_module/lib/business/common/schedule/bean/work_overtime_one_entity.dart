import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/work_overtime_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/work_overtime_one_entity.g.dart';

@JsonSerializable()
class WorkOvertimeOneEntity {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@J<PERSON><PERSON>ield(name: "overtime_long")
	String? overtimeLong;
	@J<PERSON><PERSON>ield(name: "overtime_date")
	String? overtimeDate;
	@JSONField(name: "overtime_type")
	String? overtimeType;
	@J<PERSON>NField(name: "overtime_type_name")
	String? overtimeTypeName;
	String? reason;
	@JSONField(name: "create_user_name")
	String? createUserName;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "user_list")
	List<WorkOvertimeOneUserList>? userList;

	WorkOvertimeOneEntity();

	factory WorkOvertimeOneEntity.fromJson(Map<String, dynamic> json) => $WorkOvertimeOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $WorkOvertimeOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkOvertimeOneUserList {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;
	String? avatar;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "job_name")
	String? jobName;
	String? customStatusName;

	WorkOvertimeOneUserList();

	factory WorkOvertimeOneUserList.fromJson(Map<String, dynamic> json) => $WorkOvertimeOneUserListFromJson(json);

	Map<String, dynamic> toJson() => $WorkOvertimeOneUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}