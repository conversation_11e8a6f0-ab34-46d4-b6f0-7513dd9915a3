class DayInfo {
  final int year;
  final int month;
  final int day;
  final String week;

  DayInfo({required this.year, required this.month, required this.day, required this.week});

  String getMonthName() {
    List<String> months = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月", "11月", "12月"];
    return months[month - 1];
  }

  @override
  String toString() {
    return '${year} ${getMonthName()}/$day日 $week';
  }
}
