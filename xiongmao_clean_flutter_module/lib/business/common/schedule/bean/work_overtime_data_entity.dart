import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/work_overtime_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/work_overtime_data_entity.g.dart';

@JsonSerializable()
class WorkOvertimeDataEntity {
	int? page;
	int? size;
	int? total;
	List<WorkOvertimeDataList>? list;

	WorkOvertimeDataEntity();

	factory WorkOvertimeDataEntity.fromJson(Map<String, dynamic> json) => $WorkOvertimeDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $WorkOvertimeDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkOvertimeDataList {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSO<PERSON>ield(name: "start_time")
	String? startTime;
	@JSONField(name: "start_type_name")
	String? startTypeName;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "end_type_name")
	String? endTypeName;
	@JSONField(name: "overtime_long")
	String? overtimeLong;
	@JSONField(name: "overtime_date")
	String? overtimeDate;
	@JSONField(name: "overtime_type")
	String? overtimeType;
	@JSONField(name: "overtime_type_name")
	String? overtimeTypeName;
	String? reason;
	String? introduction;

	WorkOvertimeDataList();

	factory WorkOvertimeDataList.fromJson(Map<String, dynamic> json) => $WorkOvertimeDataListFromJson(json);

	Map<String, dynamic> toJson() => $WorkOvertimeDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}