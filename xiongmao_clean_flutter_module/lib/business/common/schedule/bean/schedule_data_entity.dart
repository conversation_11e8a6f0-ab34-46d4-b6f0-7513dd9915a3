import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/schedule_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/schedule_data_entity.g.dart';

@JsonSerializable()
class ScheduleDataEntity {
	int? page;
	int? size;
	int? total;
	List<ScheduleDataList>? list;

	ScheduleDataEntity();

	factory ScheduleDataEntity.fromJson(Map<String, dynamic> json) => $ScheduleDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $ScheduleDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ScheduleDataList {
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "schedule_list")
	List<ScheduleDataListScheduleList>? scheduleList;

	ScheduleDataList();

	factory ScheduleDataList.fromJson(Map<String, dynamic> json) => $ScheduleDataListFromJson(json);

	Map<String, dynamic> toJson() => $ScheduleDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ScheduleDataListScheduleList {
	@JSONField(name: "schedule_date")
	String? scheduleDate;
	String? uuid;
	@JSONField(name: "class_uuid")
	String? classUuid;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "is_work_day")
	String? isWorkDay;

	ScheduleDataListScheduleList();

	factory ScheduleDataListScheduleList.fromJson(Map<String, dynamic> json) => $ScheduleDataListScheduleListFromJson(json);

	Map<String, dynamic> toJson() => $ScheduleDataListScheduleListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}