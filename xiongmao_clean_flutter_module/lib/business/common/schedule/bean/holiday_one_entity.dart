import 'package:xiongmao_clean_flutter_module/business/common/schedule/bean/work_overtime_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/holiday_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/holiday_one_entity.g.dart';

@JsonSerializable()
class HolidayOneEntity {
	String? uuid;
	@J<PERSON>NField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "start_date")
	String? startDate;
	@JSONField(name: "start_type")
	String? startType;
	@J<PERSON>NField(name: "start_type_name")
	String? startTypeName;
	@JSONField(name: "end_date")
	String? endDate;
	@J<PERSON><PERSON>ield(name: "end_type")
	String? endType;
	@J<PERSON><PERSON>ield(name: "end_type_name")
	String? endTypeName;
	@J<PERSON><PERSON>ield(name: "holiday_type")
	String? holidayType;
	@J<PERSON><PERSON>ield(name: "holiday_type_name")
	String? holidayTypeName;
	String? reason;
	@J<PERSON><PERSON>ield(name: "holiday_days")
	String? holidayDays;
	@JSONField(name: "create_user_name")
	String? createUserName;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "user_list")
	List<WorkOvertimeOneUserList>? userList;

	HolidayOneEntity();

	factory HolidayOneEntity.fromJson(Map<String, dynamic> json) => $HolidayOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $HolidayOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class HolidayOneUserList {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;
	String? avatar;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "holiday_hours")
	String? holidayHours;

	HolidayOneUserList();

	factory HolidayOneUserList.fromJson(Map<String, dynamic> json) => $HolidayOneUserListFromJson(json);

	Map<String, dynamic> toJson() => $HolidayOneUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}