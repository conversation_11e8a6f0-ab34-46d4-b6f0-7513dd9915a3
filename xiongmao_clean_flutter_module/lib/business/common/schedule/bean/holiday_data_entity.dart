import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';

@JsonSerializable()
class HolidayDataEntity {
	int? page;
	int? size;
	int? total;
	List<HolidayDataList>? list;

	HolidayDataEntity();

	factory HolidayDataEntity.fromJson(Map<String, dynamic> json) => $HolidayDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $HolidayDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class HolidayDataList {
	String? introduction;
	String? uuid;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "start_date")
	String? startDate;
	@JSO<PERSON>ield(name: "start_type")
	String? startType;
	@JSO<PERSON>ield(name: "start_type_name")
	String? startTypeName;
	@J<PERSON>NField(name: "end_date")
	String? endDate;
	@JSONField(name: "end_type")
	String? endType;
	@JSONField(name: "end_type_name")
	String? endTypeName;
	@JSONField(name: "holiday_type")
	String? holidayType;
	@JSONField(name: "holiday_type_name")
	String? holidayTypeName;
	String? reason;

	HolidayDataList();

	factory HolidayDataList.fromJson(Map<String, dynamic> json) => $HolidayDataListFromJson(json);

	Map<String, dynamic> toJson() => $HolidayDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}