import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/schedule_export_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/schedule_export_data_entity.g.dart';

@JsonSerializable()
class ScheduleExportDataEntity {
	@JSONField(name: "download_url")
	String? downloadUrl;

	ScheduleExportDataEntity();

	factory ScheduleExportDataEntity.fromJson(Map<String, dynamic> json) => $ScheduleExportDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $ScheduleExportDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}