import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';

class MonitoringWorkOrderControllerController extends GetxController {
  //默认的数据
  var weekList = [
    BaseChooseString("周一"),
    BaseChooseString("周二"),
    BaseChooseString("周三"),
    BaseChooseString("周四"),
    BaseChooseString("周五"),
    BaseChooseString("周六"),
    BaseChooseString("周日"),
  ].obs;

  //状态的参数
  List<BrnCommonActionSheetItem> actionsAccount = [
    BrnCommonActionSheetItem(
      '全部状态',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '已完成',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '未完成',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
  ];

  //项目的uuid 项目的名字
  var projectName = "".obs;
  var projectUuid = "".obs;

  //筛选的状态
  var status_name = "全部状态".obs;
  var status = "".obs;

  //来源(工单专用)0未知 1内部巡检 2客户投诉
  var source_type = "".obs;

  //开始的日期
  var start_month = "".obs;

  var listTotal = "0".obs;

  var listTask = <CleanPlanTaskList>[].obs;

  void initTaskList(List<CleanPlanTaskList> value) {
    listTask.value = value.toList();
  }

  void updateTaskList(List<CleanPlanTaskList> value) {
    listTask.value.addAll(value);
    listTask.value = listTask.value.toList();
  }
}
