import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/project_all_new_entity.dart';
import '../bean/project_archives_entity.dart';

/**
 * 风险监控列表
 */
class RiskPageController extends GetxController {
  var totalNumber = "0".obs;
  var project_uuid = "".obs;
  var project_name = "".obs;


  //来判断 从原生进来的是离职协议 还是入职合同，或者其他
  var position = ''.obs;


  //来判断 项目列表当前去请求那个列表 0是到期项目 1是被投诉的项目 2 任务延期 3 工单项目
  var condition = 0.obs;

  //开始的日期
  var start_month = "".obs;

  ///风险监控列表
  var list = <ProjectArchivesList>[].obs;

  void initMyList(List<ProjectArchivesList> value) {
    list.value = value.toList();
  }

  void updateMyList(List<ProjectArchivesList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///全部列表的内容
  var listProject = <ProjectAllNewList>[].obs;

  void initMyProjectList(List<ProjectAllNewList> value) {
    listProject.value = value.toList();
  }

  void updateMyProjectList(List<ProjectAllNewList> value) {
    listProject.value.addAll(value);
    listProject.value = listProject.value.toList();
  }
}
