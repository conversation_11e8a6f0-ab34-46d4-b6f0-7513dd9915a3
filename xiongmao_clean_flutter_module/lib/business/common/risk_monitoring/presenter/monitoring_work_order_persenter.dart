import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../controller/monitoring_work_order_controller.dart';
import '../iview/risk_iview.dart';

/**
 * 工作计划-异常的订单
 */
class MonitoringWorkOrderPresenter extends BasePagePresenter<RiskIView> with WidgetsBindingObserver {
  MonitoringWorkOrderControllerController controller;

  MonitoringWorkOrderPresenter(this.controller);

  int _page = 1;

  void onRefresh(String? project_uuid) {
    _page = 1;
    requestCleanPlanTaskList(project_uuid);
  }

  void loadMore(String? project_uuid) {
    _page++;
    requestCleanPlanTaskList(project_uuid);
  }

  ///获取计划任务列表
  Future<dynamic> requestCleanPlanTaskList(String? project_uuid) {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["is_self"] = "2";
    if (!TextUtil.isEmpty(controller.status.value)) {
      params["status"] = controller.status.value;
    }
    if (!TextUtil.isEmpty(controller.source_type.value)) {
      params["source_type"] = controller.source_type.value;
    }
    params["start_month"] = controller.start_month.value.replaceAll("年", '-').replaceAll("月", '');
    params["project_uuid"] = '$project_uuid';
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<CleanPlanTaskEntity>(Method.post, url: HttpApi.GET_CLEAN_PLAN_TASK_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        if ("$_page" == '1') {
          controller.initTaskList(data.list!);
        } else {
          controller.updateTaskList(data.list!);
        }
        controller.listTotal.value = "${data.total ?? '0'}";
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
