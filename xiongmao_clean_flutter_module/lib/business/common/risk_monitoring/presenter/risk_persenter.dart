import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_history_one_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/project_all_new_entity.dart';
import '../bean/project_archives_entity.dart';
import '../controller/risk_controller.dart';
import '../iview/risk_iview.dart';

/**
 * 风险监控
 */
class RiskPagePresenter extends BasePagePresenter<RiskIView> with WidgetsBindingObserver {
  RiskPageController controller;

  RiskPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    if (controller.position.value == '3' || controller.position.value == '4') {
      getArchivesListLackContract();
    } else {
      getArchivesList();
    }
  }

  void loadMore() {
    _page++;
    if (controller.position.value == '3' || controller.position.value == '4') {
      getArchivesListLackContract();
    } else {
      getArchivesList();
    }
  }

  void onRefreshProject() {
    _page = 1;
    if (controller.condition.value == 0) {
      getProjectList();
    } else {
      getProjectConditionList();
    }
  }

  void loadMoreProject() {
    _page++;
    if (controller.condition.value == 0) {
      getProjectList();
    } else {
      getProjectConditionList();
    }
  }

  //获取档案人员的列表
  Future<dynamic> getArchivesList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params['status'] = '1'; //在职
    if (!TextUtil.isEmpty(controller.project_uuid.value)) {
      params["project_uuid"] = controller.project_uuid.value;
    }
    if (controller.position.value == '2') {
      //说明是有风险的
      params['is_has_credit_wind'] = '1';
      params['is_id_number'] = '1';
    } else if (controller.position.value == '0') {
      //说明是缺保险的
      params['insurance_status'] = '2';
    }
    params['map_code'] = 'gao_de';
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ProjectArchivesEntity>(Method.get, url: HttpApi.GET_STAFF_ALL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.totalNumber.value = data.total.toString();
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  //获取档案人员 缺合同的用户列表
  Future<dynamic> getArchivesListLackContract() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["project_uuid"] = controller.project_uuid.value;
    params["main_label_code"] = (controller.position.value == '4') ? 'SeparationContract' : 'EntryContract';
    return requestNetwork<ProjectArchivesEntity>(Method.get, url: HttpApi.GET_ARCHIVES_LACK_CONTRACT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.totalNumber.value = data.total.toString();

        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  //获取到期项目列表
  Future<dynamic> getProjectList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    return requestNetwork<ProjectAllNewEntity>(Method.get, url: HttpApi.GET_PROJECT_GET_LIST_EXPIRED, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.totalNumber.value = data.total.toString();

        if (_page == 1) {
          controller.initMyProjectList(data.list ?? []);
        } else {
          controller.updateMyProjectList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  //获取当月投诉的列表 1是被投诉的项目 2 任务延期 3 工单项目
  Future<dynamic> getProjectConditionList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";

    String url = HttpApi.GET_PROJECT_GET_LIST_COMPLAINT;
    switch (controller.condition.value) {
      case 1:
        url = HttpApi.GET_PROJECT_GET_LIST_COMPLAINT;
        params["search_month"] = controller.start_month.value.replaceAll("年", '-').replaceAll("月", '');
        break;
      case 2:
        url = HttpApi.GET_PROJECT_GET_LIST_DELAY;
        params["search_month"] = controller.start_month.value.replaceAll("年", '-').replaceAll("月", '');
        break;
      case 3:
        url = HttpApi.GET_PROJECT_GET_LIST_WORK_ORDER;
        params["search_date"] = controller.start_month.value.replaceAll("年", '-').replaceAll("月", '-').replaceAll("日", '');
        break;
    }

    return requestNetwork<ProjectAllNewEntity>(Method.get, url: url, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.totalNumber.value = data.total.toString();

        if (_page == 1) {
          controller.initMyProjectList(data.list ?? []);
        } else {
          controller.updateMyProjectList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  ///自动投保
  Future<dynamic> autoInsure() {
    var params = <String, String>{};
    return requestNetwork<Object>(Method.post, url: HttpApi.AUTO_INSURE_BUY, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.autoInsureBuy();
    }, onError: (_, __) {});
  }

  ///手动更新
  Future<dynamic> updateLackContract(bool isEntry, String project_uuid) {
    var params = <String, String>{};
    params["main_label_code"] = isEntry ? "EntryContract" : "SeparationContract";
    if (!TextUtil.isEmpty(project_uuid)) {
      params["project_uuid"] = '$project_uuid';
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.UPDATE_LACK_CONTRACT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.autoInsureBuy();
    }, onError: (_, __) {});
  }
}
