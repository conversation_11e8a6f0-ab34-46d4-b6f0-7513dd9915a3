import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/monitoring_work_order_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/quality_monitoring_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/risk_page.dart';

import '../../../net/http_config.dart';

/// 风险监控
const riskPage = "riskPage";

///品质监测
const qualityMonitoringPage = "qualityMonitoringPage";

///品质监测 被投诉
const monitoringWorkOrderPage = "monitoringWorkOrderPage";

/// 风险监控
Map<String, FlutterBoostRouteFactory> riskRouterMap = {
  riskPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String position = map['position'] ?? "3";
          return RiskPage(position: position);
        });
  },
  qualityMonitoringPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String position = map['position'] ?? "0";
          return QualityMonitoringPage(position: position);
        });
  },
  monitoringWorkOrderPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String position = map['position'] ?? "0";
          String project_uuid = map['project_uuid'] ?? "";
          String project_name = map['project_name'] ?? "";
          return MonitoringWorkOrderPage(
            position: position,
            project_uuid: project_uuid,
            project_name: project_name,
          );
        });
  },
};
