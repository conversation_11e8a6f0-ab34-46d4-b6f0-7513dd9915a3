import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../quality_service/controller/work_add_plan_order_list_controller.dart';
import '../../quality_service/item/order_list_item_listview.dart';
import '../../quality_service/iview/work_add_plan_iview.dart';
import '../../quality_service/persenter/work_add_plan_order_persenter.dart';
import '../controller/monitoring_work_order_controller.dart';
import '../controller/risk_controller.dart';
import '../item/risk_item_listview.dart';
import '../item/risk_item_project_listview.dart';
import '../iview/risk_iview.dart';
import '../presenter/monitoring_work_order_persenter.dart';
import '../presenter/risk_persenter.dart';
import '../risk_router.dart';

/**
 * 品质监测
 */
class MonitoringWorkOrderPage extends StatefulWidget {
  String? position = "";
  String? project_name = "";
  String? project_uuid = "";

  MonitoringWorkOrderPage({Key? key, this.position = "", this.project_uuid = "", this.project_name = ""}) : super(key: key);

  @override
  _MonitoringWorkOrderPageState createState() => _MonitoringWorkOrderPageState();
}

class _MonitoringWorkOrderPageState extends State<MonitoringWorkOrderPage> with BasePageMixin<MonitoringWorkOrderPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<MonitoringWorkOrderPage> implements RiskIView {
  MonitoringWorkOrderPresenter? _presenter;

  final MonitoringWorkOrderControllerController _controller = MonitoringWorkOrderControllerController();

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh(_controller.projectUuid.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore(_controller.projectUuid.value);
  }

  VoidCallback? addProjectListener;

  String title = ""; //当前项目
  String subTitle = "投诉工单"; //记录当前的副标题

  @override
  void initState() {
    super.initState();

    _controller.start_month.value = DateUtil.formatDate(DateTime.now(), format: 'yyyy年MM月');

    switch (widget.position) {
      case "1":
        title = '投诉工单';
        subTitle = '被投诉的时间';
        _controller.source_type.value = '2';
        break;
      case "2":
        title = '超时任务';
        subTitle = '时间范围';
        _controller.status.value = '3'; //超时的状态
        break;
    }
    _controller.projectUuid.value = widget.project_uuid ?? "";
    _controller.projectName.value = widget.project_name ?? "";

    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
              centerTitle: title,
              centerSubTitle: widget.project_name,
              onBack: () {
                BoostNavigator.instance.pop();
              }),
          body: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(child: CommonUtils.getSimpleText(subTitle, 14, Colours.base_primary_text_title)),
                    InkWell(
                      child: CommonUtils.getSimpleText('${_controller.start_month.value}', 14, Colours.base_primary_text_title),
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.date,
                          dateFormat: 'yyyy年,MMMM月',
                          onConfirm: (dateTime, list) {
                            _controller.start_month.value = DateUtil.formatDate(dateTime, format: 'yyyy年MM月');
                            _onRefresh();
                          },
                        );
                      },
                    ),
                    Gaps.hGap4,
                    LoadAssetImage(
                      "icon_base_bottom_arrow",
                      width: 10,
                      height: 10,
                    ),
                  ],
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.listTask.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.listTotal.value) > _controller.listTask.length,
                itemBuilder: (_, index) {
                  return OrderListItem(
                    data: _controller.listTask[index],
                    onClick: () {
                      BoostNavigator.instance.push('WorkPlanTaskOnePage', arguments: {
                        'uuid': "${_controller.listTask[index].uuid}",
                      });
                    },
                  );
                },
              ))
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = MonitoringWorkOrderPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void autoInsureBuy() {
    // TODO: implement autoInsureBuy
  }
}
