import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../controller/risk_controller.dart';
import '../item/risk_item_listview.dart';
import '../item/risk_item_project_listview.dart';
import '../iview/risk_iview.dart';
import '../presenter/risk_persenter.dart';

/**
 * 风险监控
 */
class RiskPage extends StatefulWidget {
  String? position = "";

  RiskPage({Key? key, this.position = ""}) : super(key: key);

  @override
  _RiskPageState createState() => _RiskPageState();
}

class _RiskPageState extends State<RiskPage> with BasePageMixin<RiskPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RiskPage> implements RiskIView {
  RiskPagePresenter? _presenter;

  final RiskPageController _controller = RiskPageController();

  Future<dynamic> _onRefresh() async {
    if (widget.position == '1') {
      _presenter?.onRefreshProject();
    } else {
      _presenter?.onRefresh();
    }
  }

  Future<dynamic> _loadMore() async {
    if (widget.position == '1') {
      _presenter?.loadMoreProject();
    } else {
      _presenter?.loadMore();
    }
  }

  VoidCallback? addProjectListener;

  String title = ""; //当前项目

  @override
  void initState() {
    super.initState();

    _controller.position.value = widget.position ?? '';
    _controller.project_name.value = httpConfig.project_name ?? '';
    _controller.project_uuid.value = httpConfig.project_uuid ?? '';

    switch (widget.position) {
      case "0":
        title = '缺保险';
        break;
      case "1":
        title = '到期项目';
        _controller.condition.value = 0;
        break;
      case "2":
        title = '信用异常';
        break;
      case "3":
        title = '缺劳动协议';
        break;
      case "4":
        title = '缺离职协议';
        break;
    }

    _onRefresh();

    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.project_uuid.value = project_uuid;
        _controller.project_name.value = project_name;
        _onRefresh();
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: widget.position == '1'
              ? MyAppBar(
                  centerTitle: title,
                  centerSubTitle: _controller.project_name.value,
                  onBack: () {
                    BoostNavigator.instance.pop();
                  },
                )
              : MyAppBar(
                  centerTitle: title,
                  centerSubTitle: _controller.project_name.value,
                  mainRightImg: 'icon_change',
                  // 外部传入第一个图标数据
                  onBack: () {
                    BoostNavigator.instance.pop();
                  },
                  onPressed: () {
                    BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                      "method": "goto_show_project_dialog",
                      "isChangeAppProject": CommonUtils.checkRoleHeadOffice(),
                      'isNeedAll': CommonUtils.checkRoleHeadOffice(),
                      'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                      'project_uuid': _controller.project_uuid.value,
                    });
                  },
                ),
          body: MyRefreshListView(
            itemCount: ((widget.position == '1') ? _controller.listProject.length : _controller.list.length),
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: int.parse(_controller.totalNumber.value) > ((widget.position == '1') ? _controller.listProject.length : _controller.list.length),
            itemBuilder: (_, index) {
              // return RisktemListView(data: _controller.list[index]);
              return (widget.position == '1')
                  ? RiskItemProjectListView(
                      data: _controller.listProject[index],
                      type: 0,
                      onClick: () {
                        BoostNavigator.instance.push('ProjectOneWebPage', arguments: {
                          'project_uuid': '${_controller.list.value[index].uuid}',
                        }).then((value) => _onRefresh());
                      },
                    )
                  : RisktemListView(
                      data: _controller.list[index],
                      type: widget.position,
                      onClick: () {
                        switch (widget.position) {
                          case "0": //保险 跳档案详情
                            BoostNavigator.instance.push('StaffOneWebPage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                            break;
                          case "2": //信用异常
                            BoostNavigator.instance.push("creditInquiryPage", arguments: {'uuid': '${_controller.list[index].uuid}', 'user_name': '${_controller.list[index].userName}', 'id_number': '${_controller.list[index].idNumber}', 'type': '2'}).then((value) => _onRefresh());
                            break;
                          case "3": //离职协议 - 入职合同
                          case '4':
                            BoostNavigator.instance.push("contractRecordPage", arguments: {'uuid': '${_controller.list[index].uuid}', 'user_name': '${_controller.list[index].userName}'}).then((value) => _onRefresh());
                            break;
                        }
                      },
                    );
            },
          ),
          bottomNavigationBar: Visibility(
            visible: (widget.position == "0" || widget.position == "3" || widget.position == "4"),
            child: Container(
              color: Colors.white,
              padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
              child: BrnBigMainButton(
                themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 14),
                title: (widget.position == "0") ? '立即自动投保' : '更新',
                onTap: () {
                  if (widget.position == "0") {
                    _presenter?.autoInsure();
                  } else if (widget.position == "3") {
                    //3是劳动协议
                    _presenter?.updateLackContract(true, _controller.project_uuid.value);
                  } else if (widget.position == "4") {
                    //4是离职
                    _presenter?.updateLackContract(false, _controller.project_uuid.value);
                  }
                },
              ),
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = RiskPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void autoInsureBuy() {
    BrnToast.show('已提交申请，请3~5分钟后再查看。', context);
  }
}
