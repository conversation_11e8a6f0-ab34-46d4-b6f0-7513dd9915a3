import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../controller/risk_controller.dart';
import '../item/risk_item_listview.dart';
import '../item/risk_item_project_listview.dart';
import '../iview/risk_iview.dart';
import '../presenter/risk_persenter.dart';
import '../risk_router.dart';

/**
 * 品质监测
 */
class QualityMonitoringPage extends StatefulWidget {
  String? position = "";

  QualityMonitoringPage({Key? key, this.position = ""}) : super(key: key);

  @override
  _QualityMonitoringPageState createState() => _QualityMonitoringPageState();
}

class _QualityMonitoringPageState extends State<QualityMonitoringPage> with BasePageMixin<QualityMonitoringPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<QualityMonitoringPage> implements RiskIView {
  RiskPagePresenter? _presenter;

  final RiskPageController _controller = RiskPageController();

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefreshProject();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMoreProject();
  }

  String title = ""; //当前项目
  String subTitle = "被投诉的项目"; //记录当前的副标题

  @override
  void initState() {
    super.initState();

    switch (widget.position) {
      case "1":
        title = '被投诉的项目';
        subTitle = '被投诉的时间';
        _controller.condition.value = 1;
        _controller.start_month.value = DateUtil.formatDate(DateTime.now(), format: 'yyyy年MM月');
        break;
      case "2":
        title = '任务延期的项目';
        subTitle = '时间范围';
        _controller.condition.value = 2;
        _controller.start_month.value = DateUtil.formatDate(DateTime.now(), format: 'yyyy年MM月');
        break;
      case "3":
        title = '无任何工单项目';
        subTitle = '查看日期';
        _controller.condition.value = 3;
        _controller.start_month.value = DateUtil.formatDate(DateTime.now(), format: 'yyyy年MM月dd日');
        break;
    }

    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: title,
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          body: Column(
            children: [
              Container(
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(child: CommonUtils.getSimpleText(subTitle, 14, Colours.base_primary_text_title)),
                    InkWell(
                      child: CommonUtils.getSimpleText('${_controller.start_month.value}', 14, Colours.base_primary_text_title),
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.date,
                          dateFormat: _controller.condition.value == 3 ? 'yyyy年,MMMM月,dd日' : 'yyyy年,MMMM月',
                          onConfirm: (dateTime, list) {
                            _controller.start_month.value = DateUtil.formatDate(dateTime, format: _controller.condition.value == 3 ? 'yyyy年MM月dd日' : 'yyyy年MM月');
                            _onRefresh();
                          },
                        );
                      },
                    ),
                    Gaps.hGap4,
                    LoadAssetImage(
                      "icon_base_bottom_arrow",
                      width: 10,
                      height: 10,
                    ),
                  ],
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.listProject.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.totalNumber.value) > _controller.listProject.length,
                itemBuilder: (_, index) {
                  return RiskItemProjectListView(
                    data: _controller.listProject[index],
                    type: !TextUtil.isEmpty(widget.position) ? int.parse(widget.position!) : 1,
                    onClick: () {
                      BoostNavigator.instance.push(monitoringWorkOrderPage, arguments: {"position": widget.position, 'project_uuid': '${_controller.listProject[index].uuid}', 'project_name': _controller.listProject[index].projectShortName ?? _controller.listProject[index].projectName}).then((value) => _onRefresh());
                    },
                  );
                },
              ))
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = RiskPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void autoInsureBuy() {
    // TODO: implement autoInsureBuy
  }
}
