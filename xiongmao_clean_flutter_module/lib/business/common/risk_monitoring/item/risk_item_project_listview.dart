import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../bean/project_all_new_entity.dart';

/**
 * 风险监控的列表
 */
class RiskItemProjectListView extends StatelessWidget {
  ProjectAllNewList data;

  int type;

  final Function onClick;

  RiskItemProjectListView({required this.data, required this.type, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //无工单不需要跳转 直接隐藏
        if (type == 3) {
          return;
        }
        onClick();
      },
      child: Container(
        margin: const EdgeInsets.only(right: 16, left: 16, top: 10),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.0), // 设置圆角半径为10.0
          color: Colors.white, // 设置背景颜色为灰色
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 14,
          right: 14,
          bottom: 10,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: CommonUtils.getSimpleText((!TextUtil.isEmpty(data.projectShortName)) ? "${data.projectShortName}" : data.projectName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                ),
                Expanded(
                  child: CommonUtils.getSimpleText(buildTips(data), 14, Colours.base_primary, textAlign: TextAlign.right),
                ),
              ],
            ),
            Gaps.vGap6,
            Row(
              children: [
                Expanded(
                  child: CommonUtils.getSimpleText('大区经理：${data.managerUserName ?? ""}', 13, Colours.base_primary_text_title),
                ),
                Expanded(
                  child: CommonUtils.getSimpleText('客户：${data.customName ?? "-"}', 13, Colours.base_primary_text_title),
                ),
              ],
            ),
            Row(
              children: [
                Expanded(
                  child: CommonUtils.getSimpleText('服务面积：${data.square ?? "0"}㎡', 13, Colours.base_primary_text_title),
                ),
                Expanded(
                  child: CommonUtils.getSimpleText('业态：${data.projectCatParentName ?? "-"}-${data.projectCatName ?? "-"}', 13, Colours.base_primary_text_title),
                ),
              ],
            ),
            Row(
              children: [CommonUtils.getSimpleText('备注：${data.remark ?? '-'}', 13, Colours.base_primary_text_title)],
            ),
          ],
        ),
      ),
    );
  }

  String buildTips(ProjectAllNewList data) {
    String tips = '';
    switch (type) {
      case 0:
        tips = '${data.endTime}到期';
        break;
      case 1:
        tips = '投诉${data.taskNum}条';
        break;
      case 2:
        tips = '超时任务${data.taskNum}个';
        break;
    }
    return tips;
  }
}
