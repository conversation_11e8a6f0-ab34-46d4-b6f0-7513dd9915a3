import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/project_archives_entity.dart';

/// 风险监控的列表 阿姨的档案列表
class RisktemListView extends StatelessWidget {
  ProjectArchivesList data;

  String? type;

  final Function onClick;

  RisktemListView({required this.data, required this.type, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Column(
        children: [
          Gaps.line,
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(
              top: 10,
              left: 16,
              right: 16,
              bottom: 10,
            ), // 设置内边距为16.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomAvatarView(
                  name: data.userName,
                  avatarUrl: data.avatar,
                ),
                Gaps.hGap10,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(data.userName, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    CommonUtils.getSimpleText(buildStringFromItem(data), 13, Colours.erji),
                    Visibility(
                      child: CommonUtils.getSimpleText("原因：${data.reasonName ?? ''}", 12, Colours.red),
                      visible: type != '2',
                    ),
                  ],
                )),
                Gaps.hGap10,
                Image.asset(
                  'assets/images/base/icon_base_gray_arrow.png',
                  width: 20,
                  height: 20,
                )
              ],
            ),
          ),
          // Gaps.line
        ],
      ),
      onTap: () {
        onClick();
      },
    );
  }

  String buildStringFromItem(ProjectArchivesList item) {
    StringBuffer stringBuffer = StringBuffer();

    // if (item. != null && item.sexName.isNotEmpty) {
    //   stringBuffer.write(item.sexName);
    // }

    if (!TextUtil.isEmpty(item.jobName)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write(item.jobName);
    }

    if (!TextUtil.isEmpty(item.age)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.age}岁");
    }

    if (!TextUtil.isEmpty(item.workAge)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.workAge}年");
    }

    return stringBuffer.toString();
  }
}
