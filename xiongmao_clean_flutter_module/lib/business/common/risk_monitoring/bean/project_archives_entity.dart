import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/project_archives_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/project_archives_entity.g.dart';

@JsonSerializable()
class ProjectArchivesEntity {
	int? page;
	int? size;
	int? total;
	List<ProjectArchivesList>? list;

	ProjectArchivesEntity();

	factory ProjectArchivesEntity.fromJson(Map<String, dynamic> json) => $ProjectArchivesEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectArchivesEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ProjectArchivesList {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "reason_name")
	String? reasonName;
	@JSONField(name: "job_name")
	String? jobName;
	String? age;
	String? avatar;
	@JSONField(name: "work_age")
	String? workAge;
	@JSONField(name: "id_number")
	String? idNumber;
	String? mobile;
	@JSONField(name: "is_has_id_card")
	String? isHasIdCard;
	@JSONField(name: "is_has_contract")
	String? isHasContract;
	@JSONField(name: "is_has_bank_card")
	String? isHasBankCard;
	@JSONField(name: "is_has_healthy_card")
	String? isHasHealthyCard;
	@JSONField(name: "is_head_office")
	String? isHeadOffice;
	@JSONField(name: "is_selected")
	bool isSelected = false;
	@JSONField(name: "role_list")
	List<String>? roleList;

	ProjectArchivesList();

	factory ProjectArchivesList.fromJson(Map<String, dynamic> json) => $ProjectArchivesListFromJson(json);

	Map<String, dynamic> toJson() => $ProjectArchivesListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}