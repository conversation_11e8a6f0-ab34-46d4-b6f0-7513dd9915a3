import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/project_all_new_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/project_all_new_entity.g.dart';

@JsonSerializable()
class ProjectAllNewEntity {
	int? page;
	int? size;
	int? total;
	List<ProjectAllNewList>? list;

	ProjectAllNewEntity();

	factory ProjectAllNewEntity.fromJson(Map<String, dynamic> json) => $ProjectAllNewEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectAllNewEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ProjectAllNewList {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "project_cat_parent_name")
	String? projectCatParentName;
	@JSONField(name: "project_cat_name")
	String? projectCatName;
	@JSONField(name: "manager_user_name")
	String? managerUserName;
	@JSONField(name: "custom_name")
	String? customName;
	String? square;
	String? remark;
	@JSONField(name: "task_num")
	String? taskNum;
	@JSONField(name: "end_time")
	String? endTime;

	ProjectAllNewList();

	factory ProjectAllNewList.fromJson(Map<String, dynamic> json) => $ProjectAllNewListFromJson(json);

	Map<String, dynamic> toJson() => $ProjectAllNewListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}