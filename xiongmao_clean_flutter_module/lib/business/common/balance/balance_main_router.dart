import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/balance_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/recharge_balance_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/recharge_balance_web_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/withdrawal_account_bank_list_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/withdrawal_account_bind_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/withdrawal_account_list_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/withdrawal_account_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/page/withdrawal_balance_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_post_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_rules_page.dart';

import '../../../net/http_config.dart';

/// 余额首页
const balanceMainPostPage = "balanceMainPostPage";

///充值界面
const rechargeBalancePostPage = "rechargeBalancePostPage";

///提现界面
const withdrawalBalancePostPage = "withdrawalBalancePostPage";

///提现账户界面
const withdrawalAccountListPostPage = "withdrawalAccountListPostPage";

///增加提现账号
const withdrawalAccountPostPage = "withdrawalAccountPostPage";

///提现银行卡列表
const withdrawalAccountBankPostPage = "withdrawalAccountBankPostPage";

///增加提现账号-绑定界面
const withdrawalAccountBindPostPage = "withdrawalAccountBindPostPage";

///网页链接
const rechargeBalanceWebPage = "rechargeBalanceWebPage";

/// 余额首页
Map<String, FlutterBoostRouteFactory> balanceMainRouterMap = {
  balanceMainPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return BalanceMainPage();
        });
  },
};

/// 充值界面
Map<String, FlutterBoostRouteFactory> rechargeBalanceRouterMap = {
  rechargeBalancePostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? balance = map["balance"];
          return RechargeBalancePage(
            balance: balance,
          );
        });
  },
};

///提现界面
Map<String, FlutterBoostRouteFactory> withdrawalBalanceRouterMap = {
  withdrawalBalancePostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? balance = map["balance"];
          return WithdrawalBalancePage(
            balance: balance,
          );
        });
  },
};

///提现账户界面
Map<String, FlutterBoostRouteFactory> withdrawalAccountListRouterMap = {
  withdrawalAccountListPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String _account_uuid = map['_account_uuid'];
          return WithdrawalAccountListPage(account_uuid: _account_uuid);
        });
  },
};

///增加提现账号
Map<String, FlutterBoostRouteFactory> withdrawalAccountRouterMap = {
  withdrawalAccountPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? position = map["position"];
          return WithdrawalAccountPage(position: position);
        });
  },
};

///增加提现账号-银行卡列表
Map<String, FlutterBoostRouteFactory> withdrawalAccountBankRouterMap = {
  withdrawalAccountBankPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return WithdrawalAccountBankListPage();
        });
  },
};

///增加提现账号-绑定界面
Map<String, FlutterBoostRouteFactory> withdrawalAccountBindRouterMap = {
  withdrawalAccountBindPostPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? bankUser = map["bankUser"];
          String? account_no = map["account_no"];
          String? account_type = map["account_type"];
          String? bankCode = map["bankCode"];
          return WithdrawalAccountBindPage(bankUser: bankUser, account_no: account_no, account_type: account_type, bankCode: bankCode);
        });
  },
};

///增加提现账号-绑定界面
Map<String, FlutterBoostRouteFactory> rechargeBalanceWebPageRouterMap = {
  rechargeBalanceWebPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? webUrl = map["url"];
          return RechargeBalanceWebPage(url: webUrl!);
        });
  },
};
