import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:get/get_utils/get_utils.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/balance_account_controller.dart';
import '../controller/balance_controller.dart';
import '../item/balance_account_listview.dart';
import '../item/balance_main_item_listview.dart';
import '../iview/balance_account_iview.dart';
import '../iview/balance_iview.dart';
import '../presenter/balance_account_persenter.dart';
import '../presenter/balance_persenter.dart';

class WithdrawalAccountListPage extends StatefulWidget {
  String? account_uuid = "";

  WithdrawalAccountListPage({super.key, required this.account_uuid});

  @override
  _WithdrawalAccountListPageState createState() => _WithdrawalAccountListPageState();
}

class _WithdrawalAccountListPageState extends State<WithdrawalAccountListPage> with BasePageMixin<WithdrawalAccountListPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WithdrawalAccountListPage> implements BalanceAccountIView {
  final BalanceAccountController _controller = BalanceAccountController();

  BalanceAccountPresenter? _presenter;

  @override
  void initState() {
    super.initState();
    _presenter?.requestBalanceAccountList(widget.account_uuid);
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.requestBalanceAccountList(widget.account_uuid);
  }

  Future<dynamic> _loadMore() async {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '提现账户管理',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Column(
            children: [
              Container(
                width: double.infinity,
                color: Colours.base_primary_notice_bg,
                padding: EdgeInsets.all(10),
                child: CommonUtils.getSimpleText("长按某个选项，可以删除对应账户", 12, Colours.base_primary_text_title),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.accountList.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: false,
                itemBuilder: (_, index) {
                  // return WorkPostManagerListItem(provider.list[index], provider, _player, index);
                  return BalanceAccountListItem(
                    data: _controller.accountList[index],
                    onDelete: () {
                      // _presenter?.requestBalanceAccountDel(_controller.accountList[index].uuid ?? "");
                    },
                    isSelected: _controller.selectedIndex == index,
                    onTap: () {
                      BoostNavigator.instance.pop(_controller.accountList[index]);
                    },
                    onLongTap: () {
                      BrnDialogManager.showConfirmDialog(context, title: "删除该账户", cancel: '取消', confirm: '删除', message: "是否要删除该账户。", barrierDismissible: false,onConfirm: () {
                        Navigator.of(context, rootNavigator: true).pop();
                        _presenter?.requestBalanceAccountDel(_controller.accountList[index].uuid ?? "");
                      }, onCancel: () {
                        Navigator.of(context, rootNavigator: true).pop();
                      });
                    },
                  );
                },
              )),
            ],
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '添加提现账户',
          onTap: () {
            // 展示actionSheet
            showModalBottomSheet(
                context: context,
                backgroundColor: Colors.transparent,
                builder: (BuildContext context) {
                  return BrnCommonActionSheet(
                    actions: _controller.actionsAccount,
                    clickCallBack: (
                      int index,
                      BrnCommonActionSheetItem actionEle,
                    ) {
                      BoostNavigator.instance.push("withdrawalAccountPostPage", arguments: {
                        "position": '$index',
                      }).then((value) => _onRefresh());
                    },
                  );
                });
          },
        ),
      ),
    );
  }

  @override
  void balanceDrawCashStatus() {}

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalanceAccountPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getSmsCode() {}
}
