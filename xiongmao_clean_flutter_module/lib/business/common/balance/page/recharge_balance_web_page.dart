import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';

class RechargeBalanceWebPage extends StatefulWidget {
  String url;

  RechargeBalanceWebPage({this.url = ""});

  @override
  _RechargeBalanceWebPageState createState() => _RechargeBalanceWebPageState();
}

class _RechargeBalanceWebPageState extends State<RechargeBalanceWebPage> {
  late XmWebController controller;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '对公充值',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      body: XmWebView(
        onXmJsMsgCall: (XmJsMsgCall msgCall) {
          var decode = json.decode(msgCall.message);
          var name = decode["name"];
          var data = decode["data"];
          switch (name) {
            case "WebViewJavascriptBridge_openNewPromptUrl":
              print('来这里了，回调 $data');
              BoostNavigator.instance.push('webPage', arguments: {"url": "${data['url']}"});
              break;
            case "WebViewJavascriptBridge_goPublicRechargeOrder":
              BoostNavigator.instance.pop();
              break;
          }
          //回调id、方法名称和参数
          //controller.realController.runJavaScript("window.callBackHandle('100000')");
        },
        controllerSettingBack: (value) {
          controller = value;
          controller.loadRequest(Uri.parse(widget.url));
          controller.realController.runJavaScript("window.WebViewJavascriptBridgegetgetUserTokenCallBack(" + httpConfig.token + ")"); //切记这个字符串还得用''引住
        },
        domainM2: false,
      ),
    );
  }
}
