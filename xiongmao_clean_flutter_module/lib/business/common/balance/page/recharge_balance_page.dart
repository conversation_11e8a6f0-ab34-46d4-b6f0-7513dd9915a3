import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/net/h5_url.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/recharge_pre_entity.dart';
import '../controller/balance_controller.dart';
import '../item/balance_main_item_listview.dart';
import '../iview/balance_iview.dart';
import '../presenter/balance_persenter.dart';

class RechargeBalancePage extends StatefulWidget {
  String? balance = "";

  RechargeBalancePage({super.key, required this.balance});

  @override
  _RechargeBalancePageState createState() => _RechargeBalancePageState();
}

class _RechargeBalancePageState extends State<RechargeBalancePage> with BasePageMixin<RechargeBalancePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RechargeBalancePage> implements BalanceIView {
  BalancePresenter? _presenter;

  var price = '0';

  final BalanceController _controller = BalanceController();

  VoidCallback? addListener;

  @override
  void initState() {
    super.initState();

    ///添加事件响应者,监听native发往flutter端的事件
    addListener ??= BoostChannel.instance.addEventListener("pay_success_balance", (key, arguments) async {
      BoostNavigator.instance.pop();
    });
  }

  @override
  void dispose() {
    super.dispose();
    addListener?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '充值',
          actionWidget: Padding(
            padding: EdgeInsets.only(right: 16),
            child: InkWell(
              child: CommonUtils.getSimpleText('对公充值', 14, Colours.base_primary_text_title),
              onTap: () {
                _presenter?.requestBalanceRechargePre();
              },
            ),
          ),
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          BrnTextInputFormItem(
            title: "账户余额(元)",
            isEdit: false,
            unit: '元',
            controller: TextEditingController()..text = '${widget.balance}',
          ),
          Gaps.line,
          BrnTextInputFormItem(
            controller: TextEditingController()..text = "",
            title: "充值金额(元)",
            isRequire: true,
            unit: '元',
            hint: "请输入充值金额",
            onChanged: (newValue) {
              setState(() {
                price = newValue;
              });
            },
          ),
          Gaps.line,
          Gaps.vGap20,
          MyBottomButtonOne(
              title: "充值",
              isNeedBg: false,
              isNeedDivideLine: false,
              onPressed: () {
                FocusScope.of(context).unfocus(); // 关闭软键盘
                ///发送消息到原生
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "pay_native_balance_recharge", "price": price});
              })
        ],
      ),
    );
  }

  @override
  void balanceDrawCashStatus() {}

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalancePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  void finishPage() {
    Navigator.of(context, rootNavigator: true).pop();
  }

  @override
  void rechargePre(RechargePreEntity data) {
    ///审核状态：1-当前企业有进行中的充值  0-新充值订单
    switch (data.status) {
      case 0:
        BrnEnhanceOperationDialog(
          context: context,
          titleText: '对公充值提示',
          descText: "${data.prompt?.text}",
          mainButtonText: "确认",
          secondaryButtonText: "${data.prompt?.highlight}",
          onMainButtonClick: () {
            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_recharge_balance_web", "url": httpConfig.getServerType + H5Url.corporate_recharge_subrecharge_url});
          },
          onSecondaryButtonClick: () {
            BoostNavigator.instance.push('webPage', arguments: {"url": "${data.promptUrl}"});
          },
        ).show();
        break;
      case 1:
        BrnDialogManager.showConfirmDialog(context, title: "您有一笔进行中的对公充值\n暂不支持重复提交", cancel: '取消', confirm: '查看充值进度', barrierDismissible: false, onCancel: () {
          finishPage();
        }, onConfirm: () {
          BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_recharge_balance_web", "url": httpConfig.getServerType + H5Url.corporate_recharge_rechargeinfo_url});
        });
        break;
      case 2:
        BrnDialogManager.showSingleButtonDialog(context, showIcon: false, barrierDismissible: false, title: "您有一笔进行中的对公充值\n暂不支持重复提交", label: "关闭", onTap: () {
          finishPage();
        });
        break;
    }
  }

  @override
  bool get wantKeepAlive => false;
}
