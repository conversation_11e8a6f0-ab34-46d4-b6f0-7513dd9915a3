import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/balance_account_entity.dart';
import '../bean/recharge_pre_entity.dart';
import '../controller/balance_controller.dart';
import '../item/balance_main_item_listview.dart';
import '../iview/balance_iview.dart';
import '../presenter/balance_persenter.dart';

class WithdrawalBalancePage extends StatefulWidget {
  String? balance = "";

  WithdrawalBalancePage({super.key, required this.balance});

  @override
  _WithdrawalBalancePageState createState() => _WithdrawalBalancePageState();
}

class _WithdrawalBalancePageState extends State<WithdrawalBalancePage> with BasePageMixin<WithdrawalBalancePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WithdrawalBalancePage> implements BalanceIView {
  //记录提前的金额
  var price = '0';

  final BalanceController _controller = BalanceController();

  BalancePresenter? _presenter;

  TextEditingController priceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    //先获取银行列表默认取第一个
    _presenter?.requestBalanceAccountList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '提现',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          Obx(() => BrnTextSelectFormItem(
                title: "收款账号",
                value: _controller.account.value,
                isRequire: true,
                hint: '还没有收款账号，请添加',
                onTap: () {
                  BoostNavigator.instance.push("withdrawalAccountListPostPage", arguments: {"_account_uuid": _controller.account_uuid.value}).then((value) {
                    if (value is BalanceAccountList) {
                      BalanceAccountList accountInfo = value;
                      setState(() {
                        _controller.account_uuid.value = accountInfo.uuid ?? "";
                        _controller.account.value = '${accountInfo.accountUserName}-${accountInfo.accountName}(${accountInfo.accountNo!.substring(accountInfo.accountNo!.length - 4, accountInfo.accountNo!.length)})';
                      });
                    }
                  });
                },
              )),
          Gaps.line,
          BrnTextInputFormItem(
            title: "提现金额",
            isRequire: true,
            hint: "请输入提现金额",
            controller: priceController,
            onChanged: (newValue) {
              setState(() {
                price = newValue;
              });
            },
          ),
          Gaps.line,
          BrnBaseTitle(
            title: "可用余额",
            customActionWidget: Row(
              children: [
                CommonUtils.getSimpleText("${widget.balance}元", 14, Colours.black),
                Gaps.hGap10,
                InkWell(
                  child: CommonUtils.getSimpleText("全部提现", 14, Colours.base_primary),
                  onTap: () {
                    setState(() {
                      price = widget.balance ?? "0"; // 更新提现金额
                      priceController.text = price; // 更新提现金额的控制器
                    });
                  },
                ),
              ],
            ),
            onTip: () {
              BrnToast.show("点击触发回调_onTip", context);
            },
          ),
          Gaps.line,
          Gaps.vGap20,
          MyBottomButtonOne(
              isNeedBg: false,
              title: "提现",
              isDisabled: (priceController.text.isNotEmpty ? false : true),
              isNeedDivideLine: false,
              onPressed: () {
                //收款账号UUID 提现金额
                _presenter?.requestBalanceDrawCash(_controller.account_uuid.value, price);
              }),
          Padding(
            padding: EdgeInsets.only(left: 16, right: 16, top: 10),
            child: CommonUtils.getSimpleText("1.为保证资金安全同时方便操作，我们将在绑卡的时候，给管理员发送绑卡验证码，同时在每一笔提现的时候，都会给管理员发送提醒短信。\n2.提现时，微信或支付宝等支付机构将收取您0.6%的手续费。\n3.提现操作完成后，一般一周到账。", 13, Colours.text_gray),
          )
        ],
      ),
    );
  }

  @override
  void balanceDrawCashStatus() {
    BoostNavigator.instance.pop();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalancePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void rechargePre(RechargePreEntity data) {}
}
