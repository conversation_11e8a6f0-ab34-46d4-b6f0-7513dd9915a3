import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../bean/account_bank_entity.dart';
import '../controller/balance_account_controller.dart';
import '../iview/balance_account_iview.dart';
import '../presenter/balance_account_persenter.dart';

class WithdrawalAccountBindPage extends StatefulWidget {
  String? bankUser = "";
  String? account_no = "";
  String? account_type = "";
  String? bankCode = "";

  WithdrawalAccountBindPage({super.key, required this.bankUser, required this.account_no, required this.account_type, required this.bankCode});

  @override
  _WithdrawalAccountBindPageState createState() => _WithdrawalAccountBindPageState();
}

class _WithdrawalAccountBindPageState extends State<WithdrawalAccountBindPage> with BasePageMixin<WithdrawalAccountBindPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WithdrawalAccountBindPage> implements BalanceAccountIView {
  final BalanceAccountController _controller = BalanceAccountController();

  BalanceAccountPresenter? _presenter;

  final FocusNode _focusNode = FocusNode();

  final TextEditingController _codeController = TextEditingController();
  bool _isButtonDisabled = false;
  int _countdown = 60;
  Timer? _timer;

  void _startCountdown() {
    if (_isButtonDisabled) return;

    setState(() {
      _isButtonDisabled = true;
      _countdown = 60;
    });

    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      if (_countdown > 0) {
        setState(() {
          _countdown--;
        });
      } else {
        _timer?.cancel();
        setState(() {
          _isButtonDisabled = false;
        });
      }
    });
  }

  @override
  void initState() {
    super.initState();
    // 请求焦点
    Future.delayed(Duration.zero, () {
      FocusScope.of(context).requestFocus(_focusNode);
    });
    print('接收到的的姓名 ${widget.bankUser}');
  }

  @override
  void dispose() {
    _codeController.dispose();
    _timer?.cancel();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '收款账号',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          Container(
            color: Colours.white,
            padding: EdgeInsets.only(left: 20, right: 20),
            child: Row(
              children: [
                CommonUtils.getSimpleText("验证码", 14, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(
                  child: TextField(
                    controller: _codeController,
                    focusNode: _focusNode, // 绑定 FocusNode
                    style: const TextStyle(
                      fontSize: 14.0, // 设置字体大小
                      color: Colours.base_primary_text_title, // 设置字体颜色
                    ),
                    decoration: const InputDecoration(
                      hintText: '请输入验证码', // 默认提示文本
                      border: InputBorder.none, // 不显示边框
                    ),
                  ),
                ),
                Gaps.hGap10,
                Container(
                  alignment: Alignment.center,
                  height: 32,
                  decoration: BoxDecoration(
                    border: Border.all(color: _isButtonDisabled ? Colours.text_gray : Colours.base_primary), // 绿色边框
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(right: 20, left: 20),
                    child: InkWell(
                      child: CommonUtils.getSimpleText(_isButtonDisabled ? '$_countdown秒后重发' : '获取短信验证码', 12, _isButtonDisabled ? Colours.text_gray : Colours.base_primary, textAlign: TextAlign.center),
                      onTap: () {
                        _presenter?.requestPhoneCode();
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
          Gaps.line,
          Gaps.vGap20,
          MyBottomButtonOne(
              isNeedBg: false,
              title: "完成",
              isDisabled: (_codeController.text.isNotEmpty ? false : true),
              isNeedDivideLine: false,
              onPressed: () {
                ///携带参数过去验证码界面
                _presenter?.requestAddAccount(widget.bankUser, widget.account_no, widget.account_type, _codeController.text, widget.bankCode);
              }),
        ],
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalanceAccountPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getSmsCode() {
    ///获取验证码了
    _startCountdown();
  }
}
