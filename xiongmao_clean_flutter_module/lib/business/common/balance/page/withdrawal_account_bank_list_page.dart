import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/balance_account_controller.dart';
import '../controller/balance_controller.dart';
import '../item/balance_account_bank_listview.dart';
import '../item/balance_account_listview.dart';
import '../item/balance_main_item_listview.dart';
import '../iview/balance_account_iview.dart';
import '../iview/balance_iview.dart';
import '../presenter/balance_account_persenter.dart';
import '../presenter/balance_persenter.dart';

class WithdrawalAccountBankListPage extends StatefulWidget {
  @override
  _WithdrawalAccountBankListPageState createState() => _WithdrawalAccountBankListPageState();
}

class _WithdrawalAccountBankListPageState extends State<WithdrawalAccountBankListPage> with BasePageMixin<WithdrawalAccountBankListPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WithdrawalAccountBankListPage> implements BalanceAccountIView {
  final BalanceAccountController _controller = BalanceAccountController();

  BalanceAccountPresenter? _presenter;

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.requestAccountBankList();
  }

  Future<dynamic> _loadMore() async {}

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '选择开户银行',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Column(
            children: [
              Container(
                width: double.infinity,
                color: Colours.base_primary_bg_page,
                padding: EdgeInsets.all(10),
                child: CommonUtils.getSimpleText("城市商业银行、农村商业银行、信用合作联社以及其他银行，请选择“其他银行”", 13, Colours.base_primary_text_title),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.accountBankList.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: false,
                itemBuilder: (_, index) {
                  // return WorkPostManagerListItem(provider.list[index], provider, _player, index);
                  return BalanceAccountBankListItem(
                    data: _controller.accountBankList[index],
                    onItemClick: () {
                      //选中了，返回上一个界面，把数据传递过去
                      // BoostNavigator.instance.pop(_controller.accountBankList[index]);
                      BoostNavigator.instance.pop(_controller.accountBankList[index]);
                    },
                  );
                },
              )),
            ],
          )),
    );
  }

  @override
  void balanceDrawCashStatus() {}

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalanceAccountPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getSmsCode() {}
}
