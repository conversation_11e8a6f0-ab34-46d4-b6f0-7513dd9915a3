import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/controller/balance_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/iview/balance_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/presenter/balance_persenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/recharge_pre_entity.dart';
import '../item/balance_main_item_listview.dart';

class BalanceMainPage extends StatefulWidget {
  @override
  _BalanceMainPageState createState() => _BalanceMainPageState();
}

class _BalanceMainPageState extends State<BalanceMainPage> with BasePageMixin<BalanceMainPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<BalanceMainPage> implements BalanceIView {
  var _indexText = '全部'; //记录当前收支情况选中的下标内容
  var _index = 0; //记录当前收支情况选中的下标

  var _dateTagText = '全部'; //记录日期的 value

  var _typeTagText = '全部'; //记录支出类型的 tag

  final BalanceController _controller = BalanceController();

  BalancePresenter? _presenter;

  int _page = 1;

  Future<dynamic> _onRefresh() async {
    _page = 1;
    _presenter?.requestBalanceInfo();
    _presenter?.requestBalanceBillList(_page, _index, _typeTagText, _dateTagText);
  }

  Future<dynamic> _loadMore() async {
    _page++;
    _presenter?.requestBalanceBillList(_page, _index, _typeTagText, _dateTagText);
  }

  @override
  void initState() {
    super.initState();
    _presenter?.requestBalanceTypeList();
    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        _loadMore();
      }
    });
    _onRefresh();
  }

  String format = 'yyyy年-MM月-dd日';
  BrnPickerTitleConfig pickerTitleConfig = BrnPickerTitleConfig(titleContent: "选择时间范围");

  ///标签选择弹框
  void _showMulSelectTagPicker(BuildContext context) {
    BrnBottomPicker.show(context,
        title: '',
        showTitle: false,
        barrierDismissible: true,
        contentWidget: Container(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 0, top: 0, right: 10, left: 10),
                child: SelectTabWidget(
                  _controller.tagListNew,
                  multiSelect: false,
                  crossAxisCount: 4,
                  hideMore: false,
                  tabFontSize: 14,
                  defaultSelectedIndex: [_controller.tagList.indexOf(_dateTagText)],
                  lastIsAddOne: false,
                  selectedColor: Colours.base_primary,
                  bgSelectedColor: Colours.base_primary_select,
                  bgUnSelectedColor: Colours.base_primary_un_select,
                  childAspectRatio: 8 / 4,
                  itemClickCallback: (List<int> indexs) {
                    setState(() {
                      if (indexs.last == _controller.tagList.length - 1) {
                        BrnDateRangePicker.showDatePicker(
                          context,
                          isDismissible: false,
                          minDateTime: DateTime(2010, 06, 01, 00, 00, 00),
                          maxDateTime: DateTime(2029, 07, 24, 23, 59, 59),
                          pickerMode: BrnDateTimeRangePickerMode.date,
                          minuteDivider: 10,
                          pickerTitleConfig: pickerTitleConfig,
                          dateFormat: format,
                          onConfirm: (startDateTime, endDateTime, startlist, endlist) {
                            _dateTagText = "${DateUtil.formatDate(startDateTime, format: 'yyyy/MM/dd')}-${DateUtil.formatDate(endDateTime, format: 'yyyy/MM/dd')}";
                            _onRefresh();
                          },
                        );
                      } else {
                        _dateTagText = _controller.tagList[indexs.last];
                        _onRefresh();
                      }
                    });
                    // 在这里放置您想要延迟执行的代码
                    Navigator.of(context, rootNavigator: true).pop();
                  },
                ),
              ),
              Container(
                width: double.infinity,
                height: 10,
                color: Colours.base_primary_bg_page,
              ),
              InkWell(
                child: Padding(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Container(
                    width: double.infinity,
                    child: CommonUtils.getSimpleText("取消", 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                  ),
                ),
                onTap: () {
                  Navigator.of(context, rootNavigator: true).pop();
                },
              ),
            ],
          ),
        ));
  }

  ///支出 、收入类型的
  void _showTypesTagPicker(BuildContext context) {
    BrnBottomPicker.show(context,
        title: '',
        showTitle: false,
        barrierDismissible: true,
        contentWidget: Container(
          child: Column(
            children: [
              Padding(
                padding: EdgeInsets.only(bottom: 0, top: 0, right: 10, left: 10),
                child: SelectTabWidget((_index == 1) ? _controller.expenditureSrTagsNew : _controller.expenditureZcTagsNew, multiSelect: false, crossAxisCount: 4, hideMore: false, tabFontSize: 14, defaultSelectedIndex: [(_index == 1) ? _controller.expenditureSrTagsNew.indexWhere((element) => element.name == _typeTagText) : _controller.expenditureZcTagsNew.indexWhere((element) => element.name == _typeTagText)], lastIsAddOne: false, selectedColor: Colours.white, bgSelectedColor: Colours.base_primary, bgUnSelectedColor: Colours.transparent, childAspectRatio: 8 / 4, itemClickCallback: (List<int> indexs) {
                  setState(() {
                    _typeTagText = ((_index == 1) ? _controller.expenditureSrTagsNew[indexs.last].name : _controller.expenditureZcTagsNew[indexs.last].name) ?? "";
                    _onRefresh();
                  });
                  // 在这里放置您想要延迟执行的代码
                  Navigator.of(context, rootNavigator: true).pop();
                }),
              ),
              Container(
                width: double.infinity,
                height: 10,
                color: Colours.base_primary_bg_page,
              ),
              InkWell(
                child: Padding(
                  padding: EdgeInsets.only(top: 10, bottom: 10),
                  child: Container(
                    width: double.infinity,
                    child: CommonUtils.getSimpleText("取消", 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                  ),
                ),
                onTap: () {
                  Navigator.of(context, rootNavigator: true).pop();
                },
              ),
            ],
          ),
        ));
  }

  ScrollController _scrollController = ScrollController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '账户余额',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => RefreshIndicator(
            onRefresh: _onRefresh,
            child: ListView(
              controller: _scrollController,
              children: [
                Column(
                  children: [
                    ///这里是账户的余额
                    Container(
                      color: Colours.base_primary,
                      padding: EdgeInsets.only(left: 20.0, right: 20, top: 20, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CommonUtils.getSimpleText("总余额:", 14, Colours.white, fontWeight: FontWeight.bold),
                                  CommonUtils.getSimpleText("${_controller.userBalanceInfo.value.totalBalance ?? 0}元", 20, Colours.white, fontWeight: FontWeight.bold),
                                  CommonUtils.getSimpleText("总计(可用+冻结金额)", 12, Colours.white),
                                ],
                              )),
                              Padding(
                                padding: EdgeInsets.only(right: 10),
                                child: Container(
                                  color: Colors.white,
                                  height: 40,
                                  width: 1,
                                ),
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CommonUtils.getSimpleText("可用金额：${_controller.userBalanceInfo.value.balance ?? 0}元", 12, Colours.white),
                                  CommonUtils.getSimpleText("冻结金额：${_controller.userBalanceInfo.value.frozenBalance ?? 0}元", 12, Colours.white),
                                ],
                              )),
                            ],
                          ),
                          Gaps.vGap10,
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              Expanded(
                                child: Container(
                                  margin: EdgeInsets.symmetric(horizontal: 0.0),
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: Colors.white, width: 1),
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      BoostNavigator.instance.push("rechargeBalancePostPage", arguments: {'balance': _controller.userBalanceInfo.value.balance}).then((value) => _onRefresh());
                                    },
                                    child: Center(
                                      child: CommonUtils.getSimpleText("充值", 14, Colours.white),
                                    ),
                                  ),
                                ),
                              ),
                              Gaps.hGap10, // 添加间隔
                              Expanded(
                                child: Container(
                                  margin: EdgeInsets.symmetric(horizontal: 0.0),
                                  height: 36,
                                  decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    borderRadius: BorderRadius.circular(10.0),
                                    border: Border.all(color: Colors.white, width: 1),
                                  ),
                                  child: InkWell(
                                    onTap: () {
                                      BoostNavigator.instance.push("withdrawalBalancePostPage", arguments: {"balance": _controller.userBalanceInfo.value.balance}).then((value) => _onRefresh());
                                    },
                                    child: Center(
                                      child: CommonUtils.getSimpleText("提现", 14, Colours.white),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),

                    ///这里放筛选的日期
                    BrnTextSelectFormItem(
                      title: "日期",
                      value: _dateTagText,
                      onTap: () {
                        _showMulSelectTagPicker(context);
                      },
                    ),
                    Gaps.line,

                    ///这里放收支内容
                    BrnRadioInputFormItem(
                      title: "收支",
                      options: const ["全部", "收入", "支出"],
                      value: _indexText,
                      onChanged: (oldValue, newValue) {
                        setState(() {
                          _indexText = newValue!;
                          switch (_indexText) {
                            case '全部':
                              _typeTagText = "全部";
                              _index = 0;
                              break;
                            case '收入':
                              _typeTagText = "全部";
                              _index = 1;
                              break;
                            case '支出':
                              _index = 2;
                              _typeTagText = "全部";
                              break;
                          }
                          _onRefresh();
                        });
                      },
                    ),
                    Gaps.line,
                    Visibility(
                      visible: (_index != 0),
                      child: BrnTextSelectFormItem(
                        title: "类型",
                        value: _typeTagText,
                        onTap: () {
                          _showTypesTagPicker(context);
                        },
                      ),
                    ),
                  ],
                ),
                Padding(padding: const EdgeInsets.all(10), child: CommonUtils.getSimpleText("统计：收入${_controller.balanceBillList.value.srTotal ?? 0}元，支出${_controller.balanceBillList.value.zcTotal ?? 0}元", 14, Colours.base_primary_text_title)),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: _controller.list.length, // Example item count
                  itemBuilder: (context, index) {
                    return BalanceMainListItem(data: _controller.list[index]);
                  },
                ),
              ],
            ),
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BalancePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void balanceDrawCashStatus() {}

  @override
  void rechargePre(RechargePreEntity data) {}
}
