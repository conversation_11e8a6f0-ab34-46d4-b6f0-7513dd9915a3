import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/balance_account_entity.dart';
import '../bean/balance_type_entity.dart';

class BalanceController extends GetxController {
  List<BrnCommonActionSheetItem> actionsAccount = [
    BrnCommonActionSheetItem(
      '支付宝',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '银行卡',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
  ];

  //默认的数据
  var tagList = [
    '全部',
    '今天',
    '昨天',
    '本月',
    '上月',
    '其他',
  ].obs;

  //默认的数据
  var tagListNew = [
    BaseChooseString("全部"),
    BaseChooseString("今天"),
    BaseChooseString("昨天"),
    BaseChooseString("本月"),
    BaseChooseString("上月"),
    BaseChooseString("其他"),
  ].obs;

  //默认的数据 收入 支出
  var expenditureZcTagsNew = <BaseChooseString>[].obs;
  var expenditureSrTagsNew = <BaseChooseString>[].obs;

  ///用户余额存储
  var userBalanceInfo = UserBalanceEntity().obs;

  void updateUserBalanceInfo(UserBalanceEntity data) {
    userBalanceInfo.value = data;
  }

  ///余额存储明细
  var balanceBillList = BalanceBillListEntity().obs;

  void updateBalanceBillList(BalanceBillListEntity data) {
    balanceBillList.value = data;
  }

  ///存储的列表
  var list = <BalanceBillListList>[].obs;

  void initMyList(List<BalanceBillListList> value) {
    list.value = value.toList();
  }

  void updateMyList(List<BalanceBillListList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///收款账号列表
  var accountList = <BalanceAccountList>[].obs;

  var account = ''.obs; //收款账号
  var account_uuid = ''.obs; //收款账号uuid

  void initBalanceAccountList(List<BalanceAccountList> value) {
    accountList.value = value.toList();
    if (accountList.length > 1) {
      account.value = '${accountList[0].accountUserName}-${accountList[0].accountName}(${accountList[0].accountNo!.substring(accountList[0].accountNo!.length - 4, accountList[0].accountNo!.length)})';
      account_uuid.value = accountList[0].uuid ?? "";
    }
  }

  ///条件列表
  var balanceTypesSr = <BalanceTypeSrList>[].obs;
  var balanceTypesZc = <BalanceTypeZcList>[].obs;

  void updateBalanceSrMyList(List<BalanceTypeSrList> value) {
    balanceTypesSr.value = value.toList();
    expenditureSrTagsNew.add(BaseChooseString("全部"));
    for (int i = 0; i < value.length; i++) {
      expenditureSrTagsNew.add(BaseChooseString(value[i].name));
    }
  }

  void updateBalanceZcMyList(List<BalanceTypeZcList> value) {
    balanceTypesZc.value = value.toList();
    expenditureZcTagsNew.add(BaseChooseString("全部"));
    for (int i = 0; i < value.length; i++) {
      expenditureZcTagsNew.add(BaseChooseString(value[i].name));
    }
  }
}
