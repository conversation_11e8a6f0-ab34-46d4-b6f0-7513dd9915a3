import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/account_bank_entity.dart';
import '../bean/balance_account_entity.dart';
import '../bean/bank_ocr_entity.dart';

class BalanceAccountController extends GetxController {
  List<BrnCommonActionSheetItem> actionsAccount = [
    BrnCommonActionSheetItem(
      '支付宝',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '银行卡',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
  ];

  ///收款账号列表
  var accountList = <BalanceAccountList>[].obs;

  void initBalanceAccountList(List<BalanceAccountList> value, String? account_uuid) {
    accountList.value = value.toList();
    //反选已经有
    if (!TextUtil.isEmpty(account_uuid)) {
      for (int i = 0; i < accountList.length; i++) {
        if (account_uuid == accountList[i].uuid) {
          selectedIndex = i;
          break;
        }
      }
    }
  }

  ///银行卡列表
  var accountBankList = <AccountBankList>[].obs;

  void initAccountBankList(List<AccountBankList> value) {
    accountBankList.value = value.toList();
  }

  ///验证码
  var code = "".obs;

  void updateCode(String smsCode) {
    code.value = smsCode;
  }

  ///选中照片的索引
  var selectedIndex = -1.obs;

  /// ocr 识别的内容
  var bankOCR = BankOcrEntity().obs;

  var bankNo = ''.obs;
  var bankCode = ''.obs; //银行 code 给后端的
  var bankName = ''.obs; //银行名称

  void updateBankOCR(BankOcrEntity data) {
    bankOCR.value = data;
    bankNo.value = data.bankCardNumber ?? "";
    bankCode.value = data.bankCode ?? "";
    bankName.value = data.bankName ?? "";
  }
}
