import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../bean/balance_bill_list_entity.dart';

class BalanceMainListItem extends StatelessWidget {
  BalanceBillListList data;

  BalanceMainListItem({required this.data});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 1),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: CommonUtils.getSimpleText("${data.instruction}", 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                CommonUtils.getSimpleText((data.profitType == '1' ? '+${data.money}' : '-${data.money}'), 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              ],
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: CommonUtils.getSimpleText("${data.createTime}", 14, Colours.text_gray)),
                CommonUtils.getSimpleText("(可用余额${data.afterBalance}元)", 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              ],
            )
          ],
        ),
      ),
    );
  }
}
