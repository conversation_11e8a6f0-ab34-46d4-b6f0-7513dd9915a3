import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../bean/account_bank_entity.dart';
import '../bean/balance_account_entity.dart';
import '../bean/balance_bill_list_entity.dart';

class BalanceAccountBankListItem extends StatelessWidget {
  final Function onItemClick;

  AccountBankList data;

  BalanceAccountBankListItem({required this.data, required this.onItemClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(bottom: 1),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
        child: InkWell(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 10),
                    child: LoadImage(
                      data.icon ?? "",
                      width: 30,
                      height: 30,
                    ),
                  ),
                  Expanded(child: CommonUtils.getSimpleText('${data.name}', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                ],
              ),
            ],
          ),
          onTap: (){
            onItemClick();
          },
        ),
      ),
    );
  }
}
