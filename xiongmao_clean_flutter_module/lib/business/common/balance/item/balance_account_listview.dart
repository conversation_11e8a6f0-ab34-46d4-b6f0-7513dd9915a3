import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/my_brn_radio_button.dart';
import '../bean/balance_account_entity.dart';

class BalanceAccountListItem extends StatefulWidget {
  final BalanceAccountList data;
  final Function onDelete;
  final bool isSelected; // 选中状态
  final VoidCallback onTap; // 点击事件回调
  final VoidCallback onLongTap; // 点击事件回调

  BalanceAccountListItem({
    required this.data,
    required this.onDelete,
    required this.isSelected,
    required this.onTap,
    required this.onLongTap,
  });

  @override
  _BalanceAccountListItemState createState() => _BalanceAccountListItemState();
}

class _BalanceAccountListItemState extends State<BalanceAccountListItem> {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onLongPress: widget.onLongTap,
      onTap: widget.onTap, // 点击整个 item
      child: Container(
        color: Colors.white,
        margin: EdgeInsets.only(bottom: 1),
        child: Padding(
          padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
          child: Row(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 10),
                child: LoadImage(
                  widget.data.accountIcon.toString(),
                  width: 30,
                  height: 30,
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(
                      "${widget.data.accountName}",
                      14,
                      Colours.base_primary_text_title,
                      fontWeight: FontWeight.bold,
                    ),
                    CommonUtils.getSimpleText(
                      "${widget.data.accountUserName}",
                      13,
                      Colours.base_primary_text_title,
                    ),
                    CommonUtils.getSimpleText(
                      "账号${widget.data.accountNo}",
                      13,
                      Colours.base_primary_text_title,
                    ),
                  ],
                ),
              ),
              MyBrnRadioButton(
                radioIndex: 0,
                isSelected: widget.isSelected,
                onValueChangedAtIndex: (index, value) {
                  widget.onTap();
                },
              )
            ],
          ),
        ),
      ),
    );
  }
}
