import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/account_bank_entity.dart';
import '../bean/account_code_entity.dart';
import '../bean/balance_account_entity.dart';
import '../bean/bank_ocr_entity.dart';
import '../controller/balance_account_controller.dart';
import '../controller/balance_controller.dart';
import '../iview/balance_account_iview.dart';
import '../iview/balance_iview.dart';

/**
 * 账户余额充值
 */
class BalanceAccountPresenter extends BasePagePresenter<BalanceAccountIView> with WidgetsBindingObserver {
  BalanceAccountController controller;

  BalanceAccountPresenter(this.controller);

  ///获取账户列表
  Future<dynamic> requestBalanceAccountList(String? account_uuid) {
    var params = <String, String>{};
    params["is_page"] = "0";
    return requestNetwork<BalanceAccountEntity>(Method.get, url: HttpApi.GET_USER_BALANCE_ACCOUNT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initBalanceAccountList(data.list!, account_uuid);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除账户列表
  Future<dynamic> requestBalanceAccountDel(String uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_USER_BALANCE_ACCOUNT_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      requestBalanceAccountList("");
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///增加收款账户
  Future<dynamic> requestAddAccount(String? account_user_name, String? account_no, String? account_type, String? verification_code, String? account_code) {
    var params = <String, String>{};
    //账号类型 1银行卡 2支付宝
    params['account_type'] = '$account_type';
    params['account_user_name'] = "$account_user_name";
    if ("1" == account_type) {
      params['account_no'] = "$account_no";
      params['account_code'] = "$account_code";
      params['verification_code'] = "$verification_code";
    } else {
      params['account_no'] = "$account_no";
      params['verification_code'] = "$verification_code";
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_USER_BALANCE_ADD_ACCOUNT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      BoostNavigator.instance.popUntil(route: 'withdrawalAccountListPostPage');
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取银行列表
  Future<dynamic> requestAccountBankList() {
    var params = <String, String>{};
    return requestNetwork<AccountBankEntity>(Method.get, url: HttpApi.GET_USER_BALANCE_BANK_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initAccountBankList(data.list!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取验证码
  Future<dynamic> requestPhoneCode() {
    var params = <String, String>{};
    params['op_type'] = "bindCollectionAccount";
    return requestNetwork<AccountCodeEntity>(Method.post, url: HttpApi.GET_USER_BALANCE_ACCOUNT_CODE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateCode(data.code!);
        view.getSmsCode();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///自动识别银行卡的OCR
  Future<dynamic> requestAutoOcrBank(String keyUrl) {
    var params = <String, String>{};
    params['card_url'] = keyUrl;
    return requestNetwork<BankOcrEntity>(Method.post, url: HttpApi.BANK_OCR, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateBankOCR(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
