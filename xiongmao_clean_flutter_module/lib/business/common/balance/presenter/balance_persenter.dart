import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/balance_bill_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/balance/bean/user_balance_entity.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/balance_account_entity.dart';
import '../bean/balance_type_entity.dart';
import '../bean/recharge_pre_entity.dart';
import '../controller/balance_controller.dart';
import '../iview/balance_iview.dart';

/**
 * 账户余额充值
 */
class BalancePresenter extends BasePagePresenter<BalanceIView> with WidgetsBindingObserver {
  BalanceController controller;

  BalancePresenter(this.controller);

  ///获取账户余额信息
  Future<dynamic> requestBalanceInfo() {
    var params = <String, String>{};
    return requestNetwork<UserBalanceEntity>(Method.get, url: HttpApi.GET_USER_BALANCE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      controller.updateUserBalanceInfo(data!);
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取余额流水记录
  Future<dynamic> requestBalanceBillList(int page, int profit_type, String bill_type, String search_date) {
    var params = <String, String>{};
    params['page'] = '$page';
    params['size'] = '20';
    params['profit_type'] = '$profit_type'; //收支类型 1收入 2支出
    //判断当前是支出还是收入
    String bill = "";
    if (profit_type == 1) {
      if ("全部" != bill_type) {
        bill = "${controller.balanceTypesSr.firstWhere(
              (element) => bill_type == element.name,
            ).id}";
      }
    } else {
      if ("全部" != bill_type) {
        bill = "${controller.balanceTypesZc.firstWhere(
              (element) => bill_type == element.name,
            ).id}";
      }
    }

    ///账单类型 1信用查询 2电子合同 20现金充值 21对公充值 22提现
    if (!TextUtil.isEmpty(bill)) {
      params['bill_type'] = '$bill';
    }

    var search_date_result = '';
    switch (search_date) {
      case '今天':
        search_date_result = 'today';
        break;
      case '昨天':
        search_date_result = 'yesterday';
        break;
      case '本月':
        search_date_result = 'month';
        break;
      case '上月':
        search_date_result = 'last_month';
        break;
      case "全部":
        search_date_result = "";
        break;
      default:
        search_date_result = search_date.replaceAll("-", "#").replaceAll("/", "-");
        break;
    }

    ///日期筛选 today 今天 yesterday 昨天 month 本月 last_month 上月 2024-09-26#2025-09-26
    if (!TextUtil.isEmpty(search_date_result)) {
      params['search_date'] = '$search_date_result';
    }

    return requestNetwork<BalanceBillListEntity>(Method.get, url: HttpApi.GET_USER_BALANCE_BILL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      controller.updateBalanceBillList(data!);
      if (page == 1) {
        controller.initMyList(data.list!);
      } else {
        controller.updateMyList(data.list!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///提现余额的接口
  Future<dynamic> requestBalanceDrawCash(String account_uuid, String amount) {
    var params = <String, String>{};
    params["account_uuid"] = "$account_uuid";
    params["amount"] = "$amount";
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_USER_BALANCE_DRAW_CASH, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.balanceDrawCashStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///对公充值
  Future<dynamic> requestBalanceRechargePre() {
    var params = <String, String>{};
    return requestNetwork<RechargePreEntity>(Method.get, url: HttpApi.GET_USER_BALANCE_RECHARGE_PRE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.rechargePre(data!);
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取账户列表
  Future<dynamic> requestBalanceAccountList() {
    var params = <String, String>{};
    params["is_page"] = "0";
    return requestNetwork<BalanceAccountEntity>(Method.get, url: HttpApi.GET_USER_BALANCE_ACCOUNT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initBalanceAccountList(data.list!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取账户列表
  Future<dynamic> requestBalanceTypeList() {
    var params = <String, String>{};
    params["is_page"] = "0";
    return requestNetwork<BalanceTypeEntity>(Method.get, url: HttpApi.BALANCE_TYPE_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateBalanceSrMyList(data.srList!);
        controller.updateBalanceZcMyList(data.zcList!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
