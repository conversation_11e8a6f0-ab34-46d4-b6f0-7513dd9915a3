import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/account_bank_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/account_bank_entity.g.dart';

@JsonSerializable()
class AccountBankEntity {
	List<AccountBankList>? list;

	AccountBankEntity();

	factory AccountBankEntity.fromJson(Map<String, dynamic> json) => $AccountBankEntityFromJson(json);

	Map<String, dynamic> toJson() => $AccountBankEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AccountBankList {
	String? code;
	String? name;
	String? icon;

	AccountBankList();

	factory AccountBankList.fromJson(Map<String, dynamic> json) => $AccountBankListFromJson(json);

	Map<String, dynamic> toJson() => $AccountBankListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}