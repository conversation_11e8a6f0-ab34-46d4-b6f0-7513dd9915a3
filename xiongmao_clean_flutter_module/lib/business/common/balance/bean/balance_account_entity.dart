import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/balance_account_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/balance_account_entity.g.dart';

@JsonSerializable()
class BalanceAccountEntity {
	int? page;
	int? size;
	int? total;
	List<BalanceAccountList>? list;

	BalanceAccountEntity();

	factory BalanceAccountEntity.fromJson(Map<String, dynamic> json) => $BalanceAccountEntityFromJson(json);

	Map<String, dynamic> toJson() => $BalanceAccountEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BalanceAccountList {
	String? uuid;
	@JSONField(name: "account_name")
	String? accountName;
	@JSONField(name: "account_user_name")
	String? accountUserName;
	@JSONField(name: "account_no")
	String? accountNo;
	@JSONField(name: "account_code")
	String? accountCode;
	@JSONField(name: "account_type")
	String? accountType;
	@JSONField(name: "account_icon")
	String? accountIcon;

	BalanceAccountList();

	factory BalanceAccountList.fromJson(Map<String, dynamic> json) => $BalanceAccountListFromJson(json);

	Map<String, dynamic> toJson() => $BalanceAccountListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}