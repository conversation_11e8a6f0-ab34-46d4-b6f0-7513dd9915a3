import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/balance_type_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/balance_type_entity.g.dart';

@JsonSerializable()
class BalanceTypeEntity {
	@JSONField(name: "sr_list")
	List<BalanceTypeSrList>? srList;
	@JSONField(name: "zc_list")
	List<BalanceTypeZcList>? zcList;

	BalanceTypeEntity();

	factory BalanceTypeEntity.fromJson(Map<String, dynamic> json) => $BalanceTypeEntityFromJson(json);

	Map<String, dynamic> toJson() => $BalanceTypeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BalanceTypeSrList {
	int? id;
	String? name;

	BalanceTypeSrList();

	factory BalanceTypeSrList.fromJson(Map<String, dynamic> json) => $BalanceTypeSrListFromJson(json);

	Map<String, dynamic> toJson() => $BalanceTypeSrListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BalanceTypeZcList {
	int? id;
	String? name;

	BalanceTypeZcList();

	factory BalanceTypeZcList.fromJson(Map<String, dynamic> json) => $BalanceTypeZcListFromJson(json);

	Map<String, dynamic> toJson() => $BalanceTypeZcListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}