import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/account_code_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/account_code_entity.g.dart';

@JsonSerializable()
class AccountCodeEntity {
	String? code;

	AccountCodeEntity();

	factory AccountCodeEntity.fromJson(Map<String, dynamic> json) => $AccountCodeEntityFromJson(json);

	Map<String, dynamic> toJson() => $AccountCodeEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}