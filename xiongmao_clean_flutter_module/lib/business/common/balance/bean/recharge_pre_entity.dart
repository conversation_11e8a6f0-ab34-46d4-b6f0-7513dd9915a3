import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/recharge_pre_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/recharge_pre_entity.g.dart';

@JsonSerializable()
class RechargePreEntity {
	String? balance;
	@JSONField(name: "to_account")
	RechargePreToAccount? toAccount;
	int? status;
	RechargePrePrompt? prompt;
	@JSONField(name: "prompt_url")
	String? promptUrl;

	RechargePreEntity();

	factory RechargePreEntity.fromJson(Map<String, dynamic> json) => $RechargePreEntityFromJson(json);

	Map<String, dynamic> toJson() => $RechargePreEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class RechargePreToAccount {
	@JSONField(name: "account_name")
	String? accountName;
	@JSONField(name: "account_no")
	String? accountNo;
	@JSONField(name: "account_bank")
	String? accountBank;
	@JSONField(name: "bank_code")
	String? bankCode;

	RechargePreToAccount();

	factory RechargePreToAccount.fromJson(Map<String, dynamic> json) => $RechargePreToAccountFromJson(json);

	Map<String, dynamic> toJson() => $RechargePreToAccountToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class RechargePrePrompt {
	String? text;
	String? highlight;

	RechargePrePrompt();

	factory RechargePrePrompt.fromJson(Map<String, dynamic> json) => $RechargePrePromptFromJson(json);

	Map<String, dynamic> toJson() => $RechargePrePromptToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}