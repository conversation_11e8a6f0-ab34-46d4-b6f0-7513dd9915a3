import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/user_balance_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/user_balance_entity.g.dart';

@JsonSerializable()
class UserBalanceEntity {
	String? balance;
	@JSONField(name: "total_balance")
	String? totalBalance;
	@JSONField(name: "frozen_balance")
	String? frozenBalance;

	UserBalanceEntity();

	factory UserBalanceEntity.fromJson(Map<String, dynamic> json) => $UserBalanceEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserBalanceEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}