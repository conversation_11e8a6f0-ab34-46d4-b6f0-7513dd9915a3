import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/balance_bill_list_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/balance_bill_list_entity.g.dart';

@JsonSerializable()
class BalanceBillListEntity {
	int? page;
	int? size;
	@JSONField(name: "sr_total")
	String? srTotal;
	@JSONField(name: "zc_total")
	String? zcTotal;
	int? total;
	List<BalanceBillListList>? list;

	BalanceBillListEntity();

	factory BalanceBillListEntity.fromJson(Map<String, dynamic> json) => $BalanceBillListEntityFromJson(json);

	Map<String, dynamic> toJson() => $BalanceBillListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BalanceBillListList {
	@JSONField(name: "user_name")
	String? userName;
	@J<PERSON>NField(name: "before_balance")
	String? beforeBalance;
	String? money;
	@JSONField(name: "after_balance")
	String? afterBalance;
	@JSONField(name: "profit_type")
	String? profitType;
	String? instruction;
	@JSONField(name: "create_time")
	String? createTime;

	BalanceBillListList();

	factory BalanceBillListList.fromJson(Map<String, dynamic> json) => $BalanceBillListListFromJson(json);

	Map<String, dynamic> toJson() => $BalanceBillListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}