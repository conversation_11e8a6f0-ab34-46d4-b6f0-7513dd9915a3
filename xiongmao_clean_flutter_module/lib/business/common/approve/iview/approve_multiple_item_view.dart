import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';

class AppRoveMultipleItemView extends StatefulWidget {
  final List<String> items;
  final bool isSingleSelection;
  final Function(String, List<String>,List<int>) onItemSelected;
  final String selectContent;

  AppRoveMultipleItemView({
    required this.items,
    required this.selectContent,
    required this.isSingleSelection,
    required this.onItemSelected});

  @override
  _AppRoveMultipleItemViewState createState() => _AppRoveMultipleItemViewState();
}

class _AppRoveMultipleItemViewState extends State<AppRoveMultipleItemView> {
  String? _selectedItem;
  List<String> _selectedItems = [];
  List<int> _selectedIndexItems = [];

  @override
  void initState() {
    super.initState();
    _selectedItem = widget.selectContent;
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      padding: EdgeInsets.all(0.0), // 设置所有边的padding为16.0
      physics: NeverScrollableScrollPhysics(),
      shrinkWrap: true,
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        return InkWell(child: Column(children: [
          Container(child: Row(children: [
            Expanded(child: CommonUtils.getSimpleText(widget.items[index], 14, Colours.base_primary_text_title)),
            
            LoadAssetImage((widget.isSingleSelection ? _selectedItem == widget.items[index] :
            _selectedItems.contains(widget.items[index]))?"icon_check":"icon_uncheck"
              ,width: 20,height: 20,),
            
            // Checkbox(
            //   value: widget.isSingleSelection ? _selectedItem == widget.items[index] : _selectedItems.contains(widget.items[index]),
            //   onChanged: (value) {
            //     setState(() {
            //       if (widget.isSingleSelection) {
            //         _selectedItem = widget.items[index];
            //         widget.onItemSelected(_selectedItem!, _selectedItems,[index]);
            //       } else {
            //         if (value!) {
            //           _selectedItems.add(widget.items[index]);
            //           _selectedIndexItems.add(index);
            //         } else {
            //           _selectedItems.remove(widget.items[index]);
            //           _selectedIndexItems.remove(index);
            //         }
            //         widget.onItemSelected(widget.items[index], _selectedItems,_selectedIndexItems);
            //       }
            //     });
            //   },
            // )
          ],),height: 48,),
          Visibility(child: Gaps.line,visible: index!=widget.items.length-1,),
        ],),onTap: (){
              setState(() {
                if (widget.isSingleSelection) {
                  _selectedItem = widget.items[index];
                  widget.onItemSelected(_selectedItem!, _selectedItems,[index]);
                } else {
                  if (_selectedItems.contains(widget.items[index])) {
                    _selectedItems.remove(widget.items[index]);
                  } else {
                    _selectedItems.add(widget.items[index]);
                  }
                  widget.onItemSelected(widget.items[index], _selectedItems,_selectedIndexItems);
                }
              });
        },);
        // return ListTile(
        //   title: Text(widget.items[index]),
        //   trailing: Checkbox(
        //     value: widget.isSingleSelection ? _selectedItem == widget.items[index] : _selectedItems.contains(widget.items[index]),
        //     onChanged: (value) {
        //       setState(() {
        //         if (widget.isSingleSelection) {
        //           _selectedItem = widget.items[index];
        //           widget.onItemSelected(_selectedItem!, _selectedItems,[index]);
        //         } else {
        //           if (value!) {
        //             _selectedItems.add(widget.items[index]);
        //             _selectedIndexItems.add(index);
        //           } else {
        //             _selectedItems.remove(widget.items[index]);
        //             _selectedIndexItems.remove(index);
        //           }
        //           widget.onItemSelected(widget.items[index], _selectedItems,_selectedIndexItems);
        //         }
        //       });
        //     },
        //   ),
        //   onTap: () {
        //     setState(() {
        //       if (widget.isSingleSelection) {
        //         _selectedItem = widget.items[index];
        //         widget.onItemSelected(_selectedItem!, _selectedItems,[index]);
        //       } else {
        //         if (_selectedItems.contains(widget.items[index])) {
        //           _selectedItems.remove(widget.items[index]);
        //         } else {
        //           _selectedItems.add(widget.items[index]);
        //         }
        //         widget.onItemSelected(widget.items[index], _selectedItems,_selectedIndexItems);
        //       }
        //     });
        //   },
        // );
      },
    );
  }
}
