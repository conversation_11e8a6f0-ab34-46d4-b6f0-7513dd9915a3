

import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';

import '../../../../generated/get_role_all_entity.dart';
import '../../../../mvp/mvps.dart';

abstract class ApproveSettingMainPageView extends IMvpView {
  void getRoleAll(GetRoleAllEntity? data) {}

  void getMetaDta(GetMetaEntity? data) {}
}