import 'dart:convert';
import 'dart:ffi';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/role_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../risk_monitoring/bean/project_archives_entity.dart';

typedef GetUserListCallback = Function(List<ApproveDetailListUserList> data);

class AppRoveUserItemView extends StatefulWidget {
  List<ApproveDetailListUserList> userList;
  GetUserListCallback callBack;
  int style;

  AppRoveUserItemView(this.style, this.userList, this.callBack);

  @override
  _AppRoveUserItemViewState createState() => _AppRoveUserItemViewState();
}

class _AppRoveUserItemViewState extends State<AppRoveUserItemView> {
  VoidCallback? addListener;
  List<ApproveDetailListUserList>? userList;

  @override
  void initState() {
    super.initState();
    userList = widget.userList;
    addListener ??= BoostChannel.instance.addEventListener("SelectPersonnel", (key, arguments) async {
      // var argument = arguments["itemJson"];
      var itemListJsonArgument = arguments["usersJson"];
      // if (argument != null&&argument!="") {
      //   Map<String, dynamic> userMap = json.decode(argument);
      //   RoleDetailEntity roleDetailEntity = RoleDetailEntity.fromJson(userMap);
      //   var approveDetailListUserList = ApproveDetailListUserList();
      //   approveDetailListUserList.userName = roleDetailEntity.userName;
      //   approveDetailListUserList.uuid = roleDetailEntity.uuid;
      //   userList?.add(approveDetailListUserList);
      //   setState(() {
      //   });
      //   widget.callBack.call(userList??[]);
      // }
      if (itemListJsonArgument != null && itemListJsonArgument != "") {
        List<dynamic> jsonList = json.decode(itemListJsonArgument);
        List<RoleDetailEntity> items = jsonList.map((itemMap) => RoleDetailEntity.fromJson(itemMap)).toList();
        userList?.clear();
        for (var value in items) {
          var approveDetailListUserList = ApproveDetailListUserList();
          approveDetailListUserList.userName = value.userName;
          approveDetailListUserList.uuid = value.uuid;
          userList?.add(approveDetailListUserList);
          setState(() {});
          widget.callBack.call(userList ?? []);
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListView.builder(
          padding: EdgeInsets.all(0),
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: userList?.length,
          itemBuilder: (context, index) {
            // return ListTile(
            //   contentPadding: EdgeInsets.all(0),
            //   title: Text(userList?[index].userName ?? ""),
            //   trailing: IconButton(
            //     icon: Image.asset(
            //       'assets/images/base/icon_base_del.png',
            //       width: 26,
            //       height: 26,
            //     ),
            //     onPressed: () {
            //       setState(() {
            //         userList?.removeAt(index);
            //       });
            //       widget.callBack.call(userList??[]);
            //     },
            //   ),
            // );
            return Column(
              children: [
                Container(
                  height: 48,
                  child: Row(
                    children: [
                      Expanded(child: CommonUtils.getSimpleText(userList?[index].userName ?? "", 14, Colours.base_primary_text_title)),
                      InkWell(
                        child: const LoadAssetImage(
                          "common/icon_approve_delete",
                          width: 26,
                          height: 26,
                        ),
                        onTap: () {
                          setState(() {
                            userList?.removeAt(index);
                          });
                          widget.callBack.call(userList ?? []);
                        },
                      )
                    ],
                  ),
                ),
                Gaps.line,
              ],
            );
          },
        ),
        Container(
          padding: EdgeInsets.only(top: 10, bottom: 10),
          child: InkWell(
            onTap: () {
              BoostNavigator.instance.push('selectPersonnelPage', arguments: {
                'title': (widget.style == 0) ? "选择审批人" : "选择抄送人",
                'multiple': true,
                'is_head_office': '1',
                'uuids': json.encode(
                  userList?.map((e) => e.uuid).toList().join(',') ?? '',
                ),
              }).then((value) {
                if (value != null) {
                  List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                  userList?.clear();
                  for (var value in data) {
                    var approveDetailListUserList = ApproveDetailListUserList();
                    approveDetailListUserList.userName = value.userName;
                    approveDetailListUserList.uuid = value.uuid;
                    userList?.add(approveDetailListUserList);
                    setState(() {});
                    widget.callBack.call(userList ?? []);
                  }
                }
              });
            },
            child: const Text(
              "+添加",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colours.base_primary_blue),
            ),
          ),
        ),
      ],
    );
  }

  @override
  void dispose() {
    super.dispose();
    //addEventListener方法会返回一个block，执行这个block就会移除监听
    addListener?.call();
  }
}
