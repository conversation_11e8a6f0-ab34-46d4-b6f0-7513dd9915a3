import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 身份证验证
 */
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_button.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../bean/template_one_entity.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/approve_main_iview.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';
import '../presenter/custom_comonents_presenter.dart';
import '../presenter/material_presenter.dart';

/**
 * 自定义组件的界面
 */
class CustomComponentsPage extends StatefulWidget {
  TemplateOneFieldList? templateOneFieldList;
  bool show_abstract = true;

  CustomComponentsPage({super.key, required this.templateOneFieldList, required this.show_abstract});

  @override
  _CustomComponentsPageState createState() => _CustomComponentsPageState();
}

class _CustomComponentsPageState extends State<CustomComponentsPage> with BasePageMixin<CustomComponentsPage, PowerPresenter<dynamic>> implements CustomComponentsView {
  final CustomComponentController _controller = CustomComponentController();

  late CustomComponentPresenter _presenter;

  @override
  void initState() {
    super.initState();
    //如果是添加表格字段，那么就隐藏表格
    if (widget.show_abstract == false) {
      _controller.settingTagWights.removeAt(_controller.settingTagWights.length - 1);
    }
    //是编辑 来设置内容
    if (widget.templateOneFieldList != null) {
      _controller.customName.value = widget.templateOneFieldList!.fieldName ?? '';
      //是否必填
      if ('1' == widget.templateOneFieldList!.isMust) {
        _controller.isRequire.value = true;
      } else {
        _controller.isRequire.value = false;
      }
      //是否摘要
      if ('1' == widget.templateOneFieldList!.isAbstract) {
        _controller.isRequireAbstract.value = true;
      } else {
        _controller.isRequireAbstract.value = false;
      }
      //提示文字
      _controller.tipsText.value = widget.templateOneFieldList!.promptText ?? '';

      //判断种类 处理不同的内容
      switch (widget.templateOneFieldList!.fieldType) {
        case 'text':
          _controller.indexTag.value = 0;
          if ('1' == widget.templateOneFieldList!.formatType) {
            _controller.tagTextStyle.value = '单行';
          } else {
            _controller.tagTextStyle.value = '多行';
          }
          _controller.tagText.value = '文本';
          break;
        case 'number':
          _controller.indexTag.value = 1;
          _controller.tagUnit.value = widget.templateOneFieldList!.formatType!;
          _controller.tagText.value = '数值';
          _controller.unit.value = widget.templateOneFieldList!.unitName ?? '';
          break;
        case 'date':
          _controller.indexTag.value = 2;
          if ('1' == widget.templateOneFieldList!.formatType) {
            _controller.tagDate.value = '年-月-日';
          } else {
            _controller.tagDate.value = '年-月-日 时:分';
          }
          _controller.tagText.value = '日期';
          break;
        case 'single':
          _controller.indexTag.value = 3;
          _controller.singSelectStr.value = widget.templateOneFieldList!.option_list;
          _controller.tagText.value = '单选';
          break;
        case 'picture':
          _controller.indexTag.value = 4;
          _controller.tagText.value = '图片';
          _controller.isRequireCamera.value = widget.templateOneFieldList!.isMustCameraShoot == '1';
          break;
        case 'attachment':
          _controller.indexTag.value = 5;
          _controller.tagText.value = '附件';
          break;
        case 'table':
          _controller.indexTag.value = 6;
          _controller.tagText.value = '表格';
          break;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '设置字段',
      ),
      body: ListView(
        children: [
          Gaps.line,
          Obx(() => BrnTextInputFormItem(
                title: "字段名称",
                hint: "请输入",
                isRequire: true,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(10), // 限制输入长度为20个字符
                ],
                controller: TextEditingController()..text = _controller.customName.value,
                onChanged: (newValue) {
                  _controller.customName.value = newValue;
                },
              )),
          Gaps.line,
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(padding: EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                Padding(padding: EdgeInsets.only(top: 10, left: 2, right: 20), child: CommonUtils.getSimpleText("类型", 14, Colours.base_primary_text_title)),
                Expanded(
                    child: Container(
                  margin: const EdgeInsets.only(left: 0, right: 0),
                  child: SelectTabWidget(
                    _controller.settingTagWights.value,
                    multiSelect: false,
                    crossAxisCount: 3,
                    hideMore: false,
                    paddingBottom: 0,
                    paddingTop: 10,
                    tabFontSize: 14,
                    defaultSelectedIndex: [_controller.indexTag.value],
                    lastIsAddOne: false,
                    selectedColor: Colours.base_primary,
                    bgSelectedColor: Colours.base_primary_select,
                    bgUnSelectedColor: Colours.base_primary_un_select,
                    childAspectRatio: 8 / 3,
                    itemClickCallback: (List<int> indexs) {
                      LogUtil.e("indexs = $indexs");
                      _controller.tagText.value = _controller.settingTagWights[indexs[0]].name!;
                    },
                  ),
                )),
              ],
            ),
          ),
          Gaps.line,
          Obx(() => BrnSwitchFormItem(
                value: _controller.isRequire.value,
                title: '是否必填',
                onChanged: (oldValue, newValue) {
                  _controller.isRequire.value = newValue;
                },
              )),
          Gaps.line,
          Obx(() => Visibility(
                visible: (widget.show_abstract && (_controller.tagText.value == '文本' || _controller.tagText.value == '数值' || _controller.tagText.value == '日期')),
                child: BrnSwitchFormItem(
                  value: _controller.isRequireAbstract.value,
                  title: '是否摘要',
                  onChanged: (oldValue, newValue) {
                    _controller.isRequireAbstract.value = newValue;
                  },
                ),
              )),
          Gaps.line,
          Obx(() => Visibility(
              visible: _controller.tagText.value != '表格',
              child: BrnTextInputFormItem(
                title: "提示文字",
                hint: "请输入",
                inputFormatters: [
                  LengthLimitingTextInputFormatter(20), // 限制输入长度为20个字符
                ],
                controller: TextEditingController()..text = _controller.tipsText.value,
                onChanged: (newValue) {
                  _controller.tipsText.value = newValue;
                },
              ))),
          Obx(() => Visibility(
                visible: (_controller.tagText.value == '图片'),
                child: BrnSwitchFormItem(
                  value: _controller.isRequireCamera.value,
                  title: '必须相机拍摄',
                  onChanged: (oldValue, newValue) {
                    _controller.isRequireCamera.value = newValue;
                  },
                ),
              )),
          Gaps.line,
          Obx(() => Visibility(
                visible: _controller.tagText.value == '文本',
                child: BrnRadioInputFormItem(
                  title: "样式",
                  isRequire: true,
                  options: const [
                    "单行",
                    "多行",
                  ],
                  value: _controller.tagTextStyle.value,
                  onChanged: (oldValue, newValue) {
                    _controller.tagTextStyle.value = newValue!;
                  },
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '日期',
                child: BrnRadioInputFormItem(
                  title: "格式",
                  isRequire: true,
                  options: const [
                    "年-月-日",
                    "年-月-日 时:分",
                  ],
                  value: _controller.tagDate.value,
                  onChanged: (oldValue, newValue) {
                    _controller.tagDate.value = newValue!;
                  },
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '数值',
                child: BrnRadioInputFormItem(
                  title: "小数位",
                  isRequire: true,
                  options: const ['0', '1', '2'],
                  value: _controller.tagUnit.value,
                  onChanged: (oldValue, newValue) {
                    _controller.tagUnit.value = newValue!;
                  },
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '数值',
                child: Column(
                  children: [
                    Gaps.line,
                    BrnTextInputFormItem(
                      title: "单位",
                      hint: "请输入",
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(10), // 限制输入长度为20个字符
                      ],
                      controller: TextEditingController()..text = _controller.unit.value,
                      onChanged: (newValue) {
                        _controller.unit.value = newValue;
                      },
                    ),
                  ],
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '单选',
                child: Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  child: CommonUtils.getSimpleText('选项', 14, Colours.base_primary_text_title),
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '单选',
                child: ReorderableListView(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  onReorder: (oldIndex, newIndex) {
                    //更新数据
                    if (oldIndex < newIndex) {
                      newIndex -= 1; // 从上往下拖动时，index会减1
                    }
                    // 更新数据源顺序
                    String movedItem = _controller.singSelectStr.removeAt(oldIndex);
                    _controller.singSelectStr.insert(newIndex, movedItem);
                    _controller.notifySingSelectList();
                  },
                  children: List.generate(_controller.singSelectStr.length, (index) {
                    return Container(
                      key: ValueKey(_controller.singSelectStr[index]), // 每个 item 都需要一个唯一的 key
                      color: Colors.white,
                      padding: EdgeInsets.only(left: 20, right: 10, top: 10, bottom: 10),
                      child: Row(
                        children: [
                          GestureDetector(
                            onLongPress: () {},
                            child: InkWell(
                              child: const LoadAssetImage(
                                'common/icon_approve_delete',
                                width: 24,
                                height: 24,
                              ),
                              onTap: () {
                                _controller.singSelectStr.removeAt(index);
                                _controller.notifySingSelectList();
                              },
                            ), // 添加一个可以拖动的图标
                          ),
                          Gaps.hGap4,
                          Expanded(
                            child: InkWell(
                              child: CommonUtils.getSimpleText(_controller.singSelectStr[index], 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                              onTap: () {
                                showAddInputDialog(_controller.singSelectStr[index], (value) {
                                  _controller.singSelectStr[index] = value;
                                  _controller.notifySingSelectList();
                                });
                              },
                            ),
                          ),
                          const LoadAssetImage(
                            'icon_fun',
                            width: 24,
                            height: 24,
                          ),
                        ],
                      ),
                    );
                  }),
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.tagText.value == '单选',
                child: InkWell(
                  child: Container(
                    width: double.infinity,
                    color: Colors.white,
                    padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    child: CommonUtils.getSimpleText('+ 添加选项', 14, Colours.base_primary_blue),
                  ),
                  onTap: () {
                    showAddInputDialog('', (value) {
                      _controller.addTextData(value);
                    });
                  },
                ),
              ))
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '确定',
          onTap: () {
            if (TextUtil.isEmpty(_controller.customName.value)) {
              BrnToast.show('请输入字段名称', context);
              return;
            }
            TemplateOneFieldList filed = TemplateOneFieldList();

            String type = _controller.TYPE_TEXT;
            switch (_controller.tagText.value) {
              case '文本':
                type = _controller.TYPE_TEXT;
                filed.formatType = (_controller.tagTextStyle.value == '单行') ? '1' : '2'; //格式类型 数字类型
                break;
              case '数值':
                type = _controller.TYPE_NUMBER;
                filed.formatType = _controller.tagUnit.value; //格式类型 数字类型
                break;
              case '日期':
                type = _controller.TYPE_DATE;
                filed.formatType = (_controller.tagDate.value == '年-月-日') ? '1' : '2'; //格式类型 数字类型
                break;
              case '单选':
                type = _controller.TYPE_OPTION;
                filed.formatType = '';
                filed.option_list = _controller.singSelectStr.value;
                break;
              case '图片':
                type = _controller.TYPE_PICTURE;
                filed.formatType = '';
                break;
              case '附件':
                type = _controller.TYPE_ATTACHMENT;
                filed.formatType = '';
                break;
              case '表格':
                type = _controller.TYPE_TABLE;
                filed.formatType = '';
                break;
            }
            // BoostNavigator.instance.pop(CustomComponentData(type: type, name: _controller.customName.value));

            filed.fieldName = _controller.customName.value; //名字
            filed.fieldType = type; //类型
            filed.isMust = _controller.isRequire.value ? '1' : '2'; //是否必填
            filed.isAbstract = _controller.isRequireAbstract.value ? '1' : '2'; //是否摘要
            filed.promptText = _controller.tipsText.value; //提示文字
            filed.isMustCameraShoot = _controller.isRequireCamera.value ? '1' : '2'; //是否相机
            filed.fieldFormName = '';
            filed.unitName = _controller.unit.value;

            BoostNavigator.instance.pop(filed);
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CustomComponentPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  void showAddInputDialog(String text, Function(String) onConfirm) {
    BrnMiddleInputDialog(
        title: '添加单选选项',
        hintText: '请输入单选选项内容',
        cancelText: '取消',
        confirmText: '确定',
        maxLength: 1000,
        maxLines: 2,
        inputFormatters: [
          // 禁止输入空格
          FilteringTextInputFormatter.deny(RegExp(r'\s')),
        ],
        barrierDismissible: false,
        inputEditingController: TextEditingController()..text = text ?? '',
        textInputAction: TextInputAction.done,
        onConfirm: (value) {
          if (TextUtil.isEmpty(value)) {
            BrnToast.show('不支持输入空的内容作为选项', context);
            Navigator.of(context, rootNavigator: true).pop();
            return;
          }
          onConfirm(value);
          Navigator.of(context, rootNavigator: true).pop();
        },
        onCancel: () {
          Navigator.of(context, rootNavigator: true).pop();
        }).show(context);
  }
}
