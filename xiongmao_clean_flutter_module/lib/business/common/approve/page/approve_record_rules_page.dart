import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_record_filter_data.dart';
import '../controller/approve_record_controller.dart';
import '../controller/approve_record_rules_controller.dart';
import '../item/approve_record_listview.dart';
import '../iview/approve_record_iview.dart';
import '../presenter/approve_record_persenter.dart';
import '../presenter/approve_record_rules_persenter.dart';

/// 审批记录规则
class ApproveRecordRulesPage extends StatefulWidget {
  ApproveRecordFilterData? data;

  ApproveRecordRulesPage({super.key, this.data});

  @override
  _ApproveRecordRulesPageState createState() => _ApproveRecordRulesPageState();
}

class _ApproveRecordRulesPageState extends State<ApproveRecordRulesPage> with BasePageMixin<ApproveRecordRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ApproveRecordRulesPage> implements ApproveRecordIView {
  ApproveRecordRulesPresenter? _presenter;

  final ApproveRecordRulesController _controller = ApproveRecordRulesController();

  @override
  void initState() {
    super.initState();
    if (widget.data != null) {
      //这里反选现在的参数
      _controller.invertData(widget.data!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '审批记录筛选',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Column(
            children: [
              Container(
                margin: EdgeInsets.only(top: 10),
                color: Colors.white,
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText('入职日期', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    SelectTabWidget(
                      key: ValueKey(_controller.entryIndex.value),
                      // 使用 entryIndex 作为 key
                      _controller.entryDateList.value,
                      multiSelect: false,
                      crossAxisCount: 4,
                      hideMore: false,
                      paddingBottom: 0,
                      paddingTop: 6,
                      tabFontSize: 15,
                      defaultSelectedIndex: (_controller.entryIndex.value == -1) ? [] : [_controller.entryIndex.value],
                      lastIsAddOne: false,
                      selectedColor: Colours.base_primary,
                      bgSelectedColor: Colours.base_primary_select,
                      bgUnSelectedColor: Colours.base_primary_un_select,
                      childAspectRatio: 8 / 4,
                      itemClickCallback: (List<int> indexs) {
                        LogUtil.e("indexs = $indexs");
                        if (_controller.entryIndex.value == (indexs[0]).toInt()) {
                          _controller.entryIndex.value = -1;
                        } else {
                          _controller.entryIndex.value = (indexs[0]).toInt();
                        }
                        _controller.customEntryDate.value = '自定义日期范围';
                      },
                    ),
                    Gaps.vGap10,
                    InkWell(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 6),
                        decoration: BoxDecoration(
                          color: (_controller.customEntryDate.value == '自定义日期范围') ? Colours.base_primary_un_select : Colours.base_primary_select,
                          border: (_controller.customEntryDate.value == '自定义日期范围') ? Border.all(color: Colours.transparent, width: 0) : Border.all(color: Colours.base_primary, width: 1),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: CommonUtils.getSimpleText(_controller.customEntryDate.value, 15, (_controller.customEntryDate.value == '自定义日期范围') ? Colours.base_primary_text_title : Colours.base_primary, textAlign: TextAlign.center),
                      ),
                      onTap: () {
                        if (_controller.customEntryDate.value != '自定义日期范围') {
                          _controller.customEntryDate.value = '自定义日期范围';
                        } else {
                          String format = 'yyyy年-MM月-dd日';
                          BrnPickerTitleConfig pickerTitleConfig = const BrnPickerTitleConfig(titleContent: "选择自定义时间范围");
                          BrnDateRangePicker.showDatePicker(
                            context,
                            isDismissible: false,
                            minDateTime: DateTime(2020, 01, 01, 00, 00, 00),
                            maxDateTime: DateTime(2029, 12, 30, 23, 59, 59),
                            pickerMode: BrnDateTimeRangePickerMode.date,
                            minuteDivider: 10,
                            pickerTitleConfig: pickerTitleConfig,
                            dateFormat: format,
                            initialStartDateTime: _controller.customEntryStartTime,
                            initialEndDateTime: _controller.customEntryEndTime,
                            onConfirm: (startDateTime, endDateTime, startlist, endlist) {
                              _controller.customEntryStartTime = startDateTime;
                              _controller.customEntryEndTime = endDateTime;
                              _controller.customEntryStartDate.value = '${startDateTime.year}.${CommonUtils.formatToTwoDigits(startDateTime.month)}.${CommonUtils.formatToTwoDigits(startDateTime.day)}';
                              _controller.customEntryEndDate.value = '${endDateTime.year}.${CommonUtils.formatToTwoDigits(endDateTime.month)}.${CommonUtils.formatToTwoDigits(endDateTime.day)}';
                              _controller.customEntryDate.value = '${_controller.customEntryStartDate.value} ~ ${_controller.customEntryEndDate.value}';
                              _controller.entryIndex.value = -1;
                            },
                            onClose: () {
                              print("onClose");
                            },
                            onCancel: () {
                              print("onCancel");
                            },
                            onChange: (startDateTime, endDateTime, startlist, endlist) {},
                          );
                        }
                      },
                    )
                  ],
                ),
              ),

              Container(
                margin: EdgeInsets.only(top: 10),
                color: Colors.white,
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText('审批状态', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    SelectTabWidget(
                      key: ValueKey(_controller.statusIndex.value),
                      _controller.roleList.value,
                      multiSelect: false,
                      crossAxisCount: 4,
                      hideMore: false,
                      paddingBottom: 0,
                      paddingTop: 6,
                      tabFontSize: 15,
                      defaultSelectedIndex: _controller.statusIndex.value == -1 ? [] : [_controller.statusIndex.value],
                      lastIsAddOne: false,
                      selectedColor: Colours.base_primary,
                      bgSelectedColor: Colours.base_primary_select,
                      bgUnSelectedColor: Colours.base_primary_un_select,
                      childAspectRatio: 8 / 4,
                      itemClickCallback: (List<int> indexs) {
                        LogUtil.e("indexs = $indexs");
                        if (_controller.statusIndex.value == (indexs[0]).toInt()) {
                          _controller.statusIndex.value = -1;
                        } else {
                          _controller.statusIndex.value = (indexs[0]).toInt();
                        }
                      },
                    )
                  ],
                ),
              ),

              ///考勤规则
              Container(
                margin: EdgeInsets.only(top: 10),
                color: Colors.white,
                width: double.infinity,
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText('提交人', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    Gaps.vGap10,
                    InkWell(
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText(_controller.commitName.value, 15, _controller.commitName.value == '请选择' ? Colours.base_primary_text_caption : Colours.base_primary_text_title),
                          LoadImage(
                            'base/icon_base_gray_arrow',
                            height: 20,
                            width: 20,
                          ),
                        ],
                      ),
                      onTap: () {
                        BoostNavigator.instance.push('selectPersonnelPage', arguments: {'title': '选择提交人', 'status': '1', 'uuids': _controller.commitUuid.value}).then((value) {
                          if (value != null) {
                            if (value is String) {
                              _controller.commitName.value = '不限制';
                              _controller.commitName.value = '';
                              _controller.commitUuid.value = '';
                            } else if (value is List<ProjectArchivesList>) {
                              List<ProjectArchivesList> data = value;
                              if (data.isNotEmpty) {
                                _controller.commitUuid.value = data[0].uuid ?? '';
                                _controller.commitName.value = data[0].userName ?? '';
                              }
                            }
                          }
                        });
                      },
                    )
                  ],
                ),
              ),
            ],
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 60,
        child: Column(
          children: [
            Gaps.line,
            Gaps.vGap10,
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: Row(
                children: [
                  InkWell(
                    child: Container(
                      padding: const EdgeInsets.only(left: 30, right: 30, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('重置', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      var filterData = ApproveRecordFilterData();
                      filterData.isReset = true;
                      BoostNavigator.instance.pop(filterData);
                    },
                  ),
                  Gaps.hGap10,
                  Expanded(
                      child: InkWell(
                    child: Container(
                      padding: EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.base_primary,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('确定', 16, Colours.white, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      ///传递给原生的参数
                      var filterData = ApproveRecordFilterData();
                      //入职日期
                      filterData.startDate = getDateSelect((_controller.entryIndex.value == -1) ? '' : '${_controller.entryDateList[_controller.entryIndex.value].name}', _controller.customEntryDate.value.replaceAll("~", "#"));
                      //提交人
                      filterData.commitUuid = _controller.commitUuid.value;
                      filterData.commitName = _controller.commitName.value;
                      //审批状态
                      filterData.picStatus = _controller.statusIndex.value == -1 ? '' : '${_controller.statusIndex.value}';
                      BoostNavigator.instance.pop(filterData);
                    },
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getDateSelect(String date, String customDate) {
    String mSelectDate = "";

    if (date.isNotEmpty) {
      switch (date) {
        case "上周":
          mSelectDate = "lastWeek";
          break;
        case "本周":
          mSelectDate = "thisWeek";
          break;
        case "本月":
          mSelectDate = "thisMonth";
          break;
        case "上月":
          mSelectDate = "lastMonth";
          break;
      }
    } else if (customDate != "自定义日期范围") {
      mSelectDate = customDate;
    }

    return mSelectDate;
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveRecordRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;
}
