import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 身份证验证
 */
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_setting_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_multiple_item_view.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../generated/get_role_all_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_brn_radio_button.dart';
import '../iview/approve_setting_main_iview.dart';
import '../iview/approve_user_item_view.dart';
import '../presenter/approve_setting_main_presenter.dart';

/**
 * 设置入职审批的内容
 */
class AppSettingsMainPage extends StatefulWidget {
  final ApproveDetailList? item;
  String? style;

  AppSettingsMainPage({Key? key, this.item, this.style}) : super(key: key);

  @override
  _AppSettingsMainPageState createState() => _AppSettingsMainPageState();
}

class _AppSettingsMainPageState extends State<AppSettingsMainPage> with BasePageMixin<AppSettingsMainPage, PowerPresenter<dynamic>> implements ApproveSettingMainPageView {
  int _nodeTypeSelectedIndex = 0;
  int _nodeOwnerSelectedIndex = 0;
  int _countersignSelectedIndex = 0;
  late ApproveSettingMainPresenter _presenter;
  ApproveSettingPageController _controller = ApproveSettingPageController();
  ApproveDetailList? entity;
  String title = '设置审批节点';

  // List<String> _designatedSuperior = ["直属上级", "第二级上级", "第三级上级", "第四级上级", "第️五级上级", "第六级上级", "第七级上级", "第八级上级", "第九级上级", "第十级上级"];
  @override
  void initState() {
    super.initState();
    _presenter.getRoleAll();
    _presenter.getMetaDta();
    entity = widget.item;

    if (entity != null) {
      //	"node_type": "1",       //节点类型 1审批节点 2抄送节点
      // 	"approver_type": "1",   //审批人类型 1系统(自动通过) 2指定成员 3指定上级 4指定角色
      // 	"is_cosigned": "1",     //是否会签1是0否
      // 	"superiors_level": "1", //级别数
      // 	"role_id": "1",   //角色id
      // 	"user_list": ["1234", "123456"]   //用户uuid列表
      _nodeTypeSelectedIndex = ("1" == entity?.nodeType ? 0 : 1);
      _countersignSelectedIndex = ("1" == entity?.isCosigned ? 0 : 1);
      switch (entity?.approverType) {
        case "1":
          _nodeOwnerSelectedIndex = 3;
          break;
        case "2":
          _nodeOwnerSelectedIndex = 0;
          break;
        case "3":
          _nodeOwnerSelectedIndex = 1;
          break;
        case "4":
          _nodeOwnerSelectedIndex = 2;
          break;
      }
    } else {
      _nodeTypeSelectedIndex = int.parse(widget.style!);
      entity = ApproveDetailList();
    }

    title = (_nodeTypeSelectedIndex == 0) ? '设置审批节点' : '设置抄送节点';
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: title,
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ///节点类型
            // Container(
            //   margin: EdgeInsets.only(top: 12),
            //   padding: EdgeInsets.only(top: 10, right: 10, left: 10, bottom: 10),
            //   color: Colors.white,
            //   child: Row(
            //     children: <Widget>[
            //       Container(
            //         child: Row(
            //           children: [
            //             Text(
            //               "*",
            //               style: TextStyle(
            //                 fontWeight: FontWeight.bold,
            //                 color: Colors.red,
            //               ),
            //             ),
            //             Text("节点类型"),
            //           ],
            //         ),
            //         width: 90,
            //       ),
            //       Row(
            //         children: [
            //           MyBrnRadioButton(
            //             radioIndex: 0,
            //             isSelected: _nodeTypeSelectedIndex == 0,
            //             child: Text(
            //               "审批",
            //             ),
            //             onValueChangedAtIndex: (index, value) {
            //               LogUtil.e("object---index-----" + index.toString());
            //               setState(() {
            //                 _nodeTypeSelectedIndex = index;
            //               });
            //             },
            //           ),
            //           SizedBox(
            //             width: 20,
            //           ),
            //           MyBrnRadioButton(
            //             radioIndex: 1,
            //             isSelected: _nodeTypeSelectedIndex == 1,
            //             child: Text(
            //               "抄送",
            //             ),
            //             onValueChangedAtIndex: (index, value) {
            //               setState(() {
            //                 _nodeTypeSelectedIndex = index;
            //               });
            //             },
            //           ),
            //         ],
            //       ),
            //     ],
            //   ),
            // ),
            Gaps.lineLeftMargin,

            ///审批人
            Container(
              padding: EdgeInsets.only(top: 10, right: 10, left: 10, bottom: 10),
              color: Colors.white,
              child: Row(
                children: <Widget>[
                  Container(
                    child: Row(
                      children: [
                        Text(
                          "*",
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.red,
                          ),
                        ),
                        Text("审批人"),
                      ],
                    ),
                    width: 90,
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          MyBrnRadioButton(
                            radioIndex: 0,
                            isSelected: _nodeOwnerSelectedIndex == 0,
                            child: Text(
                              "指定成员",
                            ),
                            onValueChangedAtIndex: (index, value) {
                              setState(() {
                                _nodeOwnerSelectedIndex = index;
                              });
                            },
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          MyBrnRadioButton(
                            radioIndex: 1,
                            isSelected: _nodeOwnerSelectedIndex == 1,
                            child: Text(
                              "指定上级",
                            ),
                            onValueChangedAtIndex: (index, value) {
                              setState(() {
                                _nodeOwnerSelectedIndex = index;
                              });
                            },
                          ),
                        ],
                      ),
                      Row(
                        children: [
                          MyBrnRadioButton(
                            radioIndex: 2,
                            isSelected: _nodeOwnerSelectedIndex == 2,
                            child: Text(
                              "指定角色",
                            ),
                            onValueChangedAtIndex: (index, value) {
                              setState(() {
                                _nodeOwnerSelectedIndex = index;
                              });
                            },
                          ),
                          SizedBox(
                            width: 10,
                          ),
                          // Visibility(
                          //   child: MyBrnRadioButton(
                          //     radioIndex: 3,
                          //     isSelected: _nodeOwnerSelectedIndex == 3,
                          //     child: Text(
                          //       "系统(自动通过)",
                          //     ),
                          //     onValueChangedAtIndex: (index, value) {
                          //       setState(() {
                          //         _nodeOwnerSelectedIndex = index;
                          //       });
                          //     },
                          //   ),
                          //   visible: _nodeTypeSelectedIndex == 0,
                          // ), //审批才需要系统自动通过
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),

            ///以下内容，均根据上方选择来筛选内容
            Visibility(
                visible: (_nodeOwnerSelectedIndex != 3),
                child: Padding(
                  padding: EdgeInsets.only(
                    left: 12,
                    top: 10,
                    bottom: 10,
                  ),
                  child: Text(
                    '注：未匹配到成员时会该环节会自动审批通过',
                    style: TextStyle(
                      color: Colours.dark_text_gray,
                      fontSize: 14,
                    ),
                  ),
                )),

            ///这里就是选择的时候，动态显示了
            Visibility(
                visible: (_nodeOwnerSelectedIndex != 3),
                child: Container(
                  padding: const EdgeInsets.only(
                    left: 12,
                    right: 12,
                    top: 10,
                    // bottom: 10,
                  ),
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text((_nodeOwnerSelectedIndex == 2) ? "指定角色" : "选择成员",
                              style: TextStyle(
                                color: Colours.base_primary_text_title,
                                fontSize: 14,
                              )),
                          Spacer(),
                          Visibility(
                              visible: (_nodeOwnerSelectedIndex == 1),
                              child: InkWell(
                                child: Row(
                                  children: [
                                    LoadAssetImage(
                                      "icon_sample",
                                      width: 16,
                                      height: 16,
                                    ),
                                    Text("示例",
                                        style: TextStyle(
                                          color: Colours.base_primary,
                                          fontSize: 14,
                                        ))
                                  ],
                                ),
                                onTap: () {
                                  showDialog(
                                    context: context,
                                    builder: (BuildContext context) {
                                      return Dialog(
                                        backgroundColor: Colours.transparent,
                                        child: Stack(
                                          alignment: Alignment.center, // 使内容居中
                                          children: [
                                            // 图片
                                            InkWell(
                                              child: LoadAssetImage(
                                                "icon_check_sample",
                                                width: 294,
                                                height: 343,
                                              ),
                                              onTap: () {
                                                Get.back();
                                              },
                                            ),
                                          ],
                                        ),
                                      );
                                    },
                                  );
                                },
                              )),
                        ],
                      ),
                      Visibility(
                          visible: (_nodeOwnerSelectedIndex == 0 || _nodeOwnerSelectedIndex == 1),
                          child: Text((_nodeOwnerSelectedIndex == 0) ? "至少选择1位" : "以申请人的直属上级为第一级，向更高层级递",
                              style: TextStyle(
                                color: Colours.base_primary_text_hint,
                                fontSize: 12,
                              ))),
                      Gaps.vGap12,
                      Gaps.line,
                      Visibility(
                          visible: (_nodeOwnerSelectedIndex == 0),
                          child: AppRoveUserItemView(_nodeTypeSelectedIndex, entity?.userList ?? [], (list) {
                            entity?.userList = list;
                          })),
                      Visibility(
                          visible: (_nodeOwnerSelectedIndex == 1 || (_nodeOwnerSelectedIndex == 2)),
                          child: Obx(() {
                            return AppRoveMultipleItemView(
                              selectContent: (_nodeOwnerSelectedIndex == 1) ? entity?.superiorsLevelName ?? "" : entity?.roleName ?? "",
                              items: (_nodeOwnerSelectedIndex == 1) ? _controller.superiorsLevelListNameList.value : _controller.roleNameList.value,
                              isSingleSelection: true, // 设置为 true 表示单选，设置为 false 表示多选
                              onItemSelected: (selectedItem, selectedItems, selectedIndexItems) {
                                print('Current Selected Item: $selectedItem');
                                print('All Selected Items: $selectedItems');

                                //	"node_type": "1",   //节点类型 1审批节点 2抄送节点
                                // 	"approver_type": "1",   //审批人类型 1系统(自动通过) 2指定成员 3指定上级 4指定角色
                                // 	"is_cosigned": "1",     //是否会签1是0否
                                // 	"superiors_level": "1",   //级别数
                                // 	"role_id": "1",   //角色id
                                // 	"user_list": ["1234", "123456"]   用户uuid列表
                                if (selectedIndexItems.isNotEmpty) {
                                  if (_nodeOwnerSelectedIndex == 1) {
                                    entity?.superiorsLevel = _controller.superiorsLevelList.value[selectedIndexItems[0]].id ?? "";
                                    entity?.superiorsLevelName = _controller.superiorsLevelList.value[selectedIndexItems[0]].name ?? "";
                                  } else {
                                    entity?.roleId = _controller.roleList.value[selectedIndexItems[0]].id ?? "";
                                    entity?.roleName = _controller.roleList.value[selectedIndexItems[0]].name ?? "";
                                  }
                                } else {
                                  if (_nodeOwnerSelectedIndex == 1) {
                                    entity?.superiorsLevel = "";
                                    entity?.superiorsLevelName = "";
                                  } else {
                                    entity?.roleId = "";
                                    entity?.roleName = "";
                                  }
                                }
                              },
                            );
                          })),
                    ],
                  ),
                )),

            ///多人审批方式
            Visibility(
                visible: (_nodeTypeSelectedIndex == 0 && _nodeOwnerSelectedIndex != 3), //抄送的不需要多人审批方式
                child: Container(
                  margin: EdgeInsets.only(top: 10),
                  padding: EdgeInsets.only(top: 10, right: 10, left: 10, bottom: 10),
                  color: Colors.white,
                  child: Row(
                    children: <Widget>[
                      Row(
                        children: [
                          Text(
                            "*",
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.red,
                            ),
                          ),
                          Text("多人审批方式  "),
                        ],
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start, // 列中的内容左对齐
                        children: [
                          Align(
                            alignment: Alignment.centerLeft,
                            child: MyBrnRadioButton(
                              radioIndex: 0,
                              isSelected: _countersignSelectedIndex == 0,
                              child: Text(
                                "需所有成员同意(会签)",
                              ),
                              onValueChangedAtIndex: (index, value) {
                                setState(() {
                                  _countersignSelectedIndex = index;
                                });
                              },
                            ),
                          ),
                          SizedBox(
                            width: 20,
                          ),
                          MyBrnRadioButton(
                            radioIndex: 1,
                            isSelected: _countersignSelectedIndex == 1,
                            child: Text(
                              "一名成员同意即可(或签)",
                            ),
                            onValueChangedAtIndex: (index, value) {
                              setState(() {
                                _countersignSelectedIndex = index;
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
        child: BrnBigMainButton(
          bgColor: Colours.base_primary_green,
          title: '完成',
          onTap: () {
            //	"node_type": "1",   //节点类型 1审批节点 2抄送节点
            // 	"approver_type": "1",   //审批人类型 1系统(自动通过) 2指定成员 3指定上级 4指定角色
            // 	"is_cosigned": "1",     //是否会签1是0否
            // 	"superiors_level": "1",   //级别数
            // 	"role_id": "1",   //角色id
            // 	"user_list": ["1234", "123456"]   用户uuid列表
            HashMap<String, Object> params = HashMap();
            params["node_type"] = (_nodeTypeSelectedIndex == 0) ? "1" : "2";
            entity?.nodeType = (_nodeTypeSelectedIndex == 0) ? "1" : "2";
            switch (_nodeOwnerSelectedIndex) {
              case 0: //指定成员
                // params["approver_type"] = "2";
                entity?.approverType = "2";
                if (entity?.userList?.isEmpty ?? true) {
                  Toast.show("至少选择一位成员");
                  return;
                }
                if ((entity?.userList?.length ?? 0) > 10) {
                  Toast.show("指定成员最多选10个");
                  return;
                }
                entity?.isCosigned = _countersignSelectedIndex == 0 ? "1" : "0";
                // params["user_list"] = userList;
                // params["is_cosigned"] = _countersignSelectedIndex==0?"1":"0";
                break;
              case 1: //指定上级
                // params["approver_type"] = "3";
                entity?.approverType = "3";
                if (entity?.superiorsLevel == "") {
                  Toast.show("请选择上级");
                  return;
                }
                entity?.isCosigned = _countersignSelectedIndex == 0 ? "1" : "0";
                // params["superiors_level"] = superiors_level_id;
                // params["is_cosigned"] = _countersignSelectedIndex==0?"1":"0";
                break;
              case 2: //指定角色
                // params["approver_type"] = "4";
                entity?.approverType = "4";
                if (entity?.roleId == "") {
                  Toast.show("请选择角色");
                  return;
                }
                entity?.isCosigned = _countersignSelectedIndex == 0 ? "1" : "0";
                // params["role_id"] = role_id;
                // params["is_cosigned"] = _countersignSelectedIndex==0?"1":"0";
                break;
              case 3: //系统自动通过
                // params["approver_type"] = "1";
                entity?.approverType = "1";
                break;
            }
            LogUtil.e("object----111--" + (entity?.toJson().toString() ?? ""));
            BoostNavigator.instance.pop(entity);

            // HashMap<String,String> finalParams = HashMap();
            // if(item!=null){
            //   params["uuid"] = item.uuid;
            // }
            // finalParams["node_data"] = json.encode([detailList]);
            // _presenter.saveTemplate(finalParams);
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveSettingMainPresenter();
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void getRoleAll(GetRoleAllEntity? data) {
    _controller.roleList.value = data?.list ?? [];
    List<GetRoleAllList> list = data?.list ?? [];
    LogUtil.e("object---getRoleAll----" + list.toString());
    // 查找对应 id 的 name
    entity?.roleName = list
        .firstWhere(
          (item) => item.id == entity?.roleId,
          orElse: () => GetRoleAllList(),
        )
        .name;
    _controller.updateRoleNameList(list.map((e) => (e.name ?? "")).toList());
  }

  @override
  void getMetaDta(GetMetaEntity? data) {
    SpUtil.putString(Constant.META_DATA, json.encode(data));
    _controller.superiorsLevelList.value = data?.superiorsLevelList ?? [];
    List<SuperiorsLevelInfo> list = data?.superiorsLevelList ?? [];

    // 查找对应 id 的 name
    entity?.superiorsLevelName = list
        .firstWhere(
          (item) => item.id == entity?.superiorsLevel, // 默认值
          orElse: () => SuperiorsLevelInfo(),
        )
        .name;
    _controller.updateSuperiorsLevelListNameList(list.map((e) => (e.name ?? "")).toList());
  }
}
