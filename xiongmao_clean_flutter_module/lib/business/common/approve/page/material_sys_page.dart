import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../bean/material_cat_child_entity.dart';
import '../controller/material_controller.dart';
import '../iview/approve_main_iview.dart';
import '../iview/material_iview.dart';
import '../presenter/material_presenter.dart';
import '../wights/NumberInputWithButtons.dart';

/**
 * 物料列表
 */
class MaterialSysPage extends StatefulWidget {
  String? projectUuid = "";
  String? projectName = "";
  List<MaterialCatChildList> selected = [];

  MaterialSysPage({super.key, required this.projectUuid, required this.projectName, required this.selected});

  @override
  _MaterialSysPageState createState() => _MaterialSysPageState();
}

class _MaterialSysPageState extends State<MaterialSysPage> with BasePageMixin<MaterialSysPage, PowerPresenter<dynamic>> implements MaterialView {
  final MaterialController _controller = MaterialController();

  late MaterialPresenter _presenter;

  @override
  void initState() {
    super.initState();
    //判断selected 是否有值
    if (widget.selected.isNotEmpty) {
      // 创建一个新的 Map 来存储 number 和对应的 MaterialCatChildList
      final Map<String, MaterialCatChildList> map = {};

      for (MaterialCatChildList childList in widget.selected) {
        if (childList.number != null) {
          // 使用 childList.number 作为键，并将整个 childList 作为值
          map[childList.number!] = childList;
        }
      }

      // 将构建好的 Map 赋值给 _controller.selectedCounts.value
      _controller.selectedCounts.value = map;
    }

    _controller.project_uuid.value = widget.projectUuid ?? httpConfig.project_uuid;
    //先请求物料分类
    _presenter.getCatList();
  }

  Future<dynamic> _onRefresh() async {
    _presenter.onRefresh(_controller.cat_id.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter.loadMore(_controller.cat_id.value);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '选择物料',
        centerSubTitle: widget.projectName ?? '',
        actionWidget: Padding(
          padding: const EdgeInsets.only(right: 16),
          child: CommonUtils.getSimpleText('确定', 16, Colours.base_primary),
        ),
        onPressed: () {
          processAndMergeLists();
        },
      ),
      body: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
              child: Container(
            color: Colours.drawer_bg,
            child: Obx(() => ListView.builder(
                  padding: const EdgeInsets.all(0),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: false,
                  itemCount: _controller.catList.value.length,
                  itemBuilder: (context, index) {
                    return GestureDetector(
                      onTap: () {
                        setState(() {
                          _controller.cat_id.value = _controller.catList[index].id ?? '';
                          // 更新选中的索引，并触发UI更新
                          _presenter.onRefresh(_controller.catList[index].id ?? '');
                        });
                      },
                      child: Container(
                        padding: EdgeInsets.only(top: 4),
                        color: (_controller.cat_id.value == _controller.catList[index].id) ? Colors.white : Colors.transparent,
                        child: Column(
                          children: [
                            Row(
                              children: [
                                // 使用 Visibility 来控制三角形的可见性，同时保持其占用的空间
                                SizedBox(
                                  width: 24, // 给定宽度以保证有足够的空间放置图标
                                  height: 24, // 给定高度以保证有足够的空间放置图标
                                  child: Visibility(
                                    visible: _controller.cat_id.value == _controller.catList[index].id,
                                    maintainSize: true,
                                    // 即使不可见也维持大小
                                    maintainAnimation: true,
                                    maintainState: true,
                                    child: const Icon(Icons.arrow_right, size: 24, color: Colours.base_primary), // 三角形图标
                                  ),
                                ),

                                Expanded(
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CommonUtils.getSimpleText(_controller.catList[index].catName, 14, (_controller.cat_id.value == _controller.catList[index].id) ? Colours.base_primary : Colours.base_primary_text_title),
                                      CommonUtils.getSimpleText('已选${_controller.catList[index].count}个', 14, (_controller.cat_id.value == _controller.catList[index].id) ? Colours.base_primary : Colours.base_primary_text_caption),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                            Gaps.vGap4,
                            Gaps.line,
                          ],
                        ),
                      ),
                    );
                  },
                )),
          )),
          Expanded(
              flex: 2,
              child: Column(
                children: [
                  Container(
                    child: BrnSearchText(
                      innerPadding: EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                      maxHeight: 54,
                      textStyle: const TextStyle(
                        fontSize: 14,
                      ),
                      hintStyle: const TextStyle(fontSize: 14),
                      innerColor: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(10)),
                      normalBorder: Border.all(color: Color(0xFFF0F0F0), width: 1, style: BorderStyle.solid),
                      activeBorder: Border.all(color: Colours.base_primary, width: 1, style: BorderStyle.solid),
                      onTextClear: () {
                        return false;
                      },
                      action: Container(),
                      onActionTap: () {},
                      onTextCommit: (text) {
                        // BrnToast.show('提交内容 : $text', context);
                      },
                      onTextChange: (text) {
                        _controller.search_cat_child_name.value = text;
                        _onRefresh();
                      },
                    ),
                  ),
                  Expanded(
                      child: Obx(() => MyRefreshListView(
                            onRefresh: _onRefresh,
                            loadMore: _loadMore,
                            hasMore: int.parse(_controller.totalChildNumber.value) > _controller.listChild.value.length,
                            shrinkWrap: true,
                            itemCount: _controller.listChild.value.length,
                            itemBuilder: (context, index) {
                              return Column(
                                children: [
                                  Gaps.line,
                                  Container(
                                    padding: const EdgeInsets.only(top: 10, bottom: 10, left: 14, right: 14),
                                    decoration: const BoxDecoration(
                                      color: Colors.white,
                                    ),
                                    child: Row(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        LoadImage(
                                          _controller.listChild.value[index].coverUrl ?? '',
                                          width: 50,
                                          height: 50,
                                        ),
                                        Gaps.hGap4,
                                        Expanded(
                                            child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            CommonUtils.getSimpleText(_controller.listChild.value[index].productName, 14, Colours.base_primary_text_title),
                                            CommonUtils.getSimpleText('${_controller.listChild.value[index].specification}', 14, Colours.base_primary_text_title),
                                            Container(
                                              alignment: Alignment.centerRight,
                                              child: (_controller.listChild.value[index].num == 0)
                                                  ? InkWell(
                                                      child: Icon(Icons.shopping_cart),
                                                      onTap: () {
                                                        _controller.updateChildCount(_controller.listChild.value[index], 1);
                                                      },
                                                    )
                                                  : Center(
                                                      child: NumberInputWithButtons(
                                                        initialValue: _controller.listChild.value[index].num,
                                                        maxValue: 99999,
                                                        minValue: 0,
                                                        onChanged: (int value) {
                                                          // 处理返回的值
                                                          _controller.updateChildCount(_controller.listChild.value[index], value);
                                                        },
                                                      ),
                                                    ),
                                            ),
                                          ],
                                        ))
                                      ],
                                    ),
                                  )
                                ],
                              );
                            },
                          ))),
                ],
              )),
        ],
      ),
    );
  }

  void processAndMergeLists() {
    List<MaterialCatChildList> list = [];
    for (var element in _controller.selectedCounts.values) {
      if (element.num > 0) {
        list.add(element);
      }
    }
    print('传递给一层的数据 ${list.length}');
    BoostNavigator.instance.pop(list);
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = MaterialPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }
}
