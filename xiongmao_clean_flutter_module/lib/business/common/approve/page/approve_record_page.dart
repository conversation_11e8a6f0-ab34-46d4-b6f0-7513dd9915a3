import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/toast_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/approve_record_filter_data.dart';
import '../controller/approve_record_controller.dart';
import '../item/approve_record_listview.dart';
import '../iview/approve_record_iview.dart';
import '../presenter/approve_record_persenter.dart';

/// 审批记录
class ApproveRecordPage extends StatefulWidget {
  String? uuid;

  ApproveRecordPage({super.key, this.uuid});

  @override
  _ApproveRecordPageState createState() => _ApproveRecordPageState();
}

class _ApproveRecordPageState extends State<ApproveRecordPage> with BasePageMixin<ApproveRecordPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ApproveRecordPage> implements ApproveRecordIView {
  ApproveRecordPresenter? _presenter;

  final ApproveRecordController _controller = ApproveRecordController();

  @override
  void initState() {
    super.initState();
    _presenter?.getTemplateListManager();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '审批记录',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => WillPopScope(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                padding: const EdgeInsets.only(left: 6, right: 6, top: 10, bottom: 10),
                child: Column(
                  children: [
                    SelectTabWidget(
                      _controller.listRow.value,
                      multiSelect: false,
                      crossAxisCount: 4,
                      hideMore: false,
                      paddingBottom: 0,
                      paddingTop: 0,
                      tabFontSize: 15,
                      defaultSelectedIndex: [0],
                      lastIsAddOne: false,
                      selectedColor: Colours.base_primary,
                      bgSelectedColor: Colours.base_primary_select,
                      bgUnSelectedColor: Colours.base_primary_un_select,
                      childAspectRatio: 8 / 4,
                      itemClickCallback: (List<int> indexs) {
                        LogUtil.e("indexs = ${indexs[0]}  ");
                        int index = indexs[0];
                        _controller.templateUuid.value = _controller.originalListRow[index].uuid ?? '';
                        _controller.templateName.value = _controller.originalListRow[index].templateName ?? '';
                        _onRefresh();
                      },
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            child: BrnSearchText(
                              innerPadding: const EdgeInsets.only(left: 6, right: 10, top: 10, bottom: 10),
                              maxHeight: 60,
                              innerColor: Colours.base_primary_bg_page,
                              borderRadius: const BorderRadius.all(Radius.circular(4)),
                              autoFocus: false,
                              textStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_title),
                              hintStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_caption),
                              hintText: '请输入关键字查找审批',
                              onActionTap: () {},
                              onTextCommit: (text) {},
                              onTextChange: (text) {
                                _controller.keyword.value = text;
                                _onRefresh();
                              },
                            ),
                          ),
                        ),
                        InkWell(
                          child: LoadImage(
                            (_controller.filterData == null) ? 'base/icon_base_filter' : 'base/icon_base_filter_select',
                            width: 30,
                            height: 30,
                          ),
                          onTap: () {
                            BoostNavigator.instance.push('ApproveRecordRulesPage', arguments: {'filter_data': _controller.filterData?.toJson()}).then((value) {
                              if (value is ApproveRecordFilterData) {
                                if (value.isReset == true) {
                                  _controller.filterData = null;
                                } else {
                                  _controller.filterData = value;
                                }
                                _onRefresh();
                              }
                            });
                          },
                        ),
                        Gaps.hGap20,
                        InkWell(
                          child: LoadImage(
                            'base/icon_base_share',
                            width: 30,
                            height: 30,
                          ),
                          onTap: () {
                            ///获取下载链接
                            _presenter?.requestDownloadApproveRecord();
                          },
                        ),
                        Gaps.hGap10,
                      ],
                    )
                  ],
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                      itemCount: _controller.list.length,
                      onRefresh: _onRefresh,
                      loadMore: _loadMore,
                      padding: const EdgeInsets.only(top: 10),
                      hasMore: _controller.totalNumber.value > _controller.list.length,
                      itemBuilder: (_, index) {
                        return ApproveRecordListView(
                          data: _controller.list[index],
                          onClick: () {
                            ///跳转详情
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                              "method": "goto_todo_one_details",
                              "application_type": _controller.list[index].applicationType ?? '',
                              "application_no": _controller.list[index].applicationNo ?? '',
                              "application_status": _controller.list[index].applicationStatus ?? '',
                              "task_id": _controller.list[index].taskId ?? '',
                              "read_status": _controller.list[index].readStatus ?? '',
                              "type": '1',
                              "show_bottom_button": false,
                            });
                          },
                          onActionClick: () {
                            if (_controller.list[index].applicationStatus == '0') {
                              ///删除
                              DialogManager.showConfirmDialog(
                                context: context,
                                title: '提示',
                                cancel: '取消',
                                confirm: '确定',
                                message: '是否删除该条审批？',
                                onConfirm: () {
                                  _presenter?.requestDelTemplate(_controller.list[index].applicationNo ?? '');
                                },
                                onCancel: () {},
                              );
                            } else if (_controller.list[index].applicationStatus == '1') {
                              ///撤回
                              _presenter?.requestWithdraw(_controller.list[index].applicationNo ?? '');
                            }
                          },
                        );
                      })),
            ],
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          })),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveRecordPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;
}
