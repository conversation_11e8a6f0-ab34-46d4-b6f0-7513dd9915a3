import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 身份证验证
 */
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../iview/approve_main_iview.dart';

class AppRoveMainPage extends StatefulWidget {
  int position = 0;

  AppRoveMainPage({super.key, required this.position});

  @override
  _AppRoveMainPageState createState() => _AppRoveMainPageState();
}

class _AppRoveMainPageState extends State<AppRoveMainPage> with BasePageMixin<AppRoveMainPage, PowerPresenter<dynamic>> implements ApproveMainView {
  // final List<Map<String, dynamic>> approvalItems = [
  //   {'text': '入职审批', 'image': 'assets/images/base/icon_base_edit.png'},
  //   {'text': '离职审批', 'image': 'assets/images/base/icon_base_edit.png'},
  // ];

  ApproveMainController _controller = ApproveMainController();
  late ApproveMainPresenter _presenter;

  @override
  void initState() {
    super.initState();
    onRefresh();
  }

  Future<void> onRefresh() async {
    _presenter.onRefresh(HashMap());
  }

  Future<void> loadMore() async {
    _presenter.loadMore(HashMap());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (widget.position == 1) ? '选择模版查看审批记录' : '审批模板',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: Obx(() {
        return MyRefreshListView(
          itemCount: _controller.list.value.length,
          hintText: '无审批记录的查看权限，请联系管理员设置',
          itemBuilder: (context, index) {
            return _itemBuilder(index);
          },
          onRefresh: onRefresh,
          hasMore: false,
          loadMore: loadMore,
        );
      }),
      bottomNavigationBar: Visibility(
        visible: widget.position == 0,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
            title: '添加',
            onTap: () {
              BoostNavigator.instance
                  .push(
                    "addCustomComponentsPage",
                  )
                  .then((value) => _presenter.onRefresh(HashMap()));
            },
          ),
        ),
      ),
    );
  }

  Widget _itemBuilder(int index) {
    var value = _controller.list.value[index];
    return GestureDetector(
      onTap: () {
        if (widget.position == 0) {
          BoostNavigator.instance.push("addCustomComponentsPage", arguments: {"uuid": value.uuid ?? "", "is_system": value.isSystem}).then((value) => _presenter.onRefresh(HashMap()));
        } else {
          BoostNavigator.instance.push("ApproveRecordPage", arguments: {"uuid": value.uuid ?? '', "name": value.templateName}).then((value) => _presenter.onRefresh(HashMap()));
        }
      },
      child: Container(
        color: Colors.white,
        margin: const EdgeInsets.only(top: 1),
        padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            LoadImage(
              value.icon ?? '',
              width: 30,
              height: 30,
            ),
            Gaps.hGap10,
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    CommonUtils.getSimpleText(value.templateName, 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    Container(
                      margin: EdgeInsets.only(left: 4),
                      padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText(value.templateStatusName, 12, Colours.base_primary_text_caption),
                    ),
                    Container(
                      margin: const EdgeInsets.only(left: 4),
                      padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                      decoration: BoxDecoration(
                        border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText(value.isSystemName, 12, Colours.base_primary_text_caption),
                    ),
                  ],
                ),
                Visibility(visible: (!TextUtil.isEmpty(value.introduction)), child: CommonUtils.getSimpleText(value.introduction, 13, Colours.base_primary_text_caption)),
              ],
            )),
            Gaps.hGap10,
            const LoadAssetImage(
              "icon_base_arrow",
              width: 12,
              height: 12,
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveMainPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }
}
