import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/sku_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../net/http_config.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../controller/material_controller.dart';
import '../controller/material_template_controller.dart';
import '../iview/material_iview.dart';
import '../iview/material_template_iview.dart';
import '../presenter/material_presenter.dart';
import '../presenter/material_template_presenter.dart';
import '../wights/NumberInputWithButtons.dart';

/// 物料申请
class MaterialTemplatePage extends StatefulWidget {
  String? templateUuid = "";
  String? applicationNo = "";

  MaterialTemplatePage({super.key, this.templateUuid, this.applicationNo});

  @override
  _MaterialTemplatePageState createState() => _MaterialTemplatePageState();
}

class _MaterialTemplatePageState extends State<MaterialTemplatePage> with BasePageMixin<MaterialTemplatePage, PowerPresenter<dynamic>> implements MaterialTemplateView {
  final MaterialTemplateController _controller = MaterialTemplateController();

  late MaterialTemplatePresenter _presenter;


  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = httpConfig.project_uuid;
    _controller.projectName.value = httpConfig.project_name;

    if (!TextUtil.isEmpty(widget.applicationNo)) {
      _presenter.getApproveOne(widget.applicationNo);
    }


  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '物料申请',
      ),
      body: Column(
        children: [
          Obx(() => BrnTextSelectFormItem(
                title: "申请项目",
                isRequire: true,
                value: _controller.projectName.value,
                onTap: () {
                  BoostNavigator.instance.push('ProjectManagerPage', arguments: {
                    'choose_uuids': _controller.projectUuid.value.split(','),
                    'isSelected': true,
                  }).then((value) {
                    if (value is ProjectManagerList) {
                      _controller.projectUuid.value = value.uuid ?? '';
                      _controller.projectName.value = value.projectShortName ?? '';
                    }
                  });
                },
              )),
          Gaps.line,
          BrnBaseTitle(
            title: "备注",
          ),
          Container(
            padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
            color: Colours.white,
            child: BrnInputText(
              borderRadius: 4.0,
              minLines: 1,
              textString: _controller.remark.value,
              maxLength: 150,
              bgColor: Colours.base_primary_bg_page,
              autoFocus: false,
              textInputAction: TextInputAction.newline,
              hint: '请输入150字以内的备注信息',
              padding: const EdgeInsets.fromLTRB(20, 10, 20, 14),
              onTextChange: (text) {
                _controller.remark.value = text;
              },
              onSubmit: (text) {},
            ),
          ),
          Obx(() => Visibility(
                visible: _controller.listChild.value.isNotEmpty,
                child: Container(
                  color: Colours.white,
                  margin: const EdgeInsets.only(top: 10),
                  padding: const EdgeInsets.only(left: 16, top: 0, bottom: 10),
                  child: Row(
                    children: [
                      Expanded(
                          child: Row(
                        children: [
                          const LoadAssetImage(
                            'common/icon_approve_manager1',
                            width: 16,
                            height: 16,
                          ),
                          CommonUtils.getSimpleText('物品明细', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                        ],
                      )),
                      InkWell(
                        child: Padding(
                          padding: const EdgeInsets.only(top: 16, bottom: 16),
                          child: CommonUtils.getSimpleText('清除全部', 14, Colours.base_primary_blue),
                        ),
                        onTap: () {
                          BrnDialogManager.showConfirmDialog(context, title: "清除全部", cancel: '取消', confirm: '清除', message: "是否要清除全部？", barrierDismissible: false, onConfirm: () {
                            _controller.listChild.value.clear();
                            _controller.listChild.value = [];
                            Navigator.of(context, rootNavigator: true).pop();
                          }, onCancel: () {
                            Navigator.of(context, rootNavigator: true).pop();
                          });
                        },
                      ),
                      InkWell(
                        child: Padding(
                          padding: EdgeInsets.only(top: 16, bottom: 16, left: 20, right: 16),
                          child: CommonUtils.getSimpleText('添加+', 14, Colours.base_primary),
                        ),
                        onTap: () {
                          BoostNavigator.instance.push('materialSysPage', arguments: {'project_uuid': _controller.projectUuid.value, 'project_name': _controller.projectName.value, 'selected': _controller.listChild.value}).then((value) {
                            if (value is List<MaterialCatChildList>) {
                              List<MaterialCatChildList> list = value;
                              _controller.updateChildList(list);
                            }
                          });
                        },
                      ),
                    ],
                  ),
                ),
              )),
          Obx(() => Expanded(
                  child: Padding(
                padding: EdgeInsets.only(bottom: _controller.listChild.value.isNotEmpty ? 0 : 100),
                child: _controller.listChild.value.isNotEmpty
                    ? ListView.builder(
                        shrinkWrap: true,
                        itemCount: _controller.listChild.value.length,
                        itemBuilder: (context, position) {
                          return Container(
                            padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.all(Radius.circular(6)),
                            ),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                LoadImage(
                                  _controller.listChild.value[position].coverUrl ?? '',
                                  width: 50,
                                  height: 50,
                                ),
                                Gaps.hGap4,
                                Expanded(
                                    child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    CommonUtils.getSimpleText(_controller.listChild.value[position].productName, 14, Colours.base_primary_text_title),
                                    CommonUtils.getSimpleText('${_controller.listChild.value[position].specification}', 14, Colours.base_primary_text_title),
                                    Container(
                                      alignment: Alignment.centerRight,
                                      child: (_controller.listChild.value[position].num == 0)
                                          ? InkWell(
                                              child: const Icon(Icons.shopping_cart),
                                              onTap: () {},
                                            )
                                          : Center(
                                              child: NumberInputWithButtons(
                                                key: ValueKey(_controller.listChild[position].number),
                                                // 确保每个实例有唯一键
                                                initialValue: _controller.listChild[position].num,
                                                maxValue: 99999,
                                                minValue: 0,
                                                onChanged: (int value) {
                                                  _controller.listChild[position].num = value;
                                                  _controller.notifyList();
                                                },
                                              ),
                                            ),
                                    ),
                                  ],
                                ))
                              ],
                            ),
                          );
                        },
                      )
                    : Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          const LoadAssetImage(
                            "no_data",
                            width: 100,
                            height: 100,
                          ),
                          CommonUtils.getSimpleText('暂无数据', 14, Colours.base_primary_text_caption),
                          Gaps.vGap10,
                          BrnSmallMainButton(
                            title: '添加',
                            bgColor: Colours.base_primary,
                            onTap: () {
                              BoostNavigator.instance.push('materialSysPage', arguments: {'project_uuid': _controller.projectUuid.value, 'project_name': _controller.projectName.value}).then((value) {
                                if (value is List<MaterialCatChildList>) {
                                  List<MaterialCatChildList> list = value;
                                  print('111 - $list');
                                  _controller.listChild.value = list;
                                  print('222 - ${_controller.listChild.value.length}');
                                }
                              });
                            },
                          ),
                        ],
                      ),
              ))),
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            // InkWell(
            //   child: Container(
            //     padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
            //     decoration: BoxDecoration(
            //       border: Border.all(color: Colours.base_primary, width: 1),
            //       borderRadius: BorderRadius.circular(4.0),
            //     ),
            //     child: CommonUtils.getSimpleText('存草稿', 14, Colours.base_primary),
            //   ),
            //   onTap: () {
            //     submit(true);
            //   },
            // ),
            // Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                submit(false);
              },
            ))
          ],
        ),
      ),
    );
  }

  void submit(bool isDraft) {
    if (_controller.listChild.isEmpty) {
      BrnToast.show('请选择物品', context);
      return;
    }
    List<SkuListEntity> skuList = [];
    for (MaterialCatChildList child in _controller.listChild.value) {
      SkuListEntity item = SkuListEntity();
      item.number = child.number;
      item.num = '${child.num}';
      skuList.add(item);
    }
    HashMap<String, dynamic> params = HashMap();
    params['project_uuid'] = _controller.projectUuid.value; //项目的UUID
    params['template_uuid'] = widget.templateUuid ?? ''; //模版UUID
    params['remark'] = _controller.remark.value; //备注
    params['sku_list'] = json.encode(skuList); //物品明细列表
    params['is_draft'] = isDraft ? '1' : '0'; //是否草稿
    // if (!TextUtil.isEmpty(widget.applicationNo)) {
    //   params['application_no'] = widget.applicationNo!; //草稿编辑必传
    // }
    _presenter.submitMaterialRequest(params);
  }

  @override
  void createMaterial() {
    BrnToast.show("提交成功", context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = MaterialTemplatePresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }
}
