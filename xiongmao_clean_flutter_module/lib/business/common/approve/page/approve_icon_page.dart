import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 身份证验证
 */
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_icon_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_button.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../controller/add_edit_custom_components_controller.dart';
import '../controller/approve_icon_controller.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/add_edit_custom_comonents_iview.dart';
import '../iview/approve_main_iview.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';
import '../presenter/add_edit_custom_comonents_presenter.dart';
import '../presenter/approve_icon_presenter.dart';
import '../presenter/custom_comonents_presenter.dart';
import '../presenter/material_presenter.dart';

/**
 * 选择审批图标
 */
class ApproveIconPage extends StatefulWidget {
  @override
  _ApproveIconPageState createState() => _ApproveIconPageState();
}

class _ApproveIconPageState extends State<ApproveIconPage> with BasePageMixin<ApproveIconPage, PowerPresenter<dynamic>> implements ApproveIconView {
  final ApproveIconController _controller = ApproveIconController();

  late ApproveIconPresenter _presenter;

  @override
  void initState() {
    super.initState();
    _presenter.getAppRoveIconList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '选择审批图标',
      ),
      body: Container(
        color: Colors.white,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Gaps.line,
            Padding(
              padding: EdgeInsets.only(left: 20, right: 20, top: 20),
              child: CommonUtils.getSimpleText('用于在工作台中展示审批的入口', 14, Colours.base_primary_text_title),
            ),
            Obx(() => GridView.builder(
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 20, bottom: 10),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 4,
                    crossAxisSpacing: 8,
                    mainAxisSpacing: 20,
                  ),
                  shrinkWrap: true,
                  itemCount: _controller.list.value.length,
                  itemBuilder: (context, index) {
                    return InkWell(
                      onTap: () {
                        setState(() {
                          _controller.selected.value = _controller.list.value[index];
                          BoostNavigator.instance.pop(_controller.selected.value.icon);
                        });
                      },
                      child: Column(
                        children: [
                          LoadImage(
                            '${_controller.list.value[index].icon}',
                            height: 50,
                            width: 50,
                          ),
                          Gaps.vGap10,
                          LoadAssetImage(
                            (_controller.selected.value == (_controller.list.value[index])) ? "icon_check" : "icon_uncheck",
                            width: 20,
                            height: 20,
                          )
                          // CommonUtils.getSimpleText('${_controller.list.value[index].name}', 14, Colours.base_primary_text_title),
                        ],
                      ),
                    );
                  },
                ))
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ApproveIconPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }
}
