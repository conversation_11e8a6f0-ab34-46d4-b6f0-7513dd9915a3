import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_option_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_option_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../risk_monitoring/item/risk_item_listview.dart';
import '../bean/approve_project_entity.dart';
import '../bean/approve_role_entity.dart';
import '../controller/material_controller.dart';
import '../item/approve_option_listview.dart';
import '../item/approve_project_listview.dart';
import '../item/approve_role_listview.dart';
import '../iview/approve_main_iview.dart';
import '../iview/material_iview.dart';
import '../presenter/approve_option_presenter.dart';
import '../presenter/material_presenter.dart';

/// 自定义审批-选项、比如角色、项目、成员
class AppRoveOptionPage extends StatefulWidget {
  int position = 0;

  String? uuids;

  AppRoveOptionPage({super.key, required this.position, required this.uuids});

  @override
  _AppRoveOptionPageState createState() => _AppRoveOptionPageState();
}

class _AppRoveOptionPageState extends State<AppRoveOptionPage> with BasePageMixin<AppRoveOptionPage, PowerPresenter<dynamic>> implements AppRoveOptionView {
  final AppRoveOptionController _controller = AppRoveOptionController();

  AppRoveOptionPresenter? _presenter;

  String title = '选择审批记录管理者';

  @override
  void initState() {
    super.initState();
    switch (widget.position) {
      case 0:
        title = '选择审批记录管理者';
        break;
      case 1:
        title = '选择适用成员';
        break;
      case 2:
        title = '选择适用角色';
        break;
      case 3:
        title = '选择适用项目';
        break;
    }
    _controller.uuids.value = widget.uuids ?? ''; //反选
    _controller.position.value = widget.position;
    _controller.project_uuid.value = httpConfig.project_uuid;

    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: title,
      ),
      body: Obx(() => Column(
            children: [
              Visibility(
                visible: widget.position == 0 || widget.position == 1,
                child: Container(
                  child: BrnSearchText(
                    innerPadding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    maxHeight: 60,
                    innerColor: Colours.base_primary_bg_page,
                    borderRadius: const BorderRadius.all(Radius.circular(8)),
                    autoFocus: false,
                    textStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_title),
                    hintStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_caption),
                    hintText: '请输入内容查找人员',
                    onActionTap: () {},
                    onTextCommit: (text) {},
                    onTextChange: (text) {
                      _controller.keyword.value = text;
                      _onRefresh();
                    },
                  ),
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                      itemCount: getItemCount(),
                      onRefresh: _onRefresh,
                      loadMore: _loadMore,
                      hasMore: int.parse(_controller.totalNumber.value) > getItemCount(),
                      itemBuilder: (_, index) {
                        if (widget.position == 2) {
                          return RoleListView(
                            data: _controller.roleList[index],
                            onClick: () {
                              setState(() {
                                _controller.roleList[index].isSelected = !_controller.roleList[index].isSelected;
                              });
                            },
                          );
                        } else if (widget.position == 3) {
                          return ProjectListView(
                            data: _controller.projectList[index],
                            onClick: () {
                              setState(() {
                                _controller.projectList[index].isSelected = !_controller.projectList[index].isSelected;
                              });
                            },
                          );
                        } else {
                          return AppRoveOptionListView(
                            data: _controller.list[index],
                            onClick: () {
                              setState(() {
                                _controller.list[index].isSelected = !_controller.list[index].isSelected;
                              });
                            },
                          );
                        }
                      })),
            ],
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            InkWell(
              child: Container(
                padding: EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  border: Border.all(color: Colours.base_primary, width: 1),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText(widget.position == 0 ? '重置' : '不限制', 14, Colours.base_primary),
              ),
              onTap: () {
                BoostNavigator.instance.pop('Unrestricted');
              },
            ),
            Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                backResult();
              },
            ))
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AppRoveOptionPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  /**
   * 针对不同渠道进来的，处理不同的
   */
  int getItemCount() {
    if (widget.position == 2) {
      return _controller.roleList.length;
    } else if (widget.position == 3) {
      return _controller.projectList.length;
    } else {
      return _controller.list.length;
    }
  }

  void backResult() {
    String uuids = '';

    switch (widget.position) {
      case 0: //把已经选中的数据，传递调用方
      case 1:
        for (ProjectArchivesList item in _controller.list.value) {
          if (item.isSelected) {
            uuids += '${item.uuid},';
          }
        }
        break;
      case 2: //把已经选中的数据，传递调用方
        for (ApproveRoleList item in _controller.roleList.value) {
          if (item.isSelected) {
            uuids += '${item.id},';
          }
        }
        break;
      case 3: //把已经选中的数据，传递调用方
        for (ApproveProjectList item in _controller.projectList.value) {
          if (item.isSelected) {
            uuids += '${item.uuid},';
          }
        }
        break;
    }

    if (uuids.isNotEmpty) {
      uuids = uuids.substring(0, uuids.length - 1); // 去除最后一个逗号
    }

    BoostNavigator.instance.pop(uuids);
  }
}
