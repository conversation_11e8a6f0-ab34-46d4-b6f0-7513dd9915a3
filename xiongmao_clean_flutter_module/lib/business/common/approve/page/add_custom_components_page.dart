import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../bean/approve_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/common_utils.dart';
import '../bean/template_one_entity.dart';
import '../controller/add_edit_custom_components_controller.dart';
import '../iview/add_edit_custom_comonents_iview.dart';
import '../presenter/add_edit_custom_comonents_presenter.dart';

/// 添加模版、编辑模版
class AddCustomComponentsPage extends StatefulWidget {
  String? uuid;
  String? is_system;

  AddCustomComponentsPage({super.key, required this.uuid, required this.is_system});

  @override
  _AddCustomComponentsPageState createState() => _AddCustomComponentsPageState();
}

class _AddCustomComponentsPageState extends State<AddCustomComponentsPage> with BasePageMixin<AddCustomComponentsPage, PowerPresenter<dynamic>> implements AddEditCustomComponentsView {
  final AddEditCustomComponentController _controller = AddEditCustomComponentController();

  late AddEditCustomComponentPresenter _presenter;

  String title = '添加自定义模版';

  late TextEditingController templateNameController;
  late TextEditingController templateRemarkController;

  @override
  void initState() {
    super.initState();

    templateNameController = TextEditingController(text: _controller.templateName.value);
    templateRemarkController = TextEditingController(text: _controller.templateRemark.value);

    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter.getTemplateOne(widget.uuid!);
    }

    ever(_controller.templateName, (value) {
      if (templateNameController.text != value) {
        templateNameController.text = value.toString();
      }
    });

    ever(_controller.templateRemark, (value) {
      if (templateRemarkController.text != value) {
        templateRemarkController.text = value.toString();
      }
    });
  }

  @override
  void dispose() {
    templateNameController.dispose();
    templateRemarkController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (widget.is_system == '1') ? '编辑系统模版' : '编辑自定义模版',
      ),
      body: ListView(
        padding: const EdgeInsets.only(bottom: 100),
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
            child: CommonUtils.getSimpleText('基础设置', 14, Colours.base_primary_text_caption),
          ),
          BrnTextInputFormItem(
            title: "模版名称",
            hint: "请输入",
            controller: templateNameController,
            isRequire: true,
            inputFormatters: [
              LengthLimitingTextInputFormatter(10), // 限制输入长度为20个字符
            ],
            onChanged: (newValue) {
              _controller.templateName.value = newValue;
            },
          ),
          Gaps.line,
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(top: 10, bottom: 10, right: 20),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(padding: EdgeInsets.only(top: 8, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                Padding(padding: EdgeInsets.only(top: 0, left: 2, right: 20), child: CommonUtils.getSimpleText("图标", 14, Colours.base_primary_text_title)),
                Expanded(
                    child: Obx(() => Container(
                          // color: Colors.cyan,
                          alignment: Alignment.centerRight,
                          margin: const EdgeInsets.only(left: 0, right: 0),
                          child: InkWell(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 将主轴对齐方式设置为两端对齐
                              children: [
                                Visibility(
                                  child: Container(),
                                  visible: (!TextUtil.isEmpty(_controller.approveIcon.value)),
                                ),
                                (!TextUtil.isEmpty(_controller.approveIcon.value))
                                    ? LoadImage(
                                        _controller.approveIcon.value,
                                        width: 40,
                                        height: 40,
                                      )
                                    : Expanded(
                                        child: CommonUtils.getSimpleText('请选择', 14, Colours.base_primary_text_hint, textAlign: TextAlign.right),
                                      )
                              ],
                            ),
                            onTap: () {
                              BoostNavigator.instance.push('approveIconPage').then((value) {
                                print('选择图标 a ');
                                if (value is String) {
                                  print('选择图标倆 b ${value}');
                                  _controller.approveIcon.value = value ?? '';
                                }
                              });
                            },
                          ),
                        ))),
              ],
            ),
          ),
          Gaps.line,
          BrnTextInputFormItem(
            title: "说明",
            hint: "请输入",
            inputFormatters: [
              LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
            ],
            controller: templateRemarkController,
            isRequire: true,
            onChanged: (newValue) {
              _controller.templateRemark.value = newValue;
            },
          ),
          Gaps.line,
          Obx(() => BrnTextSelectFormItem(
                title: "审批流程设置",
                isRequire: true,
                hint: "请选择",
                value: _controller.approveEntryDepartValue.value,
                onTap: () {
                  //跳转审批设置
                  BoostNavigator.instance.push("appEntryDepartMainPage", arguments: {'list': _controller.approveEntryDepart.value}).then((value) {
                    print('来这里了');
                    if (value is List) {
                      print('赋值');
                      _controller.approveEntryDepart.value = value as List<ApproveDetailList>;
                      _controller.approveEntryDepartValue.value = '已设置';
                    }
                  });
                },
              )),
          Gaps.line,
          Obx(() => BrnTextSelectFormItem(
                title: "审批记录管理者",
                hint: "请选择",
                value: (!TextUtil.isEmpty(_controller.approveRecordValue.value) ? _controller.approveRecordValue.value : ''),
                onTap: () {
                  BoostNavigator.instance.push('appRoveOptionPage', arguments: {'position': 0, 'uuids': _controller.approveRecordUuidValue.value}).then((value) {
                    print('拿到的数据 -- $value');
                    if (value is String) {
                      if ('Unrestricted' == value) {
                        //不限制
                        _controller.approveRecordValue.value = '';
                        _controller.approveRecordUuidValue.value = '';
                      } else {
                        _controller.approveRecordUuidValue.value = value;
                        _controller.approveRecordValue.value = '已选择${value.split(',').length}人';
                      }
                    }
                  });
                },
              )),
          Gaps.line,
          Obx(() => BrnRadioInputFormItem(
                title: "状态",
                isRequire: true,
                options: const ["停用", "启用"],
                value: _controller.approveStatus.value,
                onChanged: (oldValue, newValue) {
                  // BrnToast.show("点击触发回调${oldValue}_${newValue}_onChanged", context);
                  _controller.approveStatus.value = newValue!;
                },
              )),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('可用范围', 15, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText('取三个条件的交集', 15, Colours.base_primary_text_caption),
              ],
            ),
          ),
          Obx(() => BrnTextSelectFormItem(
                title: "适用成员",
                hint: "请选择",
                value: _controller.approveMemberValue.value,
                onTap: () {
                  BoostNavigator.instance.push('appRoveOptionPage', arguments: {'position': 1, 'uuids': _controller.approveMemberUuidValue.value}).then((value) {
                    print('拿到的数据 -- $value');
                    if (value is String) {
                      if ('Unrestricted' == value) {
                        //不限制
                        _controller.approveMemberValue.value = '不限制';
                        _controller.approveMemberUuidValue.value = '';
                      } else {
                        _controller.approveMemberUuidValue.value = value;
                        _controller.approveMemberValue.value = '已选择${value.split(',').length}人';
                      }
                    }
                  });
                },
              )),
          Gaps.line,
          Obx(() => BrnTextSelectFormItem(
                title: "适用角色",
                hint: "请选择",
                value: _controller.approveRoleValue.value,
                onTap: () {
                  BoostNavigator.instance.push('appRoveOptionPage', arguments: {'position': 2, 'uuids': _controller.approveRoleUuidValue.value}).then((value) {
                    print('拿到的数据 -- $value');
                    if (value is String) {
                      if ('Unrestricted' == value) {
                        //不限制
                        _controller.approveRoleValue.value = '不限制';
                        _controller.approveRoleUuidValue.value = '';
                      } else {
                        _controller.approveRoleUuidValue.value = value;
                        _controller.approveRoleValue.value = '已选择${value.split(',').length}个角色';
                      }
                    }
                  });
                },
              )),
          Gaps.line,
          Obx(() => BrnTextSelectFormItem(
                title: "适用项目",
                hint: "请选择",
                value: _controller.approveProjectValue.value,
                onTap: () {
                  BoostNavigator.instance.push('appRoveOptionPage', arguments: {'position': 3, 'uuids': _controller.approveProjectUuidValue.value}).then((value) {
                    print('拿到的数据 -- $value');
                    if (value is String) {
                      if ('Unrestricted' == value) {
                        //不限制
                        _controller.approveProjectValue.value = '不限制';
                        _controller.approveProjectUuidValue.value = '';
                      } else {
                        _controller.approveProjectUuidValue.value = value;
                        _controller.approveProjectValue.value = '已选择${value.split(',').length}个项目';
                      }
                    }
                  });
                },
              )),
          Padding(
            padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
            child: CommonUtils.getSimpleText('表单内容', 14, Colours.base_primary_text_caption),
          ),
          Obx(() => Visibility(
                visible: !_controller.isSystem.value,
                child: ReorderableListView(
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  onReorder: (oldIndex, newIndex) {
                    //更新数据
                    if (oldIndex < newIndex) {
                      newIndex -= 1; // 从上往下拖动时，index会减1
                    }
                    // 更新数据源顺序
                    TemplateOneFieldList movedItem = _controller.fromTable.removeAt(oldIndex);
                    _controller.fromTable.insert(newIndex, movedItem);
                    _controller.notifySingSelectList();
                  },
                  children: List.generate(_controller.fromTable.length, (index) {
                    return Column(
                      key: ValueKey(_controller.fromTable[index]),
                      children: [
                        Container(
                          // 每个 item 都需要一个唯一的 key
                          color: Colours.white,
                          margin: EdgeInsets.only(bottom: _controller.fromTable[index].fieldType == _controller.TYPE_TABLE ? 0 : 1),
                          padding: const EdgeInsets.only(left: 20, right: 10, top: 10, bottom: 10),
                          child: Row(
                            children: [
                              GestureDetector(
                                onLongPress: () {},
                                child: InkWell(
                                  child: const LoadAssetImage(
                                    'common/icon_approve_delete',
                                    width: 20,
                                    height: 20,
                                  ),
                                  onTap: () {
                                    BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "是否要删除该'${_controller.fromTable[index].fieldName}'?", barrierDismissible: false, onConfirm: () {
                                      _controller.fromTable.removeAt(index);
                                      _controller.notifySingSelectList();
                                      Navigator.of(context, rootNavigator: true).pop();
                                    }, onCancel: () {
                                      Navigator.of(context, rootNavigator: true).pop();
                                    });
                                  },
                                ), // 添加一个可以拖动的图标
                              ),
                              Gaps.hGap10,
                              Expanded(
                                child: InkWell(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.max,
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    children: [
                                      CommonUtils.getSimpleText(_controller.fromTable[index].fieldName, 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                      Row(
                                        children: [
                                          Container(
                                            padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                            decoration: BoxDecoration(
                                              border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                                              borderRadius: BorderRadius.circular(2.0),
                                            ),
                                            child: CommonUtils.getSimpleText(handlerType(_controller.fromTable[index].fieldType ?? ''), 10, Colours.base_primary_text_caption, height: 1),
                                          ),
                                          Visibility(
                                            visible: (_controller.fromTable[index].isMust == '1'),
                                            child: Container(
                                              margin: EdgeInsets.only(left: 4),
                                              padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                              decoration: BoxDecoration(
                                                border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                                                borderRadius: BorderRadius.circular(2.0),
                                              ),
                                              child: CommonUtils.getSimpleText('必填', 10, Colours.base_primary_text_caption, height: 1),
                                            ),
                                          ),
                                          Visibility(
                                            visible: (_controller.fromTable[index].isAbstract == '1'),
                                            child: Container(
                                              margin: EdgeInsets.only(left: 4),
                                              padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                              decoration: BoxDecoration(
                                                border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                                                borderRadius: BorderRadius.circular(2.0),
                                              ),
                                              child: CommonUtils.getSimpleText('摘要', 10, Colours.base_primary_text_caption, height: 1),
                                            ),
                                          ),
                                        ],
                                      )
                                    ],
                                  ),
                                  onTap: () {
                                    gotoEditFiled(index);
                                  },
                                ),
                              ),
                              InkWell(
                                child: const LoadAssetImage(
                                  'base/icon_base_edit',
                                  width: 20,
                                  height: 20,
                                ),
                                onTap: () {
                                  gotoEditFiled(index);
                                },
                              ),
                              Gaps.hGap10,
                              const LoadAssetImage(
                                'icon_fun',
                                width: 20,
                                height: 20,
                              ),
                            ],
                          ),
                        ),
                        Visibility(
                            visible: _controller.fromTable[index].fieldType == _controller.TYPE_TABLE && _controller.fromTable[index].child!.isNotEmpty,
                            child: ReorderableListView(
                              physics: const NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              onReorder: (oldIndex, newIndex) {
                                //更新数据
                                if (oldIndex < newIndex) {
                                  newIndex -= 1; // 从上往下拖动时，index会减1
                                }
                                // 更新数据源顺序
                                TemplateOneFieldList movedItem = _controller.fromTable[index].child.removeAt(oldIndex);
                                _controller.fromTable[index].child.insert(newIndex, movedItem);
                                _controller.notifySingSelectList();
                              },
                              children: List.generate(_controller.fromTable[index].child.length, (position) {
                                return Container(
                                  color: Colors.white,
                                  key: ValueKey(_controller.fromTable[index].child[position]),
                                  child: Column(
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.only(left: 50),
                                        child: Gaps.line,
                                      ),
                                      Container(
                                        // 每个 item 都需要一个唯一的 key
                                        color: Colors.white,
                                        padding: EdgeInsets.only(left: 50, right: 10, top: 10, bottom: 10),
                                        child: Row(
                                          children: [
                                            GestureDetector(
                                              onLongPress: () {},
                                              child: InkWell(
                                                child: const LoadAssetImage(
                                                  'common/icon_approve_delete',
                                                  width: 20,
                                                  height: 20,
                                                ),
                                                onTap: () {
                                                  BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "是否要删除该'${_controller.fromTable[index].child[position].fieldName}'?", barrierDismissible: false, onConfirm: () {
                                                    _controller.fromTable[index].child.removeAt(position);
                                                    _controller.notifySingSelectList();
                                                    Navigator.of(context, rootNavigator: true).pop();
                                                  }, onCancel: () {
                                                    Navigator.of(context, rootNavigator: true).pop();
                                                  });
                                                },
                                              ), // 添加一个可以拖动的图标
                                            ),
                                            Gaps.hGap10,
                                            Expanded(
                                              child: InkWell(
                                                child: Column(
                                                  mainAxisSize: MainAxisSize.max,
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  children: [
                                                    CommonUtils.getSimpleText(_controller.fromTable[index].child[position].fieldName, 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                                    Row(
                                                      children: [
                                                        Container(
                                                          padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                                          decoration: BoxDecoration(
                                                            border: Border.all(color: Colours.base_primary_text_caption, width: 1),
                                                            borderRadius: BorderRadius.circular(4.0),
                                                          ),
                                                          child: CommonUtils.getSimpleText(handlerType(_controller.fromTable[index].child[position].fieldType ?? ''), 10, Colours.base_primary_text_caption),
                                                        ),
                                                        Visibility(
                                                          visible: (_controller.fromTable[index].child[position].isMust == '1'),
                                                          child: Container(
                                                            margin: EdgeInsets.only(left: 4),
                                                            padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                                            decoration: BoxDecoration(
                                                              border: Border.all(color: Colours.base_primary_text_caption, width: 1),
                                                              borderRadius: BorderRadius.circular(4.0),
                                                            ),
                                                            child: CommonUtils.getSimpleText('必填', 10, Colours.base_primary_text_caption),
                                                          ),
                                                        ),
                                                        Visibility(
                                                          visible: (_controller.fromTable[index].child[position].isAbstract == '1'),
                                                          child: Container(
                                                            margin: EdgeInsets.only(left: 4),
                                                            padding: const EdgeInsets.only(top: 0, bottom: 0, right: 2, left: 2),
                                                            decoration: BoxDecoration(
                                                              border: Border.all(color: Colours.base_primary_text_caption, width: 1),
                                                              borderRadius: BorderRadius.circular(4.0),
                                                            ),
                                                            child: CommonUtils.getSimpleText('摘要', 10, Colours.base_primary_text_caption),
                                                          ),
                                                        ),
                                                      ],
                                                    )
                                                  ],
                                                ),
                                                onTap: () {
                                                  gotoEditChildFiled(index, position);
                                                },
                                              ),
                                            ),
                                            Gaps.hGap10,
                                            InkWell(
                                              child: const LoadAssetImage(
                                                'base/icon_base_edit',
                                                width: 20,
                                                height: 20,
                                              ),
                                              onTap: () {
                                                gotoEditChildFiled(index, position);
                                              },
                                            ),
                                            Gaps.hGap10,
                                            const LoadAssetImage(
                                              'icon_fun',
                                              width: 20,
                                              height: 20,
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                );
                              }),
                            )),
                        Visibility(
                          visible: _controller.fromTable[index].fieldType == _controller.TYPE_TABLE,
                          child: InkWell(
                            child: Container(
                              width: double.infinity,
                              color: Colors.white,
                              margin: EdgeInsets.only(bottom: 4),
                              padding: EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                              child: Row(
                                children: [
                                  const LoadAssetImage(
                                    'common/icon_approve_add',
                                    width: 20,
                                    height: 20,
                                  ),
                                  Gaps.hGap8,
                                  CommonUtils.getSimpleText('添加表格字段', 15, Colours.base_primary_blue)
                                ],
                              ),
                            ),
                            onTap: () {
                              BoostNavigator.instance.push('customComponentsPage', arguments: {"show_abstract": false}).then((value) {
                                print('嘿嘿嘿 --- $value');
                                if (value is TemplateOneFieldList) {
                                  //表格的需要单独处理，把新增的，都放到表格里面
                                  _controller.fromTable[index].child.add(value);
                                  _controller.notifySingSelectList();
                                }
                              });
                            },
                          ),
                        ),
                      ],
                    );
                  }),
                ),
              )),
          Obx(() => Visibility(
                visible: _controller.isSystem.value,
                child: Column(
                  children: [
                    const LoadAssetImage(
                      "no_data",
                      width: 100,
                      height: 100,
                    ),
                    CommonUtils.getSimpleText('系统模板的表单内容，暂不支持自定义', 12, Colours.base_primary_text_title, textAlign: TextAlign.center),
                  ],
                ),
              )),
          Obx(() => Visibility(
                visible: !_controller.isSystem.value,
                child: InkWell(
                  child: Container(
                    color: Colors.white,
                    padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: [
                        const LoadAssetImage(
                          'common/icon_approve_add',
                          width: 20,
                          height: 20,
                        ),
                        Gaps.hGap8,
                        CommonUtils.getSimpleText('添加字段', 15, Colours.base_primary_blue)
                      ],
                    ),
                  ),
                  onTap: () {
                    BoostNavigator.instance.push('customComponentsPage').then((value) {
                      print('哈哈哈 --- $value');
                      if (value is TemplateOneFieldList) {
                        _controller.addTableData(value);
                      }
                    });
                  },
                ),
              )),
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            Visibility(
                visible: widget.is_system == '2',
                child: Row(
                  children: [
                    InkWell(
                      child: Container(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                        decoration: BoxDecoration(
                          border: Border.all(color: Colours.base_primary, width: 1),
                          borderRadius: BorderRadius.circular(4.0),
                        ),
                        child: CommonUtils.getSimpleText('删除', 14, Colours.base_primary),
                      ),
                      onTap: () {
                        BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', barrierDismissible: false, message: "是否要删除该审批模版?", onConfirm: () {
                          _presenter.deleteTemplate(widget.uuid!);
                          Navigator.of(context, rootNavigator: true).pop();
                        }, onCancel: () {
                          Navigator.of(context, rootNavigator: true).pop();
                        });
                      },
                    ),
                    Gaps.hGap10,
                  ],
                )),
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                if (TextUtil.isEmpty(_controller.templateName.value)) {
                  BrnToast.show('请输入模版名称', context);
                  return;
                }
                if (TextUtil.isEmpty(_controller.approveIcon.value)) {
                  BrnToast.show('请选择图标', context);
                  return;
                }
                if (_controller.fromTable.value.isEmpty && widget.is_system != '1') {
                  BrnToast.show('请添加表单内容', context);
                  return;
                }

                int count = 0;
                for (TemplateOneFieldList item in _controller.fromTable) {
                  if (item.isAbstract == '1') {
                    count += 1;
                  }
                }
                if (count > 3) {
                  BrnToast.show('已设置$count个摘要字段，最多只能设置3个。', context);
                  return;
                }

                HashMap<String, String> params = HashMap();
                if (_controller.approveEntryDepart.isNotEmpty) {
                  params['node_data'] = json.encode(_controller.approveEntryDepart.value); //节点数据
                  print('传给后段的值 ${json.encode(_controller.approveEntryDepart.value)}');
                }
                if (!TextUtil.isEmpty(widget.uuid)) {
                  params['uuid'] = widget.uuid!; //模板名称
                }

                params['template_name'] = _controller.templateName.value; //模板名称
                params['icon'] = _controller.approveIcon.value; //图标
                params['template_status'] = (_controller.approveStatus.value == '启用') ? '1' : '2'; //状态 1启用 2禁用
                params['introduction'] = _controller.templateRemark.value; //简介说明
                params['manager_user_uuid_list'] = _controller.approveRecordUuidValue.value; //审批记录管理者  多个用英文逗号拼接
                params['user_uuid_list'] = _controller.approveMemberUuidValue.value; //适用成员 多个用英文逗号拼接
                params['role_id_list'] = _controller.approveRoleUuidValue.value; //适用角色 多个用英文逗号拼接
                params['project_uuid_list'] = _controller.approveProjectUuidValue.value; //适用项目 多个用英文逗号拼接
                ///这里是🧍表格的内容
                if (_controller.fromTable.value.isNotEmpty) {
                  params['field_list'] = json.encode(_controller.fromTable.value); //字段列表 json格式
                }
                _presenter.saveTemplate(params);
              },
            ))
          ],
        ),
      ),
    );
  }

  void gotoEditFiled(int index) {
    //编辑某个
    BoostNavigator.instance.push('customComponentsPage', arguments: {
      'item': _controller.fromTable[index],
    }).then((value) {
      if (value is TemplateOneFieldList) {
        print('修改状态 ${value.fieldName}');
        print('修改状态 ${value.fieldType}');
        if (value.fieldType == 'table') {
          //如果是表格，看表格里面是否还存在值
          value.child = _controller.fromTable[index].child;
        }
        _controller.fromTable[index] = value;
        _controller.notifySingSelectList();
      }
    });
  }

  void gotoEditChildFiled(int index, int position) {
    BoostNavigator.instance.push('customComponentsPage', arguments: {
      'item': _controller.fromTable[index].child[position],
      "show_abstract": false,
    }).then((value) {
      if (value is TemplateOneFieldList) {
        _controller.fromTable[index].child[position] = value;
        _controller.notifySingSelectList();
      }
    });
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddEditCustomComponentPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void saveTemplate(data) {
    BrnToast.show("提交成功", context);
    BoostNavigator.instance.pop();
  }

  //这里
  String handlerType(String type) {
    String text = '未知';
    switch (type) {
      case 'text':
        text = '文本';
        break;
      case 'number':
        text = '数值';
        break;
      case 'date':
        text = '日期';
        break;
      case 'single':
        text = '单选';
        break;
      case 'picture':
        text = '图片';
        break;
      case 'attachment':
        text = '附件';
        break;
      case 'table':
        text = '表格';
        break;
    }
    return text;
  }

  @override
  void delete() {
    BrnToast.show("删除成功", context);
    BoostNavigator.instance.pop();
  }
}
