import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_main_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/presenter/approve_main_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../contract/persenter/contract_history_presenter.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';
import '../bean/material_cat_child_entity.dart';
import '../controller/material_controller.dart';
import '../controller/material_manager_controller.dart';
import '../iview/approve_main_iview.dart';
import '../iview/material_iview.dart';
import '../iview/material_manager_iview.dart';
import '../presenter/material_manager_presenter.dart';
import '../presenter/material_presenter.dart';
import '../wights/NumberInputWithButtons.dart';

/// 可申请的物料 -- 只做删除
class MaterialSysManagerPage extends StatefulWidget {
  String? projectUuid = "";
  String? projectName = "";

  MaterialSysManagerPage({super.key, required this.projectUuid, required this.projectName});

  @override
  _MaterialSysManagerPageState createState() => _MaterialSysManagerPageState();
}

class _MaterialSysManagerPageState extends State<MaterialSysManagerPage> with BasePageMixin<MaterialSysManagerPage, PowerPresenter<dynamic>> implements MaterialManagerView {
  final MaterialManagerController _controller = MaterialManagerController();

  late MaterialManagerPresenter _presenter;

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.projectUuid)) {
      _controller.project_uuid.value = widget.projectUuid!;
    } else {
      _controller.project_uuid.value = httpConfig.project_uuid;
    }
    //先请求物料分类
    _presenter.getCatList();
  }

  Future<dynamic> _onRefresh() async {
    _presenter.onRefresh(_controller.cat_id.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter.loadMore(_controller.cat_id.value);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '可申请的物料',
        centerSubTitle: widget.projectName ?? httpConfig.project_name,
        actionWidget: Row(
          children: [
            InkWell(
              child: Padding(
                padding: const EdgeInsets.only(right: 16),
                child: CommonUtils.getSimpleText('清除全部', 16, Colours.base_primary),
              ),
              onTap: () {
                DialogManager.showConfirmDialog(
                  context: context,
                  title: '提示',
                  cancel: '取消',
                  confirm: '确定',
                  message: '是否清除所有物料？',
                  onConfirm: () {
                    _presenter.deleteSkuAll();
                  },
                  onCancel: () {},
                );
              },
            ),
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: CommonUtils.getSimpleText('添加', 16, Colours.base_primary),
            )
          ],
        ),
        onPressed: () {
          BoostNavigator.instance.push('materialSysChoosePage', arguments: {
            'project_uuid': widget.projectUuid,
            'project_name': widget.projectName,
          }).then((value) {
            if (value is List<MaterialCatChildList>) {
              List<MaterialCatChildList> list = value;
              if (list.isNotEmpty) {
                final numbers = list.where((item) => item.number != null).map((item) => item.number!).toList();
                _presenter.addSku(numbers.join(','));
              }
            }
          });
        },
      ),
      body: WillPopScope(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Expanded(
                  child: Container(
                color: Colours.drawer_bg,
                child: Obx(() => ListView.builder(
                      padding: const EdgeInsets.all(0),
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: false,
                      itemCount: _controller.catList.value.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              _controller.cat_id.value = _controller.catList[index].id ?? '';
                              // 更新选中的索引，并触发UI更新
                              _presenter.onRefresh(_controller.catList[index].id ?? '');
                            });
                          },
                          child: Container(
                            padding: EdgeInsets.only(top: 4),
                            color: (_controller.cat_id.value == _controller.catList[index].id) ? Colors.white : Colors.transparent,
                            child: Column(
                              children: [
                                Row(
                                  children: [
                                    // 使用 Visibility 来控制三角形的可见性，同时保持其占用的空间
                                    SizedBox(
                                      width: 24, // 给定宽度以保证有足够的空间放置图标
                                      height: 24, // 给定高度以保证有足够的空间放置图标
                                      child: Visibility(
                                        visible: _controller.cat_id.value == _controller.catList[index].id,
                                        maintainSize: true,
                                        // 即使不可见也维持大小
                                        maintainAnimation: true,
                                        maintainState: true,
                                        child: const Icon(Icons.arrow_right, size: 24, color: Colours.base_primary), // 三角形图标
                                      ),
                                    ),

                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          CommonUtils.getSimpleText(_controller.catList[index].catName, 14, (_controller.cat_id.value == _controller.catList[index].id) ? Colours.base_primary : Colours.base_primary_text_title),
                                          CommonUtils.getSimpleText('${_controller.catList[index].skuTotal}个', 14, (_controller.cat_id.value == _controller.catList[index].id) ? Colours.base_primary : Colours.base_primary_text_caption),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                                Gaps.vGap4,
                                Gaps.line,
                              ],
                            ),
                          ),
                        );
                      },
                    )),
              )),
              Expanded(
                  flex: 2,
                  child: Column(
                    children: [
                      Container(
                        child: BrnSearchText(
                          innerPadding: EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                          maxHeight: 54,
                          textStyle: const TextStyle(
                            fontSize: 14,
                          ),
                          hintStyle: const TextStyle(fontSize: 14),
                          innerColor: Colors.white,
                          borderRadius: BorderRadius.all(Radius.circular(10)),
                          normalBorder: Border.all(color: Color(0xFFF0F0F0), width: 1, style: BorderStyle.solid),
                          activeBorder: Border.all(color: Colours.base_primary, width: 1, style: BorderStyle.solid),
                          onTextClear: () {
                            return false;
                          },
                          action: Container(),
                          onActionTap: () {},
                          onTextCommit: (text) {
                            // BrnToast.show('提交内容 : $text', context);
                          },
                          onTextChange: (text) {
                            _controller.search_cat_child_name.value = text;
                            _onRefresh();
                          },
                        ),
                      ),
                      Expanded(
                          child: Obx(() => MyRefreshListView(
                                onRefresh: _onRefresh,
                                loadMore: _loadMore,
                                hasMore: int.parse(_controller.totalChildNumber.value) > _controller.listChild.value.length,
                                shrinkWrap: true,
                                itemCount: _controller.listChild.value.length,
                                itemBuilder: (context, index) {
                                  return Column(
                                    children: [
                                      Gaps.line,
                                      Container(
                                        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                                        decoration: const BoxDecoration(
                                          color: Colors.white,
                                        ),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            LoadImage(
                                              _controller.listChild.value[index].coverUrl ?? '',
                                              width: 50,
                                              height: 50,
                                            ),
                                            Gaps.hGap4,
                                            Expanded(
                                                child: Column(
                                              crossAxisAlignment: CrossAxisAlignment.start,
                                              children: [
                                                Row(
                                                  children: [
                                                    Expanded(
                                                      child: CommonUtils.getSimpleText(_controller.listChild.value[index].productName, 14, Colours.base_primary_text_title),
                                                    ),
                                                    InkWell(
                                                      child: const LoadAssetImage(
                                                        "common/icon_approve_delete",
                                                        width: 26,
                                                        height: 26,
                                                      ),
                                                      onTap: () {
                                                        BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "是否要删除该物料", barrierDismissible: false, onConfirm: () {
                                                          Navigator.of(context, rootNavigator: true).pop();
                                                          _presenter.deleteSku(_controller.listChild.value[index].number);
                                                        }, onCancel: () {
                                                          Navigator.of(context, rootNavigator: true).pop();
                                                        });
                                                      },
                                                    )
                                                  ],
                                                ),
                                                CommonUtils.getSimpleText('${_controller.listChild.value[index].specification}', 14, Colours.base_primary_text_title),
                                              ],
                                            ))
                                          ],
                                        ),
                                      )
                                    ],
                                  );
                                },
                              ))),
                    ],
                  )),
            ],
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = MaterialManagerPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void delete() {
    BrnToast.show('操作成功', context);
    _presenter.getCatList();
  }
}
