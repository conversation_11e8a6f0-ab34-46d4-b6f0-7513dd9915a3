import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_project_entity.dart';
import '../bean/approve_role_entity.dart';

/**
 * 选择阿姨的列表
 */
class ProjectListView extends StatelessWidget {
  ApproveProjectList data;

  final Function onClick;

  ProjectListView({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick();
      },
      child: Column(
        children: [
          Gaps.line,
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(
              left: 16,
              right: 16,
            ), // 设置内边距为16.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Container(
                  height: 50,
                  margin: EdgeInsets.only(top: 10, bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      CommonUtils.getSimpleText(!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      Visibility(
                        child: CommonUtils.getSimpleText(data.projectName, 12, Colours.base_primary_text_caption),
                        visible: !TextUtil.isEmpty(data.projectName),
                      ),
                    ],
                  ),
                )),
                LoadAssetImage(
                  (data.isSelected == true) ? "icon_check" : "icon_uncheck",
                  width: 20,
                  height: 20,
                ),
              ],
            ),
          ),
          // Gaps.line
        ],
      ),
    );
  }
}
