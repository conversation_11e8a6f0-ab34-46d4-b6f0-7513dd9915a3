import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_record_entity.dart';
import '../bean/approve_role_entity.dart';

class ApproveRecordListView extends StatelessWidget {
  ApproveRecordList data;

  final Function onClick;
  final Function onActionClick;

  ApproveRecordListView({required this.data, required this.onClick, required this.onActionClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick();
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10, left: 10, right: 10),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4),
        ),
        // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.applicationTitle, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                CommonUtils.getSimpleText(data.applicationStatusName, 16, buildStatus(data), fontWeight: FontWeight.bold),
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            ListView.builder(
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: data.summaryList?.length,
                itemBuilder: (_, index) {
                  return Column(
                    children: [
                      Gaps.vGap2,
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText(data.summaryList?[index].item, 16, Colours.base_primary_text_title),
                          CommonUtils.getSimpleText(data.summaryList?[index].content, 16, Colours.base_primary_text_title),
                        ],
                      ),
                    ],
                  );
                }),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.createTime, 14, Colours.base_primary_text_title)),
                Visibility(
                  visible: data.applicationStatus == '0' || data.applicationStatus == '1',
                  child: InkWell(
                    child: Container(
                      decoration: BoxDecoration(
                        color: data.applicationStatus == '0' ? Colours.base_primary_un_select : Colours.base_primary,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      padding: const EdgeInsets.symmetric(vertical: 6, horizontal: 16),
                      child: CommonUtils.getSimpleText(data.applicationStatus == '0' ? '删除' : '撤回', 14, data.applicationStatus == '0' ? Colours.base_primary_text_title : Colours.white),
                    ),
                    onTap: () {
                      onActionClick();
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  //0是待完善 1审批中 2已通过 3已拒绝 4已撤回
  buildStatus(ApproveRecordList data) {
    if ('0' == data.applicationStatus) {
      return Colours.base_primary_purple;
    } else if ('1' == data.applicationStatus) {
      return Colours.base_primary_brown;
    } else if ('2' == data.applicationStatus) {
      return Colours.base_primary;
    } else if ('3' == data.applicationStatus) {
      return Colours.red;
    } else if ('4' == data.applicationStatus) {
      return Colours.base_primary;
    }
  }
}
