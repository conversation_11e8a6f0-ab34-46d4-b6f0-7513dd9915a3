import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_role_entity.dart';

/**
 * 选择阿姨的列表
 */
class RoleListView extends StatelessWidget {
  ApproveRoleList data;

  final Function onClick;

  RoleListView({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onClick();
      },
      child: Column(
        children: [
          Gaps.line,
          Container(
            color: Colors.white,
            padding: EdgeInsets.only(
              top: 10,
              left: 16,
              right: 16,
              bottom: 10,
            ), // 设置内边距为16.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText(data.name, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                LoadAssetImage(
                  (data.isSelected == true) ? "icon_check" : "icon_uncheck",
                  width: 20,
                  height: 20,
                ),
              ],
            ),
          ),
          // Gaps.line
        ],
      ),
    );
  }

  String buildStringFromItem(ProjectArchivesList item) {
    StringBuffer stringBuffer = StringBuffer();

    // if (item. != null && item.sexName.isNotEmpty) {
    //   stringBuffer.write(item.sexName);
    // }

    if (!TextUtil.isEmpty(item.jobName)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write(item.jobName);
    }

    if (!TextUtil.isEmpty(item.age)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.age}岁");
    }

    if (!TextUtil.isEmpty(item.workAge)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.workAge}年");
    }

    return stringBuffer.toString();
  }
}
