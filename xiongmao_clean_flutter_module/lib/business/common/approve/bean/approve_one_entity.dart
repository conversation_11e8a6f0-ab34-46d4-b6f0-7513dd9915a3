import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/template_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_one_entity.g.dart';
import 'dart:convert';

import '../../../../generated/json/material_cat_child_entity.g.dart';
import 'material_cat_child_entity.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_one_entity.g.dart';

@JsonSerializable()
class ApproveOneEntity {
	@JSONField(name: "application_title")
	String? applicationTitle;
	@JSONField(name: "application_status")
	String? applicationStatus;
	@JSONField(name: "application_status_name")
	String? applicationStatusName;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "remark")
	String? remark;
	@JSONField(name: "summary_list")
	List<ApproveOneSummaryList>? summaryList;
	@JSONField(name: "application_list")
	List<dynamic>? applicationList;
	@JSONField(name: "application_type")
	String? applicationType;
	@JSONField(name: "field_list")
	List<TemplateOneFieldList>? fieldList;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "material_sku")
	ApproveOneMaterialSku? materialSku;
	@JSONField(name: "sp_node_list")
	List<ApproveOneSpNodeList>? spNodeList;

	ApproveOneEntity();

	factory ApproveOneEntity.fromJson(Map<String, dynamic> json) => $ApproveOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveOneSummaryList {
	String? item;
	String? content;

	ApproveOneSummaryList();

	factory ApproveOneSummaryList.fromJson(Map<String, dynamic> json) => $ApproveOneSummaryListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveOneSummaryListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveOneFieldList {
	@JSONField(name: "field_form_name")
	String? fieldFormName;
	@JSONField(name: "field_name")
	String? fieldName;
	@JSONField(name: "field_type")
	String? fieldType;
	@JSONField(name: "field_value")
	List<String>? fieldValue;
	@JSONField(name: "table_list")
	List<dynamic>? tableList;
	@JSONField(name: "attachment_list")
	List<dynamic>? attachmentList;

	ApproveOneFieldList();

	factory ApproveOneFieldList.fromJson(Map<String, dynamic> json) => $ApproveOneFieldListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveOneFieldListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveOneMaterialSku {
	int? total;
	List<MaterialCatChildList>? list;

	ApproveOneMaterialSku();

	factory ApproveOneMaterialSku.fromJson(Map<String, dynamic> json) => $ApproveOneMaterialSkuFromJson(json);

	Map<String, dynamic> toJson() => $ApproveOneMaterialSkuToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveOneSpNodeList {
	String? uuid;
	@JSONField(name: "node_name")
	String? nodeName;
	@JSONField(name: "sub_node_name")
	String? subNodeName;
	@JSONField(name: "node_status")
	int? nodeStatus;
	@JSONField(name: "node_status_name")
	String? nodeStatusName;
	@JSONField(name: "user_list")
	List<dynamic>? userList;

	ApproveOneSpNodeList();

	factory ApproveOneSpNodeList.fromJson(Map<String, dynamic> json) => $ApproveOneSpNodeListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveOneSpNodeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

