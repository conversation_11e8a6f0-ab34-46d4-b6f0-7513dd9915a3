import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/material_cat_child_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/material_cat_child_entity.g.dart';

@JsonSerializable()
class MaterialCatChildEntity {
	int? page;
	int? size;
	int? total;
	List<MaterialCatChildList>? list;

	MaterialCatChildEntity();

	factory MaterialCatChildEntity.fromJson(Map<String, dynamic> json) => $MaterialCatChildEntityFromJson(json);

	Map<String, dynamic> toJson() => $MaterialCatChildEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MaterialCatChildList {
	String? number;
	@JSONField(name: "product_name")
	String? productName;
	String? specification;
	@JSONField(name: "unit_name")
	String? unitName;
	@JSONField(name: "cover_url")
	String? coverUrl;
	@JSONField(name: "material_category_id")
	String? materialCategoryId;
	int num = 0;
	bool check = false;

	MaterialCatChildList();

	factory MaterialCatChildList.fromJson(Map<String, dynamic> json) => $MaterialCatChildListFromJson(json);

	Map<String, dynamic> toJson() => $MaterialCatChildListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}