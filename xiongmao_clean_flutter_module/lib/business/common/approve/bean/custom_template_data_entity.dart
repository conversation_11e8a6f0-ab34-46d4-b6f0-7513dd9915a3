import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/custom_template_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/custom_template_data_entity.g.dart';

@JsonSerializable()
class CustomTemplateDataEntity {
  String? key;
  List<String> value = [];
  @JSONField(name: "attachment_list")
  List<CustomTemplateDataAttachmentList> attachmentList = [];
  @JSONField(name: "table_list")
  List<List<CustomTemplateDataEntity>> tableList = [];

  CustomTemplateDataEntity();

  factory CustomTemplateDataEntity.fromJson(Map<String, dynamic> json) => $CustomTemplateDataEntityFromJson(json);

  Map<String, dynamic> toJson() => $CustomTemplateDataEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CustomTemplateDataAttachmentList {
  @JSONField(name: "file_name")
  String? fileName;
  @JSONField(name: "file_url")
  String? fileUrl;

  CustomTemplateDataAttachmentList();

  factory CustomTemplateDataAttachmentList.fromJson(Map<String, dynamic> json) => $CustomTemplateDataAttachmentListFromJson(json);

  Map<String, dynamic> toJson() => $CustomTemplateDataAttachmentListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
