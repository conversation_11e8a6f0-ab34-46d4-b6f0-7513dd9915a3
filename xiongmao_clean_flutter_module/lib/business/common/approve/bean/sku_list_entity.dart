import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/sku_list_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/sku_list_entity.g.dart';

@JsonSerializable()
class SkuListEntity {
	String? number;
	String? num;

	SkuListEntity();

	factory SkuListEntity.fromJson(Map<String, dynamic> json) => $SkuListEntityFromJson(json);

	Map<String, dynamic> toJson() => $SkuListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}