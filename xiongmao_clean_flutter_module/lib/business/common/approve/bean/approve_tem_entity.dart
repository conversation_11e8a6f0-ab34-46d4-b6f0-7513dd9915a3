import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_tem_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_tem_entity.g.dart';

@JsonSerializable()
class ApproveTemEntity {
	int? page;
	int? size;
	int? total;
	List<ApproveTemList>? list;

	ApproveTemEntity();

	factory ApproveTemEntity.fromJson(Map<String, dynamic> json) => $ApproveTemEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveTemEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveTemList {
	String? uuid;
	String? icon;
	@JSONField(name: "template_status")
	String? templateStatus;
	@JSONField(name: "template_status_name")
	String? templateStatusName;
	@JSONField(name: "is_system")
	String? isSystem;
	@J<PERSON><PERSON>ield(name: "is_system_name")
	String? isSystemName;
	String? introduction;
	@JSONField(name: "template_name")
	String? templateName;

	ApproveTemList();

	factory ApproveTemList.fromJson(Map<String, dynamic> json) => $ApproveTemListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveTemListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}