import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_icon_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_icon_entity.g.dart';

@JsonSerializable()
class ApproveIconEntity {
	List<ApproveIconList>? list;

	ApproveIconEntity();

	factory ApproveIconEntity.fromJson(Map<String, dynamic> json) => $ApproveIconEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveIconEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveIconList {
	String? name;
	String? icon;

	ApproveIconList();

	factory ApproveIconList.fromJson(Map<String, dynamic> json) => $ApproveIconListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveIconListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}