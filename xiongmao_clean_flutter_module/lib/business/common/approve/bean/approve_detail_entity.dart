import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_detail_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_detail_entity.g.dart';

@JsonSerializable()
class ApproveDetailEntity {
	String? uuid = '';
	@JSONField(name: "template_name")
	String? templateName = '';
	List<ApproveDetailList>? list = [];

	ApproveDetailEntity();

	factory ApproveDetailEntity.fromJson(Map<String, dynamic> json) => $ApproveDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveDetailList {
	String? uuid = '';
	@JSONField(name: "node_name")
	String? nodeName = '';
	@JSONField(name: "sub_node_name")
	String? subNodeName = '';
	@JSONField(name: "node_type")
	String? nodeType = '';
	@JSONField(name: "approver_type")
	String? approverType = '';
	@JSONField(name: "is_cosigned")
	String? isCosigned = '';
	@JSONField(name: "superiors_level")
	String? superiorsLevel = '';
	@JSONField(name: "superiors_level_name")
	String? superiorsLevelName = '';
	@JSONField(name: "role_id")
	String? roleId = '';
	@JSONField(name: "role_name")
	String? roleName = '';
	@JSONField(name: "user_list")
	List<ApproveDetailListUserList>? userList = [];
	@JSONField(name: "user_id_list")
	List<String>? userIdList = [];
	ApproveDetailList();

	factory ApproveDetailList.fromJson(Map<String, dynamic> json) => $ApproveDetailListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveDetailListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveDetailListUserList {
	String? uuid = '';
	@JSONField(name: "user_name")
	String? userName = '';

	ApproveDetailListUserList();

	factory ApproveDetailListUserList.fromJson(Map<String, dynamic> json) => $ApproveDetailListUserListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveDetailListUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}