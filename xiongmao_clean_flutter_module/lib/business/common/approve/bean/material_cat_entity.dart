import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/material_cat_entity.g.dart';
import 'dart:convert';

import 'approve_one_entity.dart';
import 'material_cat_child_entity.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/material_cat_entity.g.dart';

@JsonSerializable()
class MaterialCatEntity {
  List<MaterialCatList>? list;

  MaterialCatEntity();

  factory MaterialCatEntity.fromJson(Map<String, dynamic> json) => $MaterialCatEntityFromJson(json);

  Map<String, dynamic> toJson() => $MaterialCatEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class MaterialCatList {
  String? id;
  @JSONField(name: "cat_name")
  String? catName;
  @JSONField(name: "sku_total")
  int? skuTotal;
  List<MaterialCatChildList> childList = [];
  int count = 0;

  MaterialCatList();

  factory MaterialCatList.fromJson(Map<String, dynamic> json) => $MaterialCatListFromJson(json);

  Map<String, dynamic> toJson() => $MaterialCatListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
