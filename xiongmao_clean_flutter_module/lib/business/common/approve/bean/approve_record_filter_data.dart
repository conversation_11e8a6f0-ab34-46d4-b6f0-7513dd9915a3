import 'dart:convert';

class ApproveRecordFilterData {
  String? startDate;
  String? picStatus;
  String? commitUuid;
  String? commitName;
  bool? isReset;

  ApproveRecordFilterData({
    this.startDate = '',
    this.picStatus = '',
    this.commitUuid = '',
    this.commitName = '',
    this.isReset = false,
  });

  factory ApproveRecordFilterData.fromJson(Map<String, dynamic> json) {
    return ApproveRecordFilterData(
      startDate: json['start_date'],
      picStatus: json['pic_status'],
      commitUuid: json['commit_uuid'],
      commitName: json['commit_name'],
      isReset: json['isReset'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'start_date': startDate,
      'pic_status': picStatus,
      'commit_uuid': commitUuid,
      'commit_name': commitName,
      'isReset': isReset,
    };
  }
}
