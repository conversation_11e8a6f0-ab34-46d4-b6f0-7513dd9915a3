import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_record_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_record_entity.g.dart';

@JsonSerializable()
class ApproveRecordEntity {
	int? page;
	int? size;
	int? total;
	List<ApproveRecordList>? list;

	ApproveRecordEntity();

	factory ApproveRecordEntity.fromJson(Map<String, dynamic> json) => $ApproveRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveRecordList {
	@JSONField(name: "application_title")
	String? applicationTitle;
	@JSONField(name: "application_no")
	String? applicationNo;
	@JSONField(name: "application_type")
	String? applicationType;
	@JSONField(name: "application_status")
	String? applicationStatus;
	@JSONField(name: "application_status_name")
	String? applicationStatusName;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "summary_list")
	List<ApproveRecordListSummaryList>? summaryList;
	@JSONField(name: "task_id")
	int? taskId;
	@JSONField(name: "read_status")
	String? readStatus;
	@JSONField(name: "template_uuid")
	String? templateUuid;
	@JSONField(name: "template_type")
	String? templateType;
	@JSONField(name: "is_system")
	String? isSystem;
	@JSONField(name: "template_name")
	String? templateName;

	ApproveRecordList();

	factory ApproveRecordList.fromJson(Map<String, dynamic> json) => $ApproveRecordListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveRecordListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveRecordListSummaryList {
	String? item;
	String? content;

	ApproveRecordListSummaryList();

	factory ApproveRecordListSummaryList.fromJson(Map<String, dynamic> json) => $ApproveRecordListSummaryListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveRecordListSummaryListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}