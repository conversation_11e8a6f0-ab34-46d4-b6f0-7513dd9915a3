import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_role_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_role_entity.g.dart';

@JsonSerializable()
class ApproveRoleEntity {
	List<ApproveRoleList>? list;

	ApproveRoleEntity();

	factory ApproveRoleEntity.fromJson(Map<String, dynamic> json) => $ApproveRoleEntityFromJson(json);

	Map<String, dynamic> toJson() => $ApproveRoleEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ApproveRoleList {
	String? id;
	String? name;
	@JSONField(name: "role_desc")
	String? roleDesc;
	@JSONField(name: "is_selected")
	bool isSelected = false;

	ApproveRoleList();

	factory ApproveRoleList.fromJson(Map<String, dynamic> json) => $ApproveRoleListFromJson(json);

	Map<String, dynamic> toJson() => $ApproveRoleListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}