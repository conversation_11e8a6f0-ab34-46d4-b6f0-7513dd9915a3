import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/template_one_entity.g.dart';
import 'dart:convert';

import 'approve_detail_entity.dart';
import 'custom_template_data_entity.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/template_one_entity.g.dart';

@JsonSerializable()
class TemplateOneEntity {
  String? uuid;
  @JSONField(name: "template_name")
  String? templateName;
  String? icon;
  String? introduction;
  @J<PERSON><PERSON>ield(name: "template_status")
  String? templateStatus;
  @J<PERSON>NField(name: "is_system")
  String? isSystem;
  @JSONField(name: "manager_user_list")
  List<TemplateOneManagerUserList>? managerUserList;
  @JSONField(name: "radius_user_list")
  List<TemplateOneRadiusUserList>? radiusUserList;
  @JSONField(name: "radius_role_list")
  List<TemplateOneRadiusRoleList>? radiusRoleList;
  @JSONField(name: "radius_project_list")
  List<TemplateOneRadiusProjectList>? radiusProjectList;
  @JSONField(name: "field_list")
  List<TemplateOneFieldList>? fieldList;

  // List<TemplateOneList>? list;
  List<ApproveDetailList>? list;

  TemplateOneEntity();

  factory TemplateOneEntity.fromJson(Map<String, dynamic> json) => $TemplateOneEntityFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneManagerUserList {
  String? uuid;
  @JSONField(name: "user_name")
  String? userName;

  TemplateOneManagerUserList();

  factory TemplateOneManagerUserList.fromJson(Map<String, dynamic> json) => $TemplateOneManagerUserListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneManagerUserListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneRadiusUserList {
  String? uuid;
  @JSONField(name: "user_name")
  String? userName;

  TemplateOneRadiusUserList();

  factory TemplateOneRadiusUserList.fromJson(Map<String, dynamic> json) => $TemplateOneRadiusUserListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneRadiusUserListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneRadiusRoleList {
  String? id;
  @JSONField(name: "role_name")
  String? roleName;

  TemplateOneRadiusRoleList();

  factory TemplateOneRadiusRoleList.fromJson(Map<String, dynamic> json) => $TemplateOneRadiusRoleListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneRadiusRoleListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneRadiusProjectList {
  String? uuid;
  @JSONField(name: "project_short_name")
  String? projectShortName;

  TemplateOneRadiusProjectList();

  factory TemplateOneRadiusProjectList.fromJson(Map<String, dynamic> json) => $TemplateOneRadiusProjectListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneRadiusProjectListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneFieldList {
  @JSONField(name: "field_form_name")
  String? fieldFormName;
  @JSONField(name: "field_name")
  String? fieldName;
  @JSONField(name: "field_type")
  String? fieldType;
  @JSONField(name: "is_must")
  String? isMust;
  @JSONField(name: "is_abstract")
  String? isAbstract;
  @JSONField(name: "prompt_text")
  String? promptText;
  @JSONField(name: "format_type")
  String? formatType;
  @JSONField(name: "is_must_camera_shoot")
  String? isMustCameraShoot;
  @JSONField(name: "unit_name")
  String? unitName;
  @JSONField(name: "option_list")
  List<String> option_list = [];
  @JSONField(name: "field_value")
  List<String> fieldValue = [];
  @JSONField(name: "attachment_list")
  List<CustomTemplateDataAttachmentList> attachmentList = [];

  List<TemplateOneFieldList> child = [];
  @JSONField(name: "table_list")
  List<List<TemplateOneFieldList>>? tableNewList = [];//这里是详情回来的
  List<TemplateOneFieldList> tableList = [];
  TemplateOneFieldList? tableTemplateOriginal;

  TemplateOneFieldList();

  factory TemplateOneFieldList.fromJson(Map<String, dynamic> json) => $TemplateOneFieldListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneFieldListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class TemplateOneList {
  String? uuid;
  @JSONField(name: "node_name")
  String? nodeName;
  @JSONField(name: "sub_node_name")
  String? subNodeName;
  @JSONField(name: "node_type")
  String? nodeType;
  @JSONField(name: "approver_type")
  String? approverType;
  @JSONField(name: "is_cosigned")
  int? isCosigned;
  @JSONField(name: "superiors_level")
  String? superiorsLevel;
  @JSONField(name: "role_id")
  String? roleId;
  @JSONField(name: "superiors_level_name")
  String? superiorsLevelName;
  @JSONField(name: "role_name")
  String? roleName;
  @JSONField(name: "user_list")
  List<dynamic>? userList;

  TemplateOneList();

  factory TemplateOneList.fromJson(Map<String, dynamic> json) => $TemplateOneListFromJson(json);

  Map<String, dynamic> toJson() => $TemplateOneListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
