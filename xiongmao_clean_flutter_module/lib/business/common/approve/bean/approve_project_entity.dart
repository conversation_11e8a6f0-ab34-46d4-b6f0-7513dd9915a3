import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/approve_project_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/approve_project_entity.g.dart';

@JsonSerializable()
class ApproveProjectEntity {
  List<ApproveProjectList>? list;

  ApproveProjectEntity();

  factory ApproveProjectEntity.fromJson(Map<String, dynamic> json) => $ApproveProjectEntityFromJson(json);

  Map<String, dynamic> toJson() => $ApproveProjectEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class ApproveProjectList {
  String? uuid;
  @JSONField(name: "project_name")
  String? projectName;
  @JSONField(name: "project_short_name")
  String? projectShortName;
  @JSONField(name: "contract_human_num")
  String? contractHumanNum;
  @JSONField(name: "total_job_num")
  String? totalJobNum;
  @JSONField(name: "on_job_num")
  String? onJobNum;
  @JSONField(name: "is_selected")
  bool isSelected = false;

  ApproveProjectList();

  factory ApproveProjectList.fromJson(Map<String, dynamic> json) => $ApproveProjectListFromJson(json);

  Map<String, dynamic> toJson() => $ApproveProjectListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
