import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../quality_service/bean/base_choose_string.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_project_entity.dart';
import '../bean/approve_role_entity.dart';

class AppRoveOptionController extends GetxController {
  var position = 0.obs;

  var project_uuid = ''.obs;

  var uuids = ''.obs;


  var keyword = ''.obs; // 关键字搜索


  //成员列表
  var list = <ProjectArchivesList>[].obs;

  void initMyList(List<ProjectArchivesList> value) {
    Set<String> uuidSet = {}; // 使用 Set 存储 dataList 中的 uuid

    if (!TextUtil.isEmpty(uuids.value)) {
      List<String> dataList = uuids.value.split(',');
      uuidSet.addAll(dataList); // 将 dataList 中的 uuid 添加到 Set 中
    }

    if (uuidSet.length > 0) {
      for (ProjectArchivesList data in value) {
        if (uuidSet.contains(data.uuid)) {
          data.isSelected = true;
        }
      }
    }

    list.value = value.toList();
  }

  void updateMyList(List<ProjectArchivesList> value) {
    Set<String> uuidSet = {}; // 使用 Set 存储 dataList 中的 uuid

    if (!TextUtil.isEmpty(uuids.value)) {
      List<String> dataList = uuids.value.split(',');
      uuidSet.addAll(dataList); // 将 dataList 中的 uuid 添加到 Set 中
    }

    if (uuidSet.length > 0) {
      for (ProjectArchivesList data in value) {
        if (uuidSet.contains(data.uuid)) {
          data.isSelected = true;
        }
      }
    }

    list.value.addAll(value);
    list.value = list.value.toList();
  }

  //角色列表
  var roleList = <ApproveRoleList>[].obs;

  void initRoleList(List<ApproveRoleList> value) {
    Set<String> uuidSet = {}; // 使用 Set 存储 dataList 中的 uuid

    if (!TextUtil.isEmpty(uuids.value)) {
      List<String> dataList = uuids.value.split(',');
      uuidSet.addAll(dataList); // 将 dataList 中的 uuid 添加到 Set 中
    }

    if (uuidSet.length > 0) {
      for (ApproveRoleList data in value) {
        if (uuidSet.contains(data.id)) {
          data.isSelected = true;
        }
      }
    }
    roleList.value = value.toList();
  }

  void updateRoleList(List<ApproveRoleList> value) {
    roleList.value.addAll(value);
    roleList.value = roleList.value.toList();
  }

  //角色列表
  var projectList = <ApproveProjectList>[].obs;

  void initProjectList(List<ApproveProjectList> value) {
    Set<String> uuidSet = {}; // 使用 Set 存储 dataList 中的 uuid

    if (!TextUtil.isEmpty(uuids.value)) {
      List<String> dataList = uuids.value.split(',');
      uuidSet.addAll(dataList); // 将 dataList 中的 uuid 添加到 Set 中
    }

    if (uuidSet.length > 0) {
      for (ApproveProjectList data in value) {
        if (uuidSet.contains(data.uuid)) {
          data.isSelected = true;
        }
      }
    }
    projectList.value = value.toList();
  }

  void updateProjectList(List<ApproveProjectList> value) {
    projectList.value.addAll(value);
    projectList.value = projectList.value.toList();
  }

  var totalNumber = "0".obs;

  void updateTotal(String? total) {
    totalNumber.value = total ?? "";
  }
}
