import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/material_cat_child_entity.g.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import '../bean/approve_one_entity.dart';
import '../bean/material_cat_child_entity.dart';

class MaterialTemplateController extends GetxController {
  var projectUuid = "".obs;
  var projectName = "".obs;

  //备注
  var remark = "".obs;

  var listChild = <MaterialCatChildList>[].obs;

  void updateChildList(List<MaterialCatChildList> newChilds) {
    // 创建一个新的 Map 来存储新数据中的项，以便快速查找
    final Map<String, MaterialCatChildList> newItemsMap = {};
    for (var newItem in newChilds) {
      if (newItem.number != null && newItem.num > 0) {
        newItemsMap[newItem.number!] = newItem;
      }
    }

    // 创建一个临时列表来存储更新后的项
    final List<MaterialCatChildList> updatedList = [];

    // 遍历 listChild 并更新或保留项
    for (var oldItem in listChild.value) {
      if (oldItem.number != null && newItemsMap.containsKey(oldItem.number)) {
        // 如果存在相同 number 的项，使用 newChilds 中的 count 更新该项
        var newItem = newItemsMap[oldItem.number]!;
        updatedList.add(newItem.copyWith(num: newItem.num)); // 确保 count 正确更新
        newItemsMap.remove(oldItem.number); // 移除已经处理过的项
      } else if (oldItem.number == null || !newItemsMap.containsKey(oldItem.number)) {
        // 如果没有对比到 number，则删除 listChild 中的数据
        print('Removing item with number ${oldItem.number}');
        continue; // 跳过不添加到 updatedList
      } else {
        // 否则保留旧项（理论上这里不会执行，因为前面的条件已经覆盖了所有情况）
        updatedList.add(oldItem);
      }
    }

    // 添加剩余的新项（即那些在 newChilds 中但在 listChild 中不存在的项）
    updatedList.addAll(newItemsMap.values);

    // 更新 listChild
    listChild.value = updatedList;

    print('Updated listChild length: ${listChild.length}');
  }

  void notifyList() {
    for (MaterialCatChildList item in listChild) {
      if (item.num == 0) {
        listChild.remove(item);
      }
    }
    listChild.refresh();
    print('Updated : $listChild');
  }

  ///处理详情
  void getApproveOne(ApproveOneEntity data) {
    projectUuid.value = data.projectUuid ?? httpConfig.project_uuid;
    projectName.value = data.projectName ?? httpConfig.project_name;
    remark.value = data.remark ?? '';
    if (data.materialSku != null) {
      listChild.value = data.materialSku?.list ?? [];
      listChild.refresh();
    }
  }
}
