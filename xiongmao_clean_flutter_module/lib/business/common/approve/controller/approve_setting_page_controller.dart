import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_role_all_entity.dart';


class ApproveSettingPageController extends GetxController{
  var superiorsLevelList = <SuperiorsLevelInfo>[].obs;

  var roleList = <GetRoleAllList>[].obs;
  void updateList(List<GetRoleAllList> value) {
    roleList.value = value;
  }

  var templateName = "".obs;
  void updateName(String? value) {
    templateName.value = value ?? "";
  }

  var roleNameList = <String>[].obs;
  void updateRoleNameList(List<String> value) {
    roleNameList.value = value;
  }

  var superiorsLevelListNameList = <String>[].obs;
  void updateSuperiorsLevelListNameList(List<String> value) {
    superiorsLevelListNameList.value = value;
  }
}