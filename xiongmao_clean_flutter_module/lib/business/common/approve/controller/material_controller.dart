import 'package:get/get.dart';
import '../bean/approve_one_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';

class MaterialController extends GetxController {
  //搜索的key
  var search_cat_child_name = "".obs;

  //项目的project_uuid
  var project_uuid = "".obs;

  //当前的catid
  var cat_id = "".obs;



  final RxMap<String, MaterialCatChildList> selectedCounts = <String, MaterialCatChildList>{}.obs;
  final RxInt totalSelectedCount = 0.obs;
  var catList = <MaterialCatList>[].obs;
  var listChild = <MaterialCatChildList>[].obs;

// 初始化 categoryCounts，用于统计每个分类下的选中项数量
  Map<String, int> categoryCountsFinally = {};

  void updateList(List<MaterialCatList> value) {
    // 创建一个新的 '全部' 条目
    MaterialCatList materialCatList = MaterialCatList()
      ..id = ""
      ..catName = "全部";

    // 创建一个新的列表，首先添加 '全部' 项，然后添加原来的 value 列表的所有元素
    List<MaterialCatList> updatedValue = [materialCatList, ...value];

    // 更新 list.value
    catList.value = updatedValue;
  }

  void updateChildList(List<MaterialCatChildList> newValue) {
    // 遍历新的 listChild 并应用已有的 selectedCounts 中的状态
    for (var child in newValue) {
      if (child.number != null && selectedCounts.containsKey(child.number)) {
        // 如果该子项的 materialCategoryId 存在于 selectedCounts 中，则恢复其 count
        child.num = selectedCounts[child.number]!.num;
      } else {
        // 否则初始化为0
        child.num = 0;
      }
    }

    // 更新 listChild 的值并强制刷新以触发 UI 更新
    listChild.value = List.from(newValue);
    if(selectedCounts.isNotEmpty){
      notifyChildList();
    }
  }

  void notifyChildList() {
    final Map<String, int> categoryCounts = {};

    // 清空 categoryCountsFinally，避免残留数据影响统计
    categoryCountsFinally.clear();

    // 计算并更新每个 catId 的已选数量（即选中项的数量），基于所有 selectedCounts 数据
    for (var entry in selectedCounts.entries) {
      final child = entry.value;
      if (child.materialCategoryId != null && child.num > 0) {
        // 只统计 count > 0 的子项
        final catId = child.materialCategoryId!;
        if (!categoryCounts.containsKey(catId)) {
          categoryCounts[catId] = 0;
        }
        categoryCounts[catId] = categoryCounts[catId]! + 1; // 每个符合条件的子项增加1
      }
    }

    // 更新 categoryCountsFinally
    categoryCountsFinally.addAll(categoryCounts);

    // 更新“全部”分类的已选数量（即所有分类中选中项的总数）
    totalSelectedCount.value = categoryCountsFinally.values.fold(0, (sum, count) => sum + count);

    // 更新 catList 中每个分类的 count 属性
    for (var category in catList) {
      if (category.id == "") {
        // 特殊处理“全部”分类
        category.count = totalSelectedCount.value;
      } else {
        category.count = categoryCountsFinally[category.id] ?? 0;
      }
    }

    // 强制刷新 catList 以触发 UI 更新
    catList.refresh();
    listChild.refresh();
  }

  void updateChildCount(MaterialCatChildList child, int count) {
    // 更新子项的 count
    child.num = count;
    // 更新 selectedCounts 中的记录
    selectedCounts[child.number!] = child.copyWith(num: count);
    // 通知重新统计
    notifyChildList();
  }

  var totalChildNumber = "0".obs;

  void updateChildTotal(String? total) {
    totalChildNumber.value = total ?? "";
  }
}
