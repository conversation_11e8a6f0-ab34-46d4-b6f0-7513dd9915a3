import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';


class ApproveEntryDepartController extends GetxController{
  var list = <ApproveDetailList>[].obs;
  void updateList(List<ApproveDetailList> value) {
    value.forEach((element) {
      element.userIdList =  element.userList?.map((e) => (e.uuid??"")).toList();
    });
    list.value = value;
  }

  var templateName = "".obs;

  void updateName(String? value) {
    templateName.value = value ?? "";
  }
}