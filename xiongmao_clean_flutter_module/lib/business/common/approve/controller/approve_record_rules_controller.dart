import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../project/bean/project_manager_entity.dart';
import '../../quality_service/bean/base_choose_string.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../bean/approve_record_entity.dart';
import '../bean/approve_record_filter_data.dart';
import '../bean/approve_tem_entity.dart';

/// 审批记录筛选
class ApproveRecordRulesController extends GetxController {
  ///这里是筛选
  var roleList = [
    BaseChooseString("待完善"),
    BaseChooseString("审批中"),
    BaseChooseString("已通过"),
    BaseChooseString("已拒绝"),
    BaseChooseString("已撤回"),
  ].obs;

  ///审批状态
  var statusIndex = (-1).obs;

  ///入职的日期
  var entryDateList = [
    BaseChooseString("上周"),
    BaseChooseString("本周"),
    BaseChooseString("本月"),
    BaseChooseString("上月"),
  ].obs;

  ///自定义日期的
  var entryIndex = (-1).obs;

  ///入职日期
  var customEntryDate = '自定义日期范围'.obs;
  var customEntryStartDate = ''.obs;
  var customEntryEndDate = ''.obs;
  DateTime customEntryStartTime = DateTime.now();
  DateTime customEntryEndTime = DateTime.now();

  ///考勤规则
  var commitUuid = ''.obs;
  var commitName = '请选择'.obs;

  void invertData(ApproveRecordFilterData filterData) {
    //入职日期
    customEntryDate.value = invertEntryDate(filterData.startDate ?? '');

    //考勤规则
    commitUuid.value = filterData.commitUuid ?? '';
    commitName.value = (TextUtil.isEmpty(filterData.commitName)) ? '请选择' : filterData.commitName ?? '请选择';

    //审批状态
    if (!TextUtil.isEmpty(filterData.picStatus)) {
      statusIndex.value = int.parse(filterData.picStatus!);
    }
  }

  ///自定义日期范围
  String invertEntryDate(String value) {
    if (value.isEmpty) {
      return '自定义日期范围';
    } else {
      if (value.contains('#')) {
        return value;
      } else {
        switch (value) {
          case 'lastWeek':
            entryIndex.value = 0;
          case 'thisWeek':
            entryIndex.value = 1;
          case 'thisMonth':
            entryIndex.value = 2;
          case 'nextMonth':
            entryIndex.value = 3;
          default:
            entryIndex.value = -1;
        }
        return '自定义日期范围';
      }
    }
  }
}
