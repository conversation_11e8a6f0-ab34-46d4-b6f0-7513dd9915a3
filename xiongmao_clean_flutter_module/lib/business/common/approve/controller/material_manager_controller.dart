import 'package:get/get.dart';
import '../bean/approve_one_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';

class MaterialManagerController extends GetxController {
  //搜索的key
  var search_cat_child_name = "".obs;

  //项目的project_uuid
  var project_uuid = "".obs;

  //当前的catid
  var cat_id = "".obs;

  var catList = <MaterialCatList>[].obs;

  void updateList(List<MaterialCatList> value) {
    int countAll = value.fold(0, (sum, item) => sum + (item.skuTotal ?? 0));

    // 创建一个新的 '全部' 条目
    MaterialCatList materialCatList = MaterialCatList()
      ..id = ""
      ..skuTotal = countAll
      ..count = 0
      ..catName = "全部";

    // 创建一个新的列表，首先添加 '全部' 项，然后添加原来的 value 列表的所有元素
    List<MaterialCatList> updatedValue = [materialCatList, ...value];

    // 更新 list.value
    catList.value = updatedValue;
  }

  var listChild = <MaterialCatChildList>[].obs;

  void updateChildList(List<MaterialCatChildList> newValue) {
    listChild.value = List.from(newValue);
  }

  void notifyChildList() {
    // 强制刷新 catList 和 listChild 以触发 UI 更新
    catList.value = catList.toList();
    listChild.refresh();
  }

  void updateChildCount(MaterialCatChildList child, int count) {
    child.num = count;
    notifyChildList();
  }

  var totalChildNumber = "0".obs;

  void updateChildTotal(String? total) {
    totalChildNumber.value = total ?? "";
  }
}
