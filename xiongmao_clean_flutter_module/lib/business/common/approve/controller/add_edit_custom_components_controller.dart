import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../bean/approve_detail_entity.dart';
import '../bean/material_cat_entity.dart';
import '../bean/template_one_entity.dart';

class AddEditCustomComponentController extends GetxController {
  String TYPE_TEXT = "text"; //文本
  String TYPE_NUMBER = "number"; //number
  String TYPE_DATE = "date"; //日期
  String TYPE_OPTION = "single"; //单选、多选
  String TYPE_PICTURE = "picture"; //图片
  String TYPE_ATTACHMENT = "attachment"; //附件
  String TYPE_TABLE = "table"; //表格

  var isSystem = false.obs;

  ///标题名称
  var title = '添加自定义模版'.obs;

  ///模板名称
  var templateName = ''.obs;

  ///说明
  var templateRemark = ''.obs;

  ///监听审批记录管理的设置
  var approveRecordSettingValue = "".obs;

  ///审批记录设置的
  var approveEntryDepart = <ApproveDetailList>[].obs;

  ///审批记录设置的value
  var approveEntryDepartValue = ''.obs;

  ///监听审批记录管理的value
  var approveRecordValue = "".obs;

  ///这是审批记录的uuid
  var approveRecordUuidValue = "".obs;

  ///适用成员的value
  var approveMemberValue = "不限制".obs;

  ///这是成员的UUid
  var approveMemberUuidValue = "".obs;

  ///适用角色的value
  var approveRoleValue = "不限制".obs;

  ///这是角色的UUid
  var approveRoleUuidValue = "".obs;

  ///适用项目的value
  var approveProjectValue = "不限制".obs;

  ///这是项目的UUid
  var approveProjectUuidValue = "".obs;

  ///状态的标识
  var approveStatus = '停用'.obs;

  //创建一个已经创建好的数组，来存放单选的选项
  var fromTable = <TemplateOneFieldList>[].obs;

  //添加文字消息
  void addTableData(TemplateOneFieldList data) {
    fromTable.value.add(data);
    notifySingSelectList();
  }

  void notifySingSelectList() {
    fromTable.value = fromTable.value.toList();
  }

  //图标的logo
  var approveIcon = ''.obs;

  ///获取模板详情
  void getTemplateOne(TemplateOneEntity data) {
    if ("1" == data.isSystem) {
      title.value = '编辑系统模版';
      isSystem.value = true;
    } else {
      title.value = '编辑自定义模版';
      isSystem.value = false;
    }

    templateName.value = data.templateName ?? ''; //名称
    approveIcon.value = data.icon ?? ''; //图标
    templateRemark.value = data.introduction ?? ''; //说明

    //1启用 2禁用
    if ('2' == data.templateStatus) {
      approveStatus.value = '停用';
    } else {
      approveStatus.value = '启用';
    }

    //审批流程设置
    if (data.list!.isNotEmpty) {
      for (ApproveDetailList list in data.list!) {
        List<String> uuid = [];
        for (var userList in list.userList!) {
          uuid.add(userList.uuid ?? '');
        }
        list.userIdList = uuid;
      }
      approveEntryDepart.value = data.list!.cast<ApproveDetailList>();
      approveEntryDepartValue.value = '已设置';
      print('审批流程设置---${approveEntryDepart.length}');
    }
    //审批记录管理
    if (data.managerUserList!.isNotEmpty) {
      approveRecordValue.value = '已选择${data.managerUserList!.length}个人';
      String uuidProject = '';
      for (TemplateOneManagerUserList item in data.managerUserList!) {
        uuidProject += '${item.uuid},';
      }
      if (uuidProject.isNotEmpty) {
        uuidProject = uuidProject.substring(0, uuidProject.length - 1); // 去除最后一个逗号
      }
      approveRecordUuidValue.value = uuidProject;
    } else {
      approveRecordValue.value = '';
    }
    //适用成员
    if (data.radiusUserList!.isNotEmpty) {
      approveMemberValue.value = '已选择${data.radiusUserList!.length}人';
      String uuids = '';
      for (TemplateOneRadiusUserList item in data.radiusUserList!) {
        uuids += '${item.uuid},';
      }
      if (uuids.isNotEmpty) {
        uuids = uuids.substring(0, uuids.length - 1); // 去除最后一个逗号
      }
      approveMemberUuidValue.value = uuids;
    } else {
      approveMemberValue.value = '不限制';
    }
    //适用角色
    if (data.radiusRoleList!.isNotEmpty) {
      approveRoleValue.value = '已选择${data.radiusRoleList!.length}个角色';
      String uuidRoles = '';
      for (TemplateOneRadiusRoleList item in data.radiusRoleList!) {
        uuidRoles += '${item.id},';
      }
      if (uuidRoles.isNotEmpty) {
        uuidRoles = uuidRoles.substring(0, uuidRoles.length - 1); // 去除最后一个逗号
      }
      approveRoleUuidValue.value = uuidRoles;
    } else {
      approveRoleValue.value = '不限制';
    }
    //适用项目
    if (data.radiusProjectList!.isNotEmpty) {
      approveProjectValue.value = '已选择${data.radiusProjectList!.length}个项目';
      String uuidProject = '';
      for (TemplateOneRadiusProjectList item in data.radiusProjectList!) {
        uuidProject += '${item.uuid},';
      }
      if (uuidProject.isNotEmpty) {
        uuidProject = uuidProject.substring(0, uuidProject.length - 1); // 去除最后一个逗号
      }
      approveProjectUuidValue.value = uuidProject;
    } else {
      approveProjectValue.value = '不限制';
    }

    //表格里面 针对有single item list的，需要把这个single 传递个child  接口编辑提交的时候 是child 拿数据的是child 是表格，single_item_list是单选的选项
    if (data.fieldList!.isNotEmpty) {
      fromTable.value = data.fieldList!;
    }
  }
}
