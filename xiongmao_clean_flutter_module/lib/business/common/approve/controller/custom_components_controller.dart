import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../quality_service/bean/base_choose_string.dart';

class CustomComponentController extends GetxController {
  String TYPE_TEXT = "text"; //文本
  String TYPE_NUMBER = "number"; //number
  String TYPE_DATE = "date"; //日期
  String TYPE_OPTION = "single"; //单选、多选
  String TYPE_PICTURE = "picture"; //图片
  String TYPE_ATTACHMENT = "attachment"; //附件
  String TYPE_TABLE = "table"; //表格

  //可选类型
  var settingTagWights = [
    BaseChooseString("文本"),
    BaseChooseString("数值"),
    BaseChooseString("日期"),
    BaseChooseString("单选"),
    BaseChooseString("图片"),
    BaseChooseString("附件"),
    BaseChooseString("表格"),
  ].obs;

  //样式的设置
  var styleWights = [
    BaseChooseString("单行"),
    BaseChooseString("多行"),
  ].obs;

  //数值的设置
  var dateWights = [
    BaseChooseString("年-月-日"),
    BaseChooseString("年-月-日 时:分"),
  ].obs;

  //日期的设置
  var unitWights = [
    BaseChooseString("0"),
    BaseChooseString("1"),
    BaseChooseString("2"),
  ].obs;

  //下标选项
  var indexTag = 0.obs;

  //创建一个已经创建好的数组，来存放单选的选项
  var singSelectStr = <String>[].obs;

  //添加文字消息
  void addTextData(String data) {
    singSelectStr.value.add(data);
    notifySingSelectList();
  }

  void notifySingSelectList() {
    singSelectStr.value = singSelectStr.value.toList();
  }

  //是否必填写
  var isRequire = false.obs;

  //是否摘要
  var isRequireAbstract = false.obs;

  //是否相机拍摄
  var isRequireCamera = false.obs;

  //字段名称
  var customName = "".obs;

  //提示文字
  var tipsText = "".obs;

  //tag选中的文字，建议用文字对比
  var tagText = '文本'.obs;

  //单位
  var unit = ''.obs;

  //文本样式的Style 年月日  小位数，都用这一个
  var tagTextStyle = '单行'.obs;

  //年月日
  var tagDate = '年-月-日'.obs;

  //小位数
  var tagUnit = '0'.obs;
}
