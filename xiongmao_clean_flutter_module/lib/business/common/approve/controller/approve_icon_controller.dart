import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../quality_service/bean/base_choose_string.dart';
import '../bean/approve_icon_entity.dart';
class ApproveIconController extends GetxController {


  var selected = ApproveIconList().obs;

  var list = <ApproveIconList>[].obs;

  void updateList(List<ApproveIconList> value) {
    list.value = value;
  }


}
