import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../project/bean/project_manager_entity.dart';
import '../../quality_service/bean/base_choose_string.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../bean/approve_record_entity.dart';
import '../bean/approve_record_filter_data.dart';
import '../bean/approve_tem_entity.dart';

/// 审批记录
class ApproveRecordController extends GetxController {
  ///记录当前的选择哪个uuid
  var templateUuid = "".obs;
  var templateName = "".obs;

  ///状态
  var status = "-1".obs;

  ///自定义的筛选
  var keyword = "".obs;

  ///考勤规则列表
  var list = <ApproveRecordList>[].obs;

  var totalNumber = 0.obs;

  void initMyList(int total, List<ApproveRecordList> value) {
    totalNumber.value = total;
    list.value = value.toList();
  }

  void updateMyList(List<ApproveRecordList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///这里是row
  var listRow = <BaseChooseString>[].obs;
  var originalListRow = <ApproveTemList>[].obs; // 存储原始数据

  void initRowMyList(List<ApproveTemList> value) {
    originalListRow.value = value.toList();

    ///然后拿originalListRow 中所有的name给listRow
    for (ApproveTemList item in originalListRow) {
      listRow.value.add(BaseChooseString(item.templateName));
    }
    listRow.refresh();
  }

  ///筛选的记录
  ApproveRecordFilterData? filterData;
}
