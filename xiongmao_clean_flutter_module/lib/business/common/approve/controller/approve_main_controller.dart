import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../bean/approve_tem_entity.dart';

class ApproveMainController extends GetxController{
  var list = <ApproveTemList>[].obs;
  void updateList(List<ApproveTemList> value) {
    list.value = value;
  }

  var totalNumber = "0".obs;
  void updateTotal(String? total) {
    totalNumber.value = total ?? "";
  }
}