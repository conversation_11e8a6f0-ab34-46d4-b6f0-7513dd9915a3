import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../bean/approve_one_entity.dart';
import '../bean/template_one_entity.dart';

class ApproveTemplateController extends GetxController {
  ///表单的列表
  var fieldList = <TemplateOneFieldList>[].obs;

  /// 存储 TextEditController 的映射
  final Map<String, TextEditingController> textEditingControllers = {};

  //获取详情
  void getTemplateOne(TemplateOneEntity data) {
    if (data.fieldList!.isNotEmpty) {
      for (TemplateOneFieldList tableData in data.fieldList!) {
        //如果是表格的话，组装下数据 table 需要一个list
        if (tableData.fieldType == 'table') {
          //复制对象为新对象
          tableData.tableTemplateOriginal = TemplateOneFieldList.fromJson(tableData.toJson());

          if (tableData.tableList == null) {
            tableData.tableList = [];
            tableData.tableList.add(TemplateOneFieldList.fromJson(tableData.toJson()));
          } else {
            tableData.tableList.add(TemplateOneFieldList.fromJson(tableData.toJson()));
          }
        }
        if (!textEditingControllers.containsKey(tableData.fieldFormName)) {
          textEditingControllers[tableData.fieldFormName!] = TextEditingController(text: '');
        }
      }
      fieldList.value = data.fieldList?.toList() ?? [];
    }
  }

  void notifyFieldList() {
    fieldList.value = fieldList.toList();
  }

  //获取审批详情  把value 赋值给现有的
  void getApproveOne(ApproveOneEntity data) {
    if (data.fieldList!.isNotEmpty && fieldList.isNotEmpty) {
      for (var template in fieldList) {
        for (var field in data.fieldList!) {
          if (template.fieldFormName == field.fieldFormName) {
            template.fieldValue = field.fieldValue;
            print('类型  ${field.fieldType} ${field.fieldName} -- 有相同的 --- ${template.fieldFormName}  ---  value ${template.fieldValue}');
            switch (field.fieldType) {
              case 'text':
                if (template.formatType == '1') {
                  if (field.fieldValue.isNotEmpty) {
                    textEditingControllers[template.fieldFormName!]?.text = field.fieldValue[0];
                  }
                }
                break;
              case 'number':
                if (field.fieldValue.isNotEmpty) {
                  textEditingControllers[template.fieldFormName!]?.text = field.fieldValue[0];
                }
                break;
              case 'picture':
              case 'attachment':
                template.attachmentList = field.attachmentList;
                break;
              case 'table': //table
                // template.tableList = field.tableList;
                print('列表大小---> ${field.tableNewList!.length}');
                List<TemplateOneFieldList> conver = [];
                for (List<TemplateOneFieldList> datas in field.tableNewList!) {
                  for (TemplateOneFieldList child in datas) {
                    //在这里进行对比，然后再创建新的
                    for (TemplateOneFieldList ccc in template.tableList) {
                      if (ccc.fieldFormName == child.fieldFormName) {
                        ccc.fieldValue = child.fieldValue;
                        print('类型  ${child.fieldType} ${child.fieldName} -- 有相同的 --- ${template.fieldFormName}  ---  value ${template.fieldValue}');
                        switch (child.fieldType) {
                          case 'text':
                            if (ccc.formatType == '1') {
                              if (child.fieldValue.isNotEmpty) {
                                textEditingControllers[ccc.fieldFormName!]?.text = child.fieldValue[0];
                              }
                            }
                            break;
                          case 'number':
                            if (child.fieldValue.isNotEmpty) {
                              textEditingControllers[ccc.fieldFormName!]?.text = child.fieldValue[0];
                            }
                            break;
                          case 'picture':
                          case 'attachment':
                            ccc.attachmentList = child.attachmentList;
                            break;
                        }
                      }
                      conver.add(ccc);
                    }
                  }
                }
                template.child = conver;
                break;
            }
          }
        }
      }
    }
    notifyFieldList();
  }
}
