import 'dart:collection';
import 'dart:convert';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_option_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/approve_project_entity.dart';
import '../bean/approve_role_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../controller/approve_main_controller.dart';
import '../controller/approve_option_controller.dart';
import '../controller/material_controller.dart';
import '../iview/material_iview.dart';

class AppRoveOptionPresenter extends BasePagePresenter<AppRoveOptionView> {
  AppRoveOptionController controller;

  AppRoveOptionPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    request();
  }

  void loadMore() {
    _page++;
    request();
  }

  void request() {
    switch (controller.position.value) {
      case 0:
      case 1:
        getArchivesList();
        break;
      case 2:
        getRoleList();
        break;
      case 3:
        getProjectManagerList();
        break;
    }
  }

  /// 获取档案列表
  Future<dynamic> getArchivesList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "100";
    params['status'] = '1'; //在职
    if (controller.position.value == 0) {
      params['is_head_office'] = '1'; //是否总部员工1是 2否
    }
    if (!TextUtil.isEmpty(controller.keyword.value)) {
      params["keyword"] = controller.keyword.value; ////关键字
    }
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ProjectArchivesEntity>(Method.get, url: HttpApi.GET_ARCHIVES_ALL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        print('进来这里了');
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  /// 获取角色列表
  Future<dynamic> getRoleList() {
    var params = <String, String>{};
    params['type'] = '0'; //类型0全部 1总部员工角色 2项目成员角色

    return requestNetwork<ApproveRoleEntity>(Method.get, url: HttpApi.GET_COMPANY_ROLE_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initRoleList(data.list ?? []);
        } else {
          controller.updateRoleList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  /// 获取项目列表
  Future<dynamic> getProjectManagerList() {
    var params = <String, String>{};
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ApproveProjectEntity>(Method.get, url: HttpApi.GET_PROJECT_MANGER_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initProjectList(data.list ?? []);
        } else {
          controller.updateProjectList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }
}
