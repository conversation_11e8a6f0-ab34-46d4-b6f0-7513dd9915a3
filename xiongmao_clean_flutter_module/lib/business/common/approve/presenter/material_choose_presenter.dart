import 'dart:collection';
import 'dart:convert';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../controller/approve_main_controller.dart';
import '../controller/material_choose_controller.dart';
import '../controller/material_controller.dart';
import '../iview/material_iview.dart';

class MaterialChoosePresenter extends BasePagePresenter<MaterialView> {
  MaterialChooseController controller;

  MaterialChoosePresenter(this.controller);

  int _page = 1;

  void onRefresh(String cat_id) {
    _page = 1;
    _getCatChildList(cat_id);
  }

  void loadMore(String cat_id) {
    _page++;
    _getCatChildList(cat_id);
  }

  /// 物料cat
  Future<dynamic> getCatList() {
    return requestNetwork<MaterialCatEntity>(Method.get, url: HttpApi.GET_CATEGORY_LIST, params: null, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.updateList(data.list?.toList() ?? []);
        onRefresh('');
      }
    }, onError: (_, __) {
      MyLog.e("========加载失败========");
    });
  }

  /// 列表的子状态
  Future<dynamic> _getCatChildList(String cat_id) {
    var params = <String, String>{};
    params['cat_id'] = cat_id;
    if (!TextUtil.isEmpty(controller.search_cat_child_name.value)) {
      params['keyword'] = controller.search_cat_child_name.value;
    }
    params['project_uuid'] = controller.project_uuid.value;
    return requestNetwork<MaterialCatChildEntity>(Method.get, url: HttpApi.GET_CATEGORY_CHILD_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        MyLog.e("========加载==_page===" + (_page.toString()));
        controller.totalChildNumber.value = data.total.toString();
        if (_page == 1) {
          controller.updateChildList(data.list?.toList() ?? []);
        } else {
          var value = controller.listChild.value;
          value.addAll(data.list ?? []);
          controller.updateChildList(value.toList());
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========加载失败========");
      if (_page > 1) {
        _page--;
      }
    });
  }
}
