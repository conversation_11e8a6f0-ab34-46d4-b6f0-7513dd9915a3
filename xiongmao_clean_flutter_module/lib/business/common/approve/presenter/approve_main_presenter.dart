
import 'dart:convert';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/approve_tem_entity.dart';
import '../controller/approve_main_controller.dart';

class ApproveMainPresenter extends BasePagePresenter<ApproveMainView> {
  ApproveMainController controller;
  ApproveMainPresenter(this.controller);

  int _page = 1;
  void onRefresh(params) {
    _page = 1;
    _getList(params);
  }
  void loadMore(params) {
    _page++;
    _getList(params);
  }

  Future<dynamic> _getList(params) {
    return requestNetwork<ApproveTemEntity>(Method.get, url: HttpApi.GET_APPROVE_TEMPLATE_LIST, params: json.encode(params), isShow: true,isClose: true,
        onSuccess: (data) {
          if (data != null && data.list != null) {
            MyLog.e("========加载==_page==="+(_page.toString()));
            if (_page == 1) {
              controller.updateList(data.list?.toList()??[]);
            } else {
              var value = controller.list.value;
              value.addAll(data.list??[]);
              controller.updateList(value.toList());
            }
          } else {
            /// 加载失败
            if(_page > 1) {
              _page--;
            }
          }
        }, onError: (_, __) {
          MyLog.e("========加载失败========");
          if(_page > 1) {
            _page--;
          }
        });
  }
}
