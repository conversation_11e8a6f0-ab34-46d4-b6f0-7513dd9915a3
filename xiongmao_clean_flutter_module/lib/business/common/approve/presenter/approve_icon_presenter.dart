import 'dart:collection';
import 'dart:convert';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/approve_icon_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../controller/approve_icon_controller.dart';
import '../controller/approve_main_controller.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/approve_icon_iview.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';

class ApproveIconPresenter extends BasePagePresenter<ApproveIconView> {
  ApproveIconController controller;

  ApproveIconPresenter(this.controller);

  Future<dynamic> getAppRoveIconList() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<ApproveIconEntity>(Method.get, url: HttpApi.GET_APPROVE_ICON_ALL, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateList(data.list!);
      }
    });
  }
}
