import 'dart:collection';
import 'dart:convert';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/approve_one_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../controller/approve_main_controller.dart';
import '../controller/material_controller.dart';
import '../controller/material_template_controller.dart';
import '../iview/material_iview.dart';
import '../iview/material_template_iview.dart';

class MaterialTemplatePresenter extends BasePagePresenter<MaterialTemplateView> {
  MaterialTemplateController controller;

  MaterialTemplatePresenter(this.controller);

  int _page = 1;

  /// 物料cat
  Future<dynamic> getCatList() {
    var params = <String, String>{};
    params['is_page'] = '2';
    return requestNetwork<MaterialCatEntity>(Method.get, url: HttpApi.GET_CATEGORY_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========加载失败========");
      if (_page > 1) {
        _page--;
      }
    });
  }

  /// 提交物料申请
  Future<dynamic> submitMaterialRequest(HashMap<String, dynamic> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.CREATE_METERIAL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.createMaterial();
    }, onError: (_, __) {});
  }

  /// 获取审批详情
  Future<dynamic> getApproveOne(String? application_no) {
    var params = <String, String>{};
    params['application_no'] = '$application_no';
    return requestNetwork<ApproveOneEntity>(Method.get, url: HttpApi.GET_APPROVE_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.getApproveOne(data);
      }
    });
  }

}
