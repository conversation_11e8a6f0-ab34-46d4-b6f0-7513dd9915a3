import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../bean/approve_record_entity.dart';
import '../bean/approve_tem_entity.dart';
import '../controller/approve_record_controller.dart';
import '../controller/approve_record_rules_controller.dart';
import '../iview/approve_record_iview.dart';

/// 审批记录筛选
class ApproveRecordRulesPresenter extends BasePagePresenter<ApproveRecordIView> with WidgetsBindingObserver {
  ApproveRecordRulesController controller;

  ApproveRecordRulesPresenter(this.controller);

}
