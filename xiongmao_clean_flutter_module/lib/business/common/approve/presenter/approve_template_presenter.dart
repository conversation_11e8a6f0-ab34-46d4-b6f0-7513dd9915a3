import 'dart:collection';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/controller/approve_template_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_template_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/approve_one_entity.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../bean/template_one_entity.dart';
import '../controller/approve_main_controller.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';

class ApproveTemplatePresenter extends BasePagePresenter<ApproveTemplateView> {
  ApproveTemplateController controller;

  ApproveTemplatePresenter(this.controller);

  /// 获取模版详情
  Future<dynamic> getTemplateOne(String uuid, String? application_no) {
    var params = <String, String>{};
    params['uuid'] = uuid;
    return requestNetwork<TemplateOneEntity>(Method.get, url: HttpApi.GET_APPROVE_TEMPLATE_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.getTemplateOne(data);
        if (!TextUtil.isEmpty(application_no)) {
          getApproveOne(application_no);
        }
      }
    });
  }

  /// 获取审批详情
  Future<dynamic> getApproveOne(String? application_no) {
    var params = <String, String>{};
    params['application_no'] = '$application_no';
    return requestNetwork<ApproveOneEntity>(Method.get, url: HttpApi.GET_APPROVE_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.getApproveOne(data);
      }
    });
  }

  /// 提交审批
  Future<dynamic> createTemplate(HashMap<String, dynamic> params) {
    Options options = Options();
    options.method = 'POST';
    options.contentType = "application/x-www-form-urlencoded";
    return requestNetwork<Object>(Method.post, url: HttpApi.CREATE_APPROVE_TEMPLATE, params: params, isShow: true, isClose: true, options: options, onSuccess: (data) {
      if (data != null) {
        view.submit();
      }
    });
  }
}
