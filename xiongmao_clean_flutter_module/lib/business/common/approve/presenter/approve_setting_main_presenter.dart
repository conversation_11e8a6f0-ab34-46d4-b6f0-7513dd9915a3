import 'dart:collection';
import 'dart:convert';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_setting_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/base_uuid_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_role_all_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';

class ApproveSettingMainPresenter extends BasePagePresenter<ApproveSettingMainPageView> {
  Future<dynamic> getRoleAll() {
    HashMap<String, String> hashMap = HashMap();
    hashMap["type"] = "3"; //0全部 1总部员工角色 2项目成员角色 3获取总部成员
    return requestNetwork<GetRoleAllEntity>(Method.get, url: HttpApi.GET_ROLE_ALL, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      view.getRoleAll(data);
    });
  }

  Future<dynamic> getMetaDta() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<GetMetaEntity>(Method.get, url: HttpApi.GET_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      view.getMetaDta(data);
    });
  }
}
