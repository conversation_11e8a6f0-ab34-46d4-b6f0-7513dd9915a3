import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:intl/intl.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../../schedule/bean/schedule_export_data_entity.dart';
import '../bean/approve_record_entity.dart';
import '../bean/approve_tem_entity.dart';
import '../controller/approve_record_controller.dart';
import '../iview/approve_record_iview.dart';

/// 审批记录
class ApproveRecordPresenter extends BasePagePresenter<ApproveRecordIView> with WidgetsBindingObserver {
  ApproveRecordController controller;

  ApproveRecordPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getAttendanceListManager();
  }

  void loadMore() {
    _page++;
    getAttendanceListManager();
  }

  ///获取审批记录的列表
  Future<dynamic> getAttendanceListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["template_uuid"] = "${controller.templateUuid}";
    params["keyword"] = "${controller.keyword}";
    params["type"] = "5"; ////列表类型 1待我处理 2我已处理 3我创建的 4抄送我的 5审批记录列表
    if (controller.filterData == null) {
      params["status"] = "-1"; //这个是筛选状态
      params["create_time"] = ''; //这个是筛选状态
      params["create_user_uuid"] = ""; //这个是筛选状态
    } else {
      ///0待完善 1审批中 2已通过 3已拒绝 4已撤回
      params["status"] = "${controller.filterData?.picStatus}";
      params["create_time"] = '${controller.filterData?.startDate}'; //这个是筛选状态
      params["create_user_uuid"] = "${controller.filterData?.commitUuid}"; //这个是筛选状态
    }

    return requestNetwork<ApproveRecordEntity>(Method.get, url: HttpApi.GET_APPROVE_RECORD_TEMPLATE_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取审批记录的row
  Future<dynamic> getTemplateListManager() {
    var params = <String, String>{};
    return requestNetwork<ApproveTemEntity>(Method.get, url: HttpApi.GET_APPROVE_TEMPLATE_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initRowMyList(data.list ?? []);

          ///计算
          if (data.list != null && data.list!.isNotEmpty) {
            controller.templateUuid.value = data.list![0].uuid!;
            controller.templateName.value = data.list![0].templateName!;
            onRefresh();
          }
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取下载的审批
  Future<dynamic> requestDownloadApproveRecord() {
    var params = <String, String>{};
    params["op_type"] = "manage_approve_record";
    params["template_uuid"] = "${controller.templateUuid}";
    params["keyword"] = "${controller.keyword}";
    params["type"] = "5"; ////列表类型 1待我处理 2我已处理 3我创建的 4抄送我的 5审批记录列表
    if (controller.filterData == null) {
      params["status"] = "-1"; //这个是筛选状态
      params["create_time"] = ''; //这个是筛选状态
      params["create_user_uuid"] = ""; //这个是筛选状态
    } else {
      params["status"] = "${controller.filterData?.picStatus}"; //这个是筛选状态
      params["create_time"] = '${controller.filterData?.startDate}'; //这个是筛选状态
      params["create_user_uuid"] = "${controller.filterData?.commitUuid}"; //这个是筛选状态
    }

    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleExportDataEntity>(Method.get, url: HttpApi.APPROVE_RECORD_EXPORT_DOWNLOAD, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': data.downloadUrl ?? '', 'fileName': '${httpConfig.company_name}-${controller.templateName.value}-${DateFormat('yyyy-MM-dd HH:mm').format(DateTime.now())}'});
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///删除某个审批
  Future<dynamic> requestDelTemplate(String uuid) {
    var params = <String, dynamic>{};
    params["application_no"] = uuid;
    return requestNetwork<Object>(Method.get, url: HttpApi.APPROVE_RECORD_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      onRefresh();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///撤回
  Future<dynamic> requestWithdraw(String uuid) {
    var params = <String, dynamic>{};
    params["application_no"] = uuid;
    return requestNetwork<Object>(Method.get, url: HttpApi.APPROVE_RECORD_CANCEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      onRefresh();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
