
import 'dart:collection';
import 'dart:convert';
import 'package:flustars/flustars.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_entry_depart_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/bean/approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../generated/base_uuid_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../controller/approve_main_controller.dart';

class ApproveEntryDepartPresenter extends BasePagePresenter<ApproveEntryDepartPageView> {
  Future<dynamic> getApproveDetail(String uuid) {
    HashMap<String,String> hashMap = HashMap();
    hashMap["uuid"] = uuid;
    return requestNetwork<ApproveDetailEntity>(Method.post, url: HttpApi.GET_APPROVE_DETAIL, queryParameters: hashMap, isShow: true,isClose: true,
        onSuccess: (data) {
      view.getApproveDetailData(data);
        });
  }

  Future<dynamic> saveTemplate(HashMap<String, String> params) {
    LogUtil.e("object-----params---"+params.toString());
    return requestNetwork<BaseUuidEntity>(Method.get, url: HttpApi.SAVE_TEMPLATE, queryParameters: params, isShow: true,isClose: true,
        onSuccess: (data) {
          view.saveTemplate(data);
        });
  }
}
