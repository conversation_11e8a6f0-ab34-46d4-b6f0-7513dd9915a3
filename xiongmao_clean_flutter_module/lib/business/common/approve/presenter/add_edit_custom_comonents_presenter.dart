import 'dart:collection';
import 'dart:convert';
import 'package:xiongmao_clean_flutter_module/business/common/approve/iview/approve_main_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../generated/base_uuid_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/material_cat_child_entity.dart';
import '../bean/material_cat_entity.dart';
import '../bean/template_one_entity.dart';
import '../controller/add_edit_custom_components_controller.dart';
import '../controller/approve_main_controller.dart';
import '../controller/custom_components_controller.dart';
import '../controller/material_controller.dart';
import '../iview/add_edit_custom_comonents_iview.dart';
import '../iview/custom_comonents_iview.dart';
import '../iview/material_iview.dart';

class AddEditCustomComponentPresenter extends BasePagePresenter<AddEditCustomComponentsView> {
  AddEditCustomComponentController controller;

  AddEditCustomComponentPresenter(this.controller);

  /// 新建/编辑模板
  Future<dynamic> saveTemplate(HashMap<String, String> params) {
    return requestNetwork<BaseUuidEntity>(Method.get, url: HttpApi.SAVE_TEMPLATE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.saveTemplate(data!);
    });
  }

  /// 获取模版详情
  Future<dynamic> getTemplateOne(String uuid) {
    var params = <String, String>{};
    params['uuid'] = uuid;
    return requestNetwork<TemplateOneEntity>(Method.get, url: HttpApi.GET_APPROVE_TEMPLATE_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.getTemplateOne(data);
      }
    });
  }

  /// 删除模版详情
  Future<dynamic> deleteTemplate(String uuid) {
    var params = <String, dynamic>{};
    params['uuid'] = uuid;
    return requestNetwork<TemplateOneEntity>(Method.get, url: HttpApi.APPROVE_TEMPLATE_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.delete();
    });
  }
}
