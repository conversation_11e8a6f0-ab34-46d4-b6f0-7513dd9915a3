import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/gaps.dart';

class NumberInputWithButtons extends StatefulWidget {
  final int initialValue;
  final int maxValue;
  final int minValue;
  final ValueChanged<int> onChanged;

  NumberInputWithButtons({
    required this.initialValue,
    required this.maxValue,
    required this.minValue,
    required this.onChanged,
    Key? key, // 添加 Key 参数
  }) : super(key: key);

  @override
  _NumberInputWithButtonsState createState() => _NumberInputWithButtonsState();
}

class _NumberInputWithButtonsState extends State<NumberInputWithButtons> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialValue.toString());
  }

  @override
  void didUpdateWidget(covariant NumberInputWithButtons oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.initialValue != oldWidget.initialValue) {
      _controller.text = widget.initialValue.toString();
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _updateValue(int value) {
    print('走点击回调了 $value');
    if (value > widget.maxValue) {
      value = widget.maxValue;
    } else if (value < widget.minValue) {
      value = widget.minValue;
    }

    setState(() {
      _controller.text = value.toString();
    });

    widget.onChanged(value);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        InkWell(
          child: Container(
            width: 24,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(50.0),
              border: Border.all(color: Colours.base_primary, width: 1),
            ),
            child: CommonUtils.getSimpleText('—', 14, Colours.base_primary, textAlign: TextAlign.center),
          ),
          onTap: () {
            int currentValue = int.tryParse(_controller.text) ?? widget.minValue;
            _updateValue(currentValue - 1);
          },
        ),
        Gaps.hGap4,
        InkWell(
          child: Container(
            width: 40,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.transparent, width: 1),
            ),
            child: CommonUtils.getSimpleText(_controller.text, 14, Colours.base_primary_text_title, textAlign: TextAlign.center),
          ),
          onTap: () {
            BrnMiddleInputDialog(
                title: '修改数量',
                hintText: '输入数量',
                cancelText: '取消',
                confirmText: '确定',
                maxLength: 4,
                maxLines: 1,
                barrierDismissible: false,
                inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                inputEditingController: _controller,
                textInputAction: TextInputAction.done,
                onConfirm: (value) {
                  Navigator.of(context, rootNavigator: true).pop();
                  if (!TextUtil.isEmpty(value)) {
                    widget.onChanged(int.parse(value));
                  }
                },
                onCancel: () {
                  Navigator.of(context, rootNavigator: true).pop();
                }).show(context);
          },
        ),
        Gaps.hGap4,
        InkWell(
          child: Container(
            width: 24,
            decoration: BoxDecoration(
              color: Colours.base_primary,
              borderRadius: BorderRadius.circular(50.0),
              border: Border.all(color: Colours.base_primary, width: 1),
            ),
            child: CommonUtils.getSimpleText('+', 14, Colours.white, textAlign: TextAlign.center),
          ),
          onTap: () {
            int currentValue = int.tryParse(_controller.text) ?? widget.maxValue;
            _updateValue(currentValue + 1);
          },
        ),
      ],
    );
  }
}
