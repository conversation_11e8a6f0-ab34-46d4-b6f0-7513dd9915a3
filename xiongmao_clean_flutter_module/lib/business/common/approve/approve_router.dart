import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/add_custom_components_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_entry_depart_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_icon_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_option_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_record_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_record_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_settings_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/approve_template_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/custom_components_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/material_sys_choose_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/material_sys_manager_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/material_sys_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/approve/page/material_template_page.dart';

import 'bean/approve_detail_entity.dart';
import '../../../net/http_config.dart';
import 'bean/approve_record_filter_data.dart';
import 'bean/material_cat_child_entity.dart';
import 'bean/template_one_entity.dart';

/// 定义页面名称
const appRoveMainPage = "appRoveMainPage";

const appEntryDepartMainPage = "appEntryDepartMainPage";

const appSettingsMainPage = "appSettingsMainPage";

//物料
const materialSysPage = "materialSysPage";

//可申请的物料
const materialSysManagerPage = "materialSysManagerPage";

//物料用品
const materialSysChoosePage = "materialSysChoosePage";

//物料审批
const materialTemplatePage = "materialTemplatePage";

//这里是自定义组件
const customComponentsPage = "customComponentsPage";

//添加编辑
const addCustomComponentsPage = "addCustomComponentsPage";

//审批图标
const approveIconPage = "approveIconPage";

//选择适用的角色
const appRoveOptionPage = "appRoveOptionPage";

//自定义审批的界面
const approveTemplatePage = "approveTemplatePage";

///审批记录
const approveRecord = "ApproveRecordPage";

///筛选的内容
const approveRecordRulesPage = "ApproveRecordRulesPage";

/// 第一个界面 审批规则
Map<String, FlutterBoostRouteFactory> appRoveRouterMap = {
  appRoveMainPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          int position = map['position'] ?? 0;
          return AppRoveMainPage(
            position: position,
          );
        });
  },
  approveRecord: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ApproveRecordPage();
        });
  },
  approveRecordRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          ApproveRecordFilterData? data;
          if (map["filter_data"] != null) {
            Map<String, dynamic> json = map["filter_data"];
            data = ApproveRecordFilterData.fromJson(json);
          }
          return ApproveRecordRulesPage(
            data: data,
          );
        });
  },
  appEntryDepartMainPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map["uuid"] ?? '';
          List<ApproveDetailList> list = map['list'];
          return AppEntryDepartMainPage(uuid: uuid, list: list);
        });
  },
  appSettingsMainPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          ApproveDetailList? item;
          if (map["item"] != null) {
            item = map["item"];
          }
          String style = map["style"] ?? "0";

          return AppSettingsMainPage(
            item: item,
            style: style,
          );
        });
  },
  //物料管理
  materialSysPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? projectUuid = map["project_uuid"] ?? '';
          String? projectName = map["project_name"] ?? '';
          List<MaterialCatChildList> selected = [];
          if (map["selected"] != null) {
            selected = map["selected"];
          }
          return MaterialSysPage(
            projectUuid: projectUuid,
            projectName: projectName,
            selected: selected,
          );
        });
  },
  //可申请的物料
  materialSysManagerPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? projectUuid = map["project_uuid"] ?? '';
          String? projectName = map["project_name"] ?? '';
          return MaterialSysManagerPage(
            projectUuid: projectUuid,
            projectName: projectName,
          );
        });
  },
  //物料管理用品选择
  materialSysChoosePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map["project_uuid"] ?? '';
          String projectName = map["project_name"] ?? '';
          List<MaterialCatChildList> selected = [];
          if (map["selected"] != null) {
            selected = map["selected"];
          }
          return MaterialSysChoosePage(
            projectUuid: projectUuid,
            projectName: projectName,
            selected: selected,
          );
        });
  },
  //物料申请
  materialTemplatePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? applicationNo = map['application_no'] ?? '';
          return MaterialTemplatePage(
            templateUuid: uuid,
            applicationNo: applicationNo,
          );
        });
  },
  //设置自定义审批组件的界面
  customComponentsPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          TemplateOneFieldList? field;
          if (map['item'] != null) {
            field = map['item'];
          }
          bool show_abstract = true;
          if (map['show_abstract'] != null) {
            show_abstract = map['show_abstract'];
          }
          return CustomComponentsPage(
            templateOneFieldList: field,
            show_abstract: show_abstract,
          );
        });
  },

  //添加、编辑自定义审批组件的界面
  addCustomComponentsPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map["uuid"] ?? '';
          String? is_system = map['is_system'] ?? '';
          return AddCustomComponentsPage(
            uuid: uuid,
            is_system: is_system,
          );
        });
  },

  //图标
  approveIconPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ApproveIconPage();
        });
  },

  //选择适用项目 、 等等都结合在一起了，
  appRoveOptionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          int position = map['position'];
          String uuids = map['uuids'] ?? '';
          return AppRoveOptionPage(position: position, uuids: uuids);
        });
  },

  //原生界面
  approveTemplatePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'] ?? '';
          String? application_no = map['application_no'] ?? '';
          String? is_system = map['is_system'] ?? '';
          String? title = map['title'] ?? '';
          String? application_status = map['application_status'] ?? '';
          return ApproveTemplatePage(
            uuid: uuid,
            application_no: application_no,
            is_system: is_system,
            title: title,
            application_status: application_status,
          );
        });
  },
};
