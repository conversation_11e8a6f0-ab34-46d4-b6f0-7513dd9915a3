import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../widgets/custom_brn3_delegate_popup.dart';
import '../../../../widgets/custom_brn7_delegate_popup.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/classes_one_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectClassesListView extends StatelessWidget {
  ClassesOneSegmentList data;
  GetMetaInClassTimeList inClassTimeList;
  GetMetaOutClassTimeList outClassTimeList;

  int index;
  int count;

  final Function() onDelete;
  final Function() onRefresh;

  List<String> externalDays = ['当日', '次日'];
  List<String> centerDays = ['至'];
  List<String> externalHours = List.generate(24, (i) => i.toString().padLeft(2, '0'));
  List<String> externalMinutes = List.generate(12, (i) => (i * 5).toString().padLeft(2, '0'));

  ProjectClassesListView({
    super.key,
    required this.data,
    required this.index,
    required this.count,
    required this.onDelete,
    required this.onRefresh,
    required this.outClassTimeList,
    required this.inClassTimeList,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonUtils.getSimpleText('班段${index + 1}', 16, Colours.base_primary_text_title),
              Visibility(
                visible: count > 1,
                child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 16, Colours.red),
                  onTap: () {
                    onDelete();
                  },
                ),
              ),
            ],
          ),
        ),
        BrnTextSelectFormItem(
          isRequire: true,
          title: "上下班时间段",
          value: buildWorkDesc(data),
          onTap: () {
            BrnMultiDataPicker(
              sync: false,
              context: context,
              title: '上下班时间段',
              delegate: Brn7RowCustomDelegate(
                days1: externalDays,
                hours1: externalHours,
                minutes1: externalMinutes,
                seconds: centerDays,
                days2: externalDays,
                hours2: externalHours,
                minutes2: externalMinutes,
                firstSelectedIndex1: TextUtil.isEmpty(data.startTimeIsToday)
                    ? 0
                    : data.startTimeIsToday == '1'
                        ? 0
                        : 1,
                secondSelectedIndex1: (TextUtil.isEmpty(data.workStartTime)) ? 0 : externalHours.indexOf(data.workStartTime!.split(':')[0]),
                thirdSelectedIndex1: (TextUtil.isEmpty(data.workStartTime)) ? 0 : externalMinutes.indexOf(data.workStartTime!.split(':')[1]),
                firstSelectedIndex2: TextUtil.isEmpty(data.endTimeIsToday)
                    ? 0
                    : data.endTimeIsToday == '1'
                        ? 0
                        : 1,
                secondSelectedIndex2: (TextUtil.isEmpty(data.workEndTime)) ? 0 : externalHours.indexOf(data.workEndTime!.split(':')[0]),
                thirdSelectedIndex2: (TextUtil.isEmpty(data.workEndTime)) ? 0 : externalMinutes.indexOf(data.workEndTime!.split(':')[1]),
              ),
              confirmClick: (list) {
                data.workStartTime = '${externalHours[list[1]]}:${externalMinutes[list[2]]}';
                data.workEndTime = '${externalHours[list[5]]}:${externalMinutes[list[6]]}';
                //是否当天
                data.startTimeIsToday = (list[0] == 0) ? '1' : '2';
                data.endTimeIsToday = (list[4] == 0) ? '1' : '2';
                onRefresh();
              },
            ).show();
          },
        ),
        Gaps.line,
        BrnTextSelectFormItem(
          isRequire: true,
          title: "上班打卡范围",
          value: buildInClass(data),
          onTap: () {
            BrnMultiDataPicker(
              sync: false,
              context: context,
              title: '上班打卡范围',
              delegate: Brn3RowCustomDelegate(
                days: inClassTimeList.beforeList!
                    .map((e) => e.name)
                    .where((name) => name != null) // 过滤掉 null 值
                    .cast<String>() // 转换为 List<String>
                    .toList(),
                hours: centerDays,
                minutes: inClassTimeList.afterList!
                    .map((e) => e.name)
                    .where((name) => name != null) // 过滤掉 null 值
                    .cast<String>() // 转换为 List<String>
                    .toList(),
                firstSelectedIndex: data.inClassStartType == null ? 0 : inClassTimeList.beforeList!.indexWhere((element) => element.id == data.inClassStartType),
                secondSelectedIndex: 0,
                thirdSelectedIndex: data.inClassEndType == null ? 0 : inClassTimeList.afterList!.indexWhere((element) => element.id == data.inClassEndType),
              ),
              confirmClick: (list) {
                data.inClassStartType = inClassTimeList.beforeList![list[0]].id;
                data.inClassEndType = inClassTimeList.afterList![list[2]].id;
                onRefresh();
              },
            ).show();
          },
        ),
        Gaps.line,
        BrnTextSelectFormItem(
          isRequire: true,
          title: "下班打卡范围",
          value: buildOutClass(data),
          onTap: () {
            BrnMultiDataPicker(
              sync: false,
              context: context,
              title: '下班打卡范围',
              delegate: Brn3RowCustomDelegate(
                days: outClassTimeList.beforeList!
                    .map((e) => e.name)
                    .where((name) => name != null) // 过滤掉 null 值
                    .cast<String>() // 转换为 List<String>
                    .toList(),
                hours: centerDays,
                minutes: outClassTimeList.afterList!
                    .map((e) => e.name)
                    .where((name) => name != null) // 过滤掉 null 值
                    .cast<String>() // 转换为 List<String>
                    .toList(),
                firstSelectedIndex: data.outClassStartType == null ? 0 : outClassTimeList.beforeList!.indexWhere((element) => element.id == data.outClassStartType),
                secondSelectedIndex: 0,
                thirdSelectedIndex: data.outClassEndType == null ? 0 : outClassTimeList.afterList!.indexWhere((element) => element.id == data.outClassEndType),
              ),
              confirmClick: (list) {
                data.outClassStartType = outClassTimeList.beforeList![list[0]].id;
                data.outClassEndType = outClassTimeList.afterList![list[2]].id;
                onRefresh();
              },
            ).show();
          },
        ),
        Gaps.line,
        BrnBaseTitle(
          title: "休息时段",
          customActionWidget: InkWell(
            child: Center(
              child: Row(
                children: [
                  Visibility(
                    visible: (!TextUtil.isEmpty(buildRestWorkDesc(data))),
                    child: InkWell(
                      child: const LoadAssetImage(
                        'icon_base_round_close',
                        width: 20,
                        height: 20,
                      ),
                      onTap: () {
                        data.restStartTime = '';
                        data.restEndTime = '';
                        data.restEndTimeIsToday = '0';
                        data.restStartTimeIsToday = '0';
                        onRefresh();
                      },
                    ),
                  ),
                  Gaps.hGap10,
                  CommonUtils.getSimpleText(buildRestWorkDesc(data), 16, Colours.base_primary_text_body, height: -0.2),
                  Visibility(child: CommonUtils.getSimpleText('请选择', 16, Colours.base_primary_text_caption, height: -0.2), visible: (TextUtil.isEmpty(buildRestWorkDesc(data)))),
                  Gaps.hGap4,
                  const LoadAssetImage(
                    'base/icon_base_gray_arrow',
                    width: 14,
                    height: 14,
                  )
                ],
              ),
            ),
            onTap: () {
              BrnMultiDataPicker(
                sync: false,
                context: context,
                title: '休息时段',
                delegate: Brn7RowCustomDelegate(
                  days1: externalDays,
                  hours1: externalHours,
                  minutes1: externalMinutes,
                  seconds: centerDays,
                  days2: externalDays,
                  hours2: externalHours,
                  minutes2: externalMinutes,
                  firstSelectedIndex1: (TextUtil.isEmpty(data.startTimeIsToday))
                      ? 0
                      : data.startTimeIsToday == '1'
                          ? 0
                          : 1,
                  secondSelectedIndex1: (TextUtil.isEmpty(data.restStartTime)) ? 0 : externalHours.indexOf(data.restStartTime!.split(':')[0]),
                  thirdSelectedIndex1: (TextUtil.isEmpty(data.restStartTime)) ? 0 : externalMinutes.indexOf(data.restStartTime!.split(':')[1]),
                  firstSelectedIndex2: (TextUtil.isEmpty(data.endTimeIsToday))
                      ? 0
                      : data.endTimeIsToday == '1'
                          ? 0
                          : 1,
                  secondSelectedIndex2: (TextUtil.isEmpty(data.restEndTime)) ? 0 : externalHours.indexOf(data.restEndTime!.split(':')[0]),
                  thirdSelectedIndex2: (TextUtil.isEmpty(data.restEndTime)) ? 0 : externalMinutes.indexOf(data.restEndTime!.split(':')[1]),
                ),
                confirmClick: (list) {
                  data.restStartTime = '${externalHours[list[1]]}:${externalMinutes[list[2]]}';
                  data.restEndTime = '${externalHours[list[5]]}:${externalMinutes[list[6]]}';
                  //是否当天
                  data.restStartTimeIsToday = (list[0] == 0) ? '1' : '2';
                  data.restEndTimeIsToday = (list[4] == 0) ? '1' : '2';
                  onRefresh();
                },
              ).show();
            },
          ),
        ),
      ],
    );
  }

  buildWorkDesc(ClassesOneSegmentList data) {
    StringBuffer stringBuffer = StringBuffer();
    if ('2' == data.startTimeIsToday) {
      stringBuffer.write('次');
    }

    if (!TextUtil.isEmpty(data.workStartTime)) {
      stringBuffer.write(data.workStartTime);
      stringBuffer.write('-');
    }

    if ('2' == data.endTimeIsToday) {
      stringBuffer.write('次');
    }
    if (!TextUtil.isEmpty(data.workEndTime)) {
      stringBuffer.write(data.workEndTime);
    }
    return (TextUtil.isEmpty(stringBuffer.toString()) ? '' : stringBuffer.toString());
  }

  buildRestWorkDesc(ClassesOneSegmentList data) {
    StringBuffer stringBuffer = StringBuffer();
    if ('2' == data.restStartTimeIsToday) {
      stringBuffer.write('次');
    }

    if (!TextUtil.isEmpty(data.restStartTime)) {
      stringBuffer.write(data.restStartTime);
      stringBuffer.write('-');
    }

    if ('2' == data.restEndTimeIsToday) {
      stringBuffer.write('次');
    }
    if (!TextUtil.isEmpty(data.restEndTime)) {
      stringBuffer.write(data.restEndTime);
    }
    return (TextUtil.isEmpty(stringBuffer.toString()) ? '' : stringBuffer.toString());
  }

  ///上班打卡范围
  buildInClass(ClassesOneSegmentList data) {
    StringBuffer stringBuffer = StringBuffer();
    if (!TextUtil.isEmpty(data.inClassStartType)) {
      stringBuffer.write(inClassTimeList.beforeList!.firstWhere((element) => element.id == data.inClassStartType).name);
      stringBuffer.write('-');
    }
    if (!TextUtil.isEmpty(data.inClassEndType)) {
      stringBuffer.write(inClassTimeList.afterList!.firstWhere((element) => element.id == data.inClassEndType).name);
    }
    return (TextUtil.isEmpty(stringBuffer.toString()) ? '' : stringBuffer.toString());
  }

  ///下班班打卡范围
  buildOutClass(ClassesOneSegmentList data) {
    StringBuffer stringBuffer = StringBuffer();
    if (!TextUtil.isEmpty(data.outClassStartType)) {
      stringBuffer.write(outClassTimeList.beforeList!.firstWhere((element) => element.id == data.outClassStartType).name);
      stringBuffer.write('-');
    }
    if (!TextUtil.isEmpty(data.outClassEndType)) {
      stringBuffer.write(outClassTimeList.afterList!.firstWhere((element) => element.id == data.outClassEndType).name);
    }
    return (TextUtil.isEmpty(stringBuffer.toString()) ? '' : stringBuffer.toString());
  }
}
