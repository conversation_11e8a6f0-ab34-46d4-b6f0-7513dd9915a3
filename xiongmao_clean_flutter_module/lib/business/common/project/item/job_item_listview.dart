import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/business_format_entity.dart';
import '../bean/job_manager_entity.dart';
import '../bean/project_manager_entity.dart';

class JobListView extends StatelessWidget {
  final JobManagerList data;
  final Function onClick;

  JobListView({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colours.white,
        borderRadius: BorderRadius.circular(0),
      ),
      padding: const EdgeInsets.only(
        left: 16,
        right: 16,
      ), // 设置内边距为16.0
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: CommonUtils.getSimpleText('${data.jobName}', 14, Colours.base_primary_text_title),
              ),
              Flexible(
                flex: 2,
                child: TextField(
                  maxLength: 4,
                  keyboardType: TextInputType.number,
                  style: TextStyle(fontSize: 14),
                  controller: TextEditingController()..text = data.jobHumanNum ?? '',
                  // 设置字体大小
                  decoration: const InputDecoration(
                    border: InputBorder.none, // 去掉边框
                    counterText: '', // 去掉字符计数
                    hintText: '请输入',
                  ),
                  onChanged: (value) {
                    print('输入点内容' + value);
                    data.jobHumanNum = value;
                  },
                ),
              ), // 添加间距
              InkWell(
                child: const LoadAssetImage(
                  'icon_base_round_close',
                  width: 20,
                  height: 20,
                ),
                onTap: () {
                  onClick();
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
