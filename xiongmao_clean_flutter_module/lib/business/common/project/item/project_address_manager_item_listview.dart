import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/address_manager_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectAddressManagerListView extends StatelessWidget {
  AddressManagerList data;

  final Function() onClick;
  final Function() onDelete;

  ProjectAddressManagerListView({required this.data, required this.onClick, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
          bottom: 10,
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ),
        // 设置内边距为16.0
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            InkWell(
              child: LoadImage(
                'base/icon_base_del',
                width: 20,
                height: 20,
              ),
              onTap: (){
                onDelete();
              },
            ),
            Gaps.hGap10,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonUtils.getSimpleText(data.address, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                  CommonUtils.getSimpleText(data.address_desc, 15, Colours.base_primary_text_caption, fontWeight: FontWeight.bold),
                ],
              ),
            ),
            Visibility(
              visible: data.isSelect == true,
              child: LoadImage(
                'icon_base_selected',
                width: 20,
                height: 20,
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  //针对类型做拼接
  String buildTypeName(AttendanceManagerRulesList data) {
    StringBuffer stringBuffer = StringBuffer();

    // for (int i = 0; i < data.addressList!.length ?? 0; i++) {
    //   stringBuffer.write(data.addressList?[i] ?? '');
    // }
    return stringBuffer.toString();
  }
}
