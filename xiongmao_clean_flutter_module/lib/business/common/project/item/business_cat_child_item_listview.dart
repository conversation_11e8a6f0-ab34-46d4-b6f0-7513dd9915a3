import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/business_format_entity.dart';
import '../bean/project_manager_entity.dart';

class BusinessCatChildListView extends StatelessWidget {
  BusinessFormatListChildList data;

  final Function onClick;

  String catChildChooseUUID;

  BusinessCatChildListView({required this.data, required this.catChildChooseUUID, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(4.0),
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(child: CommonUtils.getSimpleText('${data.catName}', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                Visibility(
                  visible: catChildChooseUUID == data.id,
                  child: const LoadAssetImage(
                    'base/icon_base_selected',
                    width: 24,
                    height: 24,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
