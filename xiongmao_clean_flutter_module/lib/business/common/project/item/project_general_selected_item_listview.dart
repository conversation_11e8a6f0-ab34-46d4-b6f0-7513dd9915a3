import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectGeneralSelectedListView extends StatelessWidget {
  ProjectManagerList data;

  String choose_uuid;
  bool isSelected;

  final Function onClick;

  ProjectGeneralSelectedListView({required this.data, required this.isSelected, required this.choose_uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(
                top: 10,
                left: 16,
                right: 16,
                bottom: 10,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Expanded(
                      child: Container(
                    height: 46,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        CommonUtils.getSimpleText(
                          !TextUtil.isEmpty(data.projectShortName)
                              ? data.projectShortName
                              : !TextUtil.isEmpty(data.projectName)
                                  ? data.projectName
                                  : '',
                          14,
                          Colours.base_primary_text_title,
                          fontWeight: FontWeight.bold,
                        ),
                        Visibility(
                          child: CommonUtils.getSimpleText('${data.projectName}', 13, Colours.base_primary_text_caption),
                          visible: !TextUtil.isEmpty(data.projectName),
                        ),
                      ],
                    ),
                  )),
                  LoadAssetImage(
                    (data.isGroupClockIn == '1') ? "icon_check" : "icon_uncheck",
                    width: 20,
                    height: 20,
                  ),
                ],
              ),
            ),
            Gaps.line,
          ],
        ),
      ),
      onTap: () {
        data.isGroupClockIn = (data.isGroupClockIn == '1') ? '2' : '1';
        onClick();
      },
    );
  }

  String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 11) {
      throw ArgumentError('Phone number must be 11 digits long');
    }
    // 使用正则表达式替换中间四位
    return phoneNumber.replaceRange(3, 7, '****');
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );

    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'Could not launch $launchUri';
    }
  }

  //针对类型做拼接
  String buildTypeName(ProjectManagerList data) {
    StringBuffer stringBuffer = StringBuffer();
    if (!TextUtil.isEmpty(data.projectCatParentName)) {
      stringBuffer.write(data.projectCatParentName);
      stringBuffer.write('-');
    }
    if (!TextUtil.isEmpty(data.projectCatName)) {
      stringBuffer.write(data.projectCatName);
    }
    return stringBuffer.toString();
  }
}
