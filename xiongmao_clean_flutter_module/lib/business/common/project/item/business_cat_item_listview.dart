import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/business_format_entity.dart';
import '../bean/project_manager_entity.dart';

class BusinessCatListView extends StatelessWidget {
  BusinessFormatList data;

  String choose_uuid;

  final Function onClick;

  BusinessCatListView({required this.data, required this.choose_uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        color: (choose_uuid == data.id) ? Colours.drawer_bg3 : Colours.transparent,
        padding: const EdgeInsets.only(
          top: 10,
          left: 14,
          right: 14,
          bottom: 10,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Opacity(
                  opacity: choose_uuid == data.id ? 1.0 : 0.0,
                  child: Container(
                    width: 4,
                    height: 26,
                    decoration: const BoxDecoration(
                      color: Colours.base_primary,
                      borderRadius: BorderRadius.only(
                        topRight: Radius.circular(10.0),
                        bottomRight: Radius.circular(10.0),
                      ),
                    ),
                  ),
                ),
                Gaps.hGap4,
                CommonUtils.getSimpleText('${data.catName}', 15, (choose_uuid == data.id) ? Colours.base_primary : Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
