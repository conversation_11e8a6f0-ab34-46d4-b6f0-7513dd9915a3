import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/classes_manager_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectClassesManagerListView extends StatelessWidget {
  ClassesManagerList data;

  final Function() onClick;
  final Function() onEdit;
  final Function() onDelete;

  ProjectClassesManagerListView({required this.data, required this.onClick, required this.onEdit, required this.onDelete});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
          bottom: 10,
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ),
        // 设置内边距为16.0
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.className, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                LoadImage(
                  data.isSelected ? 'icon_check' : 'icon_uncheck',
                  width: 20,
                  height: 20,
                ),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                CommonUtils.getSimpleText('班次类型', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(data.classTypeName ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap6,
            Row(
              children: [
                CommonUtils.getSimpleText('上班时段', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(data.classTime ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Visibility(
              visible: '1' == data.classType,
              child: Column(
                children: [
                  Gaps.vGap6,
                  Row(
                    children: [
                      CommonUtils.getSimpleText('上午下午分割点', 15, Colours.base_primary_text_title),
                      Gaps.hGap10,
                      Expanded(child: CommonUtils.getSimpleText(buildTypeName(data) ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                    ],
                  )
                ],
              ),
            ),
            Visibility(
              visible: '1' == data.classType,
              child: Column(
                children: [
                  Gaps.vGap6,
                  Row(
                    children: [
                      CommonUtils.getSimpleText('免打卡', 15, Colours.base_primary_text_title),
                      Gaps.hGap10,
                      Expanded(child: CommonUtils.getSimpleText(data.isNoClockName ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                    ],
                  )
                ],
              ),
            ),
            Gaps.vGap6,
            Row(
              children: [
                CommonUtils.getSimpleText('未打卡时默认休息', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(data.isDefaultRestName ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                  onTap: () {
                    onDelete();
                  },
                )),
                Gaps.vLine,
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('编辑', 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                  onTap: () {
                    onEdit();
                  },
                )),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  //针对类型做拼接
  String buildTypeName(ClassesManagerList data) {
    String avgTime = "";
    if (!TextUtil.isEmpty(data.avgLineTime)) {
      if ("2" == (data.avgLineIsToday)) {
        avgTime = "次${data.avgLineTime}";
      } else {
        avgTime = "${data.avgLineTime}";
      }
    }
    return avgTime;
  }
}
