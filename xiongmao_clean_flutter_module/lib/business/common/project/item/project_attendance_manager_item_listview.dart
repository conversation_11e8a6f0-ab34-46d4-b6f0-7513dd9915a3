import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectAttendanceManagerListView extends StatelessWidget {
  AttendanceManagerRulesList data;

  String choose_uuid;
  bool isSelected;

  final Function(int) onClick;
  final Function() onDelete;

  ProjectAttendanceManagerListView({
    required this.data,
    required this.onClick,
    required this.onDelete,
    required this.isSelected,
    required this.choose_uuid,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(10.0),
        ),
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
          bottom: 10,
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ),
        // 设置内边距为16.0
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.groupName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                Visibility(
                  visible: isSelected,
                  child: LoadAssetImage(
                    (data.uuid == choose_uuid) ? "icon_check" : "icon_uncheck",
                    width: 20,
                    height: 20,
                  ),
                ),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                CommonUtils.getSimpleText('工作日', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(data.workDayDesc ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap6,
            Row(
              children: [
                CommonUtils.getSimpleText('工作时段', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(data.inClassDesc ?? '', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap6,
            Row(
              children: [
                CommonUtils.getSimpleText('打卡地点', 15, Colours.base_primary_text_title),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText((data.addressList!.isNotEmpty) ? data.addressList?.join('、') : '不限制' ?? '不限制', 15, Colours.base_primary_text_title, textAlign: TextAlign.right, overflow: TextOverflow.ellipsis)),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                  onTap: () {
                    onDelete();
                  },
                )),
                Gaps.vLine,
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('编辑', 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                  onTap: () {
                    onClick(0);
                  },
                )),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick(1);
      },
    );
  }

  //针对类型做拼接
  String buildTypeName(AttendanceManagerRulesList data) {
    StringBuffer stringBuffer = StringBuffer();

    // for (int i = 0; i < data.addressList!.length ?? 0; i++) {
    //   stringBuffer.write(data.addressList?[i] ?? '');
    // }
    return stringBuffer.toString();
  }
}
