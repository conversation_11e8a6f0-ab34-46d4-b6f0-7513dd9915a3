import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/business_format_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_address_manager_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_attendance_manager_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_classes_manager_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_general_selection_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_manager_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_one_web_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_save_attendance_manager_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_save_classes_manager_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/page/project_save_page.dart';

import '../../../net/http_config.dart';

/// 项目管理
const projectManagerPage = "ProjectManagerPage";

/// 新增项目 编辑项目
const projectSavePage = "ProjectSavePage";

///业态选择
const businessFormatPage = 'BusinessFormatPage';

///可选择的项目列表
const projectGeneralSelectionPage = 'ProjectGeneralSelectionPage';

///考勤规则管理
const projectAttendanceManagerRulesPage = 'ProjectAttendanceManagerRulesPage';

///编辑考勤规则
const projectSaveAttendanceManagerRulesPage = 'ProjectSaveAttendanceManagerRulesPage';

///打卡地点
const projectAddressManagerRulesPage = 'ProjectAddressManagerRulesPage';

///班次管理
const projectClassesManagerRulesPage = 'ProjectClassesManagerRulesPage';

///编辑班次
const projectSaveClassesManagerRulesPage = 'ProjectSaveClassesManagerRulesPage';

///项目详情
const projectOneWebPage = 'ProjectOneWebPage';

/// 项目路由
Map<String, FlutterBoostRouteFactory> projectRouterMap = {
  projectManagerPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);

          ///这个参数是 是否要开启选中模式
          bool isSelected = map['isSelected'] ?? false;

          ///因为这个项目中设计到单选，也涉及到多选，所以2个参数
          bool multiple = map['multiple'] ?? false;
          List<String> choice_uuids = map['choice_uuids'];

          return ProjectManagerPage(
            isSelected: isSelected,
            multiple: multiple,
            choice_uuids: choice_uuids,
          );
        });
  },

  ///编辑项目、新增项目
  projectSavePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String project_uuid = map['project_uuid'] ?? '';
          return ProjectSavePage(project_uuid: project_uuid);
        });
  },

  ///业态
  businessFormatPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String pid = map['pid'];
          return BusinessFormatPage(pid: pid);
        });
  },

  ///可选择的项目列表
  projectGeneralSelectionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? title = map['title'];
          return ProjectGeneralSelectionPage(
            title: title,
          );
        });
  },

  ///考勤规则
  projectAttendanceManagerRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          String choose_uuid = map['choose_uuid'] ?? "";
          bool isSelected = map['isSelected'] ?? false;
          return ProjectAttendanceManagerRulesPage(
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
            choose_uuid: choose_uuid,
            isSelected: isSelected,
          );
        });
  },

  ///编辑考勤规则
  projectSaveAttendanceManagerRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          return ProjectSaveAttendanceManagerRulesPage(
            uuid: uuid,
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
          );
        });
  },

  ///打卡地点
  projectAddressManagerRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          bool multiple = map['multiple'] ?? false;
          String uuids = map['uuids'] ?? ''; //已选择的数据
          return ProjectAddressManagerRulesPage(
            multiple: multiple,
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
            uuids: uuids,
          );
        });
  },

  ///班次管理
  projectClassesManagerRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          bool multiple = map['multiple'] ?? false;
          String uuids = map['uuids'] ?? ''; //已选择的数据
          return ProjectClassesManagerRulesPage(
            multiple: multiple,
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
            uuids: uuids,
          );
        });
  },

  ///新增班次
  projectSaveClassesManagerRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          String? uuid = map['uuid'];
          return ProjectSaveClassesManagerRulesPage(
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
            uuid: uuid,
          );
        });
  },

  ///项目详情
  projectOneWebPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          return ProjectOneWebPage(
            projectUuid: project_uuid ?? httpConfig.project_uuid,
            projectName: project_name ?? httpConfig.project_name,
          );
        });
  },
};
