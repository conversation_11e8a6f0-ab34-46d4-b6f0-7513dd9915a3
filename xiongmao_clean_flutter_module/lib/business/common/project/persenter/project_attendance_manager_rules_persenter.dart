import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/attendance_one_entity.dart';
import '../bean/project_manager_entity.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';

class ProjectAttendanceManagerRulesPresenter extends BasePagePresenter<ProjectAttendanceMangerRulesIView> with WidgetsBindingObserver {
  ProjectAttendanceManagerRulesController controller;

  ProjectAttendanceManagerRulesPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    requestAttendanceManagerList();
  }

  void loadMore() {
    _page++;
    requestAttendanceManagerList();
  }

  ///获取所有的项目 不分页
  Future<dynamic> requestAttendanceManagerList() {
    var params = <String, String>{};
    params['page'] = '$_page';
    params['size'] = '20';
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params['project_uuid'] = controller.projectUuid.value;
    }
    if (!TextUtil.isEmpty(controller.keyword.value)) {
      params['keyword'] = controller.keyword.value;
    }
    params['map_code'] = 'gao_de';
    return requestNetwork<AttendanceManagerRulesEntity>(Method.get, url: HttpApi.GET_GROUP_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除考勤规则
  Future<dynamic> requestDeleteAttendanceRules(String uuid) {
    var params = <String, String>{};
    params['project_uuid'] = controller.projectUuid.value;
    params['uuid'] = uuid;
    return requestNetwork<AttendanceManagerRulesEntity>(Method.get, url: HttpApi.DEL_GROUP, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.updateStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取考勤详情
  Future<dynamic> requestAttendanceRulesOne(String uuid) {
    var params = <String, String>{};
    params['project_uuid'] = controller.projectUuid.value;
    params['uuid'] = uuid;
    return requestNetwork<AttendanceOneEntity>(Method.get, url: HttpApi.GET_GROUP_DETAIL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getAttendanceRulesOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///保存详情
  Future<dynamic> requestSaveAttendanceRules(HashMap<String, dynamic> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.SAVE_GROUP, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.updateStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
