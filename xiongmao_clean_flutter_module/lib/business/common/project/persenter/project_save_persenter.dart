import 'dart:collection';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/create_project_entity.dart';
import '../bean/job_manager_entity.dart';
import '../bean/project_manager_entity.dart';
import '../bean/project_one_entity.dart';
import '../controller/project_manager_controller.dart';
import '../controller/project_save_controller.dart';
import '../iview/project_manager_iview.dart';
import '../iview/project_save_iview.dart';

class ProjectSavePresenter extends BasePagePresenter<ProjectSaveIView> with WidgetsBindingObserver {
  ProjectSaveController controller;

  ProjectSavePresenter(this.controller);

  ///获取工作的岗位
  Future<dynamic> requestWorkJob() {
    var params = <String, String>{};
    params["size"] = "100";
    params["page"] = "1";
    return requestNetwork<JobManagerEntity>(Method.get, url: HttpApi.GET_JOB_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initJobList(data.list!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取项目的详情
  Future<dynamic> requestProjectOne() {
    var params = <String, String>{};
    params["project_uuid"] = controller.project_uuid.value;
    return requestNetwork<ProjectOneEntity>(Method.get, url: HttpApi.GET_PROJECT_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///保存
  Future<dynamic> saveCompanyProject() {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(controller.project_uuid.value)) {
      params["project_uuid"] = controller.project_uuid.value;
    }
    if (!TextUtil.isEmpty(controller.customManagerUUID.value)) {
      params["custom_uuid"] = controller.customManagerUUID.value;
    }
    if (!TextUtil.isEmpty(controller.businessFormatManagerUUID.value)) {
      params["project_cat_id"] = controller.businessFormatManagerUUID.value;
    }
    if (!TextUtil.isEmpty(controller.regionalManagerUUID.value)) {
      params["manager_user_uuid"] = controller.regionalManagerUUID.value;
    }
    if (!TextUtil.isEmpty(controller.contractCompanyUuid.value)) {
      params["contract_company_uuid"] = controller.contractCompanyUuid.value;
    }
    params['project_name'] = controller.projectFullName.value;
    params['project_short_name'] = controller.projectShortName.value;
    params['square'] = controller.roomSize.value;
    params['remark'] = controller.remark.value;
    params['start_time'] = controller.startDate.value;
    params['end_time'] = controller.endDate.value;
    params['contract_human_num'] = controller.contactNum.value;
    //合同照片
    params['contract_pic'] = json.encode(controller.contractPicList.value.map((e) => e.media_url).toList());
    //工作内容
    params['job_info_list'] = json.encode(controller.jobList.value);

    return requestNetwork<CreateProjectEntity>(Method.post, url: HttpApi.SAVE_COMPANY_PROJECT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if(data!=null){
        view.saveStatus(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
