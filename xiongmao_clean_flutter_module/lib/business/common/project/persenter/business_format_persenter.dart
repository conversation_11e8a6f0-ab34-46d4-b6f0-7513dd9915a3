import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/business_format_entity.dart';
import '../bean/project_manager_entity.dart';
import '../controller/business_format_controller.dart';
import '../controller/project_manager_controller.dart';
import '../iview/business_format_iview.dart';
import '../iview/project_manager_iview.dart';

class BusinessFormatPresenter extends BasePagePresenter<BusinessFormatIView> with WidgetsBindingObserver {
  BusinessFormatController controller;

  BusinessFormatPresenter(this.controller);

  ///获取业态内容
  Future<dynamic> requestBusinessFormatList() {
    var params = <String, String>{};
    return requestNetwork<BusinessFormatEntity>(Method.get, url: HttpApi.GET_BUSINESS_FORMAT_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initList(data.list!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
