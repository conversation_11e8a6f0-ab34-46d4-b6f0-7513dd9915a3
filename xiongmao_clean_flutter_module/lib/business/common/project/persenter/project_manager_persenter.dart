import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/project_manager_entity.dart';
import '../controller/project_manager_controller.dart';
import '../iview/project_manager_iview.dart';

class ProjectManagerPresenter extends BasePagePresenter<ProjectMangerIView> with WidgetsBindingObserver {
  ProjectManagerController controller;

  ProjectManagerPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    requestProjectManagerList();
  }

  void loadMore() {
    _page++;
    requestProjectManagerList();
  }

  ///获取所有的项目 不分页
  Future<dynamic> requestProjectManagerList() {
    var params = <String, String>{};
    params['page'] = '$_page';
    params['size'] = '20';
    if (!TextUtil.isEmpty(controller.searchText.value)) {
      params['keyword'] = controller.searchText.value;
    }
    return requestNetwork<ProjectManagerEntity>(Method.get, url: HttpApi.PROJECT_MANAGER_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///集体打卡的请求
  Future<dynamic> requestSave() {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(controller.getSelectedUuids())) {
      params['uuid_list'] = controller.getSelectedUuids();
      params['is_select'] = '1';
    } else {
      params['is_select'] = '2';
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.SETTING_GENERAL_CONFIG, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.updateStatus();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
