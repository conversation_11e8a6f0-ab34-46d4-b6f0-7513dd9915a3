import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/address_manager_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/classes_manager_entity.dart';
import '../bean/classes_one_entity.dart';
import '../bean/project_manager_entity.dart';
import '../controller/project_address_manager_rules_controller.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_classes_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../iview/project_address_manager_rules_iview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_classes_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';

class ProjectClassesManagerRulesPresenter extends BasePagePresenter<ProjectClassesMangerRulesIView> with WidgetsBindingObserver {
  ProjectClassesManagerRulesController controller;

  ProjectClassesManagerRulesPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    requestAddressManagerList();
  }

  void loadMore() {
    _page++;
    requestAddressManagerList();
  }

  ///获取所有的项目 不分页
  Future<dynamic> requestAddressManagerList() {
    var params = <String, String>{};
    params['page'] = '$_page';
    params['size'] = '20';
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params['project_uuid'] = controller.projectUuid.value;
    }
    return requestNetwork<ClassesManagerEntity>(Method.get, url: HttpApi.GET_CLASSES_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除班次列表
  Future<dynamic> requestDeleteClassesRules(String uuid) {
    var params = <String, String>{};
    params['project_uuid'] = controller.projectUuid.value;
    params['uuid'] = uuid;
    return requestNetwork<Object>(Method.get, url: HttpApi.DEL_CLASSES, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      onRefresh();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取班次详情
  Future<dynamic> requestClassesOneRules(String uuid) {
    var params = <String, String>{};
    params['project_uuid'] = controller.projectUuid.value;
    params['uuid'] = uuid;
    return requestNetwork<ClassesOneEntity>(Method.get, url: HttpApi.GET_CLASSES_DETAIL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getClassesOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///保存详情
  Future<dynamic> requestSaveClassesRules(HashMap<String, dynamic> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.SAVE_CLASSES, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.updateStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取元数据
  Future<dynamic> getMetaDta() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<GetMetaEntity>(Method.get, url: HttpApi.GET_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      view.getMetaDta(data);
    });
  }
}
