import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../controller/project_one_controller.dart';
import '../iview/project_one_iview.dart';

class ProjectOnePresenter extends BasePagePresenter<ProjectOneIView> with WidgetsBindingObserver {
  ProjectOneController controller;

  ProjectOnePresenter(this.controller);

  ///删除员工档案
  Future<dynamic> delCProject(String uuid) {
    var params = <String, String>{};
    params["project_uuid"] = uuid ?? "";
    return requestNetwork<Object>(Method.get, url: HttpApi.PROJECT_PROJECT_DELETE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.updateStatus();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
