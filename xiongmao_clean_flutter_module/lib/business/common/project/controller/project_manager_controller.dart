import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/project_manager_entity.dart';

class ProjectManagerController extends GetxController {
  /// 用来记录反选的 UUIDs
  var invertUuids = <String>[].obs;

  var uuid = "".obs;

  var searchText = "".obs;

  var listTotal = "0".obs;

  var invertList = <ProjectManagerList>[].obs;

  ///详情的项目列表
  var list = <ProjectManagerList>[].obs;

  var totalNumber = "0".obs;


  void initMyList(int total, List<ProjectManagerList> value) {
    totalNumber.value = total.toString();
    list.value = value.map((item) {
      // 检查 UUID 是否在 invertUuids 中
      item.isSelected = invertUuids.contains(item.uuid) ? true : false;
      return item;
    }).toList();
  }

  void updateMyList(List<ProjectManagerList> value) {
    for (var item in value) {
      // 检查 UUID 是否在 invertUuids 中
      item.isSelected = invertUuids.contains(item.uuid) ? true : false;
    }
    list.addAll(value); // 添加新项目到 list
  }

  ///详情
  var data = ProjectManagerList().obs;

  void updateCustomOne(ProjectManagerList entity) {
    data.value = entity;
  }

  String getSelectedUuids() {
    return list.value.where((item) => item.isGroupClockIn == '1').map((item) => item.uuid).join(',');
  }


  // 新方法：获取 isSelected 为 true 的数据
  List<ProjectManagerList> getSelectedItems() {
    return list.where((item) => item.isSelected).toList();
  }
}
