import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/address_manager_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/classes_manager_entity.dart';
import '../bean/classes_one_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectClassesManagerRulesController extends GetxController {
  var projectUuid = "".obs;
  var projectName = "".obs;

  var uuids = ''.obs; // 已经选择的数据 是这样的 123123,12312,55433,22222

  var listTotal = "0".obs;

  ///详情的项目列表
  var list = <ClassesManagerList>[].obs;

  void initMyList(List<ClassesManagerList> value) {
    list.value = value.toList();
    _updateSelectedStatus(value);
  }

  void updateMyList(List<ClassesManagerList> value) {
    list.value.addAll(value);
    _updateSelectedStatus(value);
    list.refresh();
  }

  /// 更新列表中的 isSelected 状态
  void _updateSelectedStatus(List<ClassesManagerList> items) {
    // 将 uuids 转换为一个 Set，方便快速查找
    Set<String> selectedUuids = uuids.value.split(',').toSet();

    for (var item in items) {
      if (selectedUuids.contains(item.uuid)) {
        item.isSelected = true; // 匹配到的项目设置为 true
      } else {
        item.isSelected = false; // 其他项目保持为 false
      }
    }
  }

  List<ClassesManagerList> getSelectedItems() {
    // 使用 where 方法过滤出 isSelect 为 true 的项
    List<ClassesManagerList> selectedItems = list.where((item) => item.isSelected).toList();
    return selectedItems; // 返回新列表
  }

  ///这里是新增需要到的参数
  var classesList = [
    BaseChooseString("固定班次"),
    BaseChooseString("自由上下班"),
  ].obs;

  ///班次类型
  var classesType = 0.obs;

  var classesSegment = <ClassesOneSegmentList>[].obs;

  ///新增一个新的班段
  void addClassesSegment() {
    ClassesOneSegmentList value = ClassesOneSegmentList();
    classesSegment.add(value);
    classesSegment.refresh();
  }

  ///删除一个新的班段
  void deleteClassesSegment(ClassesOneSegmentList value) {
    classesSegment.remove(value);
    classesSegment.refresh();
  }

  ///是否编辑过
  var isChange = false.obs;

  ///免打卡
  var noClock = false.obs;

  ///未打卡时默认休息
  var defClock = false.obs;

  ///可选择的时间 时段
  List<String> externalDays = ['当日', '次日'];
  List<String> externalSingDays = ['当日'];
  List<String> externalHours = List.generate(24, (i) => i.toString().padLeft(2, '0'));
  List<String> externalMinutes = List.generate(60, (i) => i.toString().padLeft(2, '0'));
  List<String> centerDays = ['至'];
  List<String> external5Minutes = List.generate(12, (i) => (i * 5).toString().padLeft(2, '0'));


  ///上班打卡范围
  var inClassTimeList = GetMetaInClassTimeList().obs;
  ///下班打卡范围
  var outClassTimeList = GetMetaOutClassTimeList().obs;


  ///自由上下班
  var freeWorkStart = ''.obs;
  var freeWorkDays = 0.obs; //类型 当日还是次日
  var freeWorkStartTime = 0.obs;
  var freeWorkEndTimeEnd = 0.obs;
  var freeWorkEnd = ''.obs;
  var freeWorkEndDays = 0.obs; //类型 当日还是次日
  var freeWorkEndStartTime = 0.obs;
  var freeWorkEndEndTime = 0.obs;

  ///分割点
  var avgLine = ''.obs;
  var avgLineDays = 0.obs; //类型 当日还是次日
  var avgLineStartTime = 0.obs;
  var avgLineEndTime = 0.obs;

  ///自由上下班次
  var actualAttendanceTime = '0'.obs;

  ///上午下午分割点
  var splitTimePoint = '0'.obs;
}
