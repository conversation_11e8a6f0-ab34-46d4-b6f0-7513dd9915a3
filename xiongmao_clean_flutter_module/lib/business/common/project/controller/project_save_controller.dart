import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../quality_service/bean/base_media_entity.dart';
import '../bean/job_manager_entity.dart';
import '../bean/project_manager_entity.dart';
import '../bean/project_one_entity.dart';

class ProjectSaveController extends GetxController {
  var project_uuid = ''.obs;

  //项目简称
  var projectShortName = ''.obs;

  //项目全称
  var projectFullName = ''.obs;

  //大区经理
  var regionalManager = '${httpConfig.user_name}'.obs;
  var regionalManagerUUID = '${httpConfig.user_uuid}'.obs;

  //合同公司
  var contractCompanyName = ''.obs;
  var contractCompanyUuid = ''.obs;

  //客户公司
  var customManager = ''.obs;
  var customManagerUUID = ''.obs;

  //项目业态
  var businessFormatManager = ''.obs;
  var businessFormatManagerUUID = ''.obs;

  //服务面积
  var roomSize = ''.obs;

  //备注
  var remark = ''.obs;

  //合作开始日期
  var startDate = ''.obs;

  //合作结束日期
  var endDate = ''.obs;

  //合同人数
  var contactNum = ''.obs;

  //合同照片
  var contractPicList = <BaseMediaEntity>[].obs;

  //拿到岗位的列表
  var jobStrList = <String>[].obs;
  var jobOriginalList = <JobManagerList>[].obs;
  var jobList = <JobManagerList>[].obs;

  void initJobList(List<JobManagerList> value) {
    jobOriginalList.value = value.toList();
    jobStrList.value = value.map((data) => data.jobName!).toList();
  }

  ///添加一个工作岗位
  void addJob(int postion) {
    jobList.add(jobOriginalList.value[postion]);
    jobList.refresh();
    print('这里拿到的列表 ${jobList.length}');
  }

  ///详情
  void initOne(ProjectOneEntity data) {
    projectShortName.value = data.projectShortName ?? '';
    projectFullName.value = data.projectName ?? '';

    regionalManager.value = data.managerUserName ?? '';
    regionalManagerUUID.value = data.managerUserUuid ?? '';

    customManager.value = data.customName ?? '';
    customManagerUUID.value = data.customUuid ?? '';

    //合同公司
    contractCompanyName.value = data.contractCompanyName ?? '';
    contractCompanyUuid.value = data.contractCompanyUuid ?? '';

    businessFormatManager.value = data.projectCatName ?? '';
    businessFormatManagerUUID.value = data.projectCatId ?? '';

    roomSize.value = data.square ?? '';
    startDate.value = data.startTime ?? '';
    endDate.value = data.endTime ?? '';

    contactNum.value = data.contractHumanNum ?? '0';

    remark.value = data.remark ?? '';

    jobList.value = data.jobInfoList!.toList();


    if (data.contractPic != null && data.contractPic!.isNotEmpty) {
      for (String item in data.contractPic!) {
        contractPicList.add(BaseMediaEntity(media_type: '1', media_url: item));
      }
    }
  }
}
