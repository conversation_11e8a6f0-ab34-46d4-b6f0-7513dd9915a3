import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/business_format_entity.dart';
import '../bean/project_manager_entity.dart';

class BusinessFormatController extends GetxController {
  var jumpUUID = ''.obs;

  //父亲的选中
  var catChooseUUID = ''.obs;

  //子类的选中
  var catChildChooseUUID = ''.obs;

  //拿到列表，直接操作
  var list = <BusinessFormatList>[].obs;

  var listChild = <BusinessFormatListChildList>[].obs;

  void initList(List<BusinessFormatList> value) {
    list.value = value.toList();
    if (list.value.isNotEmpty) {
      if (!TextUtil.isEmpty(jumpUUID.value)) {
        for (BusinessFormatList cat in list.value) {
          for (BusinessFormatListChildList catChild in cat.childList!) {
            if (catChild.id == jumpUUID.value) {
              catChooseUUID.value = cat.id ?? '';
              catChildChooseUUID.value = jumpUUID.value;
              listChild.value = cat.childList!.toList();
            }
          }
        }
      } else {
        catChooseUUID.value = list.value[0].id ?? '';
        listChild.value = list.value[0].childList!.toList();
      }
    }
  }

  void initListChild(List<BusinessFormatListChildList> value) {
    listChild.value = value.toList();
  }
}
