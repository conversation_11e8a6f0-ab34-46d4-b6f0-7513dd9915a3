import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/address_manager_entity.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectAddressManagerRulesController extends GetxController {
  var projectUuid = "".obs;
  var projectName = "".obs;

  var uuids = ''.obs; // 已经选择的数据 是这样的 123123,12312,55433,22222
  var keyword = ''.obs;

  var listTotal = "0".obs;

  ///详情的项目列表
  var list = <AddressManagerList>[].obs;

  void initMyList(List<AddressManagerList> value) {
    list.value = value.toList();
    _updateSelectedStatus(list.value);
  }

  void updateMyList(List<AddressManagerList> value) {
    list.value.addAll(value);
    _updateSelectedStatus(list.value);
    list.refresh();
  }

  /// 更新列表中的 isSelected 状态
  void _updateSelectedStatus(List<AddressManagerList> items) {
    // 将 uuids 转换为一个 Set，方便快速查找
    Set<String> selectedUuids = uuids.value.split(',').toSet();

    for (var item in items) {
      if (selectedUuids.contains(item.uuid)) {
        item.isSelect = true; // 匹配到的项目设置为 true
      } else {
        item.isSelect = false; // 其他项目保持为 false
      }
    }
  }

  List<AddressManagerList> getSelectedItems() {
    // 使用 where 方法过滤出 isSelect 为 true 的项
    List<AddressManagerList> selectedItems = list.where((item) => item.isSelect).toList();
    return selectedItems; // 返回新列表
  }
}
