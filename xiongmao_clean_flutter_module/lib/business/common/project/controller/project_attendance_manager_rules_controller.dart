import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/attendance_manager_rules_entity.dart';
import '../bean/project_manager_entity.dart';

class ProjectAttendanceManagerRulesController extends GetxController {
  var projectUuid = "".obs;
  var projectName = "".obs;

  var listTotal = "0".obs;

  var keyword = "".obs;

  ///详情的项目列表
  var list = <AttendanceManagerRulesList>[].obs;

  void initMyList(List<AttendanceManagerRulesList> value) {
    list.value = value.toList();
  }

  void updateMyList(List<AttendanceManagerRulesList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///默认的数据 编辑考勤规则
  var weekList = [
    BaseChooseString("周一"),
    BaseChooseString("周二"),
    BaseChooseString("周三"),
    BaseChooseString("周四"),
    BaseChooseString("周五"),
    BaseChooseString("周六"),
    BaseChooseString("周日"),
  ].obs;

  ///工作日的日期
  var week_invert = <int>[].obs; //这里是每周的情况下显示的内容

  ///工作日多选
  var workDays = ''.obs;

  ///编辑考勤
  var rulesName = "".obs;

  ///领班的负责人
  var rulesSelectUUID = "".obs;

  ///name
  var rulesSelectName = "".obs;

  ///地址的uuids
  var rulesSelectAddressUUID = "".obs;

  ///默认不限制
  var rulesSelectAddressName = "不限制".obs;

  ///班次
  var rulesClassName = "".obs;

  ///班次的uuid
  var rulesClassUuid = "".obs;

  ///监听是否改动过
  var isChange = false.obs;
}
