import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/item/business_cat_item_listview.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/business_format_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/business_cat_child_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/business_format_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/business_format_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 客户管理
class BusinessFormatPage extends StatefulWidget {
  String? pid = '';

  BusinessFormatPage({Key? key, this.pid = ""}) : super(key: key);

  @override
  _BusinessFormatPageState createState() => _BusinessFormatPageState();
}

class _BusinessFormatPageState extends State<BusinessFormatPage> with BasePageMixin<BusinessFormatPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<BusinessFormatPage> implements BusinessFormatIView {
  BusinessFormatPresenter? _presenter;

  final BusinessFormatController _controller = BusinessFormatController();

  @override
  void initState() {
    super.initState();
    _controller.jumpUUID.value = widget.pid ?? '';
    _presenter?.requestBusinessFormatList();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '选择业态',
      ),
      body: Obx(() => Row(
            children: [
              Expanded(
                  child: Container(
                color: Colours.drawer_bg2,
                child: ListView.builder(
                  shrinkWrap: false,
                  itemCount: _controller.list.value.length,
                  itemBuilder: (context, index) {
                    return BusinessCatListView(
                        data: _controller.list.value[index],
                        choose_uuid: _controller.catChooseUUID.value,
                        onClick: () {
                          _controller.catChooseUUID.value = _controller.list.value[index].id ?? '';
                          _controller.initListChild(_controller.list.value[index].childList!);
                        });
                  },
                ),
              )),
              Expanded(
                  flex: 2,
                  child: Align(
                    alignment: Alignment.topCenter,
                    child: Container(
                      color: Colours.white,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _controller.listChild.value.length,
                        itemBuilder: (context, index) {
                          return BusinessCatChildListView(
                              data: _controller.listChild.value[index],
                              catChildChooseUUID: _controller.catChildChooseUUID.value,
                              onClick: () {
                                BoostNavigator.instance.pop(_controller.listChild.value[index]);
                              });
                        },
                      ),
                    ),
                  ))
            ],
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = BusinessFormatPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;
}
