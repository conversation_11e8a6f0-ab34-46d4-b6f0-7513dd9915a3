import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/project_address_manager_rules_controller.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_classes_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_address_manager_item_listview.dart';
import '../item/project_attendance_manager_item_listview.dart';
import '../item/project_classes_manager_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_address_manager_rules_iview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_classes_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_address_manager_rules_persenter.dart';
import '../persenter/project_attendance_manager_rules_persenter.dart';
import '../persenter/project_classes_manager_rules_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 班次管理
class ProjectClassesManagerRulesPage extends StatefulWidget {
  String projectUuid;
  String projectName;
  String uuids;
  bool multiple;

  ProjectClassesManagerRulesPage({
    Key? key,
    required this.projectUuid,
    required this.projectName,
    required this.multiple,
    required this.uuids,
  }) : super(key: key);

  @override
  _ProjectClassesManagerRulesPageState createState() => _ProjectClassesManagerRulesPageState();
}

class _ProjectClassesManagerRulesPageState extends State<ProjectClassesManagerRulesPage> with BasePageMixin<ProjectClassesManagerRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectClassesManagerRulesPage> implements ProjectClassesMangerRulesIView {
  ProjectClassesManagerRulesPresenter? _presenter;

  final ProjectClassesManagerRulesController _controller = ProjectClassesManagerRulesController();

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();

    _controller.uuids.value = widget.uuids;

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;
    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '选择班次',
        centerSubTitle: (TextUtil.isEmpty(_controller.projectName.value) ? httpConfig.project_name : _controller.projectName.value),
        actionName: '添加',
        onPressed: () {
          BoostNavigator.instance.push('ProjectSaveClassesManagerRulesPage', arguments: {
            'project_uuid': widget.projectUuid,
            'project_name': widget.projectName,
          }).then((value) => _onRefresh());
        },
      ),
      body: WillPopScope(
        child: Obx(() => MyRefreshListView(
              itemCount: _controller.list.value.length,
              onRefresh: _onRefresh,
              loadMore: _loadMore,
              padding: const EdgeInsets.only(top: 10),
              hasMore: false,
              itemBuilder: (_, index) {
                return ProjectClassesManagerListView(
                  data: _controller.list[index],
                  onClick: () {
                    if (widget.multiple) {
                      _controller.list[index].isSelected = !_controller.list[index].isSelected;
                      _controller.list.refresh();
                    } else {
                      BoostNavigator.instance.pop(_controller.list[index]);
                    }
                  },
                  onDelete: () {
                    //删除
                    DialogManager.showConfirmDialog(
                      context: context,
                      title: '提示',
                      cancel: '取消',
                      confirm: '确定',
                      message: '是否删除该班次？',
                      onConfirm: () {
                        _presenter?.requestDeleteClassesRules(_controller.list[index].uuid ?? '');
                      },
                      onCancel: () {},
                    );
                  },
                  onEdit: () {
                    BoostNavigator.instance.push('ProjectSaveClassesManagerRulesPage', arguments: {
                      'uuid': _controller.list[index].uuid,
                      'project_uuid': widget.projectUuid,
                      'project_name': widget.projectName,
                    }).then((value) => _onRefresh());
                  },
                );
              },
            )),
        onWillPop: () async {
          if (DialogManager.hasOpenDialogs()) {
            DialogManager.dismissAllDialogs(context);
            return false; // Prevent the app from popping the route
          } else {
            return true; // Allow the app to pop the route
          }
        },
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectClassesManagerRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {}

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }

  @override
  void getClassesOne(ClassesOneEntity one) {}

  @override
  void getMetaDta(GetMetaEntity? data) {
  }
}
