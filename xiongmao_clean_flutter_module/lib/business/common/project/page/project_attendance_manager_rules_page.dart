import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_attendance_manager_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_attendance_manager_rules_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 考勤规则管理
class ProjectAttendanceManagerRulesPage extends StatefulWidget {
  String projectUuid;
  String projectName;
  bool isSelected = false;
  String? choose_uuid = "";

  ProjectAttendanceManagerRulesPage({
    Key? key,
    required this.projectUuid,
    required this.projectName,
    this.choose_uuid = "",
    required this.isSelected,
  }) : super(key: key);

  @override
  _ProjectAttendanceManagerRulesPageState createState() => _ProjectAttendanceManagerRulesPageState();
}

class _ProjectAttendanceManagerRulesPageState extends State<ProjectAttendanceManagerRulesPage> with BasePageMixin<ProjectAttendanceManagerRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectAttendanceManagerRulesPage> implements ProjectAttendanceMangerRulesIView {
  ProjectAttendanceManagerRulesPresenter? _presenter;

  final ProjectAttendanceManagerRulesController _controller = ProjectAttendanceManagerRulesController();

  VoidCallback? refreshListener;
  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();
    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;
    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });

    ///监听切换项目
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
        _onRefresh();
      });
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '考勤规则',
        centerSubTitle: (TextUtil.isEmpty(_controller.projectName.value) ? httpConfig.project_name : _controller.projectName.value),
        actionWidget: Padding(
          padding: const EdgeInsets.only(
            left: 0,
            right: 10,
          ),
          child: Row(
            children: [
              InkWell(
                highlightColor: Colors.transparent, // 透明色
                splashColor: Colors.transparent, // 透明色
                onTap: () {
                  BoostNavigator.instance.push('ProjectSaveAttendanceManagerRulesPage', arguments: {
                    'project_uuid': _controller.projectUuid.value,
                    'project_name': _controller.projectName.value,
                  }).then((value) => _onRefresh());
                },
                child: Padding(
                  padding: EdgeInsets.only(left: 10),
                  child: CommonUtils.getSimpleText('添加', 15, Colours.base_primary_text_title),
                ),
              ),
              InkWell(
                highlightColor: Colors.transparent, // 透明色
                splashColor: Colors.transparent, // 透明色
                onTap: () {
                  /// 如果是
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                    "method": "goto_show_project_dialog",
                    "isChangeAppProject": true,
                    'isNeedAll': false,
                    'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                    'project_uuid': _controller.projectUuid.value,
                  });
                },
                child: const Padding(
                  padding: EdgeInsets.only(left: 16),
                  child: LoadAssetImage(
                    "icon_change",
                    width: 24,
                    height: 24,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      body: WillPopScope(
        child: Obx(() => Column(
              children: [
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                  child: CustomSearchView(
                    hint: '搜索考勤规则',
                    onTextChanged: (text) {
                      _controller.keyword.value = text;
                      _onRefresh();
                    },
                  ),
                ),
                Expanded(
                    child: MyRefreshListView(
                  itemCount: _controller.list.value.length,
                  onRefresh: _onRefresh,
                  loadMore: _loadMore,
                  padding: const EdgeInsets.only(top: 10),
                  hasMore: false,
                  itemBuilder: (_, index) {
                    return ProjectAttendanceManagerListView(
                      data: _controller.list[index],
                      isSelected: widget.isSelected,
                      choose_uuid: widget.choose_uuid ?? '',
                      onClick: (position) {
                        if (position == 1) {
                          if (widget.isSelected) {
                            BoostNavigator.instance.pop(_controller.list[index]);
                          }
                          return;
                        }
                        //去编辑详情
                        BoostNavigator.instance.push('ProjectSaveAttendanceManagerRulesPage', arguments: {
                          'uuid': _controller.list[index].uuid,
                          'project_uuid': _controller.projectUuid.value,
                          'project_name': _controller.projectName.value,
                        }).then((value) => _onRefresh());
                      },
                      onDelete: () {
                        //删除
                        DialogManager.showConfirmDialog(
                          context: context,
                          title: '提示',
                          cancel: '取消',
                          confirm: '确定',
                          message: '是否删除该考勤规则？',
                          onConfirm: () {
                            _presenter?.requestDeleteAttendanceRules(_controller.list[index].uuid ?? '');
                          },
                          onCancel: () {},
                        );
                      },
                    );
                  },
                ))
              ],
            )),
        onWillPop: () async {
          if (DialogManager.hasOpenDialogs()) {
            DialogManager.dismissAllDialogs(context);
            return false; // Prevent the app from popping the route
          } else {
            return true; // Allow the app to pop the route
          }
        },
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectAttendanceManagerRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {
    _onRefresh();
  }

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
    addProjectListener?.call();
  }

  @override
  void getAttendanceRulesOne(AttendanceOneEntity data) {
    // TODO: implement getAttendanceRulesOne
  }
}
