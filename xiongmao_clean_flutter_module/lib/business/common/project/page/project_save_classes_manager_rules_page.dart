import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/classes_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/get_meta_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/custom_brn3_delegate_popup.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/address_manager_entity.dart';
import '../bean/classes_manager_entity.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_classes_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_attendance_manager_item_listview.dart';
import '../item/project_classes_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_classes_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_attendance_manager_rules_persenter.dart';
import '../persenter/project_classes_manager_rules_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 添加班次
class ProjectSaveClassesManagerRulesPage extends StatefulWidget {
  String? uuid;
  String projectName;
  String projectUuid;

  ProjectSaveClassesManagerRulesPage({Key? key, required this.uuid, required this.projectUuid, required this.projectName}) : super(key: key);

  @override
  _ProjectSaveClassesManagerRulesPageState createState() => _ProjectSaveClassesManagerRulesPageState();
}

class _ProjectSaveClassesManagerRulesPageState extends State<ProjectSaveClassesManagerRulesPage> with BasePageMixin<ProjectSaveClassesManagerRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectSaveClassesManagerRulesPage> implements ProjectClassesMangerRulesIView {
  ProjectClassesManagerRulesPresenter? _presenter;

  final ProjectClassesManagerRulesController _controller = ProjectClassesManagerRulesController();

  final _nameController = TextEditingController();
  final _actualAttendanceController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.requestClassesOneRules(widget.uuid ?? '');
    } else {
      _controller.addClassesSegment();
      //如果是新建，默认是8个小时
      _actualAttendanceController.text = '8';
      _controller.freeWorkStart.value = "00:00";
      _controller.freeWorkEnd.value = "23:59";
    }

    ///进来后获取一下元数据的参数
    String? jsonString = SpUtil.getString(Constant.META_DATA);
    if (TextUtil.isEmpty(jsonString)) {
      _presenter?.getMetaDta();
    } else {
      //解析
      if (jsonString != null) {
        Map<String, dynamic> jsonMap = json.decode(jsonString);
        GetMetaEntity metaData = GetMetaEntity.fromJson(jsonMap);
        // 现在可以使用 metaData 对象
        if (metaData.inClassTimeList != null) {
          _controller.inClassTimeList.value = metaData.inClassTimeList!;
        }
        if (metaData.outClassTimeList != null) {
          _controller.outClassTimeList.value = metaData.outClassTimeList!;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (!TextUtil.isEmpty(widget.uuid)) ? '编辑班次' : '添加班次',
        centerSubTitle: (!TextUtil.isEmpty(widget.projectName)) ? widget.projectName : '',
      ),
      body: WillPopScope(
        child: Obx(() => SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  BrnTextInputFormItem(
                    controller: _nameController,
                    title: "班次名称",
                    isRequire: true,
                    hint: "请输入",
                  ),
                  Gaps.line,
                  Container(
                    color: Colors.white,
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Expanded(
                          child: BrnBaseTitle(
                            title: "班次类型",
                            isRequire: true,
                          ),
                        ),
                        Gaps.hGap20,
                        Expanded(
                            flex: 2,
                            child: Container(
                              margin: const EdgeInsets.only(right: 10),
                              child: SelectTabWidget(
                                _controller.classesList.value,
                                key: ValueKey(_controller.classesType.value),
                                multiSelect: false,
                                crossAxisCount: 2,
                                hideMore: false,
                                paddingBottom: 0,
                                paddingTop: 4,
                                tabFontSize: 15,
                                defaultSelectedIndex: [_controller.classesType.value],
                                lastIsAddOne: false,
                                selectedColor: Colours.base_primary,
                                bgSelectedColor: Colours.base_primary_select,
                                bgUnSelectedColor: Colours.base_primary_un_select,
                                childAspectRatio: 6 / 2.1,
                                itemClickCallback: (List<int> indexs) {
                                  LogUtil.e("indexs = $indexs");
                                  _controller.classesType.value = indexs[0].toInt();
                                  FocusScope.of(context).requestFocus(FocusNode());
                                },
                              ),
                            )),
                      ],
                    ),
                  ),
                  Gaps.line,
                  Visibility(
                    visible: _controller.classesType.value == 0,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            // 禁止ListView滚动
                            padding: EdgeInsets.zero,
                            shrinkWrap: true,
                            itemCount: _controller.classesSegment.length,
                            itemBuilder: (_, index) {
                              return ProjectClassesListView(
                                data: _controller.classesSegment[index],
                                index: index,
                                count: _controller.classesSegment.length,
                                outClassTimeList: _controller.outClassTimeList.value,
                                inClassTimeList: _controller.inClassTimeList.value,
                                onDelete: () {
                                  FocusScope.of(context).requestFocus(FocusNode());
                                  _controller.deleteClassesSegment(_controller.classesSegment[index]);
                                },
                                onRefresh: () {
                                  FocusScope.of(context).requestFocus(FocusNode());
                                  _controller.classesSegment.refresh();
                                },
                              );
                            }),
                        Gaps.vGap10,
                        InkWell(
                          child: Container(
                            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
                            width: double.infinity,
                            color: Colors.white,
                            child: CommonUtils.getSimpleText('+ 添加班段', 16, Colours.base_primary_blue),
                          ),
                          onTap: () {
                            FocusScope.of(context).requestFocus(FocusNode());
                            if (_controller.classesSegment.length == 3) {
                              Toast.show("最多添加3个班段");
                              return;
                            }
                            _controller.addClassesSegment();
                          },
                        ),
                        Gaps.vGap10,
                        BrnTextSelectFormItem(
                          title: "上午下午分割点",
                          isRequire: true,
                          value: _controller.avgLine.value,
                          onTap: () {
                            BrnMultiDataPicker(
                              sync: false,
                              context: context,
                              title: '上午下午分割点',
                              delegate: Brn3RowCustomDelegate(
                                days: _controller.externalDays,
                                hours: _controller.externalHours,
                                minutes: _controller.externalMinutes,
                                firstSelectedIndex: _controller.avgLineDays.value,
                                secondSelectedIndex: _controller.avgLineStartTime.value,
                                thirdSelectedIndex: _controller.avgLineEndTime.value,
                              ),
                              confirmClick: (list) {
                                FocusScope.of(context).requestFocus(FocusNode());
                                StringBuffer stringBuffer = StringBuffer();
                                if (list[0] == 1) {
                                  stringBuffer.write('次');
                                }
                                _controller.avgLineDays.value = list[0];
                                _controller.avgLineStartTime.value = list[1];
                                _controller.avgLineEndTime.value = list[2];
                                _controller.avgLine.value = '$stringBuffer${_controller.externalHours[list[1]]}:${_controller.externalMinutes[list[2]]}';
                                _controller.isChange.value = true;
                              },
                            ).show();
                          },
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                          child: CommonUtils.getSimpleText('用于计算半天请假', 15, Colours.base_primary_text_body),
                        ),
                        Container(
                          padding: const EdgeInsets.only(top: 6, bottom: 6, right: 10),
                          color: Colors.white,
                          child: Row(
                            children: [
                              Padding(padding: const EdgeInsets.only(top: 8, left: 10, right: 2), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                              Expanded(
                                child: CommonUtils.getSimpleText(
                                  '免打卡',
                                  16,
                                  Colours.base_primary_text_title,
                                ),
                              ),
                              BrnSwitchButton(
                                value: _controller.noClock.value,
                                onChanged: (bool value) {
                                  FocusScope.of(context).requestFocus(FocusNode());

                                  _controller.noClock.value = value;
                                  _controller.isChange.value = true;
                                },
                              ),
                            ],
                          ),
                        ),
                        Gaps.vGap10,
                      ],
                    ),
                  ),
                  Visibility(
                    visible: _controller.classesType.value == 1,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                          child: CommonUtils.getSimpleText('打卡时间范围', 15, Colours.base_primary_text_body),
                        ),
                        BrnTextSelectFormItem(
                          title: "开始时间",
                          isRequire: true,
                          value: _controller.freeWorkStart.value,
                          onTap: () {
                            BrnMultiDataPicker(
                              sync: false,
                              context: context,
                              title: '开始时间',
                              delegate: Brn3RowCustomDelegate(
                                days: _controller.externalSingDays,
                                hours: _controller.externalHours,
                                minutes: _controller.externalMinutes,
                                firstSelectedIndex: _controller.freeWorkDays.value,
                                secondSelectedIndex: _controller.freeWorkStartTime.value,
                                thirdSelectedIndex: _controller.freeWorkEndTimeEnd.value,
                              ),
                              confirmClick: (list) {
                                FocusScope.of(context).requestFocus(FocusNode());

                                StringBuffer stringBuffer = StringBuffer();
                                if (list[0] == 1) {
                                  stringBuffer.write('次');
                                }
                                _controller.freeWorkDays.value = list[0];
                                _controller.freeWorkStartTime.value = list[1];
                                _controller.freeWorkEndTimeEnd.value = list[2];
                                _controller.freeWorkStart.value = '$stringBuffer${_controller.externalHours[list[1]]}:${_controller.externalMinutes[list[2]]}';
                                _controller.isChange.value = true;
                              },
                            ).show();
                          },
                        ),
                        Gaps.line,
                        BrnTextSelectFormItem(
                          title: "结束时间",
                          isRequire: true,
                          value: _controller.freeWorkEnd.value,
                          onTap: () {
                            BrnMultiDataPicker(
                              sync: false,
                              context: context,
                              title: '结束时间',
                              delegate: Brn3RowCustomDelegate(
                                days: _controller.externalDays,
                                hours: _controller.externalHours,
                                minutes: _controller.externalMinutes,
                                firstSelectedIndex: _controller.freeWorkEndDays.value,
                                secondSelectedIndex: _controller.freeWorkEndStartTime.value,
                                thirdSelectedIndex: _controller.freeWorkEndEndTime.value,
                              ),
                              confirmClick: (list) {
                                FocusScope.of(context).requestFocus(FocusNode());

                                StringBuffer stringBuffer = StringBuffer();
                                if (list[0] == 1) {
                                  stringBuffer.write('次');
                                }
                                _controller.freeWorkEndDays.value = list[0];
                                _controller.freeWorkEndStartTime.value = list[1];
                                _controller.freeWorkEndEndTime.value = list[2];
                                _controller.freeWorkEnd.value = '$stringBuffer${_controller.externalHours[list[1]]}:${_controller.externalMinutes[list[2]]}';
                                _controller.isChange.value = true;
                              },
                            ).show();
                          },
                        ),
                        Gaps.vGap10,
                        BrnTextInputFormItem(
                          controller: _actualAttendanceController,
                          title: "应出勤时长",
                          isRequire: true,
                          unit: "小时",
                          hint: "请输入",
                          onChanged: (_) {
                            _controller.isChange.value = true;
                          },
                        ),
                        Gaps.line,
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.only(top: 6, bottom: 6, right: 10),
                    color: Colors.white,
                    child: Row(
                      children: [
                        Padding(padding: const EdgeInsets.only(top: 8, left: 10, right: 2), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                        Expanded(
                          child: CommonUtils.getSimpleText(
                            '未打卡时默认休息',
                            16,
                            Colours.base_primary_text_title,
                          ),
                        ),
                        BrnSwitchButton(
                          value: _controller.defClock.value,
                          onChanged: (bool value) {
                            FocusScope.of(context).requestFocus(FocusNode());

                            _controller.defClock.value = value;
                            _controller.isChange.value = true;
                          },
                        ),
                      ],
                    ),
                  ),
                  Gaps.vGap10,
                  Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
                    child: CommonUtils.getSimpleText('该日员工未打卡，也没有请假记录，则认为员工这天无需出勤，在该日结束后，该日班次自动变为休息。', 15, Colours.base_primary_text_body),
                  ),
                ],
              ),
            )),
        onWillPop: () async {
          if (DialogManager.hasOpenDialogs()) {
            DialogManager.dismissAllDialogs(context);
            return false; // Prevent the app from popping the route
          } else {
            return true; // Allow the app to pop the route
          }
        },
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '保存',
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());

            if (TextUtil.isEmpty(_nameController.text.toString())) {
              BrnToast.show('请输入班次名称', context);
              return;
            }
            if (_controller.classesType.value == 0) {
              if (TextUtil.isEmpty(_controller.splitTimePoint.value)) {
                BrnToast.show('请选择上午下午分割点', context);
                return;
              }
            } else {}

            HashMap<String, dynamic> hashMap = HashMap<String, dynamic>();
            if (!TextUtil.isEmpty(widget.uuid)) {
              hashMap['uuid'] = '${widget.uuid}';
            }

            hashMap['project_uuid'] = widget.projectUuid;
            hashMap['class_name'] = _nameController.text;
            //班次类型 1固定班次 2自由班次
            hashMap['class_type'] = _controller.classesType.value == 0 ? '1' : '2';

            if (_controller.classesType.value == 0) {
              hashMap['is_no_clock'] = _controller.noClock.value ? '1' : '2'; //是否免打卡 1是2否
              hashMap['avg_line_time'] = _controller.avgLine.value.replaceAll('次', ''); //上下午分割点是否当天 1是 2否
              hashMap['avg_line_is_today'] = '${_controller.avgLineDays.value + 1}'; //上下午分割点是否当天 1是 2否
              hashMap['segment_list'] = json.encode(_controller.classesSegment.value); //班段
            } else {
              hashMap['work_start_time'] = _controller.freeWorkStart.value; //上班开始时间 (自由班次必传)
              hashMap['work_end_time'] = _controller.freeWorkEnd.value.replaceAll('次', ''); //上班结束时间 (自由班次必传)
              hashMap['work_is_today'] = '${_controller.freeWorkEndDays.value + 1}'; //上班结束时间是否今日 1是2否 默认是 (自由班次必传)
              hashMap['work_time_long'] = _actualAttendanceController.text; //应出勤时长(小时) 自由班次必传
            }

            hashMap['is_default_rest'] = _controller.defClock.value ? '1' : '2'; //未打卡时是否默认休息 1是2否(自由班次必传)

            ///等于true 说明改动过
            if (_controller.isChange.value && !TextUtil.isEmpty(widget.uuid)) {
              DialogManager.showConfirmDialog(
                context: context,
                title: '温馨提示',
                cancel: '取消',
                confirm: '确定',
                message: '班次发生了变化，将从本月开始，重置使用该班次的考勤规则下的员工的排班，是否继续？',
                onConfirm: () {
                  //是否需要重新排班 1是 2否 编辑时必传
                  hashMap['is_generate_schedule'] = "1";
                  _presenter?.requestSaveClassesRules(hashMap);
                },
                onCancel: () {},
              );
            } else {
              _presenter?.requestSaveClassesRules(hashMap);
            }
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectClassesManagerRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {
    Toast.show("操作成功");
    BoostNavigator.instance.pop();
  }

  @override
  void getClassesOne(ClassesOneEntity one) {
    _nameController.text = one.className ?? '';

    ///当前的班次类型
    if (!TextUtil.isEmpty(one.classType)) {
      _controller.classesType.value = int.parse(one.classType!) - 1;
    }

    ///上午下午分割点
    if (one.avgLineIsToday == '2') {
      _controller.avgLine.value = '次${one.avgLineTime}';
    } else {
      _controller.avgLine.value = one.avgLineTime ?? '';
    }
    _controller.avgLineDays.value = int.parse(one.avgLineIsToday ?? '1') - 1;

    ///做个分割，
    if (!TextUtil.isEmpty(one.avgLineTime) && one.avgLineTime!.contains(':')) {
      // 分割字符串，获取小时和分钟
      List<String> timeParts = one.avgLineTime!.split(':');
      String hourPart = timeParts[0];
      String minutePart = timeParts[1];

      _controller.avgLineStartTime.value = _controller.externalHours.indexOf(hourPart);
      _controller.avgLineEndTime.value = _controller.externalMinutes.indexOf(minutePart);
    }

    ///是否默认休息
    _controller.defClock.value = (one.isDefaultRest == '1') ? true : false;

    ///是否免打卡 是否免打卡 1是2否(固定班次专用)
    _controller.noClock.value = (one.isNoClock == '1') ? true : false;

    ///应出勤时长
    _actualAttendanceController.text = one.workTimeLong ?? '';

    ///拿班段
    if ('2' == one.classType && one.segmentList != null) {
      if (one.segmentList!.isNotEmpty) {
        ClassesOneSegmentList onceItem = one.segmentList![0];

        ///拿自由上下班的班次时间
        _controller.freeWorkStart.value = onceItem.workStartTime ?? '';
        List<String> timeParts = onceItem.workStartTime!.split(':');
        String hourPart = timeParts[0];
        String minutePart = timeParts[1];
        _controller.freeWorkStartTime.value = _controller.externalHours.indexOf(hourPart);
        _controller.freeWorkEndTimeEnd.value = _controller.externalMinutes.indexOf(minutePart);

        ///是否次日
        if (onceItem.endTimeIsToday== '2') {
          _controller.freeWorkEnd.value = '次${onceItem.workEndTime}';
        } else {
          _controller.freeWorkEnd.value = onceItem.workEndTime ?? '';
        }
        _controller.freeWorkEndDays.value = int.parse(onceItem.endTimeIsToday ?? '1') - 1;

        List<String> timeEndParts = onceItem.workEndTime!.split(':');
        String hourEndPart = timeEndParts[0];
        String minuteEndPart = timeEndParts[1];
        _controller.freeWorkEndStartTime.value = _controller.externalHours.indexOf(hourEndPart);
        _controller.freeWorkEndEndTime.value = _controller.externalMinutes.indexOf(minuteEndPart);
      }
    } else {
      ///拿所有的班段
      _controller.classesSegment.value = one.segmentList ?? [];
    }
  }

  @override
  void getMetaDta(GetMetaEntity? data) {
    if (data != null) {
      SpUtil.putString(Constant.META_DATA, json.encode(data));
      if (data.inClassTimeList != null) {
        _controller.inClassTimeList.value = data.inClassTimeList!;
      }
      if (data.outClassTimeList != null) {
        _controller.outClassTimeList.value = data.outClassTimeList!;
      }
    }
  }
}
