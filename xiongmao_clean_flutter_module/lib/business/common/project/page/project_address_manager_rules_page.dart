import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/project_address_manager_rules_controller.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_address_manager_item_listview.dart';
import '../item/project_attendance_manager_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_address_manager_rules_iview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_address_manager_rules_persenter.dart';
import '../persenter/project_attendance_manager_rules_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 考勤规则管理
class ProjectAddressManagerRulesPage extends StatefulWidget {
  String projectUuid;
  String projectName;
  String uuids;
  bool multiple;

  ProjectAddressManagerRulesPage({
    Key? key,
    required this.projectUuid,
    required this.projectName,
    required this.multiple,
    required this.uuids,
  }) : super(key: key);

  @override
  _ProjectAddressManagerRulesPageState createState() => _ProjectAddressManagerRulesPageState();
}

class _ProjectAddressManagerRulesPageState extends State<ProjectAddressManagerRulesPage> with BasePageMixin<ProjectAddressManagerRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectAddressManagerRulesPage> implements ProjectAddressMangerRulesIView {
  ProjectAddressManagerRulesPresenter? _presenter;

  final ProjectAddressManagerRulesController _controller = ProjectAddressManagerRulesController();

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();

    _controller.uuids.value = widget.uuids;

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;
    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: Colours.base_primary_bg_page,
        appBar: MyAppBar(
          centerTitle: '选择打卡地点',
          centerSubTitle: (TextUtil.isEmpty(_controller.projectName.value) ? httpConfig.project_name : _controller.projectName.value),
          actionName: '添加地点',
          onPressed: () {
            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_add_address", "project_uuid": _controller.projectUuid.value});
          },
        ),
        body: WillPopScope(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                color: Colors.white,
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                child: CustomSearchView(
                  hint: '搜索打卡地点',
                  onTextChanged: (text) {
                    _controller.keyword.value = text;
                    _onRefresh();
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  left: 14,
                  right: 14,
                  top: 10,
                ),
                child: CommonUtils.getSimpleText('在如下地点范围内拍摄时，会自动校准', 13, Colours.base_primary_text_title),
              ),
              Expanded(
                  child: Obx(() => MyRefreshListView(
                        itemCount: _controller.list.value.length,
                        onRefresh: _onRefresh,
                        loadMore: _loadMore,
                        padding: const EdgeInsets.only(top: 10),
                        hasMore: false,
                        itemBuilder: (_, index) {
                          return ProjectAddressManagerListView(
                            data: _controller.list[index],
                            onClick: () {
                              _controller.list[index].isSelect = !_controller.list[index].isSelect;
                              _controller.list.refresh();
                            },
                            onDelete: () {
                              //删除
                              DialogManager.showConfirmDialog(
                                context: context,
                                title: '提示',
                                cancel: '取消',
                                confirm: '确定',
                                message: '是否删除该地址？',
                                onConfirm: () {
                                  _presenter?.requestDeleteAddressRules(_controller.list[index].uuid ?? '');
                                },
                                onCancel: () {},
                              );
                            },
                          );
                        },
                      )))
            ],
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          },
        ),
        bottomNavigationBar: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: Row(
            children: [
              Expanded(
                  child: InkWell(
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  decoration: BoxDecoration(
                    color: Colours.base_primary_un_select,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: CommonUtils.getSimpleText('不限制', 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                ),
                onTap: () {
                  BoostNavigator.instance.pop(0);
                },
              )),
              Gaps.hGap10,
              Expanded(
                  child: InkWell(
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  decoration: BoxDecoration(
                    color: Colours.base_primary,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: CommonUtils.getSimpleText('确定', 16, Colours.white, textAlign: TextAlign.center),
                ),
                onTap: () {
                  if (_controller.getSelectedItems().isEmpty) {
                    Toast.show('请选择打卡地点');
                    return;
                  }
                  BoostNavigator.instance.pop(_controller.getSelectedItems());
                },
              )),
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectAddressManagerRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {}

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }
}
