import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/toast_utils.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';
import '../controller/project_one_controller.dart';
import '../iview/project_one_iview.dart';
import '../persenter/project_one_persenter.dart';

///项目详情
class ProjectOneWebPage extends StatefulWidget {
  String projectName;
  String projectUuid;

  ProjectOneWebPage({required this.projectName, required this.projectUuid});

  @override
  _ProjectOneWebPageState createState() => _ProjectOneWebPageState();
}

class _ProjectOneWebPageState extends State<ProjectOneWebPage> with BasePageMixin<ProjectOneWebPage, PowerPresenter<dynamic>> implements ProjectOneIView {
  ProjectOnePresenter? _presenter;

  final ProjectOneController _controller = ProjectOneController();

  late XmWebController controller;

  String url = "";

  @override
  void initState() {
    super.initState();
    url = "${httpConfig.getServerType}/project?project_uuid=${widget.projectUuid}&role_id=${httpConfig.role_id}&version=${httpConfig.version}";
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '项目详情',
        actionWidget: Padding(
          padding: const EdgeInsets.only(right: 16),
          child: (httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_REGIONAL_MANAGER_ID)
              ? Row(
                  children: [
                    InkWell(
                      child: CommonUtils.getSimpleText('删除', 14, Colours.base_primary_text_title),
                      onTap: () {
                        ///如果是超级管理员 或者 超管 再删除
                        DialogManager.showConfirmDialog(
                          context: context,
                          title: '提示',
                          cancel: '取消',
                          confirm: '确定',
                          message: '是否删除该项目？',
                          onConfirm: () {
                            _presenter?.delCProject(widget.projectUuid ?? '');
                          },
                          onCancel: () {},
                        );
                      },
                    ),
                    Gaps.hGap10,
                    InkWell(
                      child: CommonUtils.getSimpleText('编辑', 14, Colours.base_primary_text_title),
                      onTap: () {
                        BoostNavigator.instance.push('ProjectSavePage', arguments: {
                          'project_uuid': widget.projectUuid,
                        }).then((value) => controller.reload());
                      },
                    ),
                  ],
                )
              : Container(),
        ),
      ),
      body: WillPopScope(
          child: XmWebView(
            onXmJsMsgCall: (XmJsMsgCall msgCall) {
              var decode = json.decode(msgCall.message);
              print('H5调用原声的回调name --> ${decode.toString()}');

              var name = decode["name"];
              var data = decode["data"];

              ///回调token给用户
              switch (name) {
                case "getUserToken":
                  var token = httpConfig.token ?? "";
                  controller.realController.runJavaScript("window.WebViewJavascriptBridgegetUserTokenCallBack('" + token + "')"); //切记这个字符串还得用''引住
                  break;
                case "WebViewJavascriptBridge_goPayInsurance":
                  // print('${data['url']}  - ${data['project_name']}');
                  // BoostNavigator.instance.push('InsureRulesWebPage', arguments: {
                  //   'url': data['url'],
                  // }).then((value) => controller.reload());
                  ///先跳转原生
                  // BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoInsuranceScheme", "url": data['url'], 'project_name': data['project_name']});
                  break;
                case "WebViewJavascriptBridge_editProject":
                  int num = data['num'];
                  switch (num) {
                    case 1:
                      BoostNavigator.instance.push('ProjectAttendanceManagerRulesPage', arguments: {'project_uuid': widget.projectUuid, 'project_name': widget.projectName}).then((value) => controller.reload());
                      break;
                    case 4:
                      BoostNavigator.instance.push('regionPage', arguments: {'project_uuid': widget.projectUuid, 'project_name': widget.projectName}).then((value) => controller.reload());
                      break;
                    case 5:
                      var insuranceProductUuid = data['insurance_product_uuid'];
                      BoostNavigator.instance.push('insureMarketPage', arguments: {'project_uuid': widget.projectUuid, 'project_name': widget.projectName, 'insurance_product_uuid': insuranceProductUuid}).then((value) => controller.reload());
                      break;
                    case 6:
                      BoostNavigator.instance.push('materialSysManagerPage', arguments: {'project_uuid': widget.projectUuid, 'project_name': widget.projectName}).then((value) => controller.reload());
                      break;
                  }
                  break;
              }
            },
            controllerSettingBack: (value) {
              controller = value;
              LogUtil.e("object----url---" + (url ?? ""));
              controller.loadRequest(Uri.parse(url ?? ""));
            },
            domainM2: false,
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectOnePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestPermissionData(PermissionEntity data) {}

  @override
  void updateStatus() {
    Toast.show('操作成功');
    Future.delayed(const Duration(seconds: 2), () {
      BoostNavigator.instance.pop();
    });
  }
}
