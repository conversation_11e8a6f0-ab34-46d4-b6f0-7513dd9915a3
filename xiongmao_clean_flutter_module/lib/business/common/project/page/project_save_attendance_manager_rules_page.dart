import 'dart:collection';
import 'dart:ffi';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/address_manager_entity.dart';
import '../bean/classes_manager_entity.dart';
import '../controller/project_attendance_manager_rules_controller.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_attendance_manager_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_attendance_manager_rules_iview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_attendance_manager_rules_persenter.dart';
import '../persenter/project_manager_persenter.dart';

/// 考勤规则管理
class ProjectSaveAttendanceManagerRulesPage extends StatefulWidget {
  String? uuid;
  String projectName;
  String projectUuid;

  ProjectSaveAttendanceManagerRulesPage({Key? key, required this.uuid, required this.projectUuid, required this.projectName}) : super(key: key);

  @override
  _ProjectSaveAttendanceManagerRulesPageState createState() => _ProjectSaveAttendanceManagerRulesPageState();
}

class _ProjectSaveAttendanceManagerRulesPageState extends State<ProjectSaveAttendanceManagerRulesPage> with BasePageMixin<ProjectSaveAttendanceManagerRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectSaveAttendanceManagerRulesPage> implements ProjectAttendanceMangerRulesIView {
  ProjectAttendanceManagerRulesPresenter? _presenter;

  final ProjectAttendanceManagerRulesController _controller = ProjectAttendanceManagerRulesController();

  final _nameController = TextEditingController();

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();
    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.requestAttendanceRulesOne(widget.uuid ?? '');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (!TextUtil.isEmpty(widget.uuid)) ? '编辑考勤规则' : '添加考勤规则',
        centerSubTitle: (!TextUtil.isEmpty(widget.projectName)) ? widget.projectName : '',
      ),
      body: WillPopScope(
        child: Obx(() => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                BrnTextInputFormItem(
                  controller: _nameController,
                  title: "考勤规则名称",
                  isRequire: true,
                  hint: "请输入",
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "领班/负责人",
                  value: _controller.rulesSelectName.value,
                  onTap: () {
                    _controller.isChange.value = true;
                    BoostNavigator.instance.push('selectPersonnelPage', arguments: {'title': '选择领班/负责人', 'multiple': true, 'status': '1', 'project_uuid': '${_controller.projectUuid.value}', 'role_id': HttpConfig.ROLE_LEADER_ID, 'uuids': _controller.rulesSelectUUID.value}).then((value) {
                      if (value != null) {
                        List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                        if (data.isNotEmpty) {
                          _controller.rulesSelectName.value = data.map((item) => item.userName).where((name) => name != null).join(',');
                          _controller.rulesSelectUUID.value = data.map((item) => item.uuid).where((uuid) => uuid != null).join(',');
                        }
                      }
                    });
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                  child: CommonUtils.getSimpleText('领班只能修改其负责的“考勤规则”的考勤', 15, Colours.base_primary_text_body),
                ),
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.only(top: 6, bottom: 6, right: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(padding: const EdgeInsets.only(top: 12, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                      Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 10), child: CommonUtils.getSimpleText("工作日", 16, Colours.base_primary_text_title)),
                      Expanded(
                          child: Container(
                        margin: const EdgeInsets.only(left: 0, right: 0),
                        child: SelectTabWidget(
                          _controller.weekList.value,
                          key: UniqueKey(),
                          multiSelect: true,
                          crossAxisCount: 4,
                          hideMore: false,
                          paddingBottom: 0,
                          paddingTop: 0,
                          tabFontSize: 15,
                          defaultSelectedIndex: _controller.week_invert.value,
                          lastIsAddOne: false,
                          selectedColor: Colours.base_primary,
                          bgSelectedColor: Colours.base_primary_select,
                          bgUnSelectedColor: Colours.base_primary_un_select,
                          childAspectRatio: 6 / 4,
                          itemClickCallback: (List<int> indexs) {
                            LogUtil.e("indexs = $indexs");
                            _controller.isChange.value = true;
                            _controller.workDays.value = indexs.map((element) => element + 1).toList().join(",");
                          },
                        ),
                      )),
                    ],
                  ),
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "班次",
                  value: _controller.rulesClassName.value,
                  isRequire: true,
                  onTap: () {
                    _controller.isChange.value = true;
                    BoostNavigator.instance.push('ProjectClassesManagerRulesPage', arguments: {
                      'project_uuid': widget.projectUuid,
                      'project_name': widget.projectName,
                      'uuids': _controller.rulesClassUuid.value,
                    }).then((value) {
                      if (value != null) {
                        if (value is ClassesManagerList) {
                          ClassesManagerList data = value;
                          _controller.rulesClassName.value = data.className ?? '';
                          _controller.rulesClassUuid.value = data.uuid ?? '';
                        }
                      }
                    });
                  },
                ),
                Gaps.vGap10,
                BrnTextSelectFormItem(
                  title: "打卡地点",
                  isRequire: true,
                  value: _controller.rulesSelectAddressName.value,
                  onTap: () {
                    _controller.isChange.value = true;
                    BoostNavigator.instance.push('ProjectAddressManagerRulesPage', arguments: {
                      'project_uuid': widget.projectUuid,
                      'project_name': widget.projectName,
                      'multiple': true,
                      'uuids': _controller.rulesSelectAddressUUID.value,
                    }).then((value) {
                      if (value != null) {
                        if (value is int) {
                          _controller.rulesSelectAddressName.value = '不限制';
                          _controller.rulesSelectAddressUUID.value = '';
                        } else if (value is List<AddressManagerList>) {
                          List<AddressManagerList> data = value;
                          if (data.isNotEmpty) {
                            _controller.rulesSelectAddressName.value = data.map((item) => item.address).where((name) => name != null).join(',');
                            _controller.rulesSelectAddressUUID.value = data.map((item) => item.uuid).where((uuid) => uuid != null).join(',');
                          }
                        }
                      }
                    });
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
                  child: CommonUtils.getSimpleText('所选地点范围内使⽤“本APP”拍摄并保存，即可完成打卡', 15, Colours.base_primary_text_body),
                ),
              ],
            )),
        onWillPop: () async {
          if (DialogManager.hasOpenDialogs()) {
            DialogManager.dismissAllDialogs(context);
            return false; // Prevent the app from popping the route
          } else {
            return true; // Allow the app to pop the route
          }
        },
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '保存',
          onTap: () {
            if (TextUtil.isEmpty(_nameController.text.toString())) {
              BrnToast.show('请输入考勤规则名称', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.workDays.value)) {
              BrnToast.show('请选择工作日', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.rulesClassUuid.value)) {
              BrnToast.show('请选择班次', context);
              return;
            }
            HashMap<String, dynamic> hashMap = HashMap<String, dynamic>();
            if (!TextUtil.isEmpty(widget.uuid)) {
              hashMap['uuid'] = '${widget.uuid}';
            }
            hashMap['project_uuid'] = widget.projectUuid;
            hashMap['group_name'] = _nameController.text.toString();
            hashMap['project_class_uuid'] = _controller.rulesClassUuid.value;
            hashMap['address_uuid_list'] = _controller.rulesSelectAddressUUID.value;
            hashMap['work_day_list'] = _controller.workDays.value;
            hashMap['group_leader_uuid'] = _controller.rulesSelectUUID.value;

            ///等于true 说明改动过
            if (_controller.isChange.value && !TextUtil.isEmpty(widget.uuid)) {
              DialogManager.showConfirmDialog(
                context: context,
                title: '温馨提示',
                cancel: '取消',
                confirm: '确定',
                message: '工作日或班次发生了变化，是否从本月起更新该考勤规则下员工的排班？',
                onConfirm: () {
                  //是否需要重新排班 1是 2否 编辑时必传
                  hashMap['is_generate_schedule'] = "1";
                  _presenter?.requestSaveAttendanceRules(hashMap);
                },
                onCancel: () {},
              );
            } else {
              _presenter?.requestSaveAttendanceRules(hashMap);
            }
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectAttendanceManagerRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void updateStatus() {
    Toast.show("操作成功");
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }

  @override
  void getAttendanceRulesOne(AttendanceOneEntity data) {
    //拿到名字
    _nameController.text = data.groupName ?? "";
    //班次
    _controller.rulesClassName.value = data.projectClassName ?? '';
    _controller.rulesClassUuid.value = data.projectClassUuid ?? '';
    //领班
    if (data.leaderList!.isNotEmpty) {
      //领班
      _controller.rulesSelectName.value = data.leaderList!.map((item) => item.userName).where((name) => name != null).join(',');
      //领班的uuid
      _controller.rulesSelectUUID.value = data.leaderList!.map((item) => item.uuid).where((uuid) => uuid != null).join(',');
    }
    //打卡地点
    if (data.addressList!.isNotEmpty) {
      //打卡地点
      _controller.rulesSelectAddressName.value = data.addressList!.map((item) => item.address).where((name) => name != null).join(',');
      //打卡地点的uuid
      _controller.rulesSelectAddressUUID.value = data.addressList!.map((item) => item.uuid).where((uuid) => uuid != null).join(',');
    }
    //日期
    if (data.workDayList!.isNotEmpty) {
      _controller.week_invert.value = data.workDayList!.map((str) => (int.parse(str) - 1)).toList();
      _controller.workDays.value = _controller.week_invert.value.map((element) => element + 1).toList().join(",");
    }
  }
}
