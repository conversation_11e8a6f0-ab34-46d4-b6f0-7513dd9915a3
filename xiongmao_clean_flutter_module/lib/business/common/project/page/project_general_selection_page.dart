import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/project_manager_entity.dart';
import '../controller/project_manager_controller.dart';
import '../item/project_general_selected_item_listview.dart';
import '../item/project_manager_item_listview.dart';
import '../iview/project_manager_iview.dart';
import '../persenter/project_manager_persenter.dart';

/// 根据角色来区分当前项目下可选的项目
class ProjectGeneralSelectionPage extends StatefulWidget {
  String? choose_uuid = "";

  String? title = '可集体打卡的项目';

  ProjectGeneralSelectionPage({Key? key, this.choose_uuid = "", this.title}) : super(key: key);

  @override
  _ProjectGeneralSelectionPageState createState() => _ProjectGeneralSelectionPageState();
}

class _ProjectGeneralSelectionPageState extends State<ProjectGeneralSelectionPage> with BasePageMixin<ProjectGeneralSelectionPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectGeneralSelectionPage> implements ProjectMangerIView {
  ProjectManagerPresenter? _presenter;

  final ProjectManagerController _controller = ProjectManagerController();

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: widget.title ?? '可集体打卡的项目',
      ),
      body: Column(
        children: [
          ///列表
          Obx(() => Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.list.value.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.listTotal.value) > _controller.list.value.length,
                padding: const EdgeInsets.only(top: 10),
                itemBuilder: (_, index) {
                  return ProjectGeneralSelectedListView(
                    data: _controller.list[index],
                    isSelected: true,
                    choose_uuid: widget.choose_uuid ?? '',
                    onClick: () {
                      _controller.list.refresh();
                    },
                  );
                },
              )))
        ],
      ),

      ///这里是编辑 添加项目
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: Row(
          children: [
            InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
                decoration: BoxDecoration(
                  border: Border.all(color: Colours.base_primary, width: 1),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('全选', 14, Colours.base_primary),
              ),
              onTap: () {
                settingAllStatus(true);
              },
            ),
            Gaps.hGap10,
            InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
                decoration: BoxDecoration(
                  border: Border.all(color: Colours.base_primary, width: 1),
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('都不选', 14, Colours.base_primary),
              ),
              onTap: () {
                settingAllStatus(false);
              },
            ),
            Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                _presenter?.requestSave();
              },
            ))
          ],
        ),
      ),
    );
  }

  void settingAllStatus(bool status) {
    for (int i = 0; i < _controller.list.length; i++) {
      _controller.list.value[i].isGroupClockIn = status ? '1' : '2';
    }
    _controller.list.refresh();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectManagerPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateStatus() {
    BoostNavigator.instance.pop();
  }
}
