import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/textediting_controller_manager.dart';
import '../../company/bean/department_company_contract_entity.dart';
import '../../custom/bean/custom_manager_entity.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/business_format_entity.dart';
import '../bean/create_project_entity.dart';
import '../bean/job_manager_entity.dart';
import '../controller/project_save_controller.dart';
import '../item/job_item_listview.dart';
import '../iview/project_save_iview.dart';
import '../persenter/project_save_persenter.dart';

/// 添加、新增项目
class ProjectSavePage extends StatefulWidget {
  String? project_uuid;

  ProjectSavePage({Key? key, this.project_uuid}) : super(key: key);

  @override
  _ProjectSavePageState createState() => _ProjectSavePageState();
}

class _ProjectSavePageState extends State<ProjectSavePage> with BasePageMixin<ProjectSavePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ProjectSavePage> implements ProjectSaveIView {
  ProjectSavePresenter? _presenter;

  final ProjectSaveController _controller = ProjectSaveController();

  final TextEditingControllerManager controllerManager = TextEditingControllerManager();

  @override
  void initState() {
    super.initState();

    _presenter?.requestWorkJob();
    if (!TextUtil.isEmpty(widget.project_uuid)) {
      _controller.project_uuid.value = widget.project_uuid!;
      _presenter?.requestProjectOne();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (TextUtil.isEmpty(widget.project_uuid)) ? '新建项目' : '编辑项目',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: Obx(() => SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  child: CommonUtils.getSimpleText('基础信息', 14, Colours.base_primary_text_body),
                ),
                BrnTextInputFormItem(
                  title: "项目简称",
                  hint: "请输入",
                  controller: controllerManager.getController('projectShortName', _controller.projectShortName),
                  isRequire: true,
                  themeData: BrnFormItemConfig(
                      subTitleTextStyle: BrnTextStyle(
                    fontSize: 11,
                  )),
                  subTitle: '选择项目后会按简称显示，避免因项目名称过长，而显示不完整',
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(6), // 限制输入长度为6个字符
                  ],
                  onChanged: (newValue) {
                    _controller.projectShortName.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "项目全称",
                  hint: "请输入",
                  controller: controllerManager.getController('projectFullName', _controller.projectFullName),
                  onChanged: (newValue) {
                    _controller.projectFullName.value = newValue;
                  },
                ),
                Gaps.line,
                BrnBaseTitle(
                  title: "运营负责人",
                  isRequire: true,
                  subTitle: '设置后可避免其他运营负责人访问、管理该项目',
                  customActionWidget: InkWell(
                    child: Center(
                      child: Row(
                        children: [
                          Visibility(
                            visible: (!TextUtil.isEmpty(_controller.regionalManager.value)),
                            child: InkWell(
                              child: const LoadAssetImage(
                                'icon_base_round_close',
                                width: 20,
                                height: 20,
                              ),
                              onTap: () {
                                _controller.regionalManager.value = '';
                                _controller.regionalManagerUUID.value = '';
                              },
                            ),
                          ),
                          Gaps.hGap10,
                          CommonUtils.getSimpleText(_controller.regionalManager.value, 16, Colours.base_primary_text_body, height: -0.2),
                          Visibility(child: CommonUtils.getSimpleText('请选择', 16, Colours.base_primary_text_caption, height: -0.2), visible: (TextUtil.isEmpty(_controller.regionalManager.value))),
                          Gaps.hGap4,
                          const LoadAssetImage(
                            'base/icon_base_gray_arrow',
                            width: 14,
                            height: 14,
                          )
                        ],
                      ),
                    ),
                    onTap: () {
                      BoostNavigator.instance.push('selectPersonnelPage', arguments: {'title': '选择运营负责人', 'status': '1', 'is_head_office': '1', 'uuids': _controller.regionalManagerUUID.value}).then((value) {
                        if (value != null) {
                          List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                          if (data.isNotEmpty) {
                            _controller.regionalManager.value = data[0].userName ?? '';
                            _controller.regionalManagerUUID.value = data[0].uuid ?? '';
                          }
                        }
                      });
                    },
                  ),
                  onTip: () {},
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "合同公司",
                  value: _controller.contractCompanyName.value,
                  onTap: () {
                    BoostNavigator.instance.push('companyChoiceContractPage', arguments: {'isSelected': false, 'showBottomBut': false, 'uuid': _controller.contractCompanyUuid.value}).then((value) {
                      if (value != null) {
                        DepartmentCompanyContractList data = value as DepartmentCompanyContractList;
                        _controller.contractCompanyUuid.value = data.uuid ?? '';
                        _controller.contractCompanyName.value = data.companyName ?? '';
                      } else {
                        _controller.contractCompanyName.value = '';
                        _controller.contractCompanyUuid.value = '';
                      }
                    });
                  },
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "客户公司",
                  value: _controller.customManager.value,
                  onTap: () {
                    BoostNavigator.instance.push('customManagerPage', arguments: {'choose_uuid': _controller.customManagerUUID.value, 'isSelected': true}).then((value) {
                      if (value is CustomManagerList) {
                        print('选择客户 来这里了');
                        CustomManagerList data = value;
                        _controller.customManagerUUID.value = data.uuid ?? '';
                        _controller.customManager.value = data.customName ?? '';
                      }
                    });
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  child: CommonUtils.getSimpleText('合同信息', 14, Colours.base_primary_text_body),
                ),
                BrnTextSelectFormItem(
                  title: "项目业态",
                  isRequire: true,
                  value: _controller.businessFormatManager.value,
                  onTap: () {
                    BoostNavigator.instance.push('BusinessFormatPage', arguments: {'pid': _controller.businessFormatManagerUUID.value}).then((value) {
                      if (value is BusinessFormatListChildList) {
                        BusinessFormatListChildList data = value;
                        _controller.businessFormatManager.value = data.catName ?? '';
                        _controller.businessFormatManagerUUID.value = data.id ?? '';
                      }
                    });
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "服务面积(㎡)",
                  hint: "请输入",
                  inputType: BrnInputType.number,
                  controller: controllerManager.getController('roomSize', _controller.roomSize),
                  onChanged: (newValue) {
                    _controller.roomSize.value = newValue;
                  },
                ),
                Gaps.line,
                Container(
                  padding: const EdgeInsets.only(left: 20, top: 10, bottom: 10),
                  width: double.infinity,
                  child: CommonUtils.getSimpleText('备注', 14, Colours.base_primary_text_title),
                  color: Colours.white,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  color: Colours.white,
                  child: BrnInputText(
                    maxHeight: 200,
                    minHeight: 50,
                    borderRadius: 10.0,
                    minLines: 1,
                    textString: _controller.remark.value ?? '',
                    maxLength: 150,
                    bgColor: Colours.base_primary_bg_page,
                    autoFocus: false,
                    textInputAction: TextInputAction.newline,
                    hint: '请输入150字以内的备注信息',
                    padding: const EdgeInsets.fromLTRB(10, 10, 10, 10),
                    onTextChange: (text) {
                      _controller.remark.value = text;
                    },
                    onSubmit: (text) {},
                  ),
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "合作开始日期",
                  value: _controller.startDate.value,
                  onTap: () {
                    BrnDatePicker.showDatePicker(
                      themeData: BrnPickerConfig(
                        pickerHeight: 300,
                      ),
                      context,
                      pickerMode: BrnDateTimePickerMode.datetime,
                      dateFormat: 'yyyy年,MMMM月,dd日',
                      onConfirm: (dateTime, list) {
                        if (dateTime != null) {
                          // 确保 dateTime 不为 null
                          _controller.startDate.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd');
                        }
                      },
                    );
                  },
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "合作结束日期",
                  value: _controller.endDate.value,
                  onTap: () {
                    BrnDatePicker.showDatePicker(
                      themeData: BrnPickerConfig(
                        pickerHeight: 300,
                      ),
                      context,
                      pickerMode: BrnDateTimePickerMode.datetime,
                      dateFormat: 'yyyy年,MMMM月,dd日',
                      onConfirm: (dateTime, list) {
                        if (dateTime != null) {
                          // 确保 dateTime 不为 null
                          _controller.endDate.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd');
                        }
                      },
                    );
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "合同人数",
                  hint: "请输入",
                  inputType: BrnInputType.number,
                  controller: controllerManager.getController('contactNum', _controller.contactNum),
                  onChanged: (newValue) {
                    _controller.contactNum.value = '';
                  },
                ),
                Gaps.line,
                Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10),
                  color: Colours.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText('合同照片', 16, Colours.base_primary_text_title),
                      CommonUtils.getSimpleText('可上传多张', 16, Colours.base_primary_text_caption),
                    ],
                  ),
                ),
                Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: CustomImageGridView(
                    imageUrls: _controller.contractPicList.value,
                    maxImageCount: 4,
                    onImageUrlsChanged: (List<BaseMediaEntity> updatedUrls) {
                      _controller.contractPicList.value = updatedUrls;
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                  child: Row(
                    children: [
                      Expanded(
                          child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          CommonUtils.getSimpleText('*', 14, Colours.red),
                          CommonUtils.getSimpleText('岗位', 14, Colours.base_primary_text_title),
                        ],
                      )),
                      Expanded(
                          flex: 3,
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              CommonUtils.getSimpleText('*', 14, Colours.red),
                              CommonUtils.getSimpleText('编辑(单位:人)', 14, Colours.base_primary_text_title),
                            ],
                          )),
                    ],
                  ),
                ),

                ///动态增加的岗位
                ListView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: _controller.jobList.length,
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return JobListView(
                        data: _controller.jobList[index],
                        onClick: () {
                          _controller.jobList.removeAt(index);
                          _controller.jobList.refresh();
                        },
                      );
                    }),
                InkWell(
                  child: Container(
                    width: double.infinity,
                    color: Colors.white,
                    padding: const EdgeInsets.only(
                      top: 10,
                      bottom: 10,
                    ),
                    alignment: Alignment.center,
                    child: CommonUtils.getSimpleText('+ 添加', 14, Colours.base_primary),
                  ),
                  onTap: () {
                    SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择岗位', _controller.jobStrList.value, -1, (index, selectedText) {
                      print('当前选择的下标：$index');
                      print('当前选择的文本内容：$selectedText');
                      for (JobManagerList data in _controller.jobList.value) {
                        if (data.jobName == selectedText) {
                          BrnToast.show('您已选择该岗位，请勿重复选择', context);
                          return;
                        }
                      }
                      _controller.addJob(index);
                    });
                  },
                ),
                Gaps.vGap100,
              ],
            ),
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '保存',
          onTap: () {
            if (TextUtil.isEmpty(_controller.projectShortName.value)) {
              BrnToast.show('请输入项目简称', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.regionalManagerUUID.value)) {
              BrnToast.show('请选择运营负责人', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.businessFormatManagerUUID.value)) {
              BrnToast.show('请选择项目业态', context);
              return;
            }
            _presenter?.saveCompanyProject();
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ProjectSavePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void saveStatus(CreateProjectEntity data) {
    BrnToast.show(TextUtil.isEmpty(widget.project_uuid) ? '创建成功' : '编辑成功', context);
    BoostNavigator.instance.pop();
    if (TextUtil.isEmpty(widget.project_uuid)) {
      BoostNavigator.instance.push('ProjectOneWebPage', arguments: {
        'project_uuid': '${data.uuid}',
        'project_name': httpConfig.project_name,
      });
    }
  }

  @override
  void dispose() {
    super.dispose();
    controllerManager.dispose();
  }
}
