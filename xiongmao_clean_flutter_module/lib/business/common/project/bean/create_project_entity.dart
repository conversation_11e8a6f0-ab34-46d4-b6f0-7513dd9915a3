import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/create_project_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/create_project_entity.g.dart';

@JsonSerializable()
class CreateProjectEntity {
	String? uuid;

	CreateProjectEntity();

	factory CreateProjectEntity.fromJson(Map<String, dynamic> json) => $CreateProjectEntityFromJson(json);

	Map<String, dynamic> toJson() => $CreateProjectEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}