import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/business_format_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/business_format_entity.g.dart';

@JsonSerializable()
class BusinessFormatEntity {
	List<BusinessFormatList>? list;

	BusinessFormatEntity();

	factory BusinessFormatEntity.fromJson(Map<String, dynamic> json) => $BusinessFormatEntityFromJson(json);

	Map<String, dynamic> toJson() => $BusinessFormatEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BusinessFormatList {
	String? id;
	@JSONField(name: "cat_name")
	String? catName;
	String? pid;
	@JSONField(name: "cat_level")
	String? catLevel;
	@JSONField(name: "child_list")
	List<BusinessFormatListChildList>? childList;

	BusinessFormatList();

	factory BusinessFormatList.fromJson(Map<String, dynamic> json) => $BusinessFormatListFromJson(json);

	Map<String, dynamic> toJson() => $BusinessFormatListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class BusinessFormatListChildList {
	String? id;
	@JSONField(name: "cat_name")
	String? catName;
	String? pid;
	@JSONField(name: "cat_level")
	String? catLevel;

	BusinessFormatListChildList();

	factory BusinessFormatListChildList.fromJson(Map<String, dynamic> json) => $BusinessFormatListChildListFromJson(json);

	Map<String, dynamic> toJson() => $BusinessFormatListChildListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}