import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/classes_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/classes_one_entity.g.dart';

@JsonSerializable()
class ClassesOneEntity {
	String? uuid;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "avg_line_time")
	String? avgLineTime;
	@JSONField(name: "avg_line_is_today")
	String? avgLineIsToday;
	@JSONField(name: "is_no_clock")
	String? isNoClock;
	@JSONField(name: "class_type")
	String? classType;
	@JSONField(name: "is_default_rest")
	String? isDefaultRest;
	@JSONField(name: "work_time_long")
	String? workTimeLong;
	@J<PERSON>NField(name: "segment_list")
	List<ClassesOneSegmentList>? segmentList;

	ClassesOneEntity();

	factory ClassesOneEntity.fromJson(Map<String, dynamic> json) => $ClassesOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $ClassesOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ClassesOneSegmentList {
	@JSONField(name: "work_is_today")
	String? workIsToday;
	@JSONField(name: "work_start_time")
	String? workStartTime;
	@JSONField(name: "work_end_time")
	String? workEndTime;
	@JSONField(name: "start_time_is_today")
	String? startTimeIsToday;
	@JSONField(name: "end_time_is_today")
	String? endTimeIsToday;
	@JSONField(name: "rest_start_time")
	String? restStartTime;
	@JSONField(name: "rest_end_time")
	String? restEndTime;
	@JSONField(name: "rest_start_time_is_today")
	String? restStartTimeIsToday;
	@JSONField(name: "rest_end_time_is_today")
	String? restEndTimeIsToday;
	@JSONField(name: "in_class_start_type")
	String? inClassStartType;
	@JSONField(name: "in_class_end_type")
	String? inClassEndType;
	@JSONField(name: "out_class_start_type")
	String? outClassStartType;
	@JSONField(name: "out_class_end_type")
	String? outClassEndType;

	ClassesOneSegmentList();

	factory ClassesOneSegmentList.fromJson(Map<String, dynamic> json) => $ClassesOneSegmentListFromJson(json);

	Map<String, dynamic> toJson() => $ClassesOneSegmentListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}