import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/project_one_entity.g.dart';
import 'dart:convert';

import 'job_manager_entity.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/project_one_entity.g.dart';

@JsonSerializable()
class ProjectOneEntity {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@J<PERSON>NField(name: "custom_uuid")
	String? customUuid;
	@JSONField(name: "project_cat_pid")
	String? projectCatPid;
	@JSONField(name: "project_cat_id")
	String? projectCatId;
	String? square;
	String? remark;
	@JSONField(name: "custom_name")
	String? customName;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "contract_human_num")
	String? contractHumanNum;
	@JSONField(name: "project_cat_parent_name")
	String? projectCatParentName;
	@JSONField(name: "project_cat_name")
	String? projectCatName;
	@JSONField(name: "manager_user_uuid")
	String? managerUserUuid;
	@JSONField(name: "manager_user_name")
	String? managerUserName;
	@JSONField(name: "contract_company_uuid")
	String? contractCompanyUuid;
	@JSONField(name: "contract_company_name")
	String? contractCompanyName;
	@JSONField(name: "contract_pic")
	List<String>? contractPic;
	@JSONField(name: "job_info_list")
	List<JobManagerList>? jobInfoList;

	ProjectOneEntity();

	factory ProjectOneEntity.fromJson(Map<String, dynamic> json) => $ProjectOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

