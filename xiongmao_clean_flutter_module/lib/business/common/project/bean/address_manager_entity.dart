import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/address_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/address_manager_entity.g.dart';

@JsonSerializable()
class AddressManagerEntity {
	List<AddressManagerList>? list;

	AddressManagerEntity();

	factory AddressManagerEntity.fromJson(Map<String, dynamic> json) => $AddressManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $AddressManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AddressManagerList {
	String? uuid;
	String? address;
	String? address_desc;
	String? lnt;
	String? lat;
	bool isSelect = false;

	AddressManagerList();

	factory AddressManagerList.fromJson(Map<String, dynamic> json) => $AddressManagerListFromJson(json);

	Map<String, dynamic> toJson() => $AddressManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}