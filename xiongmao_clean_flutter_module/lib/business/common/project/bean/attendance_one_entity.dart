import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/attendance_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/attendance_one_entity.g.dart';

@JsonSerializable()
class AttendanceOneEntity {
	String? uuid;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "group_name")
	String? groupName;
	@JSONField(name: "clock_in_type")
	String? clockInType;
	@JSONField(name: "project_class_uuid")
	String? projectClassUuid;
	@J<PERSON>NField(name: "project_class_name")
	String? projectClassName;
	@JSONField(name: "in_class_desc")
	String? inClassDesc;
	@JSONField(name: "work_day_list")
	List<String>? workDayList;
	@JSONField(name: "leader_list")
	List<AttendanceOneLeaderList>? leaderList;
	@JSONField(name: "address_list")
	List<AttendanceOneAddressList>? addressList;

	AttendanceOneEntity();

	factory AttendanceOneEntity.fromJson(Map<String, dynamic> json) => $AttendanceOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AttendanceOneLeaderList {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;

	AttendanceOneLeaderList();

	factory AttendanceOneLeaderList.fromJson(Map<String, dynamic> json) => $AttendanceOneLeaderListFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceOneLeaderListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AttendanceOneAddressList {
	String? uuid;
	String? address;

	AttendanceOneAddressList();

	factory AttendanceOneAddressList.fromJson(Map<String, dynamic> json) => $AttendanceOneAddressListFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceOneAddressListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}