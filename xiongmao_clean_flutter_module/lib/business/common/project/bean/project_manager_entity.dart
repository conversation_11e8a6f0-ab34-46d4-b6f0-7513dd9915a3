import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/project_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/project_manager_entity.g.dart';

@JsonSerializable()
class ProjectManagerEntity {
	int? page;
	int? size;
	int? total;
	List<ProjectManagerList>? list;

	ProjectManagerEntity();

	factory ProjectManagerEntity.fromJson(Map<String, dynamic> json) => $ProjectManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ProjectManagerList {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "contract_human_num")
	String? contractHumanNum;
	@JSONField(name: "total_job_num")
	String? totalJobNum;
	@JSONField(name: "project_cat_parent_name")
	String? projectCatParentName;
	@JSONField(name: "project_cat_name")
	String? projectCatName;
	@JSONField(name: "manager_user_name")
	String? managerUserName;
	@JSONField(name: "custom_name")
	String? customName;
	String? square;
	@JSONField(name: "create_time")
	String? createTime;
	String? remark;
	@JSONField(name: "on_job_num")
	String? onJobNum;
	@JSONField(name: "is_group_clock_in")
	String? isGroupClockIn;
	@JSONField(name: "contract_company_name")
	String? contractCompanyName;
	bool isSelected = false;


	ProjectManagerList();

	factory ProjectManagerList.fromJson(Map<String, dynamic> json) => $ProjectManagerListFromJson(json);

	Map<String, dynamic> toJson() => $ProjectManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}