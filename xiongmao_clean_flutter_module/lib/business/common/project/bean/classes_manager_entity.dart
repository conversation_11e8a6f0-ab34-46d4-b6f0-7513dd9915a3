import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/classes_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/classes_manager_entity.g.dart';

@JsonSerializable()
class ClassesManagerEntity {
	List<ClassesManagerList>? list;

	ClassesManagerEntity();

	factory ClassesManagerEntity.fromJson(Map<String, dynamic> json) => $ClassesManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $ClassesManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ClassesManagerList {
	String? uuid;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "is_no_clock")
	String? isNoClock;
	@JSONField(name: "class_type")
	String? classType;
	@JSONField(name: "is_default_rest")
	String? isDefaultRest;
	@JSONField(name: "class_type_name")
	String? classTypeName;
	@JSONField(name: "is_no_clock_name")
	String? isNoClockName;
	@JSONField(name: "is_default_rest_name")
	String? isDefaultRestName;
	@JSONField(name: "class_time")
	String? classTime;
	@JSONField(name: "avg_line_time")
	String? avgLineTime;
	@JSONField(name: "avg_line_is_today")
	String? avgLineIsToday;
	@JSONField(name: "out_class_desc")
	String? outClassDesc;
	@JSONField(name: "in_class_desc")
	String? inClassDesc;
	@JSONField(name: "segment_total")
	int? segmentTotal;
	bool isSelected = false;

	ClassesManagerList();

	factory ClassesManagerList.fromJson(Map<String, dynamic> json) => $ClassesManagerListFromJson(json);

	Map<String, dynamic> toJson() => $ClassesManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}