import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/attendance_manager_rules_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/attendance_manager_rules_entity.g.dart';

@JsonSerializable()
class AttendanceManagerRulesEntity {
	List<AttendanceManagerRulesList>? list;

	AttendanceManagerRulesEntity();

	factory AttendanceManagerRulesEntity.fromJson(Map<String, dynamic> json) => $AttendanceManagerRulesEntityFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceManagerRulesEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AttendanceManagerRulesList {
	String? uuid;
	@JSONField(name: "group_name")
	String? groupName;
	@JSONField(name: "work_day_desc")
	String? workDayDesc;
	@JSONField(name: "in_class_desc")
	String? inClassDesc;
	@JSONField(name: "attendance_method_name")
	String? attendanceMethodName;
	@JSONField(name: "address_list")
	List<String>? addressList;

	AttendanceManagerRulesList();

	factory AttendanceManagerRulesList.fromJson(Map<String, dynamic> json) => $AttendanceManagerRulesListFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceManagerRulesListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}