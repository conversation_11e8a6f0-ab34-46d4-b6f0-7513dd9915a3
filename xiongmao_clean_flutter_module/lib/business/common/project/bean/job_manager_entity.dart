import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/job_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/job_manager_entity.g.dart';

@JsonSerializable()
class JobManagerEntity {
	List<JobManagerList>? list;

	JobManagerEntity();

	factory JobManagerEntity.fromJson(Map<String, dynamic> json) => $JobManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $JobManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class JobManagerList {
	String? uuid;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "job_human_num")
	String? jobHumanNum;
	@JSONField(name: "job_salary")
	String? jobSalary;

	JobManagerList();

	factory JobManagerList.fromJson(Map<String, dynamic> json) => $JobManagerListFromJson(json);

	Map<String, dynamic> toJson() => $JobManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}