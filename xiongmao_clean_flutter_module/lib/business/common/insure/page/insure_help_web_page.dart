import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../net/http_config.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';

class InsureHelpWebPage extends StatefulWidget {
  String url;

  InsureHelpWebPage({required this.url});

  @override
  _InsureHelpWebPageState createState() => _InsureHelpWebPageState();
}

class _InsureHelpWebPageState extends State<InsureHelpWebPage> with AutomaticKeepAliveClientMixin {
  late XmWebController controller;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: XmWebView(
        onXmJsMsgCall: (XmJsMsgCall msgCall) {
          var decode = json.decode(msgCall.message);
          print('H5调用原声的回调name --> ${decode.toString()}');

          var name = decode["name"];
          var data = decode["data"];

          ///回调token给用户
          switch (name) {
            case "getUserToken":
              var token = httpConfig.token ?? "";
              controller.realController.runJavaScript("window.WebViewJavascriptBridgegetUserTokenCallBack('" + token + "')"); //切记这个字符串还得用''引住
              break;
          }
        },
        controllerSettingBack: (value) {
          controller = value;
          LogUtil.e("object----url---" + (widget.url ?? ""));
          controller.loadRequest(Uri.parse(widget.url ?? ""));
        },
        domainM2: true,
      ),
    );
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
