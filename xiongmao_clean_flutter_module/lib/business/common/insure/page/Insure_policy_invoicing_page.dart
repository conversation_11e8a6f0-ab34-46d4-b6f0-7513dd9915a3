import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/custom_report_selector.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_contract_entity.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../item/Insure_policy_invoicing_listview.dart';
import '../iview/insure_policy_invoicing_add_iview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../presenter/insure_policy_invoicing_persenter.dart';

/// 开票列表
class InsurePolicyInvoicingPage extends StatefulWidget {
  InsurePolicyInvoicingPage({Key? key}) : super(key: key);

  @override
  _InsurePolicyInvoicingPageState createState() => _InsurePolicyInvoicingPageState();
}

class _InsurePolicyInvoicingPageState extends State<InsurePolicyInvoicingPage> with BasePageMixin<InsurePolicyInvoicingPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsurePolicyInvoicingPage> implements InsurePolicyInvoicingIView {
  InsurePolicyInvoicingPresenter? _presenter;

  final InsurePolicyInvoicingController _controller = InsurePolicyInvoicingController();

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '选择保单开票',
            centerSubTitle: _controller.contractCompanyName.value,
            actionWidget: Row(
              children: [
                InkWell(
                  onTap: () {
                    BoostNavigator.instance.push('companyChoiceContractPage', arguments: {'isSelected': true, 'showBottomBut': true}).then((value) {
                      if (value != null) {
                        DepartmentCompanyContractList data = value as DepartmentCompanyContractList;
                        _controller.contractCompanyUuid.value = data.uuid ?? '';
                        _controller.contractCompanyName.value = data.companyName ?? '';
                      } else {
                        _controller.contractCompanyName.value = '全部';
                        _controller.contractCompanyUuid.value = '';
                      }
                      _onRefresh();
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: LoadAssetImage(
                      "icon_change",
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    ///跳转到开票记录
                    BoostNavigator.instance.push('insurePolicyInvoicingRecordPage');
                  },
                  child: Padding(
                    padding: const EdgeInsets.only(left: 10, right: 10),
                    child: CommonUtils.getSimpleText('开票记录', 15, Colours.base_primary_text_title),
                  ),
                ),
              ],
            ),
          ),
          body: Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(
                        child: CustomReportSelector(
                      options: const ['新购', '退款'],
                      onSelectedIndexChanged: (index) {
                        setState(() {
                          _controller.type.value = index == 0 ? '3' : '4';
                          if (index == 0) {
                            if (!TextUtil.isEmpty(_controller.payStartDate.value) && !TextUtil.isEmpty(_controller.payEndDate.value)) {
                              _controller.searchDate.value = '${_controller.payStartDate.value}-${_controller.payEndDate.value}';
                            } else {
                              _controller.searchDate.value = '选择日期范围';
                            }
                          } else {
                            if (!TextUtil.isEmpty(_controller.policyRefundStartDate.value) && !TextUtil.isEmpty(_controller.policyRefundEndDate.value)) {
                              _controller.searchDate.value = '${_controller.policyRefundStartDate.value}-${_controller.policyRefundEndDate.value}';
                            } else {
                              _controller.searchDate.value = '选择日期范围';
                            }
                          }
                        });
                        _onRefresh();
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        flex: 2,
                        child: CustomSelectedArrowView(
                          backgroundColor: (_controller.searchDate.value == '选择日期范围') ? Colours.base_primary_bg_page : Colours.base_primary_select,
                          // 自定义背景色
                          textColor: Colours.base_primary,
                          // 自定义文字颜色
                          dateText: _controller.searchDate.value,
                          onDateTextPressed: () {
                            if (_controller.type.value == '3' && _controller.searchDate.value != '选择日期范围') {
                              _controller.searchDate.value = '选择日期范围';
                              _controller.payStartDate.value = '';
                              _controller.payEndDate.value = '';
                              _onRefresh();
                              return;
                            } else if (_controller.type.value == '4' && _controller.searchDate.value != '选择日期范围') {
                              _controller.searchDate.value = '选择日期范围';
                              _controller.policyRefundStartDate.value = '';
                              _controller.policyRefundEndDate.value = '';
                              _onRefresh();
                              return;
                            }

                            String format = 'yyyy年-MM月-dd日';
                            BrnPickerTitleConfig pickerTitleConfig = const BrnPickerTitleConfig(titleContent: "选择时间范围");
                            BrnDateRangePicker.showDatePicker(
                              context,
                              isDismissible: false,
                              minDateTime: DateTime(2024, 08, 01, 00, 00, 00),
                              maxDateTime: DateTime(2029, 12, 30, 23, 59, 59),
                              pickerMode: BrnDateTimeRangePickerMode.date,
                              minuteDivider: 10,
                              pickerTitleConfig: pickerTitleConfig,
                              dateFormat: format,
                              initialStartDateTime: (_controller.type.value == '3') ? _controller.startDateTime : _controller.refundStartDateTime,
                              initialEndDateTime: (_controller.type.value == '3') ? _controller.endDateTime : _controller.refundEndDateTime,
                              onConfirm: (startDateTime, endDateTime, startlist, endlist) {
                                if (_controller.type.value == '3') {
                                  _controller.startDateTime = startDateTime;
                                  _controller.endDateTime = endDateTime;
                                  _controller.payStartDate.value = '${startDateTime.year}.${CommonUtils.formatToTwoDigits(startDateTime.month)}.${CommonUtils.formatToTwoDigits(startDateTime.day)}';
                                  _controller.payEndDate.value = '${endDateTime.year}.${CommonUtils.formatToTwoDigits(endDateTime.month)}.${CommonUtils.formatToTwoDigits(endDateTime.day)}';
                                  _controller.searchDate.value = '${_controller.payStartDate.value}-${_controller.payEndDate.value}';
                                } else {
                                  _controller.refundStartDateTime = startDateTime;
                                  _controller.refundEndDateTime = endDateTime;
                                  _controller.policyRefundStartDate.value = '${startDateTime.year}.${CommonUtils.formatToTwoDigits(startDateTime.month)}.${CommonUtils.formatToTwoDigits(startDateTime.day)}';
                                  _controller.policyRefundEndDate.value = '${endDateTime.year}.${CommonUtils.formatToTwoDigits(endDateTime.month)}.${CommonUtils.formatToTwoDigits(endDateTime.day)}';
                                  _controller.searchDate.value = '${_controller.policyRefundStartDate.value}-${_controller.policyRefundEndDate.value}';
                                }
                                _onRefresh();
                              },
                              onClose: () {
                                print("onClose");
                              },
                              onCancel: () {
                                print("onCancel");
                              },
                              onChange: (startDateTime, endDateTime, startlist, endlist) {},
                            );
                          },
                          onNextDayPressed: () {},
                          onPreviousDayPressed: () {},
                          showLeftButton: false,
                          // 隐藏左边按钮
                          showRightButton: false, // 显示右边按钮
                        )),
                  ],
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: const EdgeInsets.only(top: 16),
                hasMore: _controller.totalNumber.value > _controller.list.length,
                itemBuilder: (_, index) {
                  return InsurePolicyInvoicingListView(
                    type: _controller.type.value,
                    data: _controller.list[index],
                    refund: _controller.type.value == '4',
                    onClick: () {
                      _controller.list[index].isSelected = !_controller.list[index].isSelected;
                      _controller.list.refresh();
                      _controller.findSelectedCount();
                    },
                  );
                },
              )),
            ],
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      color: Colours.base_primary,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CommonUtils.getSimpleText('去开票(已选择${_controller.count.value}个保单)', 16, Colours.white, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    if (TextUtil.isEmpty(_controller.getSelectedIds())) {
                      BrnToast.show('请选择保单进行开票', context);
                      return;
                    }
                    BoostNavigator.instance.push(
                      'insurePolicyInvoicingRecordEditPage',
                      arguments: {
                        'ids': _controller.getSelectedIds(),
                        'amountType': (_controller.type.value == '3') ? '1' : '2',
                      },
                    ).then((value) => _onRefresh());
                  },
                )),
                Gaps.hGap10,
                InkWell(
                  child: Container(
                    padding: const EdgeInsets.only(left: 20, right: 20, top: 7, bottom: 7),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4.0),
                      color: Colours.base_primary_un_select,
                    ),
                    child: CommonUtils.getSimpleText(_controller.selectAll.value, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                  ),
                  onTap: () {
                    if (_controller.selectAll.value == '全选') {
                      _controller.selectAllItems(true);
                      _controller.selectAll.value = '取消全选';
                    } else {
                      _controller.selectAllItems(false);
                      _controller.selectAll.value = '全选';
                    }
                  },
                ),
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsurePolicyInvoicingPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => false;
}
