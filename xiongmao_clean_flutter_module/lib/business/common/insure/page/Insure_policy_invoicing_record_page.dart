import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 投保历史
 */
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_report_selector.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/custom_tag_number_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../company/bean/department_company_contract_entity.dart';
import '../bean/custom_Insure_history_entity.dart';
import '../bean/insure_add_entity.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../controller/insure_policy_invoicing_record_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../item/Insure_history_listview.dart';
import '../item/Insure_listview.dart';
import '../item/Insure_policy_invoicing_listview.dart';
import '../item/Insure_policy_invoicing_record_listview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../iview/insure_scheme_iview.dart';
import '../presenter/insure_policy_invoicing_persenter.dart';
import '../presenter/insure_policy_invoicing_record_persenter.dart';
import '../presenter/insure_scheme_history_persenter.dart';

/// 开票记录
class InsurePolicyInvoicingRecordPage extends StatefulWidget {
  InsurePolicyInvoicingRecordPage({Key? key}) : super(key: key);

  @override
  _InsurePolicyInvoicingRecordPageState createState() => _InsurePolicyInvoicingRecordPageState();
}

class _InsurePolicyInvoicingRecordPageState extends State<InsurePolicyInvoicingRecordPage> with BasePageMixin<InsurePolicyInvoicingRecordPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsurePolicyInvoicingRecordPage> implements InsurePolicyInvoicingIView {
  InsurePolicyInvoicingRecordPresenter? _presenter;

  final InsurePolicyInvoicingRecordController _controller = InsurePolicyInvoicingRecordController();

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '开票记录',
      ),
      body: Obx(() => MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            padding: const EdgeInsets.only(top: 16),
            hasMore: int.parse(_controller.totalNumber.value) > _controller.list.value.length,
            itemBuilder: (_, index) {
              return InsurePolicyInvoicingRecordListView(
                data: _controller.list[index],
              );
            },
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsurePolicyInvoicingRecordPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void getAmountInfo(InsureAddEntity data) {
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => false;
}
