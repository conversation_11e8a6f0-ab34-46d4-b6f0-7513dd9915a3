import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/brn_config_utils.dart';
import '../controller/insure_history_one_controller.dart';
import '../iview/insure_scheme_history_one_iview.dart';
import '../presenter/insure_history_one_persenter.dart';

/// 保单详情
class InsureDetailsPage extends StatefulWidget {
  String? order_number = "";

  InsureDetailsPage({Key? key, this.order_number = ""}) : super(key: key);

  @override
  _InsureHistoryOnePageState createState() => _InsureHistoryOnePageState();
}

class _InsureHistoryOnePageState extends State<InsureDetailsPage> with BasePageMixin<InsureDetailsPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsureDetailsPage> implements InsureHistoryOneIView {
  InsureHistoryOnePagePresenter? _presenter;

  final InsureHistoryOnePageController _controller = InsureHistoryOnePageController();

  Future<dynamic> _onRefresh() async {
    _presenter?.getInsureSchemeHistoryOne();
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _controller.order_number.value = widget.order_number ?? "";

    _onRefresh();

    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        _loadMore();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '保单详情',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => RefreshIndicator(
            onRefresh: _onRefresh,
            child: ListView(
              children: [
                Column(
                  children: [
                    //保单信息
                    Container(
                      color: Colors.white,
                      margin: EdgeInsets.only(top: 10),
                      padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                      child: Column(
                        children: [
                          //保险公司
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('保险公司', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.insuranceCompanyName ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('保单号', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.policyNo ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('购买产品', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.productName ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('保险期', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText("${_controller.data.value.startTime ?? ""}-${_controller.data.value.endTime ?? ""}", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('购买时间', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.payTime ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          //保单号
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('保单状态', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.orderStatusName ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                          Gaps.vGap4,
                          //退款状态
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CommonUtils.getSimpleText('退款状态', 13, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText(_controller.data.value.refundStatusName ?? "", 13, Colours.base_primary_text_title, textAlign: TextAlign.right),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    Gaps.vGap10,
                    //被保人信息
                    Container(
                      padding: EdgeInsets.only(left: 16, right: 16),
                      color: Colors.white,
                      child: Column(
                        children: List.generate(_controller.data.value.insuredUserList?.length ?? 0, (index) {
                          return GestureDetector(
                            onTap: () {},
                            child: Container(
                              padding: EdgeInsets.only(bottom: 10, top: 10),
                              child: Column(
                                children: [
                                  Row(
                                    crossAxisAlignment: CrossAxisAlignment.center,
                                    children: [
                                      CommonUtils.getSimpleText('员工：', 13, Colours.base_primary_text_title),
                                      Expanded(
                                        child: CommonUtils.getSimpleText('该保单申请理赔后，将会被锁定，不可退款', 11, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                                      ),
                                    ],
                                  ),
                                  Gaps.vGap6,
                                  Row(
                                    children: [
                                      Container(
                                        margin: EdgeInsets.only(right: 10),
                                        child: CustomAvatarView(
                                          name: _controller.data.value.insuredUserList![index].userName ?? "",
                                          avatarUrl: _controller.data.value.insuredUserList![index].avatar,
                                        ),
                                      ),
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            CommonUtils.getSimpleText('${_controller.data.value.insuredUserList![index].userName}(${_controller.data.value.insuredUserList![index].statusName})', 15, (_controller.data.value.insuredUserList![index].statusName == "在职") ? Colours.base_primary_text_title : Colours.base_primary_text_caption, decoration: (_controller.data.value.insuredUserList![index].statusName == "在职") ? TextDecoration.none : TextDecoration.lineThrough, fontWeight: FontWeight.bold),
                                            CommonUtils.getSimpleText('${_controller.data.value.insuredUserList![index].idNumber}', 15, (_controller.data.value.insuredUserList![index].statusName == "在职") ? Colours.base_primary_text_title : Colours.base_primary_text_caption, decoration: (_controller.data.value.insuredUserList![index].statusName == "在职") ? TextDecoration.none : TextDecoration.lineThrough),
                                          ],
                                        ),
                                      ),
                                    ],
                                  )
                                ],
                              ),
                            ),
                          );
                        }),
                      ),
                    ),
                  ],
                ),
                Gaps.vGap10,
                Container(
                  padding: EdgeInsets.only(
                    left: 14,
                    top: 10,
                    bottom: 10,
                    right: 14,
                  ),
                  color: Colors.white,
                  width: double.infinity,
                  child: CommonUtils.getSimpleText("操作记录：", 13, Colours.base_primary_text_title),
                ),
                ListView.builder(
                  shrinkWrap: true,
                  physics: NeverScrollableScrollPhysics(),
                  itemCount: _controller.list.length,
                  // Example item count
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.only(left: 14, right: 14, top: 6, bottom: 6),
                      color: Colors.white,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonUtils.getSimpleText("${_controller.list[index].remark}", 13, Colours.base_primary_text_title),
                          CommonUtils.getSimpleText("${_controller.list[index].createTime}", 13, Colours.base_primary_text_caption),
                          Gaps.vGap6,
                          if (index != _controller.list.length - 1) Gaps.line,
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
        child: Row(
          children: [
            ///电子保单
            Expanded(
                child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: InkWell(
                onTap: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_ele_policy", "url": "${_controller.data.value.elecPolicyUrl}", 'user_name': '${_controller.data.value.insuredUserName}', 'order_number': '${_controller.data.value..orderNumber}'});
                },
                child: CommonUtils.getSimpleText('电子保单', 14, Colours.base_primary, textAlign: TextAlign.center),
              ),
            )),

            Gaps.hGap10,

            ///电子发票
            // Expanded(
            //     child: Container(
            //   height: 40,
            //   alignment: Alignment.center,
            //   decoration: BoxDecoration(
            //     border: Border.all(color: Colours.base_primary),
            //     borderRadius: BorderRadius.circular(5.0),
            //   ),
            //   child: InkWell(
            //     onTap: () {},
            //     child: CommonUtils.getSimpleText('电子发票', 14, Colours.base_primary, textAlign: TextAlign.center),
            //   ),
            // )),
            //
            // Gaps.hGap10,

            ///理赔流程
            Expanded(
                child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: InkWell(
                onTap: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111938749780", "close": true, "share": true});
                },
                child: CommonUtils.getSimpleText('理赔流程', 14, Colours.base_primary, textAlign: TextAlign.center),
              ),
            )),
            Gaps.hGap10,

            ///申请理赔
            Expanded(
                child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: InkWell(
                onTap: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_claims_process", "order_number": "${_controller.data.value.orderNumber}", "guarantee": "${_controller.data.value.policyNo}"});
                },
                child: CommonUtils.getSimpleText('申请理赔', 14, Colours.base_primary, textAlign: TextAlign.center),
              ),
            )),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsureHistoryOnePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  saveStatus() {}

  @override
  bool get wantKeepAlive => false;
}
