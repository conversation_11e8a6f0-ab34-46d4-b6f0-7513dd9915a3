import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

/**
 * 投保历史
 */
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/insure_add_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_report_selector.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/custom_tag_number_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../company/bean/department_company_contract_entity.dart';
import '../bean/custom_Insure_history_entity.dart';
import '../controller/insure_policy_invoicing_add_controller.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../controller/insure_policy_invoicing_record_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../item/Insure_history_listview.dart';
import '../item/Insure_listview.dart';
import '../item/Insure_policy_invoicing_listview.dart';
import '../item/Insure_policy_invoicing_record_listview.dart';
import '../iview/insure_policy_invoicing_add_iview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../iview/insure_scheme_iview.dart';
import '../presenter/insure_policy_invoicing_add_persenter.dart';
import '../presenter/insure_policy_invoicing_persenter.dart';
import '../presenter/insure_policy_invoicing_record_persenter.dart';
import '../presenter/insure_scheme_history_persenter.dart';

/// 添加开票信息
class InsurePolicyInvoicingRecordAddPage extends StatefulWidget {
  String ids;
  String amountType;

  InsurePolicyInvoicingRecordAddPage({Key? key, required this.ids, required this.amountType}) : super(key: key);

  @override
  _InsurePolicyInvoicingRecordAddPageState createState() => _InsurePolicyInvoicingRecordAddPageState();
}

class _InsurePolicyInvoicingRecordAddPageState extends State<InsurePolicyInvoicingRecordAddPage> with BasePageMixin<InsurePolicyInvoicingRecordAddPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsurePolicyInvoicingRecordAddPage> implements InsurePolicyInvoicingAddIView {
  InsurePolicyInvoicingAddPresenter? _presenter;

  final InsurePolicyInvoicingAddController _controller = InsurePolicyInvoicingAddController();

  late TextEditingController invoiceEmailController;
  late TextEditingController invoiceTitleController;
  late TextEditingController invoiceNumberController;
  late TextEditingController addressController;
  late TextEditingController mobileController;
  late TextEditingController bankController;
  late TextEditingController bankNoController;

  @override
  void initState() {
    super.initState();
    _presenter?.getAmountRecentlyInfo();
    if (!TextUtil.isEmpty(widget.ids) && !TextUtil.isEmpty(widget.amountType)) {
      _presenter?.getAmountInfo(widget.ids, widget.amountType);
    }

    // 初始化控制器并绑定初始值
    invoiceEmailController = TextEditingController(text: _controller.email.value);
    invoiceTitleController = TextEditingController(text: _controller.invoiceTitle.value);
    invoiceNumberController = TextEditingController(text: _controller.invoiceNumber.value);
    addressController = TextEditingController(text: _controller.address.value);
    mobileController = TextEditingController(text: _controller.mobile.value);
    bankController = TextEditingController(text: _controller.bank.value);
    bankNoController = TextEditingController(text: _controller.bankNo.value);

    // 监听 _controller 的变化并同步到 TextEditingController
    ever(_controller.email, (value) {
      if (invoiceEmailController.text != value) {
        invoiceEmailController.text = value.toString();
      }
    });
    ever(_controller.invoiceTitle, (value) {
      if (invoiceTitleController.text != value) {
        invoiceTitleController.text = value.toString();
      }
    });
    ever(_controller.invoiceNumber, (value) {
      if (invoiceNumberController.text != value) {
        invoiceNumberController.text = value.toString();
      }
    });
    ever(_controller.address, (value) {
      if (addressController.text != value) {
        addressController.text = value.toString();
      }
    });
    ever(_controller.mobile, (value) {
      if (mobileController.text != value) {
        mobileController.text = value.toString();
      }
    });
    ever(_controller.bank, (value) {
      if (bankController.text != value) {
        bankController.text = value.toString();
      }
    });
    ever(_controller.bankNo, (value) {
      if (bankNoController.text != value) {
        bankNoController.text = value.toString();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '添加开票信息',
          ),
          body: SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  padding: EdgeInsets.only(top: 14, bottom: 14, left: 16, right: 16),
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('关键信息', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText((widget.amountType == '1' ? '正数发票' : '负数发票'), 14, Colours.base_primary_text_title),
                          CommonUtils.getSimpleText(
                            '已选${(_controller.data.value.orderTotal == null) ? 0 : _controller.data.value.orderTotal ?? 0}个保单${widget.amountType == '1' ? '(${(_controller.data.value.refundTotal == null) ? 0 : _controller.data.value.refundTotal ?? 0}个已退款，无法开票)' : ''}',
                            14,
                            Colours.base_primary_text_title,
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("开票金额", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("预计${(TextUtil.isEmpty(_controller.data.value.amount)) ? 0 : _controller.data.value.amount ?? 0}元", 14, Colours.red, textAlign: TextAlign.right),
                        ],
                      ),
                    ],
                  ),
                ),
                Gaps.vGap12,
                BrnTextInputFormItem(
                  title: "电子邮箱（收发票）",
                  hint: "请输入",
                  inputType: BrnInputType.email,
                  controller: invoiceEmailController,
                  isRequire: true,
                  onChanged: (newValue) {
                    _controller.email.value = newValue;
                  },
                ),
                Gaps.vGap12,
                Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                      Expanded(child: Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("开票类型", 16, Colours.base_primary_text_title))),
                      Expanded(
                          child: Container(
                        margin: const EdgeInsets.only(left: 0, right: 0),
                        child: SelectTabWidget(
                          key: ValueKey(_controller.policyType.value),
                          // 设置 key
                          _controller.policyTypeList.value,
                          multiSelect: false,
                          crossAxisCount: 2,
                          hideMore: false,
                          paddingBottom: 0,
                          paddingTop: 0,
                          tabFontSize: 15,
                          defaultSelectedIndex: [(int.parse(_controller.policyType.value) - 1)],
                          lastIsAddOne: false,
                          selectedColor: Colours.base_primary,
                          bgSelectedColor: Colours.base_primary_select,
                          bgUnSelectedColor: Colours.base_primary_un_select,
                          childAspectRatio: 6 / 3,
                          itemClickCallback: (List<int> indexs) {
                            LogUtil.e("indexs = $indexs");
                            _controller.policyType.value = "${(indexs[0] + 1)}";
                            LogUtil.e("indexs = ${_controller.policyType.value}");
                          },
                        ),
                      )),
                    ],
                  ),
                ),
                Gaps.line,
                Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                      Expanded(child: Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("合并发票", 16, Colours.base_primary_text_title))),
                      Expanded(
                          child: Container(
                        margin: const EdgeInsets.only(left: 0, right: 0),
                        child: SelectTabWidget(
                          _controller.meagerTypeList.value,
                          key: ValueKey(_controller.policyMeagerType.value),
                          // 设置 key
                          multiSelect: false,
                          crossAxisCount: 2,
                          hideMore: false,
                          paddingBottom: 0,
                          paddingTop: 0,
                          tabFontSize: 15,
                          defaultSelectedIndex: [(int.parse(_controller.policyMeagerType.value) - 1)],
                          lastIsAddOne: false,
                          selectedColor: Colours.base_primary,
                          bgSelectedColor: Colours.base_primary_select,
                          bgUnSelectedColor: Colours.base_primary_un_select,
                          childAspectRatio: 6 / 3,
                          itemClickCallback: (List<int> indexs) {
                            LogUtil.e("indexs = $indexs");
                            _controller.policyMeagerType.value = "${(indexs[0] + 1)}";
                            LogUtil.e("indexs = ${_controller.policyMeagerType.value}");
                          },
                        ),
                      )),
                    ],
                  ),
                ),
                Gaps.line,
                Container(
                  color: Colors.white,
                  child: Row(
                    children: [
                      Expanded(
                          child: BrnTextInputFormItem(
                        title: "发票抬头",
                        controller: invoiceTitleController,
                        hint: "请输入",
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                        ],
                        isRequire: true,
                        onChanged: (newValue) {
                          _controller.invoiceTitle.value = newValue;
                        },
                      )),
                      InkWell(
                        child: Container(
                          margin: const EdgeInsets.only(right: 16),
                          padding: const EdgeInsets.only(left: 5, right: 5, top: 2, bottom: 2),
                          decoration: BoxDecoration(
                            border: Border.all(color: Colours.base_primary_text_caption, width: 0.5),
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          child: CommonUtils.getSimpleText('快速输入', 16, Colours.base_primary_text_caption),
                        ),
                        onTap: () {
                          BoostNavigator.instance.push('companyChoiceContractPage', arguments: {'isSelected': true}).then((value) {
                            if (value != null) {
                              DepartmentCompanyContractList data = value as DepartmentCompanyContractList;
                              _controller.contractCompanyUuid.value = data.uuid ?? '';
                              _controller.invoiceTitle.value = data.companyName ?? '';
                              _controller.invoiceNumber.value = data.creditCode ?? '';
                              _controller.address.value = data.address ?? '';
                              _controller.mobile.value = data.mobile ?? '';
                              _controller.bankNo.value = data.bankNo ?? '';
                              _controller.bank.value = data.bank ?? '';
                            }
                          });
                        },
                      )
                    ],
                  ),
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "发票纳税人识别号",
                  controller: invoiceNumberController,
                  hint: "请输入",
                  isRequire: true,
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                  ],
                  onChanged: (newValue) {
                    _controller.invoiceNumber.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "地址",
                  controller: addressController,
                  hint: "请输入",
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                  ],
                  isRequire: (_controller.policyType.value == '2'),
                  onChanged: (newValue) {
                    _controller.address.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  controller: mobileController,
                  title: "电话",
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                  ],
                  hint: "请输入",
                  isRequire: (_controller.policyType.value == '2'),
                  onChanged: (newValue) {
                    _controller.mobile.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "开户行",
                  controller: bankController,
                  hint: "请输入",
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                  ],
                  isRequire: (_controller.policyType.value == '2'),
                  onChanged: (newValue) {
                    _controller.bank.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "银行卡号",
                  controller: bankNoController,
                  hint: "请输入",
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(50), // 限制输入长度为20个字符
                  ],
                  isRequire: (_controller.policyType.value == '2'),
                  inputType: BrnInputType.number,
                  onChanged: (newValue) {
                    _controller.bankNo.value = newValue;
                  },
                ),
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: InkWell(
              child: Container(
                padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4.0),
                ),
                child: CommonUtils.getSimpleText('确定', 16, Colours.white, textAlign: TextAlign.center),
              ),
              onTap: () {
                if (TextUtil.isEmpty(_controller.email.value)) {
                  BrnToast.show('请输入电子邮箱', context);
                  return;
                }
                if (TextUtil.isEmpty(_controller.policyType.value)) {
                  BrnToast.show('请选择开票类型', context);
                  return;
                }
                if (TextUtil.isEmpty(_controller.policyMeagerType.value)) {
                  BrnToast.show('请选择是否合并发票', context);
                  return;
                }
                if (TextUtil.isEmpty(_controller.invoiceTitle.value)) {
                  BrnToast.show('请输入发票抬头', context);
                  return;
                }
                if (TextUtil.isEmpty(_controller.invoiceNumber.value)) {
                  BrnToast.show('请输入纳税人识别号', context);
                  return;
                }

                ///如果选择的开票类型是专票 地址、电话、开户型、银行卡号都必须填
                if (_controller.policyType.value == '2') {
                  if (TextUtil.isEmpty(_controller.address.value)) {
                    BrnToast.show('请输入地址', context);
                    return;
                  }
                  if (TextUtil.isEmpty(_controller.mobile.value)) {
                    BrnToast.show('请输入电话', context);
                    return;
                  }
                  if (TextUtil.isEmpty(_controller.bank.value)) {
                    BrnToast.show('请输入开户行', context);
                    return;
                  }
                  if (TextUtil.isEmpty(_controller.bankNo.value)) {
                    BrnToast.show('请输入银行卡号', context);
                    return;
                  }
                }
                _presenter?.createInsureInvoicing(widget.amountType, widget.ids);
              },
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsurePolicyInvoicingAddPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void dispose() {
    // 释放 TextEditingController
    invoiceEmailController.dispose();
    invoiceTitleController.dispose();
    invoiceNumberController.dispose();
    addressController.dispose();
    mobileController.dispose();
    bankController.dispose();
    bankNoController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void createSuccess() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }
}
