import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_route_bar.dart';

import '../../../../net/http_config.dart';
import 'Insure_history_page.dart';
import 'insure_help_web_page.dart';
import 'insure_rules_web_page.dart';

/// 保险商城
class InsurePage extends StatefulWidget {
  String searchName;

  InsurePage({super.key, required this.searchName});

  @override
  _InsurePageState createState() => _InsurePageState();
}

class _InsurePageState extends State<InsurePage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int currentIndex = 0;

  List<String> nameList = [];
  List<Widget> tabChildren = [];

  @override
  void initState() {
    super.initState();
    _initializeTabs();
    _tabController = TabController(length: nameList.length, vsync: this, initialIndex: currentIndex);
    _tabController.addListener(_handleTabChange);
  }

  void _initializeTabs() {
    if ((httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID) || (httpConfig.role_id == HttpConfig.ROLE_MANGER_ID) || (httpConfig.role_id == HttpConfig.ROLE_HR_ID)) {
      nameList = ["保单", "参保规则", "帮助"];
      tabChildren = [
        InsureHistoryPage(
          searchName: widget.searchName,
        ), // 保单
        InsureRulesWebPage(
          url: '${httpConfig.getServerType}/insurance/rule',
        ), // 参保规则
        InsureHelpWebPage(url: "${httpConfig.getServerTypeM2}/syqj/insurance/help"), // 帮助
      ];
    } else {
      nameList = ["保单", "帮助"];
      tabChildren = [
        InsureHistoryPage(
          searchName: widget.searchName,
        ), // 保单
        InsureHelpWebPage(url: "${httpConfig.getServerTypeM2}/syqj/insurance/help"), // 帮助
      ];
    }
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        currentIndex = _tabController.index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppRouteBar(
        onSelect: (int value) {
          setState(() {
            currentIndex = value;
            _tabController.animateTo(value);
          });
        },
        actionWidget: Row(
          children: [
            Visibility(
              visible: !(httpConfig.role_id == HttpConfig.ROLE_REGIONAL_MANAGER_ID || httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID),
              child: Padding(
                padding: const EdgeInsets.only(right: 10),
                child: BrnIconAction(
                  child: const LoadAssetImage(
                    'common/icon_insure_tic',
                    width: 20,
                    height: 20,
                  ),
                  iconPressed: () {
                    BoostNavigator.instance.push('InsurePolicyInvoicingPage');
                  },
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: BrnIconAction(
                child: Image.asset(
                  'assets/images/base/icon_base_share.png',
                  scale: 3.0,
                  height: 30,
                  width: 30,
                ),
                iconPressed: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_share_wx_link", "title": '保险帮助', 'link': '${httpConfig.getServerTypeM2}/syqj/insurance/help'});
                },
              ),
            )
          ],
        ),
        nameList: nameList,
        tabController: _tabController,
      ),
      body: TabBarView(
        physics: const NeverScrollableScrollPhysics(),
        controller: _tabController,
        children: tabChildren,
      ),
    );
  }
}
