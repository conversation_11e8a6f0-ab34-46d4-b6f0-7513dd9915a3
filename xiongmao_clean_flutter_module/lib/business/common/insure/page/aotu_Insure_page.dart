import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/item/Insure_listview.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';

/**
 * 自动投保
 */
class AutoInsurePage extends StatefulWidget {
  @override
  _AutoInsurePageState createState() => _AutoInsurePageState();
}

class _AutoInsurePageState extends State<AutoInsurePage> {
  bool isAutoPurchaseEnabled = true;

  String switchText = "1、员工入职时，若未无闲置参保名额，将参保失败，开启此功能后将通过余额支付功能自动为您购买保险，请确保余额充足。\n2、若因余额不足导致未参保成功，系统将在余额充值后再次自动参保。";
  String switchTextTrue = "1、员工入职时，若未无闲置参保名额，将参保失败，开启此功能后将通过余额支付功能自动为您购买保险，请确保余额充足。\n2、若因余额不足导致未参保成功，系统将在余额充值后再次自动参保。";
  String switchTextFalse = "1、员工入职时，若未无闲置参保名额，将参保失败，开启此功能后将通过余额支付功能自动为您购买保险。\n2、若因余额不足导致未参保成功，系统将在余额充值后再次自动参保。";

  @override
  Widget build(BuildContext context) {
    // return InsureListView();
    return SingleChildScrollView(
      child: Column(
        children: [
          //入离职自动替换被保人
          Container(
            color: Colors.white,
            margin: const EdgeInsets.only(top: 10),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "入离职自动替换被保人",
                        style: TextStyle(color: Colors.black, fontSize: 16.0, fontWeight: FontWeight.bold),
                      ),
                    ),
                    Text(
                      "已启用",
                      style: TextStyle(color: Colors.black, fontSize: 16.0, fontWeight: FontWeight.bold),
                    )
                  ],
                ),
                Text(
                  "1、保险有效期内，若被保人离职，将自动解除被保关系，被保名额将闲置。\n2、此时会依次查找同一项目内以及整个公司下是否还有员工未参保，若有，则将该员工自动列为被保人。\n3、若保险名额闲置时，未发现未参保员工，则新员工入职成功后，将自动被列为被保人.",
                  style: TextStyle(color: Colors.grey, fontSize: 10.0, fontWeight: FontWeight.bold),
                )
              ],
            ),
          ),
          //入职自动增购保险
          Container(
            color: Colors.white,
            margin: const EdgeInsets.only(top: 10),
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Expanded(
                      child: Text(
                        "入职自动增购保险",
                        style: TextStyle(color: Colors.black, fontSize: 16.0, fontWeight: FontWeight.bold),
                      ),
                    ),
                    BrnSwitchButton(
                      value: isAutoPurchaseEnabled,
                      // 使用新变量来表示Switch的状态
                      onChanged: (bool value) {
                        // 更新Switch的状态
                        setState(() {
                          isAutoPurchaseEnabled = value;
                          switchText = value ? switchTextTrue : switchTextFalse;
                        });
                      },
                      // activeColor: Colors.blue,
                      // inactiveThumbColor: Colors.grey,
                      // inactiveTrackColor: Colors.grey[200],
                    ),
                  ],
                ),
                Text(
                  switchText,
                  style: TextStyle(color: Colors.grey, fontSize: 10.0, fontWeight: FontWeight.bold),
                ),
                Gaps.vGap10,
                Text(
                  "默认保险套餐",
                  style: TextStyle(color: Colors.black, fontSize: 12.0, fontWeight: FontWeight.bold),
                ),
                // InsureListView(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
