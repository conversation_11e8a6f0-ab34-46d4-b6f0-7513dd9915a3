import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_iview.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../net/http_config.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/load_image.dart';
import '../controller/insure_scheme_controller.dart';
import '../item/Insure_history_listview.dart';
import '../item/Insure_listview.dart';
import '../item/Insure_personnel_listview.dart';
import '../presenter/insure_scheme_persenter.dart';

/// 保险商城
class InsureMarketPage extends StatefulWidget {
  String? project_uuid = "";
  String? insurance_product_uuid = "";

  InsureMarketPage({Key? key, this.project_uuid = "", this.insurance_product_uuid = ""}) : super(key: key);

  @override
  _InsureMarketPageState createState() => _InsureMarketPageState();
}

class _InsureMarketPageState extends State<InsureMarketPage> with BasePageMixin<InsureMarketPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsureMarketPage> implements InsureSchemeSaveIView {
  InsureSchemePagePresenter? _presenter;

  final InsureSchemePageController _controller = InsureSchemePageController();

  @override
  void initState() {
    super.initState();
    _presenter?.getInsureSchemeAll();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '参保方案',
        onBack: () {
          BoostNavigator.instance.pop();
        },
        actionWidget: Padding(
          padding: const EdgeInsets.only(right: 16),
          child: BrnIconAction(
            child: Image.asset(
              'assets/images/icon_question.png',
              scale: 3.0,
              height: 30,
              width: 30,
            ),
            iconPressed: () {
              BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "${httpConfig.getServerTypeM2}/syqj/insurance/help", "close": true, "share": true, "title": '保险帮助'});
            },
          ),
        ),
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Column(
        children: [
          Expanded(
            child: SingleChildScrollView(
              child: Container(
                color: Colours.base_primary_bg_page,
                child: Column(
                  children: [
                    //保险详情
                    Obx(() => InsureListView(
                          onClick: (uuid, sku_uuid) {
                            _controller.insurance_uuid.value = uuid;
                            _controller.insurance_sku.value = sku_uuid;
                          },
                          key: UniqueKey(),
                          list: _controller.list.value,
                          insurance_product_uuid: widget.insurance_product_uuid ?? "",
                        )),
                    //提示语
                    Padding(
                      padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonUtils.getSimpleText('1.营业执照名称与劳务合同签章名称必须保持一致，否则不予理赔。', 13, Colours.red, textAlign: TextAlign.left),
                          CommonUtils.getSimpleText('2.商业保洁责任险由大地保险承保。\n3.本保险将生成电子保单，分享和打印同样具有法律效力。\n4.购买流程、理赔流程、常见问题等资料详见右上角的帮助说明。', 13, textAlign: TextAlign.left, Colours.base_primary_text_title),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ),
          Gaps.line,
          Obx(() => Container(
                padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                // 底部悬浮视图的高度
                color: Colors.white,
                // 底部悬浮视图的背景颜色
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Wrap(
                      children: [
                        GestureDetector(
                          onTap: () {
                            _controller.checked.value = !_controller.checked.value;
                          },
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              LoadAssetImage(
                                (_controller.checked.value) ? "icon_check" : "icon_uncheck",
                                width: 20,
                                height: 20,
                              ),
                              Gaps.hGap4,
                              CommonUtils.getSimpleText('我已阅读并同意', 13, Colours.base_primary_text_title),
                            ],
                          ),
                        ),
                        InkWell(
                          child: CommonUtils.getSimpleText('《投保须知及声明》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111934745828"});
                          },
                        ),
                        CommonUtils.getSimpleText('、', 13, Colours.base_primary_text_title),
                        InkWell(
                          child: CommonUtils.getSimpleText('《客户告知书》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955438709"});
                          },
                        ),
                        CommonUtils.getSimpleText('、', 13, Colours.base_primary_text_title),
                        InkWell(
                          child: CommonUtils.getSimpleText('《委托合同》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111951244788"});
                          },
                        ),
                        CommonUtils.getSimpleText('、', 13, Colours.base_primary_text_title),
                        InkWell(
                          child: CommonUtils.getSimpleText('《特别约定》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955502564"});
                          },
                        ),
                        CommonUtils.getSimpleText('、', 13, Colours.base_primary_text_title),
                        InkWell(
                          child: CommonUtils.getSimpleText('《保险条款》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955518693"});
                          },
                        ),
                        CommonUtils.getSimpleText('和', 13, Colours.base_primary_text_title),
                        InkWell(
                          child: CommonUtils.getSimpleText('《缴款委托书》', 13, Colours.base_primary_blue),
                          onTap: () {
                            BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111951326572"});
                          },
                        ),
                      ],
                    ),
                    Gaps.vGap10,
                    Row(
                      children: [
                        Container(
                          width: 100, // 第一个按钮占据的空间较小
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(6.0),
                            border: Border.all(color: Colours.base_primary, width: 1.0),
                          ),
                          child: InkWell(
                            onTap: () {
                              BrnDialogManager.showConfirmDialog(context, title: "提示", cancel: '取消', confirm: '确定', message: "确定该项目不参保吗？请勿在同一天内对该项目反复开启/关闭参保，可能会导致员工重复购买保险，给公司带来损失。", barrierDismissible: false, onConfirm: () {
                                Navigator.of(context, rootNavigator: true).pop();
                                _presenter?.setInsureSchemeSetting("${widget.project_uuid}", "0", "", "");
                              }, onCancel: () {
                                Navigator.of(context, rootNavigator: true).pop();
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.all(8.0),
                              child: CommonUtils.getSimpleText("不参保", 16, Colours.base_primary, textAlign: TextAlign.center),
                            ),
                          ),
                        ),
                        Gaps.hGap16,
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colours.base_primary,
                              borderRadius: BorderRadius.circular(6.0),
                            ),
                            child: InkWell(
                              onTap: () {
                                if (TextUtil.isEmpty(_controller.insurance_uuid.value)) {
                                  BrnToast.show("请选择保险产品", context);
                                  return;
                                }
                                if (!_controller.checked.value) {
                                  BrnToast.show("请先阅读并同意各项协议", context);
                                  return;
                                }
                                _presenter?.getCompanyOne();
                              },
                              child: Container(
                                padding: EdgeInsets.all(8.0),
                                child: CommonUtils.getSimpleText("确定参保", 16, Colours.white, textAlign: TextAlign.center),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  @override
  saveStatus() {
    BrnToast.show("操作成功", context);
    Future.delayed(Duration(seconds: 1), () {
      BoostNavigator.instance.pop();
    });
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsureSchemePagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  getCompanyOne(companyOneEntity) {
    final Widget action = Padding(
      padding: const EdgeInsets.only(left: 14, right: 14, top: 20),
      child: RichText(
        text: TextSpan(
          style: TextStyle(color: Colors.black),
          children: <TextSpan>[
            const TextSpan(text: '① 请确认要您投保公司的营业执照信息：', style: TextStyle(color: Colours.base_primary_text_title)), // 公司名称变蓝色
            TextSpan(text: '\n'),
            TextSpan(text: '公司名称：', style: TextStyle(color: Colours.base_primary_text_title)), // 公司名称变蓝色
            TextSpan(text: '${companyOneEntity.invoiceTitle}', style: TextStyle(color: Colors.red)), // 公司名称的具体内容变红色
            TextSpan(text: '\n'),
            TextSpan(text: '统一社会信用代码：', style: TextStyle(color: Colours.base_primary_text_title)), // 统一社会信用代码变蓝色
            TextSpan(text: '${companyOneEntity.creditCode}', style: TextStyle(color: Colors.red)), // 统一社会信用代码的具体内容变红色
            TextSpan(text: '\n\n'),
            TextSpan(text: '② 请', style: TextStyle(color: Colours.base_primary_text_title)),
            TextSpan(text: '确保上方投保的营业执照与您签署合同的是同一个公司', style: TextStyle(color: Colours.base_primary_jv)), // 入职审批通过变绿色
            TextSpan(text: '，否则将影响后续理赔！', style: TextStyle(color: Colours.base_primary_text_title)),
            TextSpan(text: '\n\n'),
            TextSpan(text: '③ 系统将在', style: TextStyle(color: Colours.base_primary_text_title)),
            TextSpan(text: '入职审批通过时', style: TextStyle(color: Colours.base_primary_jv)), // 入职审批通过变绿色
            TextSpan(text: '，为', style: TextStyle(color: Colours.base_primary_text_title)),
            TextSpan(text: '入职到本项目', style: TextStyle(color: Colours.base_primary_jv)), // 入职审批通过变绿色
            TextSpan(text: '，且参保方式为“商业保险”', style: TextStyle(color: Colours.base_primary_text_title)),
            TextSpan(text: '的员工，使用余额自动购买保险！', style: TextStyle(color: Colours.base_primary_jv)), // 入职审批通过变绿色
            TextSpan(text: '\n\n'),
            TextSpan(
              text: '是否确定继续？', style: TextStyle(color: Colours.base_primary_text_title), // 确保上方投保的营业执照与您签署合同的是同一个公司变橙色
            ),
          ],
        ),
      ),
    );

    BrnDialogManager.showConfirmDialog(context, title: "提示", cancel: '取消', confirm: '确定', messageWidget: action, barrierDismissible: false, onConfirm: () {
      _presenter?.setInsureSchemeSetting("${widget.project_uuid}", "1", _controller.insurance_uuid.value, _controller.insurance_sku.value);
      Navigator.of(context, rootNavigator: true).pop();
    }, onCancel: () {
      Navigator.of(context, rootNavigator: true).pop();
    });
  }
}
