import 'package:flutter/cupertino.dart';

/**
 * 投保历史
 */
import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/custom_Insure_history_entity.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../item/Insure_history_listview.dart';
import '../item/Insure_listview.dart';
import '../iview/insure_scheme_iview.dart';
import '../presenter/insure_scheme_history_persenter.dart';

/// 保险商城
class InsureHistoryPage extends StatefulWidget {
  String? searchName = "";

  InsureHistoryPage({Key? key, this.searchName = ""}) : super(key: key);

  @override
  _InsureHistoryPageState createState() => _InsureHistoryPageState();
}

class _InsureHistoryPageState extends State<InsureHistoryPage> with BasePageMixin<InsureHistoryPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsureHistoryPage> implements InsureSchemeSaveIView {
  InsureSchemeHistoryPagePresenter? _presenter;

  final InsureSchemeHistoryPageController _controller = InsureSchemeHistoryPageController();

  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '购买时间最近的排前',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '购买时间最远的排前',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '到期时间最近的排前',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '到期时间最远的排前',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
  ];

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _controller.search_user_name.value = widget.searchName ?? "";
    _onRefresh();

    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        _loadMore();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.only(right: 16.0, left: 16.0, top: 10.0, bottom: 10.0),
                child: Column(
                  children: [
                    //搜索栏
                    Row(
                      children: [
                        Expanded(
                            child: CustomSearchView(
                          initialText: _controller.search_user_name.value,
                          hint: '请输入员工姓名',
                          onTextChanged: (text) {
                            _controller.search_user_name.value = text;
                            _onRefresh();
                          },
                        )),
                        Gaps.hGap10,
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(4.0),
                            border: Border.all(color: Colours.base_primary, width: 1.0),
                          ),
                          child: Padding(
                            padding: EdgeInsets.symmetric(horizontal: 6.0, vertical: 6.0),
                            child: InkWell(
                              onTap: () {
                                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_claims_process"});
                              },
                              child: CommonUtils.getSimpleText("理赔记录(${_controller.data.value.claimsTotal ?? 0})", 13, Colours.base_primary),
                            ),
                          ),
                        ),
                      ],
                    ),
                    Gaps.vGap10,
                    SelectTabWidget(
                      [
                        BaseChooseString("全部 ${_controller.data.value.allTotal ?? 0}"),
                        BaseChooseString("已过期 ${_controller.data.value.expiredTotal ?? 0}"),
                        BaseChooseString("近7天过保 ${_controller.data.value.nearWeekTotal ?? 0}"),
                      ],
                      multiSelect: false,
                      crossAxisCount: 3,
                      hideMore: false,
                      paddingBottom: 0,
                      paddingTop: 0,
                      tabFontSize: 13,
                      defaultSelectedIndex: [0],
                      lastIsAddOne: false,
                      selectedColor: Colours.base_primary,
                      bgSelectedColor: Colours.base_primary_select,
                      bgUnSelectedColor: Colours.base_primary_un_select,
                      childAspectRatio: 8 / 3,
                      radius: 50,
                      itemClickCallback: (List<int> indexs) {
                        _controller.type.value = "${indexs[0]}";
                        _onRefresh();
                      },
                    ),
                    Gaps.vGap10,
                    //筛选条件
                    InkWell(
                      child: Container(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonUtils.getSimpleText(_controller.sort_title.value, 14, Colours.base_primary_text_title),
                            Image.asset(
                              'assets/images/base/icon_base_gray_arrow.png',
                              height: 20,
                              width: 20,
                            )
                          ],
                        ),
                      ),
                      onTap: () {
                        // 展示actionSheet
                        showModalBottomSheet(
                            context: context,
                            backgroundColor: Colors.transparent,
                            builder: (BuildContext context) {
                              return BrnCommonActionSheet(
                                actions: actions,
                                cancelTitle: "取消",
                                clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                  String title = actionEle.title;
                                  _controller.sort_title.value = title;
                                  // BrnToast.show("title: $title, index: $index", context);
                                  _controller.sort_type.value = "${(index + 1)}";
                                  _onRefresh();
                                },
                              );
                            });
                      },
                    ),
                  ],
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: EdgeInsets.only(top: 10),
                hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return InsureHistoryListView(data: _controller.list[index]);
                },
              )),
            ],
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsureSchemeHistoryPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  saveStatus() {}

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;

  @override
  getCompanyOne(companyOneEntity) {
    // TODO: implement getCompanyOne
    throw UnimplementedError();
  }
}
