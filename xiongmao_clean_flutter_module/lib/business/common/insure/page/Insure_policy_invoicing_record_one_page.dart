import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../bean/insure_add_entity.dart';
import '../bean/insure_policy_invoicing_record_one_entity.dart';
import '../controller/insure_policy_invoicing_record_controller.dart';
import '../item/Insure_policy_invoicing_record_one_listview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../presenter/insure_policy_invoicing_record_persenter.dart';

/// 开票记录
class InsurePolicyInvoicingRecordOnePage extends StatefulWidget {
  String uuid;

  InsurePolicyInvoicingRecordOnePage({Key? key, required this.uuid}) : super(key: key);

  @override
  _InsurePolicyInvoicingRecordOnePageState createState() => _InsurePolicyInvoicingRecordOnePageState();
}

class _InsurePolicyInvoicingRecordOnePageState extends State<InsurePolicyInvoicingRecordOnePage> with BasePageMixin<InsurePolicyInvoicingRecordOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<InsurePolicyInvoicingRecordOnePage> implements InsurePolicyInvoicingIView {
  InsurePolicyInvoicingRecordPresenter? _presenter;

  final InsurePolicyInvoicingRecordController _controller = InsurePolicyInvoicingRecordController();

  @override
  void initState() {
    super.initState();
    _presenter?.getInsureRecordOne(widget.uuid);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '开票详情',
      ),
      body: Obx(() => SingleChildScrollView(
            child: Column(
              children: [
                Container(
                  padding: const EdgeInsets.only(top: 14, bottom: 14, left: 16, right: 16),
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('关键信息', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText((_controller.data.value.amountType == '1' ? '正数发票' : '负数发票'), 14, Colours.base_primary_text_title),
                          CommonUtils.getSimpleText(
                            '已选${(_controller.data.value.orderTotal == null) ? 0 : _controller.data.value.orderTotal ?? 0}个保单${_controller.data.value.amountType == '1' ? '(${(_controller.data.value.refundTotal == null) ? 0 : _controller.data.value.refundTotal ?? 0}个已退款，无法开票)' : ''}',
                            14,
                            Colours.base_primary_text_title,
                            textAlign: TextAlign.right,
                          ),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("开票金额", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("预计${(TextUtil.isEmpty(_controller.data.value.amount)) ? 0 : _controller.data.value.amount ?? 0}元", 14, Colours.red, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("申请时间", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.createTime) ? '' : _controller.data.value.createTime)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                    ],
                  ),
                ),
                Gaps.vGap12,
                Container(
                  padding: const EdgeInsets.only(top: 14, bottom: 14, left: 16, right: 16),
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("电子邮箱（收发票）", 14, Colours.base_primary_text_title),
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.email) ? '' : _controller.data.value.email)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("开票类型", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText((_controller.data.value.invoiceType == '1' ? '普票' : '专票'), 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("合并发票", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText((_controller.data.value.isMerge == '1' ? '是' : '否'), 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("发票抬头", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.invoiceTitle) ? '' : _controller.data.value.invoiceTitle)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("发票纳税人识别号", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${TextUtil.isEmpty(_controller.data.value.identificationNumber) ? '' : _controller.data.value.identificationNumber}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("地址", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.address) ? '' : _controller.data.value.address)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("电话", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.mobile) ? '' : _controller.data.value.mobile)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("开户行", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.bank) ? '' : _controller.data.value.bank)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                      Gaps.vGap12,
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          CommonUtils.getSimpleText("银行账号", 14, Colours.base_primary_text_title),
                          Gaps.vGap12,
                          CommonUtils.getSimpleText("${(TextUtil.isEmpty(_controller.data.value.bankNo) ? '' : _controller.data.value.bankNo)}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
                        ],
                      ),
                    ],
                  ),
                ),
                Gaps.vGap16,
                _controller.data.value.orderList != null && _controller.data.value.orderList!.isNotEmpty
                    ? ListView.builder(
                        shrinkWrap: true,
                        physics: const NeverScrollableScrollPhysics(),
                        padding: const EdgeInsets.only(bottom: 50, left: 10, right: 10),
                        itemCount: _controller.data.value.orderList!.length,
                        itemBuilder: (context, index) {
                          return InsurePolicyInvoicingRecordOneListView(
                            type: _controller.data.value.amountType,
                            data: _controller.data.value.orderList![index],
                          );
                        },
                      )
                    : Container(),
              ],
            ),
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = InsurePolicyInvoicingRecordPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void getAmountInfo(InsureAddEntity data) {}

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => false;
}
