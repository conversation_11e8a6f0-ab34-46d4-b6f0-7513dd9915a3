import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';

/// 项目参保方案
class InsureRulesWebPage extends StatefulWidget {
  String url;

  InsureRulesWebPage({required this.url});

  @override
  _InsureHelpWebPageState createState() => _InsureHelpWebPageState();
}

class _InsureHelpWebPageState extends State<InsureRulesWebPage> with AutomaticKeepAliveClientMixin {
  late XmWebController controller;

  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');

      BoostNavigator.instance.push(
        'webPage',
        arguments: {
          'url': '${httpConfig.getServerTypeM2}/syqj/insurance/companyinsurance?insurancetype=2&project_uuid=$project_uuid',
          'title': '项目参保方案',
          'subTitle': project_name ?? httpConfig.project_name,
          'domainM2': true,
        },
      ).then((value) {
        if (value != null) {
          String project_uuid = value as String;
          BoostNavigator.instance.push('ProjectOneWebPage', arguments: {'project_uuid': project_uuid, 'project_name': project_name ?? httpConfig.project_name});
        }
        controller.loadRequest(Uri.parse(widget.url ?? ""));
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: XmWebView(
        onXmJsMsgCall: (XmJsMsgCall msgCall) {
          var decode = json.decode(msgCall.message);
          print('H5调用原声的回调name --> ${decode.toString()}');

          var name = decode["name"];
          var data = decode["data"];
          switch (name) {
            case "WebViewJavascriptBridge_goPayInsurance":
              var url = data['url'];
              print('来这里了 ${httpConfig.getServerTypeM2}$url');
              var projectName = data["project_name"];

              BoostNavigator.instance.push(
                'webPage',
                arguments: {
                  'url': '${httpConfig.getServerTypeM2}$url',
                  'title': (TextUtil.isEmpty(projectName)) ? '企业参保方案' : '项目参保方案',
                  'subTitle': projectName ?? httpConfig.project_name,
                  'domainM2': true,
                },
              ).then((value) {
                if (value != null) {
                  String projectUuid = value as String;
                  BoostNavigator.instance.push('ProjectOneWebPage', arguments: {
                    'project_uuid': '${projectUuid}',
                    'project_name': projectName ?? httpConfig.project_name,
                  });
                }
                controller.loadRequest(Uri.parse(widget.url ?? ""));
              });
              break;

            ///选择项目弹窗
            case 'WebViewJavascriptBridge_jumpCustomer':
              BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false, 'isNeedAll': false, 'project_uuid': httpConfig.project_uuid});
              break;
          }
        },
        controllerSettingBack: (value) {
          controller = value;
          LogUtil.e("object----url---" + (widget.url ?? ""));
          controller.loadRequest(Uri.parse(widget.url ?? ""));
        },
        domainM2: false,
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }

  @override
  // TODO: implement wantKeepAlive
  bool get wantKeepAlive => true;
}
