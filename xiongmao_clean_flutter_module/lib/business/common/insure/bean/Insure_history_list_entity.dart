import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/Insure_history_list_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/Insure_history_list_entity.g.dart';

@JsonSerializable()
class InsureHistoryListEntity {
	int? page;
	int? size;
	@JSONField(name: "claims_total")
	int? claimsTotal;
	@JSONField(name: "all_total")
	int? allTotal;
	@JSONField(name: "expired_total")
	int? expiredTotal;
	@JSONField(name: "near_week_total")
	int? nearWeekTotal;
	int? total;
	List<InsureHistoryListList>? list;

	InsureHistoryListEntity();

	factory InsureHistoryListEntity.fromJson(Map<String, dynamic> json) => $InsureHistoryListEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureHistoryListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsureHistoryListList {
	@JSONField(name: "order_number")
	String? orderNumber;
	@JSONField(name: "insurance_company_name")
	String? insuranceCompanyName;
	@JSONField(name: "policy_no")
	String? policyNo;
	@JSONField(name: "product_name")
	String? productName;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "order_status_name")
	String? orderStatusName;
	@JSONField(name: "refund_status")
	String? refundStatus;
	@JSONField(name: "refund_status_name")
	String? refundStatusName;
	@JSONField(name: "insured_user_name")
	String? insuredUserName;
	@JSONField(name: "insured_user_id_number")
	String? insuredUserIdNumber;
	@JSONField(name: "insured_user_status_name")
	String? insuredUserStatusName;
	@JSONField(name: "pay_time")
	String? payTime;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "policy_refund_time")
	String? policyRefundTime;
	@JSONField(name: "elec_policy_url")
	String? elecPolicyUrl;
	@JSONField(name: "price")
	String? price;
	@JSONField(name: "refund_money")
	String? refundMoney;
	bool isSelected = false;

	InsureHistoryListList();

	factory InsureHistoryListList.fromJson(Map<String, dynamic> json) => $InsureHistoryListListFromJson(json);

	Map<String, dynamic> toJson() => $InsureHistoryListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}