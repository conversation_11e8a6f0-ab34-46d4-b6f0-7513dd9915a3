import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/Insure_history_one_new_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/Insure_history_one_new_entity.g.dart';

@JsonSerializable()
class InsureHistoryOneNewEntity {
	@JSONField(name: "order_number")
	String? orderNumber;
	@JSONField(name: "insurance_company")
	String? insuranceCompany;
	@JSONField(name: "insurance_company_name")
	String? insuranceCompanyName;
	@JSONField(name: "policy_no")
	String? policyNo;
	@JSONField(name: "product_name")
	String? productName;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "order_status")
	String? orderStatus;
	@JSONField(name: "order_status_name")
	String? orderStatusName;
	@JSO<PERSON>ield(name: "refund_status")
	String? refundStatus;
	@JSONField(name: "refund_status_name")
	String? refundStatusName;
	@JSONField(name: "insured_user_name")
	String? insuredUserName;
	@JSONField(name: "insured_user_id_number")
	String? insuredUserIdNumber;
	@JSONField(name: "pay_time")
	String? payTime;
	@JSONField(name: "elec_policy_url")
	String? elecPolicyUrl;
	@JSONField(name: "insured_user_list")
	List<InsureHistoryOneNewInsuredUserList>? insuredUserList;

	InsureHistoryOneNewEntity();

	factory InsureHistoryOneNewEntity.fromJson(Map<String, dynamic> json) => $InsureHistoryOneNewEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureHistoryOneNewEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsureHistoryOneNewInsuredUserList {
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "id_number")
	String? idNumber;
	@JSONField(name: "status_name")
	String? statusName;
	String? avatar;
	@JSONField(name: "is_valid")
	String? isValid;

	InsureHistoryOneNewInsuredUserList();

	factory InsureHistoryOneNewInsuredUserList.fromJson(Map<String, dynamic> json) => $InsureHistoryOneNewInsuredUserListFromJson(json);

	Map<String, dynamic> toJson() => $InsureHistoryOneNewInsuredUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}