import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_policy_invoicing_record_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_policy_invoicing_record_entity.g.dart';

@JsonSerializable()
class InsurePolicyInvoicingRecordEntity {
	int? page;
	int? size;
	int? total;
	List<InsurePolicyInvoicingRecordList>? list;

	InsurePolicyInvoicingRecordEntity();

	factory InsurePolicyInvoicingRecordEntity.fromJson(Map<String, dynamic> json) => $InsurePolicyInvoicingRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsurePolicyInvoicingRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsurePolicyInvoicingRecordList {
	String? uuid;
	@J<PERSON><PERSON>ield(name: "create_user_name")
	String? createUserName;
	@JSONField(name: "create_time")
	String? createTime;
	String? amount;
	@JSONField(name: "invoice_title")
	String? invoiceTitle;

	InsurePolicyInvoicingRecordList();

	factory InsurePolicyInvoicingRecordList.fromJson(Map<String, dynamic> json) => $InsurePolicyInvoicingRecordListFromJson(json);

	Map<String, dynamic> toJson() => $InsurePolicyInvoicingRecordListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}