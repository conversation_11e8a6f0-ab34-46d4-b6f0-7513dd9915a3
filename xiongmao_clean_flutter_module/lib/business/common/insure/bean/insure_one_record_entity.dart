import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_one_record_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_one_record_entity.g.dart';

@JsonSerializable()
class InsureOneRecordEntity {
	int? page;
	int? size;
	int? total;
	List<InsureOneRecordList>? list;

	InsureOneRecordEntity();

	factory InsureOneRecordEntity.fromJson(Map<String, dynamic> json) => $InsureOneRecordEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureOneRecordEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsureOneRecordList {
	String? remark;
	@JSONField(name: "create_time")
	String? createTime;

	InsureOneRecordList();

	factory InsureOneRecordList.fromJson(Map<String, dynamic> json) => $InsureOneRecordListFromJson(json);

	Map<String, dynamic> toJson() => $InsureOneRecordListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}