import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_recently_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_recently_one_entity.g.dart';

@JsonSerializable()
class InsureRecentlyOneEntity {
	String? uuid;
	@JSONField(name: "contract_company_uuid")
	String? contractCompanyUuid;
	@JSONField(name: "invoice_title")
	String? invoiceTitle;
	@JSONField(name: "amount_type")
	String? amountType;
	@J<PERSON>NField(name: "amount_type_name")
	String? amountTypeName;
	String? email;
	@J<PERSON><PERSON>ield(name: "invoice_type")
	String? invoiceType;
	@JSONField(name: "invoice_type_name")
	String? invoiceTypeName;
	@JSONField(name: "identification_number")
	String? identificationNumber;
	String? address;
	String? mobile;
	String? bank;
	@JSONField(name: "bank_no")
	String? bankNo;
	@JSONField(name: "is_merge")
	String? isMerge;
	@JSO<PERSON>ield(name: "is_merge_name")
	String? isMergeName;

	InsureRecentlyOneEntity();

	factory InsureRecentlyOneEntity.fromJson(Map<String, dynamic> json) => $InsureRecentlyOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureRecentlyOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}