import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/company_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/company_one_entity.g.dart';

@JsonSerializable()
class CompanyOneEntity {
	String? uuid;
	@JSONField(name: "full_name")
	String? fullName;
	@JSONField(name: "company_name")
	String? companyName;
	@JSONField(name: "credit_code")
	String? creditCode;
	@J<PERSON>NField(name: "invoice_title")
	String? invoiceTitle;
	@J<PERSON><PERSON>ield(name: "identification_number")
	String? identificationNumber;
	@JSONField(name: "business_license")
	String? businessLicense;

	CompanyOneEntity();

	factory CompanyOneEntity.fromJson(Map<String, dynamic> json) => $CompanyOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $CompanyOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}