import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_scheme_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_scheme_data_entity.g.dart';

@JsonSerializable()
class InsureSchemeDataEntity {
	List<InsureSchemeDataList>? list;

	InsureSchemeDataEntity();

	factory InsureSchemeDataEntity.fromJson(Map<String, dynamic> json) => $InsureSchemeDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureSchemeDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsureSchemeDataList {
	String? uuid;
	@JSONField(name: "product_name")
	String? productName;
	@JSONField(name: "insurance_company")
	String? insuranceCompany;
	@JSONField(name: "insurance_no")
	String? insuranceNo;
	String? introduction;
	@JSONField(name: "cover_url")
	String? coverUrl;
	@JSONField(name: "description_url")
	String? descriptionUrl;
	@JSONField(name: "sku_list")
	List<InsureSchemeDataListSkuList>? skuList;

	InsureSchemeDataList();

	factory InsureSchemeDataList.fromJson(Map<String, dynamic> json) => $InsureSchemeDataListFromJson(json);

	Map<String, dynamic> toJson() => $InsureSchemeDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsureSchemeDataListSkuList {
	String? unit;
	String? num;
	@JSONField(name: "sku_type")
	String? skuType;
	String? money;

	InsureSchemeDataListSkuList();

	factory InsureSchemeDataListSkuList.fromJson(Map<String, dynamic> json) => $InsureSchemeDataListSkuListFromJson(json);

	Map<String, dynamic> toJson() => $InsureSchemeDataListSkuListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}