import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_policy_invoicing_record_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_policy_invoicing_record_one_entity.g.dart';

@JsonSerializable()
class InsurePolicyInvoicingRecordOneEntity {
	String? uuid;
	@JSONField(name: "order_total")
	String? orderTotal;
	@JSONField(name: "refund_total")
	String? refundTotal;
	@JSONField(name: "create_time")
	String? createTime;
	String? amount;
	@JSONField(name: "invoice_title")
	String? invoiceTitle;
	@JSONField(name: "amount_type")
	String? amountType;
	@JSONField(name: "amount_type_name")
	String? amountTypeName;
	String? email;
	@JSONField(name: "invoice_type")
	String? invoiceType;
	@JSONField(name: "invoice_type_name")
	String? invoiceTypeName;
	@JSO<PERSON>ield(name: "identification_number")
	String? identificationNumber;
	String? address;
	String? mobile;
	String? bank;
	@JSONField(name: "bank_no")
	String? bankNo;
	@JSONField(name: "is_merge")
	String? isMerge;
	@JSONField(name: "is_merge_name")
	String? isMergeName;
	@JSONField(name: "order_list")
	List<InsurePolicyInvoicingRecordOneOrderList>? orderList;

	InsurePolicyInvoicingRecordOneEntity();

	factory InsurePolicyInvoicingRecordOneEntity.fromJson(Map<String, dynamic> json) => $InsurePolicyInvoicingRecordOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsurePolicyInvoicingRecordOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class InsurePolicyInvoicingRecordOneOrderList {
	@JSONField(name: "order_number")
	String? orderNumber;
	@JSONField(name: "policy_no")
	String? policyNo;
	@JSONField(name: "date_time")
	String? dateTime;
	@JSONField(name: "insured_user_name")
	String? insuredUserName;
	@JSONField(name: "insured_user_id_number")
	String? insuredUserIdNumber;
	@JSONField(name: "insured_user_status_name")
	String? insuredUserStatusName;
	@JSONField(name: "order_status_name")
	String? orderStatusName;
	String? amount;

	InsurePolicyInvoicingRecordOneOrderList();

	factory InsurePolicyInvoicingRecordOneOrderList.fromJson(Map<String, dynamic> json) => $InsurePolicyInvoicingRecordOneOrderListFromJson(json);

	Map<String, dynamic> toJson() => $InsurePolicyInvoicingRecordOneOrderListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}