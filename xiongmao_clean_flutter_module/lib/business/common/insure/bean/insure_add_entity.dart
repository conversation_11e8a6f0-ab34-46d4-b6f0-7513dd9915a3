import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/insure_add_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/insure_add_entity.g.dart';

@JsonSerializable()
class InsureAddEntity {
	@JSONField(name: "order_total")
	int? orderTotal;
	@JSONField(name: "refund_total")
	int? refundTotal;
	String? amount;

	InsureAddEntity();

	factory InsureAddEntity.fromJson(Map<String, dynamic> json) => $InsureAddEntityFromJson(json);

	Map<String, dynamic> toJson() => $InsureAddEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}