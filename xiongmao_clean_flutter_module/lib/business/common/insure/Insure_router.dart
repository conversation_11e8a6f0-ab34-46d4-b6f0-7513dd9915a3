import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_history_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_market_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_policy_invoicing_add_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_policy_invoicing_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_policy_invoicing_record_one_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_policy_invoicing_record_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/insure_rules_web_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';

import '../../../net/http_config.dart';
import 'bean/insure_add_entity.dart';

/// 之前的总投保的
const insurePage = "insurePage";

///参保方案
const insureMarketPage = "insureMarketPage";

///投保记录
const insureHistoryPage = "insureHistoryPage";

///保险详情
const insureHistoryOnePage = "insureHistoryOnePage";

///开票信息
const insurePolicyInvoicingPage = "InsurePolicyInvoicingPage";

///开票记录
const insurePolicyInvoicingRecordPage = "insurePolicyInvoicingRecordPage";

///开票记录详情
const insurePolicyInvoicingRecordOnePage = "insurePolicyInvoicingRecordOnePage";

///添加开票信息
const insurePolicyInvoicingRecordEditPage = "insurePolicyInvoicingRecordEditPage";

///商业保险
const insureRulesWebPage = "InsureRulesWebPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> insureRouterMap = {
  insurePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String searchName = map['search_name'] ?? '';
          return InsurePage(
            searchName: searchName,
          );
        });
  },

  ///参保方案
  insureMarketPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String project_uuid = map["project_uuid"] ?? httpConfig.project_uuid;
          String insurance_product_uuid = map["insurance_product_uuid"] ?? '';
          return InsureMarketPage(project_uuid: project_uuid, insurance_product_uuid: insurance_product_uuid);
        });
  },

  /// 保险
  insureHistoryPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String searchName;
          if (map.containsKey('search_name')) {
            searchName = map['search_name'];
          } else {
            searchName = '';
          }
          return InsureHistoryPage(searchName: searchName);
        });
  },

  /// 详情
  insureHistoryOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String order_number = map["order_number"];
          return InsureDetailsPage(
            order_number: order_number,
          );
        });
  },

  /// 电子发票开票管理界面
  insurePolicyInvoicingPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return InsurePolicyInvoicingPage();
        });
  },

  /// 电子发票开票记录
  insurePolicyInvoicingRecordPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return InsurePolicyInvoicingRecordPage();
        });
  },

  /// 电子发票开票记录 详情
  insurePolicyInvoicingRecordOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map["uuid"];
          return InsurePolicyInvoicingRecordOnePage(
            uuid: uuid,
          );
        });
  },

  /// 添加开票信息
  insurePolicyInvoicingRecordEditPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String ids = map["ids"];
          String amountType = map["amountType"];
          return InsurePolicyInvoicingRecordAddPage(
            ids: ids,
            amountType: amountType,
          );
        });
  },

  /// 商业保险
  insureRulesWebPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String url = map["url"];
          return InsureRulesWebPage(
            url: url,
          );
        });
  },
};
