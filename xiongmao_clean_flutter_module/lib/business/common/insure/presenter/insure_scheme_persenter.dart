import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/bean/company_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/insure_scheme_data_entity.dart';
import '../controller/insure_scheme_controller.dart';
import '../iview/insure_scheme_iview.dart';

/// 参保方案
class InsureSchemePagePresenter extends BasePagePresenter<InsureSchemeSaveIView> with WidgetsBindingObserver {
  InsureSchemePageController controller;

  InsureSchemePagePresenter(this.controller);

  //获取参保方案
  Future<dynamic> getInsureSchemeAll() {
    var params = <String, String>{};
    return requestNetwork<InsureSchemeDataEntity>(Method.get, url: HttpApi.GET_INSURE_SCHEME_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initMyList(data.list!);
      }
    }, onError: (_, __) {});
  }

  //参保设置
  Future<dynamic> setInsureSchemeSetting(String project_uuid, String is_insurance, String? insurance_uuid, String? insurance_sku) {
    var params = <String, String>{};
    params["project_uuid"] = project_uuid;
    params["is_insurance"] = is_insurance;
    if ("1" == is_insurance) {
      //参保的情况下，再增加这俩个参数
      params["insurance_uuid"] = "$insurance_uuid";
      params["insurance_sku"] = "$insurance_sku";
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_INSURE_SCHEME_SETTING, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.saveStatus();
    }, onError: (_, __) {});
  }

  //参保设置
  Future<dynamic> getCompanyOne() {
    var params = <String, String>{};
    params["uuid"] = httpConfig.company_uuid;
    return requestNetwork<CompanyOneEntity>(Method.post, url: HttpApi.GET_COMPANY_BASE_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getCompanyOne(data);
      }
    }, onError: (_, __) {});
  }
}
