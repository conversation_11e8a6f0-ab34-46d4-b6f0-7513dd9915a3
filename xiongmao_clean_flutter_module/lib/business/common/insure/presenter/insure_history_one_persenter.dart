import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_history_one_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/Insure_history_one_new_entity.dart';
import '../bean/insure_one_record_entity.dart';
import '../controller/insure_history_one_controller.dart';
import '../controller/insure_scheme_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../iview/insure_scheme_iview.dart';

/**
 * 参保方案
 */
class InsureHistoryOnePagePresenter extends BasePagePresenter<InsureHistoryOneIView> with WidgetsBindingObserver {
  InsureHistoryOnePageController controller;

  InsureHistoryOnePagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getInsureHistoryActionRecord();
  }

  void loadMore() {
    _page++;
    getInsureHistoryActionRecord();
  }

  //获取详情的操作记录
  Future<dynamic> getInsureHistoryActionRecord() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "100";
    params["order_number"] = "${controller.order_number.value}";
    return requestNetwork<InsureOneRecordEntity>(Method.get, url: HttpApi.GET_INSURE_SCHEME_HISTORY_ACTION_RECORD, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  //获取保险详情
  Future<dynamic> getInsureSchemeHistoryOne() {
    var params = <String, String>{};
    params["order_number"] = "${controller.order_number.value}";
    params["is_insured_user_list"] = "1"; //是否查询被保人列表数据1是0否
    return requestNetwork<InsureHistoryOneNewEntity>(Method.get, url: HttpApi.GET_INSURE_SCHEME_HISTORY_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initData(data);
      }
    }, onError: (_, __) {});
  }
}
