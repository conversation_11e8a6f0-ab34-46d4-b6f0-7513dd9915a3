import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/insure_add_entity.dart';
import '../bean/insure_policy_invoicing_record_entity.dart';
import '../bean/insure_policy_invoicing_record_one_entity.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../controller/insure_policy_invoicing_record_controller.dart';
import '../controller/insure_scheme_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../iview/insure_policy_invoicing_add_iview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../iview/insure_scheme_iview.dart';

/// 保险开票信息
class InsurePolicyInvoicingRecordPresenter extends BasePagePresenter<InsurePolicyInvoicingIView> with WidgetsBindingObserver {
  InsurePolicyInvoicingRecordController controller;

  InsurePolicyInvoicingRecordPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getInsureRecord();
  }

  void loadMore() {
    _page++;
    getInsureRecord();
  }

  ///获取保险的记录
  Future<dynamic> getInsureRecord() {
    var params = <String, String>{};
    params["page"] = "$_page";

    return requestNetwork<InsurePolicyInvoicingRecordEntity>(Method.get, url: HttpApi.INSURE_INVOICE_GET_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  ///获取记录的详情
  Future<dynamic> getInsureRecordOne(String uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";

    ///是否查询订单列表 1是0否
    params["is_order_list"] = "1";

    return requestNetwork<InsurePolicyInvoicingRecordOneEntity>(Method.get, url: HttpApi.INSURE_INVOICE_GET_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initData(data);
      }
    }, onError: (_, __) {});
  }

}
