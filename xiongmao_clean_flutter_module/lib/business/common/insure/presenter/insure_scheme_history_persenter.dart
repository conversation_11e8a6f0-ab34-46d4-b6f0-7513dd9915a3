import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/Insure_history_list_entity.dart';
import '../controller/insure_scheme_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../iview/insure_scheme_iview.dart';

/**
 * 参保方案
 */
class InsureSchemeHistoryPagePresenter extends BasePagePresenter<InsureSchemeSaveIView> with WidgetsBindingObserver {
  InsureSchemeHistoryPageController controller;

  InsureSchemeHistoryPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getInsureSchemeHistory();
  }

  void loadMore() {
    _page++;
    getInsureSchemeHistory();
  }

  //获取保险的记录
  Future<dynamic> getInsureSchemeHistory() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["user_name"] = controller.search_user_name.value;
    params["type"] = controller.type.value;
    params["sort_type"] = controller.sort_type.value;
    params["is_claims_total"] = "1";
    params["is_all_total"] = "1";
    params["is_expired_total"] = "1"; //是否查询已过期总数 1是0否
    params["is_near_week_total"] = "1"; //是否查询近7天总数 1是0否

    return requestNetwork<InsureHistoryListEntity>(Method.get, url: HttpApi.GET_INSURE_SCHEME_HISTORY_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initData(data);
      }
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? [], '${data.total ?? 0}');
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }
}
