import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/insure_add_entity.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../controller/insure_scheme_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../iview/insure_scheme_iview.dart';

/// 保险开票信息
class InsurePolicyInvoicingPresenter extends BasePagePresenter<InsurePolicyInvoicingIView> with WidgetsBindingObserver {
  InsurePolicyInvoicingController controller;

  InsurePolicyInvoicingPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    controller.count.value = 0;
    getInsureSchemeHistory();
  }

  void loadMore() {
    _page++;
    getInsureSchemeHistory();
  }

  //获取保险的记录
  Future<dynamic> getInsureSchemeHistory() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["type"] = controller.type.value;

    //状态 0全部 1已过期 2近7天过期 3开票新购 4开票退款
    if (controller.type.value == '3') {
      if (!TextUtil.isEmpty(controller.payStartDate.value)) {
        params["pay_start_date"] = controller.payStartDate.value.replaceAll('.', '-');
      }
      if (!TextUtil.isEmpty(controller.payEndDate.value)) {
        params["pay_end_date"] = controller.payEndDate.value.replaceAll('.', '-');
      }
    } else if (controller.type.value == '4') {
      if (!TextUtil.isEmpty(controller.policyRefundStartDate.value)) {
        params["policy_refund_start_date"] = controller.policyRefundStartDate.value.replaceAll('.', '-');
      }
      if (!TextUtil.isEmpty(controller.policyRefundEndDate.value)) {
        params["policy_refund_end_date"] = controller.policyRefundEndDate.value.replaceAll('.', '-');
      }
    }

    if (!TextUtil.isEmpty(controller.contractCompanyUuid.value)) {
      params["contract_company_uuid"] = controller.contractCompanyUuid.value;
    }

    return requestNetwork<InsureHistoryListEntity>(Method.get, url: HttpApi.GET_INSURE_SCHEME_HISTORY_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? [], data.total ?? 0);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }
}
