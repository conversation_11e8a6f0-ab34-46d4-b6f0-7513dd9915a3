import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/insure_add_entity.dart';
import '../bean/insure_recently_one_entity.dart';
import '../controller/insure_policy_invoicing_add_controller.dart';
import '../controller/insure_policy_invoicing_controller.dart';
import '../controller/insure_scheme_controller.dart';
import '../controller/insure_scheme_history_controller.dart';
import '../iview/insure_policy_invoicing_add_iview.dart';
import '../iview/insure_policy_invoicing_iview.dart';
import '../iview/insure_scheme_iview.dart';

/// 保险开票信息
class InsurePolicyInvoicingAddPresenter extends BasePagePresenter<InsurePolicyInvoicingAddIView> with WidgetsBindingObserver {
  InsurePolicyInvoicingAddController controller;

  InsurePolicyInvoicingAddPresenter(this.controller);

  ///获取开票信息
  Future<dynamic> getAmountInfo(String ids, String amountType) {
    var params = <String, String>{};

    ///订单编号列表
    params["order_number_list"] = ids;

    ///金额类型 1正数发票 2负数发票
    params["amount_type"] = amountType;

    return requestNetwork<InsureAddEntity>(Method.get, url: HttpApi.INSURE_INVOICE_AMOUNT_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.data.value = data;
      }
    }, onError: (_, __) {});
  }

  ///获取最近开票信息
  Future<dynamic> getAmountRecentlyInfo() {
    var params = <String, String>{};
    return requestNetwork<InsureRecentlyOneEntity>(Method.get, url: HttpApi.INSURE_INVOICE_AMOUNT_INFO_LAST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.dataRecentlyOne(data);
      }
    }, onError: (_, __) {});
  }

  ///新增开票信息
  Future<dynamic> createInsureInvoicing(String amountType, String ids) {
    var params = <String, String>{};

    //金额类型 1正数发票 2负数发票
    params["amount_type"] = amountType;
    //订单编号
    params["order_number_list"] = ids;
    //电子邮箱
    params["email"] = controller.email.value;
    //开票类型 1普票 2专票
    params["invoice_type"] = controller.policyType.value;
    //是否合并发票 1是 2否
    params["is_merge"] = controller.policyMeagerType.value;
    //发票抬头
    params["invoice_title"] = controller.invoiceTitle.value;
    //发票纳税人识别号
    params["identification_number"] = controller.invoiceNumber.value;
    //地址 专票必传
    params["address"] = controller.address.value;
    //银行账号 专票必传
    params["bank_no"] = controller.bankNo.value;
    //电话 专票必传
    params["mobile"] = controller.mobile.value;
    //开户行 专票必传
    params["bank"] = controller.bank.value;
    //合同公司ID
    params["contract_company_uuid"] = controller.contractCompanyUuid.value;

    return requestNetwork<Object>(Method.get, url: HttpApi.INSURE_INVOICE_CREATE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.createSuccess();
      }
    }, onError: (_, __) {});
  }
}
