import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/custom_Insure_history_entity.dart';
import '../bean/insure_policy_invoicing_record_entity.dart';

class InsurePolicyInvoicingRecordListView extends StatelessWidget {
  InsurePolicyInvoicingRecordList data;

  InsurePolicyInvoicingRecordListView({super.key, required this.data});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent, // 取消水波纹颜色
      highlightColor: Colors.transparent, // 取消高亮背景色
      onTap: () {
        BoostNavigator.instance.push('insurePolicyInvoicingRecordOnePage', arguments: {'uuid': '${data.uuid}'});
      },
      child: Container(
        margin: const EdgeInsets.only(right: 15, left: 15, bottom: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0), // 设置圆角半径为10.0
          color: Colors.white, // 设置背景颜色为灰色
        ),
        padding: const EdgeInsets.only(
          top: 16,
          left: 12,
          right: 12,
          bottom: 16,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(child: CommonUtils.getSimpleText('${data.createUserName}申请开票', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                CommonUtils.getSimpleText(
                  '详情',
                  14,
                  Colours.base_primary_text_title,
                ),
                Gaps.hGap4,
                const LoadAssetImage(
                  'icon_base_arrow',
                  width: 14,
                  height: 14,
                )
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            //保单号
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("申请时间", 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.createTime, 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            //在保人员
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("发票金额", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("预计${data.amount}元", 14, Colours.red, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("发票抬头", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("${data.invoiceTitle}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
