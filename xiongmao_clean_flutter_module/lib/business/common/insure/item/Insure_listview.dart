import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/custom_Insure_entity.dart';
import '../bean/insure_scheme_data_entity.dart';

class InsureListView extends StatefulWidget {
  final Function(String uuid, String sku_uuid) onClick;

  List<InsureSchemeDataList> list;

  String insurance_product_uuid;

  InsureListView({super.key, required this.onClick, required this.list, required this.insurance_product_uuid});

  @override
  _InsureListViewState createState() => _InsureListViewState();
}

class _InsureListViewState extends State<InsureListView> {
  int selectedItemIndex = -1; // 记录当前选中的item索引

  @override
  void initState() {
    super.initState();

    if (!TextUtil.isEmpty(widget.insurance_product_uuid)) {
      for (int i = 0; i < widget.list.length; i++) {
        if (widget.insurance_product_uuid == widget.list[i].uuid) {
          selectedItemIndex = i;
          widget.onClick("${widget.list[selectedItemIndex].uuid}", "${widget.list[selectedItemIndex].skuList![0].skuType}"); // 触发点击事件
          break;
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: NeverScrollableScrollPhysics(),
      // 禁止ListView滚动
      padding: EdgeInsets.only(top: 10),
      shrinkWrap: true,
      itemCount: widget.list.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              selectedItemIndex = index; // 更新选中的item index
              widget.onClick("${widget.list[index].uuid}", "${widget.list[index].skuList![0].skuType}"); // 触发点击事件
            });
          },
          child: Container(
            margin: EdgeInsets.only(left: 10, right: 10, bottom: 10),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(10.0),
            ),
            padding: EdgeInsets.all(10.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start, // 头像在顶部
                  children: [
                    Container(
                      margin: EdgeInsets.only(right: 8.0, top: 4.0),
                      child: InkWell(
                        child: LoadImage(
                          widget.list[index].coverUrl ?? "",
                          fit: BoxFit.contain,
                          height: 99,
                          width: 90,
                        ),
                        onTap: () {
                          BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_detail", "url": "${widget.list[index].descriptionUrl}", "title": "${widget.list[index].productName}"});
                        },
                      ),
                    ),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(child: CommonUtils.getSimpleText(widget.list[index].productName, 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                              LoadAssetImage(
                                (index == selectedItemIndex) ? "icon_check" : "icon_uncheck", // 根据选中状态显示不同的图标
                                width: 20,
                                height: 20,
                              ),
                            ],
                          ),
                          CommonUtils.getSimpleText(widget.list[index].introduction, 13, Colours.base_primary_text_caption),
                          CommonUtils.getSimpleText("${widget.list[index].skuList![0].money}${widget.list[index].skuList![0].unit}", 14, Colours.red, fontWeight: FontWeight.bold),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
