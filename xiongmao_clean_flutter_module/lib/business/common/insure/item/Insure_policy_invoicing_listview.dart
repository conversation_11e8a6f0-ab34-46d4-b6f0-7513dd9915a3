import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/custom_Insure_history_entity.dart';

class InsurePolicyInvoicingListView extends StatelessWidget {
  String type;
  InsureHistoryListList data;

  bool refund;

  final Function onClick;

  InsurePolicyInvoicingListView({super.key, required this.type, required this.data, required this.refund, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent, // 取消水波纹颜色
      highlightColor: Colors.transparent, // 取消高亮背景色
      onTap: () {
        onClick();
      },
      child: Container(
        margin: const EdgeInsets.only(right: 15, left: 15, bottom: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0), // 设置圆角半径为10.0
          color: Colors.white, // 设置背景颜色为灰色
        ),
        padding: const EdgeInsets.only(
          top: 16,
          left: 12,
          right: 12,
          bottom: 16,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText('订单号：${data.orderNumber}', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    CommonUtils.getSimpleText(
                      '${(type == '3') ? '订单金额' : '退款金额'}：${!refund ? data.price : data.refundMoney}元',
                      12,
                      Colours.red,
                    ),
                  ],
                )),
                LoadImage(
                  (data.isSelected) ? 'icon_check' : 'icon_uncheck',
                  width: 20,
                  height: 20,
                )
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            //保单号
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("保单号", 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.policyNo, 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            //在保人员
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("员工", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("${data.insuredUserName}(${data.insuredUserStatusName})", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText((type == '3' ? '下单时间' : '退款时间'), 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText((type == '3') ? "${data.createTime}" : '${data.policyRefundTime}', 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
