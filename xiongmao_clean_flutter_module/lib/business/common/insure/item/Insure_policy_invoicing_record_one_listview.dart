import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/insure_policy_invoicing_record_one_entity.dart';

class InsurePolicyInvoicingRecordOneListView extends StatelessWidget {
  InsurePolicyInvoicingRecordOneOrderList data;

  String? type;

  InsurePolicyInvoicingRecordOneListView({super.key, required this.data, required this.type});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      splashColor: Colors.transparent, // 取消水波纹颜色
      highlightColor: Colors.transparent, // 取消高亮背景色
      onTap: () {
        // BoostNavigator.instance.push('insurePolicyInvoicingRecordOnePage', arguments: {'uuid': '${data.uuid}'});
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0), // 设置圆角半径为10.0
          color: Colors.white, // 设置背景颜色为灰色
        ),
        margin: const EdgeInsets.only(bottom: 14),
        padding: const EdgeInsets.only(
          left: 14,
          right: 14,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Gaps.vGap12,
            //订单号
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("订单号", 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.orderNumber, 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText((type == '1') ? "订单金额" : "退款金额", 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText('${data.amount}元', 14, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("保单号", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("${data.policyNo}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("员工", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("${data.insuredUserName}(${data.insuredUserStatusName})", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap12,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText((type == '1') ? "下单时间" : "退款时间", 14, Colours.base_primary_text_title),
                CommonUtils.getSimpleText("${data.dateTime}", 14, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap12,
          ],
        ),
      ),
    );
  }
}
