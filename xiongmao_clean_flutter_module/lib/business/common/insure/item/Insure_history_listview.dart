import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../bean/Insure_history_list_entity.dart';
import '../bean/custom_Insure_history_entity.dart';

class InsureHistoryListView extends StatelessWidget {
  InsureHistoryListList data;

  InsureHistoryListView({required this.data});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        //在这里跳转详情
        BoostNavigator.instance.push('insureHistoryOnePage', arguments: {"order_number": "${data.orderNumber}"});
      },
      child: Container(
        margin: const EdgeInsets.only(right: 15, left: 15, bottom: 15),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10.0), // 设置圆角半径为10.0
          color: Colors.white, // 设置背景颜色为灰色
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 16,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.insuranceCompanyName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                Expanded(
                  child: CommonUtils.getSimpleText('${data.payTime}购买', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold, textAlign: TextAlign.right),
                ),
              ],
            ),
            Gaps.vGap8,
            Gaps.line,
            Gaps.vGap8,
            //订单号
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("订单号", 15, Colours.base_primary_text_caption),
                Expanded(child: CommonUtils.getSimpleText(data.orderNumber, 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap6,
            //保单号
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("保单号", 15, Colours.base_primary_text_caption),
                Expanded(child: CommonUtils.getSimpleText(data.policyNo, 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap6,
            //购买产品
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("购买产品", 15, Colours.base_primary_text_caption),
                Gaps.hGap4,
                Expanded(
                  child: CommonUtils.getSimpleText(data.productName, 15, Colours.base_primary_text_title, textAlign: TextAlign.right),
                ),
              ],
            ),
            Gaps.vGap6,
            //保险期
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("保险期", 15, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.startTime} - ${data.endTime}", 15, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap6,
            //在保人员
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("员工", 15, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.insuredUserName}(${data.insuredUserStatusName})", 15, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap6,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("保单状态", 15, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.orderStatusName}", 15, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap6,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("退款状态", 15, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.refundStatusName}", 15, Colours.base_primary_text_title, textAlign: TextAlign.right),
              ],
            ),
            Gaps.vGap8,
            Gaps.line,
            Gaps.vGap16,
            //底部按钮操作
            Row(
              children: [
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      // 处理点击事件
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_ele_policy", "url": "${data.elecPolicyUrl}", 'user_name': '${data.insuredUserName}', 'order_number': '${data.orderNumber}'});
                    },
                    child: CommonUtils.getSimpleText("电子保单", 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                ),
                Gaps.vLine,
                Gaps.hGap15,
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111938749780", "close": true, "share": true});
                    },
                    child: CommonUtils.getSimpleText("理赔流程", 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                ),
                Gaps.hGap15,
                Gaps.vLine,
                Expanded(
                  child: GestureDetector(
                    onTap: () {
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_insure_claims_process", "order_number": "${data.orderNumber}", "guarantee": "${data.policyNo}"});
                    },
                    child: CommonUtils.getSimpleText("申请理赔", 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}
