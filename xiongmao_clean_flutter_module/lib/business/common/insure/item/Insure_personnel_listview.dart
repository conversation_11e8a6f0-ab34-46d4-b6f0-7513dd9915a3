import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../bean/custom_Insure_entity.dart';
import '../bean/custom_Insure_personnel_entity.dart';

class InsurePersonnelListView extends StatefulWidget {
  @override
  _InsurePersonnelListViewState createState() => _InsurePersonnelListViewState();
}

class _InsurePersonnelListViewState extends State<InsurePersonnelListView> {
  int selectedItemIndex = 0; // 记录当前选中的item索引

  final List<CustomInsurePersonnelEntity> dataList = [
    CustomInsurePersonnelEntity(name: "王跃正", desc: '女 / 主管 / 60岁 / 0.3年', isInsure: 1, select: false),
    CustomInsurePersonnelEntity(name: "宫崎骏", desc: '男 / 主管 / 58岁 / 1.3年', isInsure: 1, select: false),
    CustomInsurePersonnelEntity(name: "王权", desc: '男 / 主管 / 50岁 / 1年', isInsure: 2, select: false),
  ];

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      physics: NeverScrollableScrollPhysics(),
      // 禁止ListView滚动
      padding: EdgeInsets.zero,
      shrinkWrap: true,
      itemCount: dataList.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              dataList[index].select = !dataList[index].select;
            });
          },
          child: Container(
            color: Colors.white, // 根据选中状态设置背景色
            padding: EdgeInsets.all(8.0),
            child: Row(
              // crossAxisAlignment: CrossAxisAlignment.start, // 头像在顶部
              children: [
                Container(
                  margin: EdgeInsets.only(right: 8.0, top: 4.0),
                  child: CustomAvatarView(
                    name: dataList[index].name,
                    size: 40,
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        dataList[index].name,
                        style: TextStyle(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        dataList[index].desc,
                        style: TextStyle(color: Colors.deepOrange, fontSize: 10.0),
                      ),
                    ],
                  ),
                ),
                //选中状态
                Container(
                  margin: EdgeInsets.only(left: 8.0),
                  child: Icon(
                    Icons.check_circle,
                    color: dataList[index].select ? Colors.green : Colors.grey,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
