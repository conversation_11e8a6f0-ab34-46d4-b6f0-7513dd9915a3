import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/insure_policy_invoicing_record_entity.dart';
import '../bean/insure_policy_invoicing_record_one_entity.dart';

/// 保险开票信息
class InsurePolicyInvoicingRecordController extends GetxController {
  ///保险记录list
  var list = <InsurePolicyInvoicingRecordList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<InsurePolicyInvoicingRecordList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<InsurePolicyInvoicingRecordList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///详情
  var data = InsurePolicyInvoicingRecordOneEntity().obs;

  void initData(InsurePolicyInvoicingRecordOneEntity value) {
    data.value = value;
  }


}
