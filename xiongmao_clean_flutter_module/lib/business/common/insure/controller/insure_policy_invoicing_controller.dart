import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../bean/Insure_history_list_entity.dart';

/// 保险开票信息
class InsurePolicyInvoicingController extends GetxController {
  var contractCompanyName = "全部".obs;
  var contractCompanyUuid = "".obs;

  var searchDate = "选择日期范围".obs;

  ///新购的日期
  DateTime startDateTime = DateTime.now();
  DateTime endDateTime = DateTime.now();

  ///退款的日期
  DateTime refundStartDateTime = DateTime.now();
  DateTime refundEndDateTime = DateTime.now();

  //保险列表的状态 状态 0全部 1已过期 2近7天过期 3开票新购 4开票退款
  var type = "3".obs;

  ///购买开始日期
  var payStartDate = "".obs;

  ///购买结束日期
  var payEndDate = "".obs;

  ///退保开始日期
  var policyRefundStartDate = "".obs;

  ///退保结束日期
  var policyRefundEndDate = "".obs;

  ///全选的状态
  var selectAll = "全选".obs;

  var data = InsureHistoryListEntity().obs;

  ///保险记录list
  var list = <InsureHistoryListList>[].obs;

  var totalNumber = 0.obs;

  void initMyList(List<InsureHistoryListList> value, int total) {
    totalNumber.value = total;
    list.value = value.toList();
    selectAll.value = '全选';
  }

  void updateMyList(List<InsureHistoryListList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  void initData(InsureHistoryListEntity value) {
    data.value = value;
  }

  ///统计计数的信息
  var count = 0.obs;

  void findSelectedCount() {
    count.value = list.where((item) => item.isSelected).length;
  }

  /// 将列表中所有项的 isSelected 设置为 true
  void selectAllItems(bool isSelected) {
    for (var item in list) {
      item.isSelected = isSelected;
    }
    findSelectedCount();
  }

  /// 获取选中项的 id，并返回逗号分隔的字符串
  String getSelectedIds() {
    // 过滤选中的项，并提取它们的 id
    List<String> selectedIds = list
        .where((item) => item.isSelected) // 筛选选中的项
        .map((item) => item.orderNumber.toString()) // 提取 id 并转换为字符串
        .toList();

    // 将 id 列表拼接成逗号分隔的字符串
    return selectedIds.join(",");
  }
}
