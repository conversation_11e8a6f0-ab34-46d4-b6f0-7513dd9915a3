import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../bean/Insure_history_list_entity.dart';


/**
 * 保险的列表、订单详情
 */
class InsureSchemeHistoryPageController extends GetxController {
  //保险列表的状态
  var type = "0".obs;

  //搜索查询的名字
  var search_user_name = "".obs;

  //保险列表的sort 类型
  var sort_type = "".obs;

  var sort_title = "购买时间最近的排前".obs;

  var data = InsureHistoryListEntity().obs;

  var totalNumber = "0".obs;

  ///保险记录list
  var list = <InsureHistoryListList>[].obs;

  void initMyList(List<InsureHistoryListList> value, String all) {
    totalNumber.value = all;
    list.value = value.toList();
  }

  void updateMyList(List<InsureHistoryListList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  void initData(InsureHistoryListEntity value) {
    data.value = value;
  }
}
