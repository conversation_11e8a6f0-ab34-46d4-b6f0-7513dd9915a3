import 'dart:ffi';

import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../bean/insure_scheme_data_entity.dart';


/**
 * 参保方案
 */
class InsureSchemePageController extends GetxController {
  var insurance_uuid = "".obs;
  var insurance_sku = "".obs;

  var checked = false.obs;

  var selectItem = -1.obs;

  ///方案list
  var list = <InsureSchemeDataList>[].obs;

  void initMyList(List<InsureSchemeDataList> value) {
    list.value = value.toList();
  }
}
