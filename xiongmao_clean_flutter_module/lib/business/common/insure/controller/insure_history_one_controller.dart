import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';


import '../bean/Insure_history_one_new_entity.dart';
import '../bean/insure_one_record_entity.dart';

/**
 * 保险订单详情、保单
 */
class InsureHistoryOnePageController extends GetxController {
  //订单编号
  var order_number = "".obs;

  var data = InsureHistoryOneNewEntity().obs;

  ///保险记录list
  var list = <InsureOneRecordList>[].obs;

  void initMyList(List<InsureOneRecordList> value) {
    list.value = value.toList();
  }

  void updateMyList(List<InsureOneRecordList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  void initData(InsureHistoryOneNewEntity value) {
    data.value = value;
  }
}
