import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/Insure_history_list_entity.dart';
import '../bean/insure_add_entity.dart';
import '../bean/insure_recently_one_entity.dart';

/// 保险开票信息
class InsurePolicyInvoicingAddController extends GetxController {
  var data = InsureAddEntity().obs;

  ///开票类型
  var policyTypeList = [
    BaseChooseString("普票"),
    BaseChooseString("专票"),
  ].obs;

  ///开票合并
  var meagerTypeList = [
    BaseChooseString("是"),
    BaseChooseString("否"),
  ].obs;

  ///添加开票信息
  var contractCompanyUuid = "".obs; //合同公司的id
  var email = "".obs; //邮箱
  var policyType = "1".obs; //开票类型
  var policyMeagerType = "1".obs; //开票合并
  var invoiceTitle = "".obs; //发票抬头
  var invoiceNumber = "".obs; //纳税人编号
  var address = "".obs; //发票抬头
  var mobile = "".obs; //电话
  var bank = "".obs; //电话
  var bankNo = "".obs; //银行卡号

  ///获取最近一次开票的信息
  void dataRecentlyOne(InsureRecentlyOneEntity data) {
    contractCompanyUuid.value = data.contractCompanyUuid ?? "";
    email.value = data.email ?? "";
    invoiceTitle.value = data.invoiceTitle ?? "";
    invoiceNumber.value = data.identificationNumber ?? "";
    address.value = data.address ?? "";
    mobile.value = data.mobile ?? "";
    bank.value = data.bank ?? "";
    bankNo.value = data.bankNo ?? "";
    policyType.value = data.invoiceType ?? "";
    policyMeagerType.value = data.isMerge ?? "";
  }
}
