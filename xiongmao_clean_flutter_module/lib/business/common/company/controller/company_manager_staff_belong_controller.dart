import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/department_company_entity.dart';

class CompanyManagerStaffBelongController extends GetxController {
  var uuid = ''.obs;

  var list = <DepartmentCompanyList>[].obs;

  void initMyList(List<DepartmentCompanyList> value) {
    list.value = checkSelected(value).toList();
  }

  void updateMyList(List<DepartmentCompanyList> value) {
    list.value.addAll(checkSelected(value));
    list.value = list.value.toList();
  }

  List<DepartmentCompanyList> checkSelected(List<DepartmentCompanyList> value) {
    for (var element2 in value) {
      if (uuid.value == element2.uuid) {
        element2.isSelected = true;
      }
    }
    return value;
  }
}
