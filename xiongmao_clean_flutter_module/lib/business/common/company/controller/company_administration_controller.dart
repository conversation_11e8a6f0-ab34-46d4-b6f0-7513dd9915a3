import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/department_company_entity.dart';

class CompanyAdministrationController extends GetxController {
  //默认的数据
  var weekList = [
    BaseChooseString("公司"),
    BaseChooseString("事业部"),
    BaseChooseString("部门"),
    BaseChooseString("区域"),
    BaseChooseString("门店"),
  ].obs;

  var projectUuid = "".obs;

  var saveDepartmentUuid = "".obs;

  ///拿到根部门
  var data = DepartmentCompanyEntity().obs;

  ///获取子部门
  var list = <DepartmentCompanyList>[].obs;

  void initMyList(List<DepartmentCompanyList> value) {
    list.value = value.toList();
  }

  ///新增部门
  var departmentName = "".obs;

  ///类型 1公司 2事业部 3部门 4区域 5门店
  var departmentType = "".obs;

  ///上级组织UUID
  var parentUuid = "".obs;

  ///上级组织Name
  var parentName = "".obs;

  ///合同公司
  var contractCompanyUuid = "".obs;
  var contractCompanyName = "".obs;
}
