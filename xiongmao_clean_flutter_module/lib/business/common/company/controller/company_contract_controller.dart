import 'package:bruno/bruno.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../contract/bean/contract_main_info_entity.dart';
import '../bean/department_company_contract_entity.dart';
import '../bean/department_company_entity.dart';

class CompanyContractController extends GetxController {
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  var totalNumber = "0".obs;

  var list = <DepartmentCompanyContractList>[].obs;

  void initMyList(int total, List<DepartmentCompanyContractList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<DepartmentCompanyContractList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  var uuid = ''.obs;

  ///公司名称
  var companyName = ''.obs;
  var companyNo = ''.obs;
  var companyAddress = ''.obs;
  var companyMobile = ''.obs;
  var companyBankName = ''.obs;
  var companyBankNo = ''.obs;
  var companyPic = ''.obs;

  ///编辑的时候用哇
  void fillData(DepartmentCompanyContractList data) {
    companyName.value = data.companyName ?? '';
    companyNo.value = data.creditCode ?? '';
    companyAddress.value = data.address ?? '';
    companyMobile.value = data.mobile ?? '';
    companyBankName.value = data.bank ?? '';
    companyBankNo.value = data.bankNo ?? '';
    companyPic.value = data.businessLicense ?? '';
  }

  ///电子签设置 *********** 电子签设置 电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置
  var infoEntity = ContractMainInfoEntity().obs;

  void updateContractMainInfoEntity(ContractMainInfoEntity data) {
    infoEntity.value = data;
  }

  ///电子签设置 *********** 电子签设置 电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置
}
