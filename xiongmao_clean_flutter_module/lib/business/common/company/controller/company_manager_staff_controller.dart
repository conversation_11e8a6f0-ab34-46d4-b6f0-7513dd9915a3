import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/department_company_entity.dart';

class CompanyManagerStaffController extends GetxController {
  var uuid = "".obs;

  var data = DepartmentCompanyEntity().obs;

  var user_uuid_list = <String>[].obs;

  var list = <ProjectArchivesList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<ProjectArchivesList> value) {
    totalNumber.value = total.toString();
    list.value = checkSelected(value).toList();
  }

  void updateMyList(List<ProjectArchivesList> value) {
    list.value.addAll(checkSelected(value));
    list.value = list.value.toList();
  }

  List<ProjectArchivesList> checkSelected(List<ProjectArchivesList> value) {
    if (data.value != null && data.value.responseUserList != null && data.value.responseUserList!.isNotEmpty) {
      for (var element in data.value.responseUserList!) {
        for (var element2 in value) {
          if (element.userUuid == element2.uuid) {
            element2.isSelected = true;

            ///一进来，选的谁
            user_uuid_list.add(element2.uuid!);
          }
        }
      }
    }
    return value;
  }

  ///获取所有的选中的数据 uuid
  void selectedTotal() {
    user_uuid_list.clear();
    for (var element in list) {
      if (element.isSelected) {
        user_uuid_list.add(element.uuid!);
      }
    }
    list.refresh();
  }
}
