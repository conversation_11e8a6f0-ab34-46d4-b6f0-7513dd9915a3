import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_manager_staff_belong_controller.dart';
import '../controller/company_manager_staff_controller.dart';
import '../item/administration_choice_belong_list_Item.dart';
import '../item/company_manager_staff_listview.dart';
import '../iview/administration_iview.dart';
import '../persenter/company_administration_persenter.dart';
import '../persenter/company_manager_staff_belong_persenter.dart';
import '../persenter/company_manager_staff_persenter.dart';

/// 编辑总部成员、选择所属部门
class CompanyManagerStaffBelongPage extends StatefulWidget {
  String? uuid;

  CompanyManagerStaffBelongPage({
    super.key,
    this.uuid,
  });

  @override
  _AddCompanyAdministrationPage createState() => _AddCompanyAdministrationPage();
}

class _AddCompanyAdministrationPage extends State<CompanyManagerStaffBelongPage> with BasePageMixin<CompanyManagerStaffBelongPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyManagerStaffBelongPage> implements AdministrationIView {
  CompanyManagerStaffBelongPresenter? _presenter;

  final CompanyManagerStaffBelongController _controller = CompanyManagerStaffBelongController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.white,
          appBar: MyAppBar(
              centerTitle: '选择所属部门',
              onBack: () {
                BoostNavigator.instance.pop();
              }),
          body: MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: false,
            itemBuilder: (_, index) {
              return AdministrationChoiceBelongListItem(
                data: _controller.list[index],
                gotoLevel: () {
                  ///跳转到其他界面选择上级部门
                  List<String> parentUuids = [widget.uuid ?? ''];
                  BoostNavigator.instance.push('companyChoiceAdministrationPage', arguments: {"choice_uuids": parentUuids, 'uuid': _controller.list[index].uuid}).then((value) {
                    if (value != null) {
                      DepartmentCompanyList? departmentCompany = value as DepartmentCompanyList;
                      if (departmentCompany != null) {
                        BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                          "method": "goto_add_company_staff",
                          "uuid": departmentCompany.uuid ?? "",
                          "department_name": departmentCompany.departmentName ?? "",
                        });
                      }
                      BoostNavigator.instance.pop(departmentCompany);
                    }
                  });
                },
                check: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                    "method": "goto_add_company_staff",
                    "uuid": _controller.list[index].uuid ?? "",
                    "department_name": _controller.list[index].departmentName ?? "",
                  });
                  BoostNavigator.instance.pop(_controller.list[index]);
                  // BoostNavigator.instance.pop({
                  //   "uuid": _controller.list[index].uuid ?? "",
                  //   "department_name": _controller.list[index].departmentName ?? "",
                  // });
                },
              );
            },
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyManagerStaffBelongPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {}

  @override
  editOne(DepartmentCompanyEntity data) {}
}
