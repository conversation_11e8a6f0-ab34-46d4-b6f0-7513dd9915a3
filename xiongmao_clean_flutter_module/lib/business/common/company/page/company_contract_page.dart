import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/department_company_contract_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_contract_controller.dart';
import '../item/administration_list_Item.dart';
import '../item/company_contract_list_Item.dart';
import '../iview/administration_iview.dart';
import '../iview/company_contract_iview.dart';
import '../persenter/company_administration_persenter.dart';
import '../persenter/company_contract_persenter.dart';

/// 合同管理
class CompanyContractPage extends StatefulWidget {
  CompanyContractPage({super.key});

  @override
  _CompanyAdministrationPage createState() => _CompanyAdministrationPage();
}

class _CompanyAdministrationPage extends State<CompanyContractPage> with BasePageMixin<CompanyContractPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyContractPage> implements CompanyContractIView {
  CompanyContractPresenter? _presenter;

  final CompanyContractController _controller = CompanyContractController();

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();
    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh('');
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore('');
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '合同公司',
          ),
          body: WillPopScope(
              child: MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return CompanyContactListItem(
                    data: _controller.list[index],
                    delete: () {
                      DialogManager.showConfirmDialog(
                        context: context,
                        title: '提示',
                        cancel: '取消',
                        confirm: '确定',
                        message: '是否删除该合同公司？',
                        onConfirm: () {
                          _presenter?.deleteDepartmentContract(_controller.list[index].uuid);
                        },
                        onCancel: () {},
                      );
                    },
                    action: (int type) {
                      if (type == 1) {
                        ///继续认证 直接获取法大大的链接
                        _presenter?.getContractFddUrl(_controller.list[index].customerId ?? '');
                      } else {
                        //状态 1草稿 2审核中 3审核驳回 4审核通过 5电子签认证中 6待授权企业自动签署 7已电子签认证
                        if (_controller.list[index].auditStatus == '2') {
                          ///这里是撤回
                          DialogManager.showConfirmDialog(
                            context: context,
                            title: '提示',
                            cancel: '取消',
                            confirm: '确定',
                            message: '是否撤回该申请',
                            onConfirm: () {
                              _presenter?.cancelDepartmentContract(_controller.list[index].uuid);
                            },
                            onCancel: () {},
                          );
                        } else if (_controller.list[index].auditStatus == '4') {
                          ///去电子签认证
                          _presenter?.getContractRegisterUser(_controller.list[index].uuid ?? '');
                        } else if (_controller.list[index].auditStatus == '5') {
                          ///这里是取消认证
                          DialogManager.showConfirmDialog(
                            context: context,
                            title: '提示',
                            cancel: '取消',
                            confirm: '确定',
                            message: '是否取消认证',
                            onConfirm: () {
                              _presenter?.cancelDepartmentContractAuth(_controller.list[index].uuid);
                            },
                            onCancel: () {},
                          );
                        } else if (_controller.list[index].auditStatus == '6') {
                          ///待授权企业自动签署
                          BrnDialogManager.showConfirmDialog(context, title: "提示", message: '开启自动签署后，发起合同签约时，无需公司法人短信确认即可自动完成公司印章签署，是否确认开启？', cancel: '取消', confirm: '开启授权', barrierDismissible: false, onConfirm: () {
                            Get.back();
                            //请求自动签署的接口 如果是关闭的情况，才能请求接口哦
                            _presenter?.getAutoSignUrl(_controller.list[index].customerId);
                          }, onCancel: () {
                            Get.back();
                          });
                        } else if (_controller.list[index].auditStatus == '7') {
                          ///已电子签认证 跳转设置印章
                          _presenter?.getContractMainInfo(_controller.list[index].uuid ?? '');
                        } else {
                          ///其他都是编辑
                          BoostNavigator.instance.push('addCompanyContractPage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                        }
                      }
                    },
                  );
                },
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
          bottomNavigationBar: Container(
            color: Colors.white,
            height: 54,
            child: Column(
              children: [
                Gaps.line,
                Padding(
                  padding: const EdgeInsets.only(left: 16, right: 16, top: 6),
                  child: BrnBigMainButton(
                    themeData: BrnButtonConfig(
                      bigButtonHeight: 40,
                      bigButtonFontSize: 16,
                      bigButtonRadius: 4,
                    ),
                    title: '+ 添加合同公司',
                    onTap: () {
                      BoostNavigator.instance.push('addCompanyContractPage', arguments: {
                        'uuid': '',
                      }).then((value) => _onRefresh());
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyContractPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    _onRefresh();
  }

  @override
  editOne(DepartmentCompanyEntity data) {}

  @override
  updateContractMainInfoEntity(ContractMainInfoEntity data) {
    _controller.updateContractMainInfoEntity(data);
    var cert = data.cert;
    BoostNavigator.instance.push("contractEditSealPage", arguments: {"companyName": cert?.companyName ?? "", "organization": data.sealNo ?? "", "sealCdnUrl": data.sealCdnUrl, "customer_id": data.customerId}).then((value) {
      _onRefresh();
    });
  }

  @override
  gotoAutoAuth(String url) {
    BoostNavigator.instance.push("contractAutoSignPage", arguments: {"url": url}).then((value) => _onRefresh());
  }

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }
}
