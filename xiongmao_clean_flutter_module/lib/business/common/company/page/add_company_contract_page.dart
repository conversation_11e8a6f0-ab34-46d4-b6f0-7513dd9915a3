import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_image_grid_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/Image_compress_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_contract_controller.dart';
import '../iview/administration_iview.dart';
import '../iview/company_contract_iview.dart';
import '../persenter/company_administration_persenter.dart';
import '../persenter/company_contract_persenter.dart';

/// 行政组织管理 增加部门
class AddCompanyContractPage extends StatefulWidget {
  String? uuid = "";

  AddCompanyContractPage({
    super.key,
    this.uuid,
  });

  @override
  _AddCompanyContractPage createState() => _AddCompanyContractPage();
}

class _AddCompanyContractPage extends State<AddCompanyContractPage> with BasePageMixin<AddCompanyContractPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AddCompanyContractPage> implements CompanyContractIView {
  CompanyContractPresenter? _presenter;

  final CompanyContractController _controller = CompanyContractController();

  late TextEditingController _companyNameController;
  late TextEditingController _companyNoController;
  late TextEditingController _companyAddressController;
  late TextEditingController _companyMobileController;
  late TextEditingController _companyBankNameController;
  late TextEditingController _companyBankNoController;

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.uuid)) {
      _controller.uuid.value = widget.uuid ?? '';
      _presenter?.getDepartmentContractOne(widget.uuid ?? '');
    }

    _companyNameController = TextEditingController(text: _controller.companyName.value);
    _companyNoController = TextEditingController(text: _controller.companyNo.value);
    _companyAddressController = TextEditingController(text: _controller.companyAddress.value);
    _companyMobileController = TextEditingController(text: _controller.companyMobile.value);
    _companyBankNameController = TextEditingController(text: _controller.companyBankName.value);
    _companyBankNoController = TextEditingController(text: _controller.companyBankNo.value);

    // 调用方法来设置监听
    _setupTextFieldListener(_controller.companyName, _companyNameController);
    _setupTextFieldListener(_controller.companyNo, _companyNoController);
    _setupTextFieldListener(_controller.companyAddress, _companyAddressController);
    _setupTextFieldListener(_controller.companyMobile, _companyMobileController);
    _setupTextFieldListener(_controller.companyBankName, _companyBankNameController);
    _setupTextFieldListener(_controller.companyBankNo, _companyBankNoController);
  }

  void _setupTextFieldListener(Rx<String> notifier, TextEditingController controller) {
    ever(notifier, (value) {
      if (controller.text != value) {
        controller.text = value.toString();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
          centerTitle: (TextUtil.isEmpty(widget.uuid)) ? "新建合同公司" : '编辑合同公司',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      body: SingleChildScrollView(
        child: Column(
          children: [
            BrnTextInputFormItem(
              isRequire: true,
              title: "公司名称",
              hint: "请输入",
              controller: _companyNameController,
              onChanged: (newValue) {
                _controller.companyName.value = newValue;
              },
            ),
            Gaps.line,
            BrnTextInputFormItem(
              isRequire: true,
              title: "统一社会信用代码",
              hint: "请输入",
              maxCharCount: 50,
              controller: _companyNoController,
              onChanged: (newValue) {
                _controller.companyNo.value = newValue;
              },
            ),
            Gaps.line,
            BrnTextInputFormItem(
              title: "地址",
              hint: "请输入",
              maxCharCount: 50,
              controller: _companyAddressController,
              onChanged: (newValue) {
                _controller.companyAddress.value = newValue;
              },
            ),
            Gaps.line,
            BrnTextInputFormItem(
              title: "电话",
              hint: "请输入",
              maxCharCount: 50,
              controller: _companyMobileController,
              onChanged: (newValue) {
                _controller.companyMobile.value = newValue;
              },
            ),
            Gaps.line,
            BrnTextInputFormItem(
              title: "开户行",
              hint: "请输入",
              maxCharCount: 50,
              controller: _companyBankNameController,
              onChanged: (newValue) {
                _controller.companyBankName.value = newValue;
              },
            ),
            Gaps.line,
            BrnTextInputFormItem(
              title: "银行账号",
              hint: "请输入",
              maxCharCount: 50,
              controller: _companyBankNoController,
              onChanged: (newValue) {
                _controller.companyBankNo.value = newValue;
              },
            ),
            Gaps.line,
            Obx(() => Container(
                  padding: EdgeInsets.only(bottom: 10, top: 10, right: 20),
                  color: Colors.white,
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Expanded(
                        child: BrnBaseTitle(
                          isRequire: true,
                          title: "营业执照",
                        ),
                      ),
                      InkWell(
                        child: Container(
                          width: 100,
                          height: 100,
                          decoration: BoxDecoration(
                            color: Colors.white,
                            borderRadius: BorderRadius.circular(8.0),
                          ),
                          child: (TextUtil.isEmpty(_controller.companyPic.value))
                              ? const LoadAssetImage(
                                  'common/icon_add_image',
                                  width: 100,
                                  height: 100,
                                )
                              : Stack(
                                  children: [
                                    Positioned.fill(
                                      child: InkWell(
                                        child: LoadImage(
                                          _controller.companyPic.value,
                                          width: 100,
                                          height: 100,
                                          radius: 6,
                                        ),
                                        onTap: () {
                                          BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_open_preview", "url": _controller.companyPic.value, "type": "1"});
                                        },
                                      ),
                                    ),
                                    Positioned(
                                      top: 4,
                                      right: 4,
                                      child: GestureDetector(
                                        onTap: () {
                                          _controller.companyPic.value = '';
                                        },
                                        child: const LoadAssetImage(
                                          'icon_base_round_close',
                                          width: 20,
                                          height: 20,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) {
                                return BrnCommonActionSheet(
                                  actions: _controller.actions,
                                  clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                    selectImage(index);
                                  },
                                );
                              });
                        },
                      ),
                    ],
                  ),
                )),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 54,
        child: Column(
          children: [
            Gaps.line,
            // 按钮
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 6),
              child: Row(
                children: [
                  InkWell(
                    child: Container(
                      padding: const EdgeInsets.only(left: 16, right: 16, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('存草稿', 16, Colours.base_primary_text_title),
                    ),
                    onTap: () {
                      _presenter?.saveDepartmentContact(true);
                    },
                  ),
                  Gaps.hGap10,
                  Expanded(
                      child: BrnBigMainButton(
                    themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                    title: '确定',
                    onTap: () {
                      if (TextUtil.isEmpty(_controller.companyName.value)) {
                        BrnToast.show('请输入公司名称', context);
                        return;
                      }
                      if (TextUtil.isEmpty(_controller.companyNo.value)) {
                        BrnToast.show('请输入统一社会信用代码', context);
                        return;
                      }
                      if (TextUtil.isEmpty(_controller.companyPic.value)) {
                        BrnToast.show('请上传营业执照', context);
                        return;
                      }
                      _presenter?.saveDepartmentContact(false);
                    },
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  ///打开相机拍照
  selectImage(int imageSource) async {
    List<Media> pickerPaths = [];

    if (imageSource == 0) {
      // 拍照模式：只允许选择一张图片
      Media? photo = await ImagePickers.openCamera();
      if (photo != null) {
        pickerPaths.add(photo);
      }
    } else {
      // 相册模式：允许多选图片
      pickerPaths = await ImagePickers.pickerPaths(
        galleryMode: GalleryMode.image,
        selectCount: 1, // 剩余可选图片数量
        showGif: false,
        compressSize: 100, // 内置压缩功能，单位为 KB
      );
    }

    if (pickerPaths.isEmpty) {
      return;
    }
    print('拿到图片了${pickerPaths[0].path}');

    var imageCompressAndGetFile = await ImageCompressUtils.imageCompressAndGetFile(pickerPaths[0].path ?? '');

    //拿到图进行上传的操作
    BrnToast.show("上传中，请稍等", context);
    QiNiuUtils(imageCompressAndGetFile?.path, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, successCallback: (keyUrl, hashUrl) {
      print("七牛上传成功--$keyUrl");
      BrnToast.show("上传成功", context);
      _controller.companyPic.value = keyUrl ?? '';
    }, errorCallback: (error) {
      print("七牛上传失败--$error");
      BrnToast.show("上传失败", context);
    }).upload();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyContractPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    _companyNameController.dispose();
    _companyNoController.dispose();
    _companyAddressController.dispose();
    _companyMobileController.dispose();
    _companyBankNameController.dispose();
    _companyBankNoController.dispose();
    super.dispose();
  }

  @override
  updateContractMainInfoEntity(ContractMainInfoEntity data) {}

  @override
  gotoAutoAuth(String url) {}
}
