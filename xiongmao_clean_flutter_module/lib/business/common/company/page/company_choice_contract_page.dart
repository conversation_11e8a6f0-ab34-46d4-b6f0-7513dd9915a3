import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../bean/department_company_contract_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_contract_controller.dart';
import '../item/administration_list_Item.dart';
import '../item/company_contract_choice_list_Item.dart';
import '../item/company_contract_list_Item.dart';
import '../iview/administration_iview.dart';
import '../iview/company_contract_iview.dart';
import '../persenter/company_administration_persenter.dart';
import '../persenter/company_contract_persenter.dart';

/// 关联合同公司
class CompanyChoiceContractPage extends StatefulWidget {
  String? uuid;
  String? subTitle;
  bool? isSelected;
  bool? showBottomBut;

  CompanyChoiceContractPage({super.key, required this.uuid, this.subTitle, required this.isSelected, required this.showBottomBut});

  @override
  _CompanyChoiceContractPage createState() => _CompanyChoiceContractPage();
}

class _CompanyChoiceContractPage extends State<CompanyChoiceContractPage> with BasePageMixin<CompanyChoiceContractPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyChoiceContractPage> implements CompanyContractIView {
  CompanyContractPresenter? _presenter;

  final CompanyContractController _controller = CompanyContractController();

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.getDepartmentListManager('4');
  }

  Future<dynamic> _loadMore() async {
    _presenter?.getDepartmentListManager('4');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (widget.isSelected == true) ? '选择合同公司' : '关联合同公司',
        centerSubTitle: widget.subTitle,
        actionName: (widget.isSelected == true) ? "" : '不关联',
        onPressed: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: Obx(() => WillPopScope(
          child: MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 10),
            hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
            itemBuilder: (_, index) {
              return CompanyContactChoiceListItem(
                uuid: widget.uuid ?? '',
                data: _controller.list[index],
                onClick: () {
                  BoostNavigator.instance.pop(_controller.list[index]);
                },
              );
            },
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          })),
      bottomNavigationBar: Visibility(
        visible: widget.showBottomBut == true,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: Row(
            children: [
              Expanded(
                  child: InkWell(
                child: Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                  decoration: BoxDecoration(
                    color: Colours.base_primary,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: CommonUtils.getSimpleText('全部', 16, Colours.white, textAlign: TextAlign.center),
                ),
                onTap: () {
                  BoostNavigator.instance.pop();
                },
              )),
            ],
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyContractPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  editOne(DepartmentCompanyEntity data) {}

  @override
  updateContractMainInfoEntity(ContractMainInfoEntity data) {}

  @override
  gotoAutoAuth(String url) {
  }
}
