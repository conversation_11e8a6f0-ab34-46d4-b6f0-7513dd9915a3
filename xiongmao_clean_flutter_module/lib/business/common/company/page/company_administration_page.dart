import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/bean/department_company_entity.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/company_administration_controller.dart';
import '../item/administration_list_Item.dart';
import '../iview/administration_iview.dart';
import '../persenter/company_administration_persenter.dart';

/// 行政组织管理
class CompanyAdministrationPage extends StatefulWidget {
  String? uuid;
  bool isHideHeader;

  CompanyAdministrationPage({super.key, this.uuid, this.isHideHeader = false});

  @override
  _CompanyAdministrationPage createState() => _CompanyAdministrationPage();
}

class _CompanyAdministrationPage extends State<CompanyAdministrationPage> with BasePageMixin<CompanyAdministrationPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyAdministrationPage> implements AdministrationIView {
  CompanyAdministrationPresenter? _presenter;

  final CompanyAdministrationController _controller = CompanyAdministrationController();

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.uuid)) {
      _controller.projectUuid.value = widget.uuid ?? '';
    }
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.getDepartmentListManager();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.getDepartmentListManager();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.white,
          appBar: widget.isHideHeader
              ? null
              : MyAppBar(
                  centerTitle: '管理行政组织',
                  centerSubTitle: _controller.data.value.departmentName ?? '',
                  onBack: () {
                    BoostNavigator.instance.pop();
                  }),
          body: WillPopScope(
              child: Column(
                children: [
                  Container(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                    child: Row(
                      children: [
                        LoadImage(
                          'common/icon_common_company',
                          width: 40,
                          height: 40,
                        ),
                        Gaps.hGap10,
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                CommonUtils.getSimpleText(_controller.data.value.departmentName, 14, Colours.base_primary_text_title),
                                Gaps.hGap10,
                                Container(
                                  padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                                  decoration: BoxDecoration(
                                    color: Colours.base_primary_select,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                  child: CommonUtils.getSimpleText(_controller.data.value.departmentTypeName, 12, Colours.base_primary),
                                ),
                              ],
                            ),
                            Visibility(
                              child: CommonUtils.getSimpleText('合同公司：${_controller.data.value.contractCompanyName}', 12, Colours.base_primary_text_caption),
                              visible: (!TextUtil.isEmpty(_controller.data.value.contractCompanyName)),
                            ),
                            CommonUtils.getSimpleText('负责人：${getBuildName(_controller.data.value.responseUserList)}', 12, Colours.base_primary_text_caption),
                          ],
                        )
                      ],
                    ),
                  ),
                  Gaps.lineLeftMargin,
                  Expanded(
                    child: MyRefreshListView(
                      itemCount: _controller.list.length,
                      onRefresh: _onRefresh,
                      loadMore: _loadMore,
                      hasMore: false,
                      itemBuilder: (_, index) {
                        return AdministrationListItem(
                          data: _controller.list[index],
                          gotoLevel: () {
                            BoostNavigator.instance.push('companyAdministrationPage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
          bottomNavigationBar: Container(
            color: Colors.white,
            height: 54,
            child: Column(
              children: [
                Gaps.line,
                Row(
                  children: [
                    Gaps.hGap10,
                    Expanded(
                      child: InkWell(
                        child: Container(
                          height: 36,
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtils.getSimpleText('编辑', 16, Colours.white, textAlign: TextAlign.center),
                        ),
                        onTap: () {
                          _presenter?.getDepartmentOne(_controller.data.value.uuid ?? '');
                        },
                      ),
                    ),
                    Gaps.hGap10,
                    Expanded(
                      child: InkWell(
                        child: Container(
                          height: 36,
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtils.getSimpleText('添加下级', 16, Colours.white, textAlign: TextAlign.center),
                        ),
                        onTap: () {
                          BoostNavigator.instance.push('addCompanyAdministrationPage', arguments: {
                            'uuid': widget.uuid ?? _controller.data.value.uuid ?? '',
                            'root': TextUtil.isEmpty(widget.uuid) ? true : false,
                            'add': true,
                            'data': _controller.data.value,
                          }).then((value) => _onRefresh());
                        },
                      ),
                    ),
                    Gaps.hGap10,
                    Expanded(
                      child: InkWell(
                        child: Container(
                          height: 36,
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtils.getSimpleText('负责人', 16, Colours.white, textAlign: TextAlign.center),
                        ),
                        onTap: () {
                          BoostNavigator.instance.push('companyManagerStaffPage', arguments: {
                            'data': _controller.data.value,
                          }).then((value) => _onRefresh());
                        },
                      ),
                    ),
                    Gaps.hGap10,
                    Visibility(
                      visible: !TextUtil.isEmpty(widget.uuid),
                      child: Expanded(
                        child: InkWell(
                          child: Container(
                            height: 36,
                            margin: const EdgeInsets.only(top: 10),
                            decoration: BoxDecoration(
                              color: Colours.base_primary_un_select,
                              borderRadius: BorderRadius.circular(4.0),
                            ),
                            alignment: Alignment.center,
                            child: CommonUtils.getSimpleText('删除', 16, Colours.base_primary_text_title, textAlign: TextAlign.center),
                          ),
                          onTap: () {
                            DialogManager.showConfirmDialog(
                              context: context,
                              title: '提示',
                              cancel: '取消',
                              confirm: '确定',
                              message: '是否删除该部门？',
                              onConfirm: () {
                                _presenter?.deleteDepartment();
                              },
                              onCancel: () {},
                            );
                          },
                        ),
                      ),
                    ),
                    Visibility(
                      visible: !TextUtil.isEmpty(widget.uuid),
                      child: Gaps.hGap10,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyAdministrationPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('删除成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  editOne(DepartmentCompanyEntity data) {
    BoostNavigator.instance.push('addCompanyAdministrationPage', arguments: {
      'uuid': widget.uuid ?? _controller.data.value.uuid ?? '',
      'root': TextUtil.isEmpty(widget.uuid) ? true : false,
      'add': false,
      'data': data,
    }).then((value) => _onRefresh());
  }
}
