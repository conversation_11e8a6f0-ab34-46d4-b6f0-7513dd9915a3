import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_manager_staff_controller.dart';
import '../item/company_manager_staff_listview.dart';
import '../iview/administration_iview.dart';
import '../persenter/company_administration_persenter.dart';
import '../persenter/company_manager_staff_persenter.dart';

/// 选择部门负责人
class CompanyManagerStaffPage extends StatefulWidget {
  DepartmentCompanyEntity data;

  CompanyManagerStaffPage({
    super.key,
    required this.data,
  });

  @override
  _AddCompanyAdministrationPage createState() => _AddCompanyAdministrationPage();
}

class _AddCompanyAdministrationPage extends State<CompanyManagerStaffPage> with BasePageMixin<CompanyManagerStaffPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CompanyManagerStaffPage> implements AdministrationIView {
  CompanyManagerStaffPresenter? _presenter;

  final CompanyManagerStaffController _controller = CompanyManagerStaffController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.data.uuid ?? '';
    _controller.data.value = widget.data;
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
              centerTitle: '选择部门负责人',
              centerSubTitle: widget.data.departmentName ?? '',
              onBack: () {
                BoostNavigator.instance.pop();
              }),
          body: MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: int.parse(_controller.totalNumber.value) > _controller.list.value.length,
            itemBuilder: (_, index) {
              return CompanyManagerStaffView(
                data: _controller.list[index],
                onClick: () {
                  _controller.list[index].isSelected = !_controller.list[index].isSelected;
                  _controller.selectedTotal();
                },
              );
            },
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            height: 54,
            child: Column(
              children: [
                Gaps.line,
                Row(
                  children: [
                    Gaps.hGap10,
                    Expanded(
                      child: InkWell(
                        child: Container(
                          height: 36,
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: Colours.base_primary_un_select,
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtils.getSimpleText('无负责人', 16, Colours.base_primary_text_title, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
                        ),
                        onTap: () {
                          _presenter?.cleanDepartmentManagerStaff();
                        },
                      ),
                    ),
                    Gaps.hGap10,
                    Expanded(
                      flex: 2,
                      child: InkWell(
                        child: Container(
                          height: 36,
                          margin: const EdgeInsets.only(top: 10),
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          alignment: Alignment.center,
                          child: CommonUtils.getSimpleText('确定(已选${_controller.user_uuid_list.length}人)', 16, Colours.white, textAlign: TextAlign.center),
                        ),
                        onTap: () {
                          if (_controller.user_uuid_list.length <= 0) {
                            BrnToast.show("至少选择一个负责人", context);
                            return;
                          }
                          _presenter?.setDepartmentManager();
                        },
                      ),
                    ),
                    Visibility(
                      visible: !TextUtil.isEmpty(widget.data.uuid),
                      child: Gaps.hGap10,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyManagerStaffPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  editOne(DepartmentCompanyEntity data) {
  }
}
