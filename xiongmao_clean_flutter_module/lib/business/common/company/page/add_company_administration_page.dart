import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/department_company_contract_entity.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../iview/administration_iview.dart';
import '../persenter/company_administration_persenter.dart';

/// 行政组织管理 增加部门
class AddCompanyAdministrationPage extends StatefulWidget {
  String? uuid = "";
  bool isRoot;
  bool isAdd;
  DepartmentCompanyEntity data;

  AddCompanyAdministrationPage({
    super.key,
    required this.isRoot,
    required this.isAdd,
    required this.data,
    this.uuid,
  });

  @override
  _AddCompanyAdministrationPage createState() => _AddCompanyAdministrationPage();
}

class _AddCompanyAdministrationPage extends State<AddCompanyAdministrationPage> with BasePageMixin<AddCompanyAdministrationPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AddCompanyAdministrationPage> implements AdministrationIView {
  CompanyAdministrationPresenter? _presenter;

  final CompanyAdministrationController _controller = CompanyAdministrationController();

  @override
  void initState() {
    super.initState();

    ///只有是编辑的时候，才携带uuid
    if (!widget.isAdd) {
      ///如果是根目录，那么就取data里面的uuid 反之 取uuid
      _controller.saveDepartmentUuid.value = (widget.isRoot) ? widget.data.uuid ?? "" : widget.uuid ?? "";
      _controller.departmentType.value = widget.data.departmentType ?? '';
      _controller.departmentName.value = widget.data.departmentName ?? '';
    }

    ///如果是新增的情况下 那么就取下
    if (widget.isAdd) {
      //默认把当前上级显示出来
      _controller.parentUuid.value = widget.data.uuid ?? '';
      _controller.parentName.value = widget.data.departmentName ?? '';
    } else {
      _controller.parentUuid.value = widget.data.parentUuid ?? '';
      _controller.parentName.value = widget.data.parentDepartmentName ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
          centerTitle: widget.isAdd ? "新建部门" : '编辑部门',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      body: Column(
        children: [
          BrnTextInputFormItem(
            isRequire: true,
            title: "名称",
            hint: "请输入",
            controller: TextEditingController()..text = (widget.isAdd) ? "" : widget.data.departmentName ?? '',
            onChanged: (newValue) {
              _controller.departmentName.value = newValue;
            },
          ),
          Gaps.line,
          Visibility(
            visible: (!widget.isRoot || widget.isAdd),
            child: BrnTextSelectFormItem(
              title: '上级',
              isRequire: true,
              value: _controller.parentName.value,
              onTap: () {
                // 定义一个字符串数组
                List<String> parentUuids = [_controller.parentUuid.value];

                ///跳转到其他界面选择上级部门
                BoostNavigator.instance.push(
                  'companyChoiceAdministrationPage',
                  arguments: {"choice_uuids": parentUuids, 'selfUuid': widget.data.uuid ?? ""},
                ).then((value) {
                  if (value != null) {
                    setState(() {
                      DepartmentCompanyList data = value as DepartmentCompanyList;
                      _controller.parentUuid.value = data.uuid ?? '';
                      _controller.parentName.value = data.departmentName ?? '';
                    });
                  }
                });
              },
            ),
          ),

          Gaps.line,
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(top: 10, bottom: 10, right: 10),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 20, Colours.red)),
                Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("类型", 14, Colours.base_primary_text_title)),
                Gaps.hGap50,
                Expanded(
                    child: Container(
                  margin: const EdgeInsets.only(left: 0, right: 0),
                  child: SelectTabWidget(
                    _controller.weekList.value,
                    multiSelect: false,
                    crossAxisCount: 4,
                    hideMore: false,
                    paddingBottom: 0,
                    paddingTop: 0,
                    tabFontSize: 13,
                    defaultSelectedIndex: [(widget.isAdd) ? -1 : (int.parse(widget.data.departmentType ?? '0') - 1)],
                    lastIsAddOne: false,
                    selectedColor: Colours.base_primary,
                    bgSelectedColor: Colours.base_primary_select,
                    bgUnSelectedColor: Colours.base_primary_un_select,
                    childAspectRatio: 6 / 4,
                    itemClickCallback: (List<int> indexs) {
                      LogUtil.e("点击的 indexs = $indexs");
                      _controller.departmentType.value = (indexs[0] + 1).toString();
                      LogUtil.e("点击的 indexs = ${_controller.departmentType.value}");
                    },
                  ),
                )),
              ],
            ),
          )
        ],
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 54,
        child: Column(
          children: [
            Gaps.line,
            // 按钮
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 6),
              child: BrnBigMainButton(
                themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                title: '确定',
                onTap: () {
                  if (TextUtil.isEmpty(_controller.departmentName.value)) {
                    BrnToast.show('请输入名称', context);
                    return;
                  }
                  if (TextUtil.isEmpty(_controller.departmentType.value)) {
                    BrnToast.show('请选择部门类型', context);
                    return;
                  }
                  _presenter?.saveDepartment();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CompanyAdministrationPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  editOne(DepartmentCompanyEntity data) {}
}
