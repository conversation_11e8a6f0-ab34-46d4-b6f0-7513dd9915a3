import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_route_bar.dart';

import '../../../../net/http_config.dart';
import 'company_administration_page.dart';
import 'company_contract_page.dart';

/// 合同公司 行政组织
class CompanyMainPage extends StatefulWidget {
  @override
  _CompanyMainPageState createState() => _CompanyMainPageState();
}

class _CompanyMainPageState extends State<CompanyMainPage> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  int currentIndex = 0;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: currentIndex);
    _tabController.addListener(_handleTabChange);
  }

  @override
  void dispose() {
    _tabController.removeListener(_handleTabChange);
    _tabController.dispose();
    super.dispose();
  }

  void _handleTabChange() {
    if (_tabController.indexIsChanging) {
      setState(() {
        currentIndex = _tabController.index;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppRouteBar(
        onSelect: (int value) {
          setState(() {
            currentIndex = value;
          });
        },
        nameList: const [
          "合同公司",
          "行政组织",
        ],
        tabController: _tabController,
      ),
      body: TabBarView(
        // physics: const NeverScrollableScrollPhysics(),
        controller: _tabController,
        children: [
          CompanyContractPage(),
          CompanyAdministrationPage(
            isHideHeader: true,
          ),
        ],
      ),
    );
  }
}
