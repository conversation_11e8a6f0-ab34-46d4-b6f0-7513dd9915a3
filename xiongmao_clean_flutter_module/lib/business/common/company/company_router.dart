import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/add_company_administration_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/add_company_contract_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_administration_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_choice_administration_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_choice_contract_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_contract_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_manager_staff_belong_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/company/page/company_manager_staff_page.dart';

import '../../../net/http_config.dart';
import 'bean/department_company_entity.dart';

/// 公司管理
const companyMainPage = "companyMainPage";

/// 合同公司管理
const companyContractPage = "companyContractPage";

/// 行政管理
const companyAdministrationPage = "companyAdministrationPage";

///增加部门
const addCompanyAdministrationPage = "addCompanyAdministrationPage";

/// 部门风险部门人
const companyManagerStaffPage = "companyManagerStaffPage";

///针对部门进行多选、单选
const companyChoiceAdministrationPage = "companyChoiceAdministrationPage";

/// 编辑总部成员，所属部门
const companyManagerStaffBelongPage = "companyManagerStaffBelongPage";

///添加合同公司
const addCompanyContractPage = "addCompanyContractPage";

///关联合同公司
const companyChoiceContractPage = "companyChoiceContractPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> companyAdministrationRouterMap = {
  ///公司管理
  companyMainPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return CompanyMainPage();
        });
  },

  ///合同公司管理
  companyContractPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return CompanyContractPage();
        });
  },

  ///行政管理
  companyAdministrationPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"];
          return CompanyAdministrationPage(
            uuid: uuid,
          );
        });
  },

  ///编辑/新增部门
  addCompanyAdministrationPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          bool isRoot = false;
          if (map['root'] != null) {
            isRoot = map['root'] as bool;
          }
          bool isAdd = false;
          if (map['add'] != null) {
            isAdd = map['add'] as bool;
          }
          String uuid = map['uuid'] ?? '';
          DepartmentCompanyEntity data = map['data'];
          return AddCompanyAdministrationPage(
            isRoot: isRoot,
            isAdd: isAdd,
            uuid: uuid,
            data: data,
          );
        });
  },

  ///部门负责人
  companyManagerStaffPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          DepartmentCompanyEntity data = map['data'];
          return CompanyManagerStaffPage(
            data: data,
          );
        });
  },

  ///对部门进行多选，单选
  companyChoiceAdministrationPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          bool multiple = false; //默认是多选
          if (map['multiple'] != null) {
            multiple = map['multiple'] as bool;
          }
          String? uuid = map["uuid"]; // 这个id 是这个洁面循环使用的 不用管
          String? selfUuid = map["selfUuid"]; // 这个id 是选择层级的是哈，来区分是不是自己。以及他下面的子级
          List<String> choice_uuids = map['choice_uuids'] ?? [];
          return CompanyChoiceAdministrationPage(
            multiple: multiple,
            uuid: uuid,
            selfUuid: selfUuid,
            choice_uuids: choice_uuids,
          );
        });
  },

  ///编辑总部成员，所属部门
  companyManagerStaffBelongPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"]; //所属部门
          return CompanyManagerStaffBelongPage(uuid: uuid);
        });
  },

  ///添加合同公司
  addCompanyContractPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"]; //合同公司的UUid
          return AddCompanyContractPage(uuid: uuid);
        });
  },

  ///关联合同公司
  companyChoiceContractPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? subTitle = map['subTitle'];
          bool? isSelected = map['isSelected']; //是否要进行选择
          bool? showBottomBut = map['showBottomBut']; //是否要显示底部按钮
          return CompanyChoiceContractPage(
            uuid: uuid,
            subTitle: subTitle,
            isSelected: isSelected,
            showBottomBut: showBottomBut,
          );
        });
  },
};
