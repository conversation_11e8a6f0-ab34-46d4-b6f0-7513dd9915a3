import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_manager_staff_controller.dart';
import '../iview/administration_iview.dart';

class CompanyManagerStaffPresenter extends BasePagePresenter<AdministrationIView> with WidgetsBindingObserver {
  CompanyManagerStaffController controller;

  CompanyManagerStaffPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getDepartmentListManager();
  }

  void loadMore() {
    _page++;
    getDepartmentListManager();
  }

  ///获取负责人列表
  Future<dynamic> getDepartmentListManager() {
    var params = <String, String>{};
    params["uuid"] = controller.uuid.value;
    params['page'] = '$_page';

    return requestNetwork<ProjectArchivesEntity>(Method.get, url: HttpApi.COMPANY_DEPARTMENT_MANAGER_STAFF_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  ///部门清空负责人
  Future<dynamic> cleanDepartmentManagerStaff() {
    HashMap<String, String> params = HashMap();
    params["uuid"] = controller.uuid.value;
    return requestNetwork<Object>(Method.get, url: HttpApi.CLEAN_COMPANY_DEPARTMENT_MANAGER_STAFF_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///部门设置负责人
  Future<dynamic> setDepartmentManager() {
    HashMap<String, String> params = HashMap();
    params["uuid"] = controller.uuid.value;
    params["user_uuid_list"] = controller.user_uuid_list.join(',');
    return requestNetwork<Object>(Method.get, url: HttpApi.SET_COMPANY_DEPARTMENT_MANAGER_STAFF_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }
}
