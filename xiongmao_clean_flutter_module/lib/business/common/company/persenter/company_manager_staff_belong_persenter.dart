import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/department_company_entity.dart';
import '../bean/department_company_staff_belong_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_manager_staff_belong_controller.dart';
import '../controller/company_manager_staff_controller.dart';
import '../iview/administration_iview.dart';

class CompanyManagerStaffBelongPresenter extends BasePagePresenter<AdministrationIView> with WidgetsBindingObserver {
  CompanyManagerStaffBelongController controller;

  CompanyManagerStaffBelongPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getDepartmentListManager();
  }

  void loadMore() {
    _page++;
    getDepartmentListManager();
  }

  ///获取负责人列表
  Future<dynamic> getDepartmentListManager() {
    var params = <String, String>{};
    params['page'] = '$_page';

    return requestNetwork<DepartmentCompanyStaffBelongEntity>(Method.get, url: HttpApi.GET_DEPARTMENT_MANAGER_LIST_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList( data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }
}
