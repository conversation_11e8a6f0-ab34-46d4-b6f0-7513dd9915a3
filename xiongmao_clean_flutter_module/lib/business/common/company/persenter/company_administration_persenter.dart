import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../iview/administration_iview.dart';

class CompanyAdministrationPresenter extends BasePagePresenter<AdministrationIView> with WidgetsBindingObserver {
  CompanyAdministrationController controller;

  CompanyAdministrationPresenter(this.controller);

  ///获取根部门 子部门
  Future<dynamic> getDepartmentListManager() {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params["parent_uuid"] = controller.projectUuid.value;
    }
    return requestNetwork<DepartmentCompanyEntity>(Method.get, url: HttpApi.GET_COMPANY_DEPARTMENT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.data.value = data;
        controller.initMyList(data.list ?? []);
      } else {}
    }, onError: (_, __) {});
  }

  ///删除指定的部门
  Future<dynamic> deleteDepartment() {
    HashMap<String, String> params = HashMap();
    params["uuid"] = controller.projectUuid.value;
    return requestNetwork<Object>(Method.get, url: HttpApi.DELETE_COMPANY_DEPARTMENT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///编辑、新建走这
  Future<dynamic> saveDepartment() {
    HashMap<String, String> params = HashMap();
    if (!TextUtil.isEmpty(controller.saveDepartmentUuid.value)) {
      params["uuid"] = controller.saveDepartmentUuid.value;
    }
    if (!TextUtil.isEmpty(controller.departmentType.value)) {
      params["department_type"] = controller.departmentType.value;
    }
    if (!TextUtil.isEmpty(controller.parentUuid.value)) {
      params["parent_uuid"] = controller.parentUuid.value;
    }
    params["department_name"] = controller.departmentName.value;
    return requestNetwork<Object>(Method.get, url: HttpApi.SAVE_COMPANY_DEPARTMENT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///获取部门详情
  Future<dynamic> getDepartmentOne(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    return requestNetwork<DepartmentCompanyEntity>(Method.get, url: HttpApi.GET_DEPARTMENT_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.editOne(data!);
    }, onError: (_, __) {});
  }
}
