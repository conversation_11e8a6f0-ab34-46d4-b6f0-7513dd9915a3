import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../contract/bean/contract_auto_sign_entity.dart';
import '../../contract/bean/contract_fdd_verify_entity.dart';
import '../../contract/bean/contract_main_info_entity.dart';
import '../../contract/bean/contract_register_info_entity.dart';
import '../bean/department_company_contract_entity.dart';
import '../bean/department_company_entity.dart';
import '../controller/company_administration_controller.dart';
import '../controller/company_contract_controller.dart';
import '../iview/administration_iview.dart';
import '../iview/company_contract_iview.dart';

/// 合同公司的管理
class CompanyContractPresenter extends BasePagePresenter<CompanyContractIView> with WidgetsBindingObserver {
  CompanyContractController controller;

  CompanyContractPresenter(this.controller);

  int _page = 1;

  void onRefresh(String? status) {
    _page = 1;
    getDepartmentListManager(status);
  }

  void loadMore(String? status) {
    _page++;
    getDepartmentListManager(status);
  }

  ///获取合同列表
  Future<dynamic> getDepartmentListManager(String? status) {
    var params = <String, String>{};
    params['page'] = '$_page';
    if (!TextUtil.isEmpty(status)) {
      params['status'] = '$status'; //状态 1草稿 2审核中 3审核驳回 4审核通过
    }
    return requestNetwork<DepartmentCompanyContractEntity>(Method.get, url: HttpApi.GET_COMPANY_CONTRACT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  ///删除合同列表
  Future<dynamic> deleteDepartmentContract(String? uuid) {
    HashMap<String, String> params = HashMap();
    params["uuid"] = '${uuid}';
    return requestNetwork<Object>(Method.get, url: HttpApi.DEL_COMPANY_CONTRACT_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///编辑、新建走这
  Future<dynamic> saveDepartmentContact(bool isDraft) {
    HashMap<String, String> params = HashMap();
    if (!TextUtil.isEmpty(controller.uuid.value)) {
      params["uuid"] = controller.uuid.value;
    }
    params["company_name"] = controller.companyName.value;
    params["credit_code"] = controller.companyNo.value;
    params["business_license"] = controller.companyPic.value;
    params["mobile"] = controller.companyMobile.value;
    params["address"] = controller.companyAddress.value;
    params["bank_no"] = controller.companyBankNo.value;
    params["bank"] = controller.companyBankName.value;

    ///是否草稿
    params["is_draft"] = isDraft ? '1' : '0';

    return requestNetwork<Object>(Method.get, url: HttpApi.SAVE_COMPANY_CONTRACT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///获取部门详情
  Future<dynamic> getDepartmentContractOne(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    return requestNetwork<DepartmentCompanyContractList>(Method.get, url: HttpApi.GET_COMPANY_CONTRACT_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillData(data);
      }
    }, onError: (_, __) {});
  }

  ///撤回操作
  Future<dynamic> cancelDepartmentContract(String? uuid) {
    HashMap<String, String> params = HashMap();
    params["uuid"] = '$uuid';
    return requestNetwork<Object>(Method.get, url: HttpApi.CANCEL_COMPANY_CONTRACT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///取消认证
  Future<dynamic> cancelDepartmentContractAuth(String? uuid) {
    HashMap<String, String> params = HashMap();
    params["uuid"] = '$uuid';
    return requestNetwork<Object>(Method.get, url: HttpApi.CANCEL_COMPANY_CONTRACT_AUTH, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.viewRefresh();
    }, onError: (_, __) {});
  }

  ///电子签设置 *********** 电子签设置 电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置

  ///获取合同配置信息 type 是渠道谁调用了 他
  Future<dynamic> getContractMainInfo(String contractCompanyUuid) {
    var params = <String, String>{};
    params['customer_type'] = "2"; //类型 1个人 2企业
    params['contract_company_uuid'] = contractCompanyUuid; //合同公司UUID 企业必传
    return requestNetwork<ContractMainInfoEntity>(Method.get, url: HttpApi.GET_CONRRACT_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.updateContractMainInfoEntity(data);
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同第三方帐户注册
  Future<dynamic> getContractRegisterUser(String contractCompanyUuid) {
    var params = <String, String>{};
    params['customer_type'] = "2"; //类型 1个人 2企业
    params['contract_company_uuid'] = contractCompanyUuid; //合同公司UUID 企业必传
    return requestNetwork<ContractRegisterInfoEntity>(Method.get, url: HttpApi.GET_CONTRACT_REGISTER, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        getContractFddUrl(data.data);
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同第三方帐户认证的链接 未认证的，则直接走法大大的获取认证的接口
  Future<dynamic> getContractFddUrl(String? customerId) {
    var params = <String, String>{};
    params['customer_id'] = "$customerId";
    return requestNetwork<ContractFddVerifyEntity>(Method.get, url: HttpApi.GET_VERIFY_URL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        LogUtil.e("object-------1111-----" + (data.url ?? ""));
        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoWebPage", "url": data.url ?? ""});
      }
    }, onError: (_, __) {});
  }

  ///自动授权接口
  Future<dynamic> getAutoSignUrl(String? customerId) {
    var params = <String, String>{};
    params['customer_id'] = "$customerId";
    return requestNetwork<ContractAutoSignEntity>(Method.get, url: HttpApi.GET_AUTO_SIGN, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        LogUtil.e("-----authUrl----" + (data.authUrl ?? ""));
        view.gotoAutoAuth(data.authUrl ?? '');
      }
    }, onError: (_, __) {});
  }

  ///电子签设置 *********** 电子签设置 电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置电子签设置 *********** 电子签设置
}
