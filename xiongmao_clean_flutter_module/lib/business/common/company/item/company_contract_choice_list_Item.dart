import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/department_company_contract_entity.dart';
import '../bean/department_company_entity.dart';

class CompanyContactChoiceListItem extends StatelessWidget {
  String? uuid;

  DepartmentCompanyContractList data;

  final Function onClick;

  CompanyContactChoiceListItem({
    this.uuid,
    required this.data,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        margin: EdgeInsets.only(bottom: 16),
        decoration: const BoxDecoration(
          borderRadius: BorderRadius.all(Radius.circular(4)),
          color: Colours.white,
        ),
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 20),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.companyName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                LoadAssetImage(
                  (uuid == data.uuid) ? 'icon_check' : 'icon_uncheck',
                  width: 20,
                  height: 20,
                ),
              ],
            ),
            Gaps.vGap12,
            Gaps.line,
            Gaps.vGap12,
            Row(
              children: [
                CommonUtils.getSimpleText('统一社会信用代码', 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.creditCode, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
              ],
            ),
            Gaps.vGap8,
            Row(
              children: [
                CommonUtils.getSimpleText('地址', 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.address, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
              ],
            ),
            Gaps.vGap8,
            Row(
              children: [
                CommonUtils.getSimpleText('电话', 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.mobile, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
              ],
            ),
            Gaps.vGap8,
            Row(
              children: [
                CommonUtils.getSimpleText('开户行', 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.bank, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
              ],
            ),
            Gaps.vGap8,
            Row(
              children: [
                CommonUtils.getSimpleText('银行账号', 14, Colours.base_primary_text_title),
                Expanded(child: CommonUtils.getSimpleText(data.bankNo, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
