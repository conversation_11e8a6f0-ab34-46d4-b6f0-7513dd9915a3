import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/department_company_entity.dart';

class AdministrationChoiceListItem extends StatelessWidget {
  final Function gotoLevel;
  final Function check;

  DepartmentCompanyList data;

  AdministrationChoiceListItem({required this.data, required this.gotoLevel, required this.check});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 0),
        child: Column(
          children: [
            Padding(
              padding: const EdgeInsets.only(left: 10),
              child: Row(
                children: [
                  InkWell(
                    child: Container(
                      padding: const EdgeInsets.all(14),
                      child: LoadImage(
                        (data.isSelected == true) ? 'icon_check' : 'icon_uncheck',
                        width: 20,
                        height: 20,
                      ),
                    ),
                    onTap: () {
                      check();
                    },
                  ),
                  Gaps.hGap10,
                  const LoadAssetImage(
                    'common/icon_common_company',
                    width: 40,
                    height: 40,
                  ),
                  Gaps.hGap10,
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            CommonUtils.getSimpleText(data.departmentName, 16, Colours.base_primary_text_title),
                            Gaps.hGap10,
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colours.base_primary_select,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: CommonUtils.getSimpleText(data.departmentTypeName, 12, Colours.base_primary),
                            ),
                          ],
                        ),
                        CommonUtils.getSimpleText('负责人：${getBuildName(data.responseUserList)}', 12, Colours.base_primary_text_caption),
                      ],
                    ),
                  ),
                  Gaps.hGap4,
                  Visibility(
                    visible: data.isHasChild == '1',
                    child: const LoadAssetImage(
                      'icon_base_arrow',
                      width: 14,
                      height: 14,
                    ),
                  )
                ],
              ),
            ),
            Gaps.vGap10,
            Gaps.line,
          ],
        ),
      ),
      onTap: () {
        gotoLevel();
      },
    );
  }
}

String getBuildName(List<DepartmentCompanyListResponseUserList>? data) {
  StringBuffer stringBuffer = StringBuffer();
  if (data != null && data.isNotEmpty) {
    for (int i = 0; i < data.length; i++) {
      stringBuffer.write(data[i].userName);
      if (i < data.length - 1) {
        stringBuffer.write(","); // 在名称后添加逗号，除了最后一个
      }
    }
  } else {
    stringBuffer.write('无');
  }

  return stringBuffer.toString();
}
