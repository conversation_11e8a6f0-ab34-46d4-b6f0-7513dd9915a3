import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../widgets/custom_avatar_view.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';

/// 部门负责人列表
class CompanyManagerStaffView extends StatelessWidget {
  ProjectArchivesList data;

  final Function onClick;

  CompanyManagerStaffView({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Column(
        children: [
          Gaps.line,
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(
              top: 10,
              left: 16,
              right: 16,
              bottom: 10,
            ), // 设置内边距为16.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomAvatarView(
                  name: data.userName,
                  avatarUrl: data.avatar,
                ),
                Gaps.hGap10,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(data.userName, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    CommonUtils.getSimpleText(getBuildName(data.roleList), 13, Colours.erji),
                  ],
                )),
                Gaps.hGap10,
                Visibility(
                  visible: data.isSelected,
                  child: const LoadAssetImage(
                    'base/icon_base_selected',
                    width: 20,
                    height: 20,
                  ),
                ),
              ],
            ),
          ),
          // Gaps.line
        ],
      ),
      onTap: () {
        onClick();
      },
    );
  }

  String getBuildName(List<String>? data) {
    StringBuffer stringBuffer = StringBuffer();
    if (data != null && data.isNotEmpty) {
      for (int i = 0; i < data.length; i++) {
        stringBuffer.write(data[i]);
        if (i < data.length - 1) {
          stringBuffer.write(","); // 在名称后添加逗号，除了最后一个
        }
      }
    } else {
      stringBuffer.write('无角色');
    }

    return stringBuffer.toString();
  }
}
