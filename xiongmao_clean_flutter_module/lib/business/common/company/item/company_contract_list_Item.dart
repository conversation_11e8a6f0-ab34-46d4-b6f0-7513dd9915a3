import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/department_company_contract_entity.dart';
import '../bean/department_company_entity.dart';

class CompanyContactListItem extends StatelessWidget {
  DepartmentCompanyContractList data;

  final Function delete;

  final Function(int type) action;

  CompanyContactListItem({required this.data, required this.delete, required this.action});

  @override
  Widget build(BuildContext context) {
    String actionName = "编辑";

    //状态 1草稿 2审核中 3审核驳回 4审核通过 5电子签认证中 6待授权企业自动签署 7已电子签认证
    Color color = Colours.base_primary_text_title;
    Color bgColor = Colours.base_primary_text_title;

    if (data.auditStatus == '1') {
      color = Colours.base_primary_text_body;
      bgColor = Colours.base_primary_text_body;
      actionName = '编辑';
    } else if (data.auditStatus == '2') {
      color = Colours.base_primary;
      bgColor = Colours.base_primary;
      actionName = '撤回';
    } else if (data.auditStatus == '3') {
      color = Colours.red;
      bgColor = Colours.red;
      actionName = '编辑';
    } else if (data.auditStatus == '4') {
      color = Colours.base_primary_blue;
      bgColor = Colours.base_primary_blue;
      actionName = '电子签认证';
    } else if (data.auditStatus == '5') {
      color = Colours.base_primary_blue;
      bgColor = Colours.base_primary_blue;
      actionName = '取消认证';
    } else if (data.auditStatus == '6') {
      color = Colours.base_primary_blue;
      bgColor = Colours.base_primary_blue;
      actionName = '授权企业自动签署';
    } else if (data.auditStatus == '7') {
      color = Colours.base_primary_blue;
      bgColor = Colours.base_primary_blue;
      actionName = '设置印章';
    }

    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(8)),
        color: Colours.white,
      ),
      padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(child: CommonUtils.getSimpleText(data.companyName, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
              Container(
                decoration: BoxDecoration(
                  border: Border.all(color: bgColor, width: 1),
                  borderRadius: BorderRadius.circular(2.0),
                ),
                child: CommonUtils.getSimpleText(data.auditStatusName, 14, color),
                padding: const EdgeInsets.only(left: 3, right: 3),
              )
            ],
          ),
          Gaps.vGap10,
          Gaps.line,
          Gaps.vGap10,
          Row(
            children: [
              CommonUtils.getSimpleText('统一社会信用代码', 14, Colours.base_primary_text_title),
              Expanded(child: CommonUtils.getSimpleText(data.creditCode, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
            ],
          ),
          Gaps.vGap8,
          Row(
            children: [
              CommonUtils.getSimpleText('地址', 14, Colours.base_primary_text_title),
              Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(data.address)) ? '-' : data.address, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
            ],
          ),
          Gaps.vGap8,
          Row(
            children: [
              CommonUtils.getSimpleText('电话', 14, Colours.base_primary_text_title),
              Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(data.mobile)) ? '-' : data.mobile, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
            ],
          ),
          Gaps.vGap8,
          Row(
            children: [
              CommonUtils.getSimpleText('开户行', 14, Colours.base_primary_text_title),
              Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(data.bank)) ? '-' : data.bank, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
            ],
          ),
          Gaps.vGap8,
          Row(
            children: [
              CommonUtils.getSimpleText('银行账号', 14, Colours.base_primary_text_title),
              Expanded(child: CommonUtils.getSimpleText((TextUtil.isEmpty(data.bankNo)) ? '-' : data.bankNo, 14, Colours.base_primary_text_title, textAlign: TextAlign.end)),
            ],
          ),
          Visibility(
            visible: data.auditStatus == '3',
            child: Column(
              children: [
                Gaps.vGap8,
                CommonUtils.getSimpleText('审核建议：${data.auditSuggest ?? ''}', 14, Colours.red, textAlign: TextAlign.start),
                Gaps.vGap8,
              ],
            ),
          ),
          Gaps.vGap8,
          Row(
            children: [
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(1),
                  decoration: const BoxDecoration(
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                    color: Colours.base_primary_bg_page,
                  ),
                  child: Stack(
                    children: [
                      LoadImage(
                        data.businessLicense ?? '',
                        width: double.infinity,
                        height: 100,
                        radius: 4,
                      ),
                      Positioned(
                        left: 0,
                        right: 0,
                        bottom: 0, // 调整以设置文本距离底部的距离
                        child: Container(
                          padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                          decoration: BoxDecoration(
                            color: Colors.black.withOpacity(0.5), // 半透明背景
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(4.0), // 仅左下角圆角
                              bottomRight: Radius.circular(4.0), // 仅右下角圆角
                            ),
                          ),
                          child: CommonUtils.getSimpleText('营业执照', 12, Colours.white, textAlign: TextAlign.center),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Gaps.hGap10,
              Expanded(
                child: Visibility(
                  visible: (!TextUtil.isEmpty(data.sealCdnUrl)),
                  child: Container(
                    padding: const EdgeInsets.all(1),
                    decoration: const BoxDecoration(
                      borderRadius: BorderRadius.all(Radius.circular(4)),
                      color: Colours.base_primary_bg_page,
                    ),
                    child: Stack(
                      children: [
                        Center(
                          child: LoadImage(
                            data.sealCdnUrl ?? '',
                            height: 100,
                            radius: 4,
                          ),
                        ),
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0, // 调整以设置文本距离底部的距离
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 2, horizontal: 10),
                            decoration: BoxDecoration(
                              color: Colors.black.withOpacity(0.5), // 半透明背景
                              borderRadius: const BorderRadius.only(
                                bottomLeft: Radius.circular(4.0), // 仅左下角圆角
                                bottomRight: Radius.circular(4.0), // 仅右下角圆角
                              ),
                            ),
                            child: CommonUtils.getSimpleText('公司印章', 12, Colours.white, textAlign: TextAlign.center),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
          Gaps.vGap14,
          Gaps.line,
          Gaps.vGap14,
          Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                  onTap: () {
                    delete();
                  },
                )),
                Gaps.vLine,
                Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText(actionName, 16, Colours.base_primary, textAlign: TextAlign.center),
                  onTap: () {
                    action(0);
                  },
                )),

                ///认证中的时候，显示以下2个View 并且点击直接跳转法大大
                Visibility(
                  visible: data.auditStatus == '5',
                  child: Gaps.vLine,
                ),
                Visibility(
                  visible: data.auditStatus == '5',
                  child: Expanded(
                      child: InkWell(
                    child: CommonUtils.getSimpleText('继续认证', 16, Colours.base_primary, textAlign: TextAlign.center),
                    onTap: () {
                      ///直接跳转法大大的链接
                      action(1);
                    },
                  )),
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
