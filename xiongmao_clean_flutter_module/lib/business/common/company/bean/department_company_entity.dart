import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/department_company_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/department_company_entity.g.dart';

@JsonSerializable()
class DepartmentCompanyEntity {
	String? uuid;
	@JSONField(name: "department_name")
	String? departmentName;
	@JSONField(name: "department_type")
	String? departmentType;
	@JSONField(name: "department_type_name")
	String? departmentTypeName;
	@JSONField(name: "is_has_child")
	String? isHasChild;
	@JSONField(name: "parent_uuid")
	String? parentUuid;
	@JSONField(name: "parent_department_name")
	String? parentDepartmentName;
	@JSONField(name: "contract_company_uuid")
	String? contractCompanyUuid;
	@JSONField(name: "contract_company_name")
	String? contractCompanyName;
	@JSONField(name: "response_user_list")
	List<DepartmentCompanyListResponseUserList>? responseUserList;
	List<DepartmentCompanyList>? list;

	bool isSelected = false;


	DepartmentCompanyEntity();

	factory DepartmentCompanyEntity.fromJson(Map<String, dynamic> json) => $DepartmentCompanyEntityFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}


@JsonSerializable()
class DepartmentCompanyList {
	String? uuid;
	@JSONField(name: "department_name")
	String? departmentName;
	@JSONField(name: "department_type")
	String? departmentType;
	@JSONField(name: "department_type_name")
	String? departmentTypeName;
	@JSONField(name: "is_has_child")
	String? isHasChild;
	@JSONField(name: "contract_company_uuid")
	String? contractCompanyUuid;
	@JSONField(name: "contract_company_name")
	String? contractCompanyName;
	@JSONField(name: "response_user_list")
	List<DepartmentCompanyListResponseUserList>? responseUserList;
	bool isSelected = false;

	DepartmentCompanyList();

	factory DepartmentCompanyList.fromJson(Map<String, dynamic> json) => $DepartmentCompanyListFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class DepartmentCompanyListResponseUserList {
	@JSONField(name: "user_uuid")
	String? userUuid;
	@JSONField(name: "user_name")
	String? userName;

	DepartmentCompanyListResponseUserList();

	factory DepartmentCompanyListResponseUserList.fromJson(Map<String, dynamic> json) => $DepartmentCompanyListResponseUserListFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyListResponseUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}