import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/department_company_contract_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/department_company_contract_entity.g.dart';

@JsonSerializable()
class DepartmentCompanyContractEntity {
	int? page;
	int? size;
	int? total;
	List<DepartmentCompanyContractList>? list;

	DepartmentCompanyContractEntity();

	factory DepartmentCompanyContractEntity.fromJson(Map<String, dynamic> json) => $DepartmentCompanyContractEntityFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyContractEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class DepartmentCompanyContractList {
	String? uuid;
	@JSONField(name: "company_name")
	String? companyName;
	@JSONField(name: "credit_code")
	String? creditCode;
	String? mobile;
	@JSONField(name: "business_license")
	String? businessLicense;
	String? address;
	@JSONField(name: "bank_no")
	String? bankNo;
	String? bank;
	@JSONField(name: "audit_status")
	String? auditStatus;
	@JSONField(name: "audit_status_name")
	String? auditStatusName;
	@JSONField(name: "audit_suggest")
	String? auditSuggest;
	@JSONField(name: "customer_id")
	String? customerId;
	@JSONField(name: "seal_cdn_url")
	String? sealCdnUrl;

	DepartmentCompanyContractList();

	factory DepartmentCompanyContractList.fromJson(Map<String, dynamic> json) => $DepartmentCompanyContractListFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyContractListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}