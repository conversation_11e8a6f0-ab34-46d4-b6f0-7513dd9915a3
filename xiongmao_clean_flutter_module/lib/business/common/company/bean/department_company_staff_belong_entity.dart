import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/department_company_staff_belong_entity.g.dart';
import 'dart:convert';

import 'department_company_entity.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/department_company_staff_belong_entity.g.dart';

@JsonSerializable()
class DepartmentCompanyStaffBelongEntity {
	List<DepartmentCompanyList>? list;

	DepartmentCompanyStaffBelongEntity();

	factory DepartmentCompanyStaffBelongEntity.fromJson(Map<String, dynamic> json) => $DepartmentCompanyStaffBelongEntityFromJson(json);

	Map<String, dynamic> toJson() => $DepartmentCompanyStaffBelongEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

