import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_history_one_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/iview/jigsaw_puzzle_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../../quality_service/bean/plan_task_one_new_entity.dart';
import '../controller/jigasw_puzzle_controller.dart';

class JigsawPuzzlePagePresenter extends BasePagePresenter<JigsawPuzzleIView> with WidgetsBindingObserver {
  JigsawPuzzlePageController controller;

  JigsawPuzzlePagePresenter(this.controller);

  ///获取计划详情 - 编辑的时候获取
  Future<dynamic> requestCleanPlanTaskOne(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<PlanTaskOneNewEntity>(Method.post, url: HttpApi.CONTRCT_ONE_TASK, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.updateCleanPlanTaskOneEntityEntity(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
