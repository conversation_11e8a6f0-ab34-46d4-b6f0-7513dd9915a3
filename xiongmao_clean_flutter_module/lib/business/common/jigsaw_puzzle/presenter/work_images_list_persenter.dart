import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/work_circle_entity.dart';
import '../controller/work_images_list_controller.dart';
import '../iview/work_images_iview.dart';

/**
 * 工作图片
 */
class WorkImagesListPresenter extends BasePagePresenter<WorkImagesIView> with WidgetsBindingObserver {
  WorkImagesListController controller;

  WorkImagesListPresenter(this.controller);

  int _page = 1;

  void onRefresh(String? project_uuid) {
    _page = 1;
    requestCleanPlanTaskList(project_uuid);
  }

  void loadMore(String? project_uuid) {
    _page++;
    requestCleanPlanTaskList(project_uuid);
  }

  ///获取计划任务列表
  Future<dynamic> requestCleanPlanTaskList(String? project_uuid) {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["layout"] = "2"; //布局 1默认 2紧凑 需要紧凑模式
    params["project_uuid"] = '$project_uuid';
    return requestNetwork<WorkCircleEntity>(Method.get, url: HttpApi.GET_WORK_CIRCLE_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        if ("$_page" == '1') {
          controller.initTaskList(data.list!);
        } else {
          controller.updateTaskList(data.list!);
        }
        controller.listTotal.value = "${data.total ?? '0'}";
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
