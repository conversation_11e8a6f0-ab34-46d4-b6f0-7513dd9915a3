import 'package:bruno/bruno.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/puzzle_item_data.dart';
import '../bean/puzzle_layout_data.dart';

class JigsawPuzzlePageController extends GetxController {
  final List<PuzzleLayoutItem> puzzleLayoutItems = [
    PuzzleLayoutItem(title: '单列', imageSelectName: 'common/icon_puzzle_sing_select', imageUnSelectName: 'common/icon_puzzle_sing_unselect'),
    PuzzleLayoutItem(title: '双列', imageSelectName: 'common/icon_puzzle_double_select', imageUnSelectName: 'common/icon_puzzle_double_unselect'),
    PuzzleLayoutItem(title: '九宫格', imageSelectName: 'common/icon_puzzle_jiu_select', imageUnSelectName: 'common/icon_puzzle_jiu_unselect'),
  ];

  //图片的方式
  List<BrnCommonActionSheetItem> actionsAccount = [
    BrnCommonActionSheetItem(
      '选择本地相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '选择已上传的照片',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '立即拍摄',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
  ];

  //标题-
  var title = '工作汇报'.obs;

  //汇报人
  var reporter = ''.obs;

  //日期
  var reporterDate = ''.obs;

  //记录点击的
  var selectedIndex = 0.obs;

  //布局的展示方式
  var layoutGridCount = 1.obs;

  //是否保存
  var saveStatus = false.obs;

  //图片、文字的处理
  var puzzleItems = <PuzzleItem>[].obs;

  //添加文字消息
  void addTextData(PuzzleItem data) {
    puzzleItems.value.add(data);
    notifyPuzzleList();
  }

  //添加图怕消息
  void addPhotoData(PuzzleItem data) {
    if (puzzleItems.isNotEmpty) {
      int lastItemType = puzzleItems.last.type; // 假设PuzzleItem类有一个type字段来表示类型
      if (lastItemType == 2) {
        //如果是图片，那么就个原先的图片里面增加值
        // 最后一条数据的type值是指定的值
        // 在这里可以执行您需要的操作，比如添加新数据
        puzzleItems.last.images.addAll(data.images);
      } else {
        // 最后一条数据的type值不是指定的值
        // 可以根据需要进行其他操作或者不添加新数据
        puzzleItems.value.add(data);
      }
    } else {
      // 如果列表为空，直接添加新数据
      puzzleItems.value.add(data);
    }
    notifyPuzzleList();
  }

  void notifyPuzzleList() {
    puzzleItems.value = puzzleItems.value.toList();
  }

  //删除指定item文字
  void removePuzzleText() {}
}
