import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/work_circle_entity.dart';

class WorkImagesListController extends GetxController {
  //项目的uuid 项目的名字
  var projectName = "".obs;
  var projectUuid = "".obs;

  var listTotal = "0".obs;

  var listTask = <WorkCircleList>[].obs;

  void initTaskList(List<WorkCircleList> value) {
    listTask.value = value.toList();
  }

  void updateTaskList(List<WorkCircleList> value) {
    listTask.value.addAll(value);
    listTask.value = listTask.value.toList();
  }

  //记录被点击的Items
  var selectedItems = <WorkCircleListList>[].obs;

  void toggleSelectedItem(WorkCircleListList item) {
    if (selectedItems.contains(item)) {
      selectedItems.remove(item);
    } else {
      selectedItems.add(item);
    }
    selectedItems.value = selectedItems.value.toList();
  }
}
