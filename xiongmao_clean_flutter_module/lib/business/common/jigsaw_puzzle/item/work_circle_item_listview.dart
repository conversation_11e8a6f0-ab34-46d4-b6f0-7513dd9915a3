import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/controller/work_images_list_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../bean/puzzle_layout_data.dart';
import '../bean/work_circle_entity.dart';
import '../controller/jigasw_puzzle_controller.dart';

/**
 * 紧凑布局
 */
class WorkCircleListView extends StatelessWidget {
  WorkCircleList data;

  WorkCircleListView({
    required this.data,
  });

  @override
  Widget build(BuildContext context) {
    var find = Get.find<WorkImagesListController>();

    return GestureDetector(
      onTap: () => {},
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomAvatarView(
              name: data.userName ?? "",
              avatarUrl: data.avatar,
            ),
            Gaps.hGap10,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          CommonUtils.getSimpleText(data.userName ?? "", 16, Colours.base_primary_text_title),
                          Gaps.hGap8,
                          CommonUtils.getSimpleText(data.jobName ?? "", 15, Colours.erji),
                        ],
                      ),
                      CommonUtils.getSimpleText(DateUtil.formatDateStr(data.createTime!, format: 'MM月dd日') ?? "", 13, Colours.base_primary_text_caption),
                    ],
                  ),
                  GridView.builder(
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 3,
                      crossAxisSpacing: 8,
                      mainAxisSpacing: 8,
                    ),
                    itemCount: data.list!.length,
                    itemBuilder: (context, itemIndex) {
                      return GestureDetector(
                        onTap: () {
                          find.toggleSelectedItem(data.list![itemIndex]);
                        },
                        child: Stack(
                          children: [
                            Container(
                              width: double.infinity,
                              child: LoadImage(
                                data.list![itemIndex].picThumb ?? "",
                                radius: 6,
                                height: double.infinity,
                                fit: BoxFit.cover,
                              ),
                            ),
                            Positioned(
                              top: 4,
                              left: 4,
                              child: Image.asset(
                                (find.selectedItems.contains(data.list![itemIndex])) ? 'assets/images/icon_check.png' : 'assets/images/icon_uncheck.png',
                                width: 18,
                                height: 18,
                              ),
                            ),
                            Positioned(
                              left: 0,
                              right: 0,
                              bottom: 3,
                              child: Container(
                                decoration: BoxDecoration(
                                  gradient: LinearGradient(
                                    begin: Alignment.bottomCenter,
                                    end: Alignment.topCenter,
                                    colors: [Colors.black, Colors.transparent],
                                  ),
                                ),
                                child: Column(
                                  children: [
                                    CommonUtils.getSimpleText(data.list![itemIndex].createTime ?? '', 12, Colours.white, fontWeight: FontWeight.bold, textAlign: TextAlign.center, height: 1),
                                    CommonUtils.getSimpleText(data.list![itemIndex].actTypeName ?? '', 10, Colours.white, textAlign: TextAlign.center, height: 1),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
