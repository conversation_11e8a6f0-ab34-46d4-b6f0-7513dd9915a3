import 'dart:io';
import 'dart:ui';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../bean/puzzle_item_data.dart';
import '../bean/puzzle_layout_data.dart';
import '../controller/jigasw_puzzle_controller.dart';

/// 图片、文字操作的布局
class PuzzleItemListView extends StatelessWidget {
  final Function onTap;
  final Function onPictureTap;
  final Function onTextCloseTap;
  PuzzleItem data;

  // PuzzleItemListView({required this.data, required this.gridCount, required this.onTap,Key? key}) :super(key: key) ;

  PuzzleItemListView({required this.data, required this.onTap, required this.onPictureTap, required this.onTextCloseTap});

  @override
  Widget build(BuildContext context) {
    var find = Get.find<JigsawPuzzlePageController>();
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        data.type == 1
            ? InkWell(
                child: Container(
                  alignment: Alignment.centerLeft,
                  padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                  child: Row(
                    children: [
                      Expanded(child: CommonUtils.getSimpleText(data.text, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),
                      Obx(() => Visibility(
                            visible: !find.saveStatus.value,
                            child: InkWell(
                              child: const LoadAssetImage(
                                'icon_base_round_close',
                                width: 20,
                                height: 20,
                              ),
                              onTap: () => onTextCloseTap(),
                            ),
                          )),
                    ],
                  ),
                ),
                onTap: () {
                  onTap();
                },
              )
            : Obx(() =>
            GridView.builder(
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.only(left: 10, right: 10, top: 10),
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: find.layoutGridCount.value,
                    crossAxisSpacing: 1,
                    mainAxisSpacing: 1,
                  ),
                  shrinkWrap: true,
                  itemCount: data.images.length,
                  itemBuilder: (context, index) {
                    final imageUrl = data.images[index];

                    return Stack(
                      children: [
                        // 背景模糊效果
                        ClipRect(
                          child: BackdropFilter(
                            filter: ImageFilter.blur(sigmaX: 5.0, sigmaY: 5.0),
                            child: Container(
                              color: Colors.black.withOpacity(0), // 确保背景透明
                            ),
                          ),
                        ),

                        // 图片内容
                        ClipRect(
                          child: Align(
                            alignment: Alignment.center,
                            child: (imageUrl.contains('http'))
                                ? LoadImage(imageUrl ?? '')
                                : Image.file(
                              File(imageUrl),
                              width: double.infinity, // 设置图片宽度占满容器
                              fit: BoxFit.cover, // 确保图片填满容器
                            ),
                          ),
                        ),

                        // 删除按钮
                        Obx(() => Visibility(
                          visible: !find.saveStatus.value,
                          child: Positioned(
                            top: 5,
                            right: 5,
                            child: GestureDetector(
                              onTap: () {
                                data.images.removeAt(index);
                                find.notifyPuzzleList();
                              },
                              child: const LoadAssetImage(
                                'icon_base_round_close',
                                width: 20,
                                height: 20,
                              ),
                            ),
                          ),
                        )),
                      ],
                    );
                  },
                )),
        if (data.type == 2 && data.images.isNotEmpty)
          Obx(() => Visibility(
              visible: !find.saveStatus.value,
              child: Padding(
                padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                child: InkWell(
                  child: CommonUtils.getSimpleText('在此处追加图', 14, Colours.base_primary),
                  onTap: () => onPictureTap(),
                ),
              )))
      ],
    );
  }
}
