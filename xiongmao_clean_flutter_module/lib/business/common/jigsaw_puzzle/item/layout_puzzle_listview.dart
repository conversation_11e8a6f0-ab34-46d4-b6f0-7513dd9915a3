import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../bean/puzzle_layout_data.dart';

/**
 * 布局的列表
 */
class LayoutPuzzleListView extends StatelessWidget {
  final int index;
  final int selectedIndex;
  final Function onTap;
  PuzzleLayoutItem data;

  LayoutPuzzleListView({required this.data, required this.index, required this.selectedIndex, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onTap(),
      child: Container(
        margin: const EdgeInsets.only(top: 10, bottom: 10, right: 10),
        padding: const EdgeInsets.only(left: 4, right: 4),
        decoration: BoxDecoration(
          border: Border.all(color: selectedIndex == index ? Colours.base_primary : Colours.base_primary_line_b, width: selectedIndex == index ? 1 : 1),
          borderRadius: BorderRadius.circular(2.0),
        ),
        child: Column(
          children: [
            CommonUtils.getSimpleText(data.title, 13, selectedIndex == index ? Colours.base_primary : Colours.base_primary_text_title),
            LoadAssetImage(
              selectedIndex == index ? data.imageSelectName : data.imageUnSelectName,
              width: 36,
              height: 36,
            ),
          ],
        ),
      ),
    );
  }
}
