import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/page/jigsaw_puzzle_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/jigsaw_puzzle/page/work_images_list_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/monitoring_work_order_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/quality_monitoring_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/risk_page.dart';

import '../../../net/http_config.dart';

/// 拼图汇报
const jigsawPuzzlePage = "jigsawPuzzlePage";
const workImagesListPage = "workImagesListPage";

/// 风险监控
Map<String, FlutterBoostRouteFactory> jigsawPuzzlePageRouterMap = {
  jigsawPuzzlePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String position = map['position'] ?? "0";
          String reporter_name = map['reporter_name'] ?? "";
          String task_uuid = map['task_uuid'] ?? "";
          return JigsawPuzzlePage(
            position: position,
            reporter_name: reporter_name,
            task_uuid: task_uuid,
          );
        });
  },
  workImagesListPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String position = map['position'] ?? "0";
          return WorkImagesListPage(
            position: position,
          );
        });
  },
};
