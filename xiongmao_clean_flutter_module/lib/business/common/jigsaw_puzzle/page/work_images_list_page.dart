import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_button.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_switch.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/state_layout.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../quality_service/controller/work_add_plan_order_list_controller.dart';
import '../../quality_service/item/order_list_item_listview.dart';
import '../../quality_service/iview/work_add_plan_iview.dart';
import '../../quality_service/persenter/work_add_plan_order_persenter.dart';
import '../controller/work_images_list_controller.dart';
import '../item/work_circle_item_listview.dart';
import '../iview/work_images_iview.dart';
import '../presenter/jigsaw_puzzle_persenter.dart';
import '../presenter/work_images_list_persenter.dart';

/// 选择已经上传过的项目
class WorkImagesListPage extends StatefulWidget {
  String? position = "";

  WorkImagesListPage({Key? key, this.position = ""}) : super(key: key);

  @override
  _WorkImagesListPageState createState() => _WorkImagesListPageState();
}

class _WorkImagesListPageState extends State<WorkImagesListPage> with BasePageMixin<WorkImagesListPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkImagesListPage> implements WorkImagesIView {
  WorkImagesListPresenter? _presenter;

  final WorkImagesListController _controller = Get.put(WorkImagesListController());

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh(_controller.projectUuid.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore(_controller.projectUuid.value);
  }

  VoidCallback? addProjectListener;

  String title = "选择照片"; //当前项目
  String subTitle = ""; //记录当前的副标题

  @override
  void initState() {
    super.initState();
    _controller.projectName.value = httpConfig.project_name;
    _controller.projectUuid.value = httpConfig.project_uuid;
    _controller.selectedItems.value = [];

    ///监听从原生选择项目的内容
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      setState(() {
        _controller.projectName.value = project_name;
        _controller.projectUuid.value = project_uuid;
        _onRefresh();
      });
    });

    _onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: title,
            centerSubTitle: _controller.projectName.value,
            mainRightImg: 'icon_change',
            onPressed: () {
              BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false});
            },
          ),
          body: MyRefreshListView(
            itemCount: _controller.listTask.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: int.parse(_controller.listTotal.value) > _controller.listTask.length,
            itemBuilder: (_, index) {
              return WorkCircleListView(
                data: _controller.listTask[index],
              );
            },
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
            child: Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText('已经选择${_controller.selectedItems.length}张照片', 15, Colours.base_primary_text_title)),
                InkWell(
                  child: Container(
                    padding: EdgeInsets.only(top: 10, bottom: 10, left: 24, right: 24),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colours.base_primary_blue,
                    ),
                    child: CommonUtils.getSimpleText('确定', 15, Colours.white, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    BoostNavigator.instance.pop(_controller.selectedItems);
                  },
                )
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkImagesListPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }

  @override
  bool get wantKeepAlive => false;
}
