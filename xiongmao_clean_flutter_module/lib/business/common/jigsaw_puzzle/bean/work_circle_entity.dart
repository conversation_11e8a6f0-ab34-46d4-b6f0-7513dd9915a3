import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/work_circle_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/work_circle_entity.g.dart';

@JsonSerializable()
class WorkCircleEntity {
	int? page;
	int? size;
	int? total;
	List<WorkCircleList>? list;
	@JSONField(name: "latest_record_uuid")
	String? latestRecordUuid;

	WorkCircleEntity();

	factory WorkCircleEntity.fromJson(Map<String, dynamic> json) => $WorkCircleEntityFromJson(json);

	Map<String, dynamic> toJson() => $WorkCircleEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkCircleList {
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "job_name")
	String? jobName;
	String? avatar;
	@JSONField(name: "create_time")
	String? createTime;
	List<WorkCircleListList>? list;

	WorkCircleList();

	factory WorkCircleList.fromJson(Map<String, dynamic> json) => $WorkCircleListFromJson(json);

	Map<String, dynamic> toJson() => $WorkCircleListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkCircleListList {
	String? uuid;
	@JSONField(name: "act_type")
	String? actType;
	@JSONField(name: "act_type_name")
	String? actTypeName;
	@JSONField(name: "message_type")
	String? messageType;
	@JSONField(name: "media_url")
	String? mediaUrl;
	@JSONField(name: "pic_thumb")
	String? picThumb;
	@JSONField(name: "origin_media_url")
	String? originMediaUrl;
	@JSONField(name: "video_cover_url")
	String? videoCoverUrl;
	@JSONField(name: "create_time")
	String? createTime;

	WorkCircleListList();

	factory WorkCircleListList.fromJson(Map<String, dynamic> json) => $WorkCircleListListFromJson(json);

	Map<String, dynamic> toJson() => $WorkCircleListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}