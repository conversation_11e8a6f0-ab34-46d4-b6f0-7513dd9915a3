import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../net/http_config.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';

class WebPage extends StatefulWidget {
  String url;
  String? title;
  String? subTitle;
  bool domainM2;

  WebPage({required this.url, this.title, this.subTitle, required this.domainM2});

  @override
  _WebPageState createState() => _WebPageState();
}

class _WebPageState extends State<WebPage> {
  late XmWebController controller;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: widget.title ?? "详情",
        centerSubTitle: widget.subTitle ?? '',
      ),
      body: XmWebView(
        onXmJsMsgCall: (XmJsMsgCall msgCall) {
          var decode = json.decode(msgCall.message);
          print('decode -- $decode');
          var name = decode["name"];
          var data = decode["data"];

          ///回调token给用户
          switch (name) {
            case "getUserToken":
              var token = httpConfig.token ?? "";
              controller.realController.runJavaScript("window.WebViewJavascriptBridgegetUserTokenCallBack('" + token + "')"); //切记这个字符串还得用''引住
              break;

            ///买完保险跳转客户端页面
            case 'backCustomerDetail':
              String? project_uuid = "";

              if (data['project_uuid'] != null) {
                project_uuid = data['project_uuid'];
              } else {
                project_uuid = httpConfig.project_uuid;
              }

              BoostNavigator.instance.pop();
              break;

            ///买完保险跳转客户端页面
            case 'openAgreenTotal':
              int num = data['num'];
              if (1 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111934745828"});
              } else if (2 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955438709"});
              } else if (3 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111951244788"});
              } else if (4 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955502564"});
              } else if (5 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111955518693"});
              } else if (6 == num) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_base_web", "url": "https://m2.jiazhengye.cn/help/listDetail?number=6724111951326572"});
              }
              break;
          }
        },
        controllerSettingBack: (value) {
          controller = value;
          LogUtil.e("object----url---${widget.url ?? ""}");
          controller.loadRequest(Uri.parse(widget.url ?? ""));
        },
        domainM2: widget.domainM2,
      ),
    );
  }
}
