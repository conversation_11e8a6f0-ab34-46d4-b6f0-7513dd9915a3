import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';

import '../../../net/http_config.dart';

/// 定义页面名称
const webPage = "webPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> webRouterMap = {
  webPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String url = map['url'];
          String? title = map['title'];
          String? subTitle = map['subTitle'];

          ///是M2的域名还是啥域名，要做处理
          bool domainM2 = map['domainM2'];

          return WebPage(
            url: url,
            title: title,
            subTitle: subTitle,
            domainM2: domainM2,
          );
        });
  },
};
