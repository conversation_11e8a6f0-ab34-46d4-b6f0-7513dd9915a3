/**
 * 适用人群 JOSN 非默认方案必传
    type
    类型 1部门 2项目 3角色 4岗位
    compare
    eq 等于
    neq 不等于
    contain 包含
    uncontain 不包含
    belong 属于
    unbelong 不属于
 */
class CameraRulesJson {
  String type;
  String compare;
  String value;

  CameraRulesJson({
    required this.type,
    required this.compare,
    required this.value,
  });

  @override
  String toString() {
    return '{type: $type, compare: $compare, value: $value}';
  }

  Map<String, dynamic> toJson() {
    return {
      'type': type,
      'compare': compare,
      'value': value,
    };
  }
}
