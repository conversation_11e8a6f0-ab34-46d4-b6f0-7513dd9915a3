import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/work_post_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/work_post_manager_entity.g.dart';

@JsonSerializable()
class WorkPostManagerEntity {
	int? page;
	int? size;
	int? total;
	List<WorkPostManagerList>? list;

	WorkPostManagerEntity();

	factory WorkPostManagerEntity.fromJson(Map<String, dynamic> json) => $WorkPostManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $WorkPostManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkPostManagerList {
	String? uuid;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "job_salary")
	String? jobSalary;
	@JSONField(name: "is_system")
	int? isSystem;

	WorkPostManagerList();

	factory WorkPostManagerList.fromJson(Map<String, dynamic> json) => $WorkPostManagerListFromJson(json);

	Map<String, dynamic> toJson() => $WorkPostManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}