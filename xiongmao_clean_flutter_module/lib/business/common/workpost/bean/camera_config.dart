class CameraConfig {
  String logoPath; //logo的地址
  int logoWidth; //logo 的宽
  int logoHeight; //logo 的高
  int logoPosition; //logo 的位置
  int scale; //logo 缩放的倍数

  CameraConfig({
    required this.logoPath,
    required this.logoWidth,
    required this.logoHeight,
    required this.logoPosition,
    required this.scale,
  });


  @override
  String toString() {
    return 'CameraConfig{logoPath: $logoPath, logoWidth: $logoWidth, logoHeight: $logoHeight, logoPosition: $logoPosition, scale: $scale}';
  }

  factory CameraConfig.fromJson(Map<String, dynamic> json) {
    return CameraConfig(
      logoPath: json['logoPath'],
      logoWidth: json['logoWidth'],
      logoHeight: json['logoHeight'],
      logoPosition: json['logoPosition'],
      scale: json['scale'],
    );
  }
}
