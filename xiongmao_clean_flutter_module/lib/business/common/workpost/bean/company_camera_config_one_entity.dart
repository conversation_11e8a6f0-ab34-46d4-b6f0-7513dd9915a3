import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/company_camera_config_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/company_camera_config_one_entity.g.dart';

@JsonSerializable()
class CompanyCameraConfigOneEntity {
	String? uuid;
	@JSONField(name: "configuration_name")
	String? configurationName;
	String? introduction;
	@JSONField(name: "save_photo_mobile")
	String? savePhotoMobile;
	@JSONField(name: "open_work_photo")
	String? openWorkPhoto;
	@JSONField(name: "dk_confirm")
	String? dkConfirm;
	@JSONField(name: "dk_switch_camera")
	String? dkSwitchCamera;
	@JSONField(name: "watermark_logo")
	String? watermarkLogo;
	@JSONField(name: "watermark_logo_pos")
	String? watermarkLogoPos;
	@JSONField(name: "watermark_logo_width")
	String? watermarkLogoWidth;
	@JSONField(name: "watermark_logo_height")
	String? watermarkLogoHeight;
	@JSONField(name: "is_show_app")
	String? isShowApp;
	@JSONField(name: "is_see_kq")
	String? isSeeKq;
	@JSONField(name: "is_see_photo")
	String? isSeePhoto;
	@JSONField(name: "is_minimalism")
	String? isMinimalism;
	@JSONField(name: "watermark_logo_scale")
	String? watermarkLogoScale;
	@JSONField(name: "is_default")
	String? isDefault;
	@JSONField(name: "configuration_status")
	String? configurationStatus;
	@JSONField(name: "range_user")
	List<CompanyCameraConfigOneRangeUser>? rangeUser;

	CompanyCameraConfigOneEntity();

	factory CompanyCameraConfigOneEntity.fromJson(Map<String, dynamic> json) => $CompanyCameraConfigOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $CompanyCameraConfigOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CompanyCameraConfigOneRangeUser {
	String? type;
	@JSONField(name: "type_name")
	String? typeName;
	String? compare;
	@JSONField(name: "compare_name")
	String? compareName;
	@JSONField(name: "value_list")
	List<CompanyCameraConfigOneRangeUserValueList>? valueList;

	CompanyCameraConfigOneRangeUser();

	factory CompanyCameraConfigOneRangeUser.fromJson(Map<String, dynamic> json) => $CompanyCameraConfigOneRangeUserFromJson(json);

	Map<String, dynamic> toJson() => $CompanyCameraConfigOneRangeUserToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CompanyCameraConfigOneRangeUserValueList {
	String? id;
	String? name;

	CompanyCameraConfigOneRangeUserValueList();

	factory CompanyCameraConfigOneRangeUserValueList.fromJson(Map<String, dynamic> json) => $CompanyCameraConfigOneRangeUserValueListFromJson(json);

	Map<String, dynamic> toJson() => $CompanyCameraConfigOneRangeUserValueListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}