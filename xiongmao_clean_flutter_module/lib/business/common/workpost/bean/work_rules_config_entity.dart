import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/work_rules_config_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/work_rules_config_entity.g.dart';

@JsonSerializable()
class WorkRulesConfigEntity {
	@JSONField(name: "max_age")
	String? maxAge;
	@JSONField(name: "is_entry_before_today")
	String? isEntryBeforeToday;
	@JSONField(name: "is_upload_not_guilty")
	String? isUploadNotGuilty;
	@JSONField(name: "is_entry_sign")
	String? isEntrySign;
	@JSONField(name: "payroll_date")
	String? payrollDate;
	@JSONField(name: "clock_in_range")
	String? clockInRange;
	@JSONField(name: "is_see_kq")
	String? isSeeKq;
	@JSONField(name: "is_see_photo")
	String? isSeePhoto;
	@JSONField(name: "is_minimalism")
	String? isMinimalism;
	@JSONField(name: "schedule_date")
	String? scheduleDate;
	@JSONField(name: "is_save_photo_mobile")
	String? isSavePhotoMobile;
	@JSONField(name: "is_open_work_photo")
	String? isOpenWorkPhoto;
	@JSONField(name: "is_dk_confirm")
	String? isDkConfirm;
	@JSONField(name: "head_office_mode")
	String? headOfficeMode;
	@JSONField(name: "project_list")
	List<WorkRulesConfigProjectList>? projectList;
	@JSONField(name: "role_access")
	List<WorkRulesConfigRoleAccess>? roleAccess;

	WorkRulesConfigEntity();

	factory WorkRulesConfigEntity.fromJson(Map<String, dynamic> json) => $WorkRulesConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $WorkRulesConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkRulesConfigProjectList {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;

	WorkRulesConfigProjectList();

	factory WorkRulesConfigProjectList.fromJson(Map<String, dynamic> json) => $WorkRulesConfigProjectListFromJson(json);

	Map<String, dynamic> toJson() => $WorkRulesConfigProjectListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class WorkRulesConfigRoleAccess {
	@JSONField(name: "role_id")
	String? roleId;
	@JSONField(name: "access_type")
	String? accessType;

	WorkRulesConfigRoleAccess();

	factory WorkRulesConfigRoleAccess.fromJson(Map<String, dynamic> json) => $WorkRulesConfigRoleAccessFromJson(json);

	Map<String, dynamic> toJson() => $WorkRulesConfigRoleAccessToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}