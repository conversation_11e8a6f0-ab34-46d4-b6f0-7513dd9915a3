import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/company_camera_config_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/company_camera_config_entity.g.dart';

@JsonSerializable()
class CompanyCameraConfigEntity {
	List<CompanyCameraConfigList>? list;

	CompanyCameraConfigEntity();

	factory CompanyCameraConfigEntity.fromJson(Map<String, dynamic> json) => $CompanyCameraConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $CompanyCameraConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CompanyCameraConfigList {
	String? uuid;
	@JSONField(name: "configuration_name")
	String? configurationName;
	String? introduction;
	@JSONField(name: "is_default")
	String? isDefault;

	CompanyCameraConfigList();

	factory CompanyCameraConfigList.fromJson(Map<String, dynamic> json) => $CompanyCameraConfigListFromJson(json);

	Map<String, dynamic> toJson() => $CompanyCameraConfigListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}