import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../iview/work_post_save_iview.dart';

/**
 * 客户管理-编辑-新建岗位
 */
class WorkPostAddEditPagePresenter extends BasePagePresenter<WorkPostSaveIView> with WidgetsBindingObserver {
  //编辑-新建岗位
  Future<dynamic> requestWorkPostSave(String? uuid, String job_name, String job_salary) {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(uuid)) {
      params["uuid"] = "$uuid";
    }
    params["job_name"] = job_name;
    params["job_salary"] = job_salary;
    return requestNetwork<Object>(Method.post, url: HttpApi.WORK_POST_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workSaveStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
