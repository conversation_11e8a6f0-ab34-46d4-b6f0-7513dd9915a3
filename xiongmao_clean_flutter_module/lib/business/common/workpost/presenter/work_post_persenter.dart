import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/work_post_manager_entity.dart';
import '../controller/work_post_manager_page_controller.dart';
import '../iview/work_post_iview.dart';

/**
 * 客户管理-获取列表
 */
class WorkPostManagerPagePresenter extends BasePagePresenter<WorkPostIView> with WidgetsBindingObserver {
  WorkPostManagerPageController controller;

  WorkPostManagerPagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getWorkListManager();
  }

  void loadMore() {
    _page++;
    getWorkListManager();
  }

  //获取岗位列表
  Future<dynamic> getWorkListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["project_uuid"] = httpConfig.project_uuid;
    return requestNetwork<WorkPostManagerEntity>(Method.get, url: HttpApi.GET_WORK_MANAGER_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  //删除非系统的预制岗位
  Future<dynamic> requestDeleteWork(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.DELETE_WORK, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.deleteWork();
    }, onError: (_, __) {
      MyLog.e("========客户管理-删除岗位========");
    });
  }
}
