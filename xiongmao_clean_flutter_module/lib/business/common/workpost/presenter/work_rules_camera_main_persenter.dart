import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../bean/company_camera_config_entity.dart';
import '../controller/work_rules_camera_main_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../iview/work_rules_role_permission_save_iview.dart';

/// 用工规则（企业配置）
class WorkRulesCameraMainPagePresenter extends BasePagePresenter<WorkRulesRolePermissionSaveIView> with WidgetsBindingObserver {
  WorkRulesCameraMainPageController controller;

  WorkRulesCameraMainPagePresenter(this.controller);

  ///获取用户规则
  Future<dynamic> getConfigInfo() {
    var params = <String, String>{};
    return requestNetwork<CompanyCameraConfigEntity>(Method.get, url: HttpApi.COMPANY_CAMERA_CONFIG_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        //拿到角色的配置权限信息
        controller.initMyList(data.list);
        view.viewRefresh();
      }
    }, onError: (_, __) {});
  }

  ///删除企业配置
  Future<dynamic> deleteCompanyConfig(String uuid) {
    var params = <String, dynamic>{};
    params['uuid'] = uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.DELETE_COMPANY_CAMERA_CONFIG, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workRulesSaveStatus();
    }, onError: (_, __) {});
  }

  ///排序企业配置
  Future<dynamic> sortCompanyConfig(String uuid, int sort) {
    var params = <String, dynamic>{};
    params['uuid'] = uuid;
    params['sort'] = "$sort}";
    return requestNetwork<Object>(Method.post, url: HttpApi.SORT_COMPANY_CAMERA_CONFIG, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workRulesSaveStatus();
    }, onError: (_, __) {});
  }
}
