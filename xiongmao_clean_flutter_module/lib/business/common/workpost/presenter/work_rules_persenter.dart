import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/work_post_manager_entity.dart';
import '../controller/work_post_manager_page_controller.dart';
import '../controller/work_rules_controller.dart';
import '../iview/work_post_iview.dart';

/// 用工规则（企业配置）
class WorkRulesPagePresenter extends BasePagePresenter<WorkRulesSaveIView> with WidgetsBindingObserver {
  WorkRulesPageController controller;

  WorkRulesPagePresenter(this.controller);

  //获取用户规则
  Future<dynamic> getWorkRulesInfo() {
    var params = <String, String>{};
    return requestNetwork<WorkRulesConfigEntity>(Method.get, url: HttpApi.WORK_RULES_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateMaxAge(data.maxAge);
        controller.updateSalaryDate(data.payrollDate);
        controller.updateEntryBeforeToday(data.isEntryBeforeToday == "1" ? false : true);
        controller.updateUploadNotGuilty(data.isUploadNotGuilty == "1" ? true : false);
        controller.updateEntrySign(data.isEntrySign == "1" ? true : false);
        controller.updateClockDistance('${data.clockInRange}米');
        controller.updateMyAttendance(data.isSeeKq == "1" ? true : false);
        controller.updateMyPhoto(data.isSeePhoto == "1" ? true : false);
        controller.updateScheduleDate(data.scheduleDate);
        controller.updateTogetherClock('${data.projectList?.length ?? 0}个');
        controller.updateMinimalism(data.isMinimalism == "1" ? true : false);
        controller.updateSavePic(data.isSavePhotoMobile == "1" ? true : false);
        controller.updateOpenWorkCamera(data.isOpenWorkPhoto == "1" ? true : false);
        controller.updateClockReConfirm(data.isDkConfirm == "1" ? true : false);
        controller.initTaskList(data.projectList);
      }
    }, onError: (_, __) {});
  }

  ///保存企业配置
  Future<dynamic> updateWorkRules() {
    var params = <String, String>{};
    //用工年龄上限
    if (!TextUtil.isEmpty(controller.maxAge.toString())) {
      params["max_age"] = controller.maxAge.toString();
    }
    // 允许补入今日之前的入职信息 1是 2否
    params["is_entry_before_today"] = controller.isEntryBeforeToday.value ? "2" : "1";
    //是否拍照上传无犯罪证明 1是 2否
    params["is_upload_not_guilty"] = controller.isUploadNotGuilty.value ? "1" : "2";
    //办理入职时是否需要入职的员工签字确认 1是 2否
    params["is_entry_sign"] = controller.isEntrySign.value ? "1" : "2";
    //薪资日期
    if (!TextUtil.isEmpty(controller.salaryDate.toString())) {
      params["payroll_date"] = controller.salaryDate.toString();
    }
    //打卡的位置范围
    if (!TextUtil.isEmpty(controller.distance.value)) {
      params["clock_in_range"] = controller.distance.value.toString().replaceAll("米", '');
    }
    //是否允许员工查看考勤 1是2否
    params["is_see_kq"] = controller.myAttendance.value ? "1" : "2";
    //是否允许员工查看个人相册
    params["is_see_photo"] = controller.myPhoto.value ? "1" : "2";
    //是否开启极简模式 1是2否
    params["is_minimalism"] = controller.minimalism.value ? "1" : "2";
    //排班日期
    if (!TextUtil.isEmpty(controller.schedule.toString())) {
      params["schedule_date"] = controller.schedule.toString();
    }
    //是否保存相片到手机 1是 2否
    params["is_save_photo_mobile"] = controller.savePic.value ? "1" : "2";
    //是否开启工作拍照 1是 2否'
    params["is_open_work_photo"] = controller.openWorkCamera.value ? "1" : "2";
    //是否开启打卡确认 1是 2否
    params["is_dk_confirm"] = controller.clockReConfirm.value ? "1" : "2";
    return requestNetwork<Object>(Method.post, url: HttpApi.SAVE_WORK_RULES_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workRulesSaveStatus();
    }, onError: (_, __) {});
  }
}
