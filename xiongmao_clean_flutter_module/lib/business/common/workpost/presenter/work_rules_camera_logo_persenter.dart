import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../controller/work_rules_camera_logo_controller.dart';
import '../controller/work_rules_camera_main_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../iview/work_rules_role_permission_save_iview.dart';

/// 用工规则（企业配置）
class WorkRulesCameraLogoPresenter extends BasePagePresenter<WorkRulesRolePermissionSaveIView> with WidgetsBindingObserver {
  WorkRulesCameraLogoController controller;

  WorkRulesCameraLogoPresenter(this.controller);

  ///获取用户规则
  Future<dynamic> getWorkRulesInfo() {
    var params = <String, String>{};
    return requestNetwork<WorkRulesConfigEntity>(Method.get, url: HttpApi.WORK_RULES_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        //拿到角色的配置权限信息
        view.viewRefresh();
      }
    }, onError: (_, __) {});
  }

  ///保存企业配置
  Future<dynamic> updateWorkRules() {
    var params = <String, String>{};
    return requestNetwork<Object>(Method.post, url: HttpApi.SAVE_WORK_RULES_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workRulesSaveStatus();
    }, onError: (_, __) {});
  }
}
