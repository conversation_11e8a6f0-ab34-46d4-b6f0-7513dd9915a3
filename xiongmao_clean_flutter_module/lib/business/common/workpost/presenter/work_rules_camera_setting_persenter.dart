import 'dart:collection';
import 'dart:convert';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../bean/company_camera_config_one_entity.dart';
import '../controller/work_rules_camera_main_controller.dart';
import '../controller/work_rules_camera_setting_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../iview/work_rules_role_permission_save_iview.dart';

/// 企业配置方案
class WorkRulesCameraSettingPagePresenter extends BasePagePresenter<WorkRulesRolePermissionSaveIView> with WidgetsBindingObserver {
  WorkRulesCameraSettingController controller;

  WorkRulesCameraSettingPagePresenter(this.controller);

  ///获取用户规则
  Future<dynamic> getConfigOne() {
    var params = <String, String>{};
    params["uuid"] = controller.uuid.value;
    return requestNetwork<CompanyCameraConfigOneEntity>(Method.get, url: HttpApi.COMPANY_CAMERA_CONFIG_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillData(data);
      }
    }, onError: (_, __) {});
  }

  ///保存企业配置
  Future<dynamic> updateWorkRules(HashMap<String, dynamic> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.SAVE_COMPANY_CAMERA_CONFIG, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.workRulesSaveStatus();
    }, onError: (_, __) {});
  }
}
