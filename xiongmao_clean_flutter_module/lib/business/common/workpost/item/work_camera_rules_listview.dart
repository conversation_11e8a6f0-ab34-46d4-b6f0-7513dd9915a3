import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../res/gaps.dart';
import '../bean/company_camera_config_entity.dart';
import '../bean/company_camera_config_one_entity.dart';
import '../bean/work_post_manager_entity.dart';

class WorkCameraRulesListItem extends StatelessWidget {
  final Function(int) onEdit;
  final Function onDelete;
  CompanyCameraConfigOneRangeUser data;
  int position;

  WorkCameraRulesListItem({
    required this.position,
    required this.data,
    required this.onEdit,
    required this.onDelete,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.0),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText('${position + 1}.${data.typeName}${data.compareName}${data.valueList!.map((e) => e.name).toList().join(',')}', 15, Colours.base_primary_text_title)),
                GestureDetector(
                  onTap: () {
                    onDelete();
                  },
                  child: Image.asset(
                    'assets/images/base/icon_base_del.png',
                    width: 22,
                    height: 22,
                  ),
                ),
              ],
            ),
            Gaps.vGap10,

            ///条件 1
            GestureDetector(
              child: Container(
                margin: const EdgeInsets.only(bottom: 10),
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 6,
                ),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: CommonUtils.getSimpleText('${data.typeName} >', 15, Colours.base_primary_text_title, textAlign: TextAlign.center, overflow: TextOverflow.ellipsis),
              ),
              onTap: () {
                onEdit(0);
              },
            ),

            ///条件 2
            GestureDetector(
              child: Container(
                margin: const EdgeInsets.only(bottom: 10),
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 6,
                ),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: CommonUtils.getSimpleText('${data.compareName} >', 15, Colours.base_primary_text_title, textAlign: TextAlign.center, overflow: TextOverflow.ellipsis),
              ),
              onTap: () {
                onEdit(1);
              },
            ),

            ///条件 3
            GestureDetector(
              child: Container(
                margin: const EdgeInsets.only(bottom: 10),
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 6,
                ),
                width: double.infinity,
                decoration: BoxDecoration(
                  color: Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: CommonUtils.getSimpleText('${data.valueList!.map((e) => e.name).toList().join(',')} >', 15, Colours.base_primary_text_title, textAlign: TextAlign.center, overflow: TextOverflow.ellipsis),
              ),
              onTap: () {
                onEdit(2);
              },
            ),
          ],
        ),
      ),
    );
  }
}
