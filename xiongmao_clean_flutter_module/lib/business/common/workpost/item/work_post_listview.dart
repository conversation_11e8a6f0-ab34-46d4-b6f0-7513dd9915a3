import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/gaps.dart';
import '../bean/work_post_manager_entity.dart';

class WorkPostManagerListItem extends StatelessWidget {
  final Function onDelete;
  final Function onEdit;
  WorkPostManagerList data;

  WorkPostManagerListItem({required this.data, required this.onDelete, required this.onEdit});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.0),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonUtils.getSimpleText("${data.jobName}", 16, Colours.base_primary_text_title),
                  ],
                ),
                Gaps.vGap2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CommonUtils.getSimpleText('标准薪资：${data.jobSalary}', 12, Colors.grey, textAlign: TextAlign.center),
                  ],
                ),
              ],
            )),
            Row(
              children: [
                //是否系统预置岗位 1是2否
                Visibility(
                    visible: data.isSystem == 2,
                    child: GestureDetector(
                      onTap: () {
                        onDelete();
                      },
                      child: Image.asset(
                        'assets/images/base/icon_base_del.png',
                        width: 22,
                        height: 22,
                      ),
                    )),
                Gaps.hGap20,
                GestureDetector(
                  onTap: () {
                    onEdit();
                  },
                  child: Image.asset(
                    'assets/images/base/icon_base_edit.png',
                    width: 22,
                    height: 22,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
