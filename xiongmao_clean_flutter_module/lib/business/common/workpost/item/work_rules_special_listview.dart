import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/gaps.dart';
import '../bean/rules_special_data_entity.dart';
import '../bean/work_post_manager_entity.dart';

class WorkRulesSpecialManagerListItem extends StatelessWidget {
  final Function onDelete;
  final Function onEdit;
  RulesSpecialDataList data;

  WorkRulesSpecialManagerListItem({required this.data, required this.onDelete, required this.onEdit});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        margin: EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(0),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.0),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
          child: Row(
            children: [
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText("${data.userName}", 16, Colours.base_primary_text_title),
                    ],
                  ),
                  Gaps.vGap2,
                  Wrap(
                    children: [
                      CommonUtils.getSimpleText('数据权限：${getBuildName(data)}', 12, Colors.grey, textAlign: TextAlign.left),
                    ],
                  ),
                ],
              )),
              Gaps.hGap2,
              Row(
                children: [
                  //是否系统预置岗位 1是2否
                  GestureDetector(
                    onTap: () {
                      onEdit();
                    },
                    child: Image.asset(
                      'assets/images/base/icon_base_edit.png',
                      width: 22,
                      height: 22,
                    ),
                  ),
                  Gaps.hGap20,
                  GestureDetector(
                    onTap: () {
                      onDelete();
                    },
                    child: Image.asset(
                      'assets/images/base/icon_base_del.png',
                      width: 22,
                      height: 22,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
      onTap: () {
        onEdit();
      },
    );
  }
}

String getBuildName(RulesSpecialDataList data) {
  StringBuffer stringBuffer = StringBuffer();

  if (data.departmentList != null && data.departmentList!.isNotEmpty) {
    for (int i = 0; i < data.departmentList!.length; i++) {
      stringBuffer.write(data.departmentList![i].departmentName);
      if (i < data.departmentList!.length - 1) {
        stringBuffer.write(","); // 在名称后添加逗号，除了最后一个
      }
    }
  }

  return stringBuffer.toString();
}
