import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/gaps.dart';
import '../bean/company_camera_config_entity.dart';
import '../bean/work_post_manager_entity.dart';

class WorkCameraMainListItem extends StatelessWidget {
  final Function onDelete;
  final Function onEdit;
  CompanyCameraConfigList data;

  WorkCameraMainListItem({Key? key, required this.data, required this.onDelete, required this.onEdit}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.0),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                if (data.isDefault == "1") {
                  return;
                }
                onDelete();
              },
              child: LoadImage(
                data.isDefault == "1" ? 'common/icon_approve_def_delete' : 'common/icon_approve_delete',
                width: 22,
                height: 22,
              ),
            ),
            Gaps.hGap10,
            Expanded(
                child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    CommonUtils.getSimpleText("${data.configurationName}", 16, Colours.base_primary_text_title),
                  ],
                ),
                Gaps.vGap2,
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    CommonUtils.getSimpleText('${data.introduction}', 12, Colors.grey, textAlign: TextAlign.center),
                  ],
                ),
              ],
            )),
            Row(
              children: [
                GestureDetector(
                  onTap: () {
                    onEdit();
                  },
                  child: Image.asset(
                    'assets/images/base/icon_base_edit.png',
                    width: 22,
                    height: 22,
                  ),
                ),
                Gaps.hGap20,
                LoadImage(
                  data.isDefault == "1" ? "icon_fun_un" : "icon_fun",
                  width: 22,
                  height: 22,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
