import 'dart:async';
import 'dart:ui';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_advanced_seekbar/flutter_advanced_seekbar.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/camera_config.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/role_custom_data.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_rules_camera_logo_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_rules_camera_logo_persenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/my_app_bar.dart';
import '../iview/work_rules_role_permission_save_iview.dart';

/// 相机等配置
class WorkCameraCompanyLogoPage extends StatefulWidget {
  CameraConfig? config;

  WorkCameraCompanyLogoPage({Key? key, this.config}) : super(key: key);

  @override
  _WorkCameraCompanyLogoPageState createState() => _WorkCameraCompanyLogoPageState();
}

class _WorkCameraCompanyLogoPageState extends State<WorkCameraCompanyLogoPage> with BasePageMixin<WorkCameraCompanyLogoPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkCameraCompanyLogoPage> implements WorkRulesRolePermissionSaveIView {
  WorkRulesCameraLogoPresenter? _presenter;

  final WorkRulesCameraLogoController _controller = WorkRulesCameraLogoController();

  @override
  void initState() {
    super.initState();
    if (widget.config != null) {
      _controller.fillData(widget.config!);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: '企业品牌图预览',
            actionName: '确定',
            onPressed: () {
              ///这里是保存 把图片上传到七牛云 然后把当前的参数带回去
              if (_controller.localPath.value.contains("jiazhengye")) {
                _uploadlogo(_controller.localPath.value);
                return;
              }
              _uploadImageToQiNiu(_controller.localPath.value);
            },
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: Stack(
            children: [
              ///后面高斯模糊的背景图
              ImageFiltered(
                imageFilter: ImageFilter.blur(sigmaX: 4, sigmaY: 4),
                child: Container(
                  color: Colours.base_primary_text_body,
                ),
              ),

              ///选择的图片可设置方向 （右上）
              Visibility(
                visible: true,
                child: Positioned(
                  top: 0,
                  right: 0,
                  child: Transform.scale(
                    alignment: Alignment.topRight, // 从右上角放大
                    // 根据图片尺寸动态计算缩放比例
                    scale: _calculateImageScale(),
                    child: LoadImage(
                      _controller.localPath.value,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),

              ///选择的图片可设置方向 （左上）
              Visibility(
                visible: true,
                child: Positioned(
                  top: 0,
                  left: 0,
                  child: Transform.scale(
                    alignment: Alignment.topLeft,
                    // 根据图片尺寸动态计算缩放比例
                    scale: _calculateImageScale(),
                    child: LoadImage(
                      _controller.localPath.value,
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ),

              ///选择的图片可设置方向 （中间）
              Visibility(
                visible: true,
                child: Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  bottom: 0,
                  child: Align(
                    alignment: Alignment.center,
                    child: Transform.scale(
                      alignment: Alignment.center,
                      // 根据图片尺寸动态计算缩放比例
                      scale: _calculateImageScale(),
                      child: LoadImage(
                        _controller.localPath.value,
                        fit: BoxFit.contain,
                      ),
                    ),
                  ),
                ),
              ),

              ///水印的原图的位置(固定 左下)
              Positioned(
                bottom: 0,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Visibility(
                      visible: true,
                      child: Positioned(
                        child: Transform.scale(
                          alignment: Alignment.bottomLeft, // 从左下角放大
                          // 根据图片尺寸动态计算缩放比例
                          scale: _calculateImageScale(),
                          child: LoadImage(
                            _controller.localPath.value,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.only(right: 30, left: 6, bottom: 6),
                      color: const Color(0x30000000),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              CommonUtils.getSimpleText('00:00', 40, Colours.white),
                              Gaps.hGap4,
                              CommonUtils.getSimpleText('00:00\n周五', 20, Colours.white, height: 1),
                            ],
                          ),
                          Row(
                            children: [
                              LoadImage(
                                'common/icon_camera_address',
                                width: 10,
                                height: 10,
                              ),
                              Gaps.hGap2,
                              CommonUtils.getSimpleText('北京市海淀区上地东路', 14, Colours.white, overflow: TextOverflow.ellipsis)
                            ],
                          ),
                          Row(
                            children: [
                              LoadImage(
                                'common/icon_camera_safe',
                                width: 10,
                                height: 10,
                              ),
                              Gaps.hGap2,
                              CommonUtils.getSimpleText('熊猫清洁云已验证照片真实性', 14, Colours.white, overflow: TextOverflow.ellipsis)
                            ],
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),

              ///水印防伪码的位置(固定 右下)
              Positioned(
                bottom: 0,
                right: 0,
                child: Padding(
                  padding: const EdgeInsets.only(
                    right: 4,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      CommonUtils.getSimpleText('熊猫清洁云', 10, Colours.white, overflow: TextOverflow.ellipsis),
                      Gaps.vGap2,
                      LoadImage(
                        'common/icon_camera_true_time',
                        width: 50,
                        height: 14,
                      ),
                      CommonUtils.getSimpleText('防伪码 ${DateTime.now().millisecondsSinceEpoch}', 8, Colours.white, overflow: TextOverflow.ellipsis)
                    ],
                  ),
                ),
              ),
            ],
          ),
          bottomNavigationBar: Container(
            height: 90,
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colours.transparent,
                      border: Border.all(color: Colours.base_primary, width: 1),
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    child: CommonUtils.getSimpleText(TextUtil.isEmpty(_controller.localPath.value) ? '上传图片' : '重新选图', 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                  onTap: () async {
                    ///打开相册选择图片
                    List<Media> pickerPaths = await ImagePickers.pickerPaths(
                      galleryMode: GalleryMode.image,
                      selectCount: 1, // 剩余可选图片数量
                      showGif: false,
                      compressSize: 100, // 内置压缩功能，单位为 KB
                    );
                    print('选择玩手机-${pickerPaths[0].path}');
                    _controller.positionPhoto.value = 2;
                    _controller.localPath.value = (pickerPaths[0].path!);
                    // 获取本地图片宽高
                    final file = File(pickerPaths[0].path!);
                    final bytes = await file.readAsBytes();
                    ui.decodeImageFromList(bytes, (image) {
                      // 保存原始图片尺寸，不进行裁剪
                      _controller.logoWidth.value = image.width;
                      _controller.logoHeight.value = image.height;

                      // 设置统一的初始缩放值，让所有位置显示大小一致
                      // 设置为60%，这样用户可以向上调整到100%，也可以向下调整到30%
                      _controller.scalePhoto.value = 60;
                    });
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colours.transparent,
                      border: Border.all(color: Colours.base_primary, width: 1),
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    child: CommonUtils.getSimpleText('调整位置', 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    showModalBottomSheet(
                        context: context,
                        backgroundColor: Colors.transparent,
                        builder: (BuildContext context) {
                          return BrnCommonActionSheet(
                            actions: _controller.actions,
                            clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                              //水印LOGO图片位置 1左上 2左下 3居中 4右上 有logo则必传
                              switch (actionEle.title) {
                                case '左上':
                                  _controller.positionPhoto.value = 1;
                                  break;
                                case '左下':
                                  _controller.positionPhoto.value = 2;
                                  break;
                                case '居中':
                                  _controller.positionPhoto.value = 3;
                                  break;
                                case '右上':
                                  _controller.positionPhoto.value = 4;
                                  break;
                              }
                            },
                          );
                        });
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colours.transparent,
                      border: Border.all(color: Colours.base_primary, width: 1),
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    child: CommonUtils.getSimpleText('调整大小', 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    BrnBottomPicker.show(context,
                        showTitle: false,
                        contentWidget: Container(
                            height: 200,
                            padding: const EdgeInsets.fromLTRB(10, 0, 10, 0),
                            child: Container(
                              height: 50,
                              padding: const EdgeInsets.symmetric(horizontal: 15, vertical: 10),
                              child: AdvancedSeekBar(
                                const Color(0xffeeeff3), // 背景条的颜色
                                16, // 圆点默认大小
                                defaultProgress: _controller.scalePhoto.value,
                                Colors.blue,
                                // 圆点的颜色
                                fillProgress: true,
                                // 完成的进度是否标记为圆点的颜色
                                seekBarStarted: () {},
                                seekBarProgress: (v) {
                                  ///这里变化的同时，要动态的去调整本地选择图片的狂高大小
                                  _controller.scalePhoto.value = v < 30 ? 30 : v;
                                },
                                seekBarFinished: (v) {
                                  // 完成seek回调
                                  print('完成seek回调 $v');
                                },
                              ),
                            )));
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: EdgeInsets.symmetric(vertical: 10, horizontal: 4),
                    decoration: BoxDecoration(
                      color: Colours.transparent,
                      border: Border.all(color: Colours.base_primary, width: 1),
                      borderRadius: BorderRadius.circular(6.0),
                    ),
                    child: CommonUtils.getSimpleText('重置', 16, Colours.base_primary, textAlign: TextAlign.center),
                  ),
                  onTap: () {
                    _controller.localPath.value = '';
                    _controller.scalePhoto.value = 50;
                  },
                )),
              ],
            ),
          ),
        ));
  }

  /// 单张图片上传到七牛云
  Future<void> _uploadImageToQiNiu(String filePath) async {
    BrnToast.show("图片保存中", context);
    QiNiuUtils(filePath, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, sendProgressCallback: (progress) {
      print("第 1 张图片上传进度: $progress%");
    }, successCallback: (keyUrl, hashUrl) {
      print("第 1 张图片上传成功--$keyUrl");
      _uploadlogo(keyUrl!);
    }, errorCallback: (error) {
      print("第 1 张图片上传失败--$error");
    }).upload();
  }

  void _uploadlogo(String keyUrl) {
    BoostNavigator.instance.pop(CameraConfig(
      logoPath: keyUrl ?? '',
      logoWidth: _controller.logoWidth.value,
      logoHeight: _controller.logoHeight.value,
      logoPosition: _controller.positionPhoto.value,
      scale: _controller.scalePhoto.value,
    ));
  }

  /// 根据图片尺寸动态计算缩放比例
  /// 目标：滑动到100%时就是合适的显示大小，不能再大了
  /// 小图片最大缩放不超过0.67倍，大图片基础缩放0.15倍最大0.4倍
  double _calculateImageScale({double minScale = 0.05}) {
    int imageWidth = _controller.logoWidth.value;
    int imageHeight = _controller.logoHeight.value;
    double userScale = _controller.scalePhoto.value / 100.0; // 用户设置的缩放比例 (0.3-1.0)

    // 如果图片尺寸未获取到，使用默认缩放
    if (imageWidth == 0 || imageHeight == 0) {
      return userScale * 0.3;
    }

    // 计算图片的最大尺寸
    int maxDimension = imageWidth > imageHeight ? imageWidth : imageHeight;

    double maxUserScale; // 100%时的最大缩放比例

    if (maxDimension <= 150) {
      // 小图片：100%时最大0.5倍
      maxUserScale = 0.5;
    } else if (maxDimension >= 1000) {
      // 大图片（如1080*1440）：100%时最大0.15倍，防止占满屏幕
      maxUserScale = 0.15;
    } else {
      // 中等图片：根据尺寸线性插值计算最大缩放
      // 150-1000之间，最大缩放从0.5线性减少到0.15
      double ratio = (maxDimension - 150.0) / (1000.0 - 150.0);
      maxUserScale = 0.5 - (ratio * 0.35); // 从0.5减少到0.15
    }

    // 用户缩放比例映射到实际缩放范围
    // userScale范围是0.3-1.0，映射到minScale-maxUserScale
    double actualScale = minScale + (userScale - 0.3) / 0.7 * (maxUserScale - minScale);

    // 确保不超过最小和最大限制
    actualScale = actualScale.clamp(minScale, maxUserScale);

    print('图片缩放比例: $actualScale');
    return actualScale;
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesCameraLogoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;

  @override
  workRulesSaveStatus() {}

  @override
  viewRefresh() {
    setState(() {});
  }
}
