import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_rules_camera_setting_persenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/custom_input_dialog.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../company/bean/department_company_entity.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../staff/bean/permission_entity.dart';
import '../bean/camera_config.dart';
import '../bean/company_camera_config_one_entity.dart';
import '../controller/work_rules_camera_setting_controller.dart';
import '../item/work_camera_rules_listview.dart';
import '../iview/work_rules_role_permission_save_iview.dart';

/// 相机添加/配置方案
class WorkCameraSettingPage extends StatefulWidget {
  String uuid = "";

  WorkCameraSettingPage({Key? key, required this.uuid}) : super(key: key);

  @override
  _WorkCameraSettingPageState createState() => _WorkCameraSettingPageState();
}

class _WorkCameraSettingPageState extends State<WorkCameraSettingPage> with BasePageMixin<WorkCameraSettingPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkCameraSettingPage> implements WorkRulesRolePermissionSaveIView {
  WorkRulesCameraSettingPagePresenter? _presenter;

  final WorkRulesCameraSettingController _controller = WorkRulesCameraSettingController();

  ///监听滑动
  final ScrollController _scrollController = ScrollController();

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: Duration(milliseconds: 300), // 设置动画持续时间
          curve: Curves.easeInOut, // 设置动画曲线
        );
      }
    });
  }

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid;
    if (!TextUtil.isEmpty(_controller.uuid.value)) {
      ///如果有uuid 获取以前原先的配置详情
      _presenter?.getConfigOne();
    } else {
      ///说明是新建，那么就执行 addRules
      _controller.addInItRules();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: (TextUtil.isEmpty(widget.uuid)) ? '添加配置方案' : '编辑配置方案',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => SingleChildScrollView(
            controller: _scrollController,
            child: Column(
              children: [
                ///基础设置
                Container(
                  color: Colors.white,
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        child: CommonUtils.getSimpleText('基础设置', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextInputFormItem(
                        isRequire: true,
                        title: "方案名称",
                        hint: "请输入",
                        controller: _controller.configName.value,
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextInputFormItem(
                        isRequire: true,
                        title: "描述",
                        hint: "请输入",
                        controller: _controller.introduction.value,
                      ),
                      Gaps.lineLeftMargin,
                      Container(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonUtils.getSimpleText(
                                  '启用',
                                  16,
                                  Colours.base_primary_text_title,
                                ),
                              ],
                            )),
                            Obx(() => BrnSwitchButton(
                                  size: Size(40, 20),
                                  value: _controller.isOpenStatus.value,
                                  // 使用新变量来表示Switch的状态
                                  onChanged: (bool value) {
                                    // 更新Switch的状态
                                    _controller.isOpenStatus.value = value;
                                  },
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                ///相机设置
                Container(
                  color: Colors.white,
                  width: double.infinity,
                  margin: EdgeInsets.only(bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        child: CommonUtils.getSimpleText('相机设置', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextSelectFormItem(
                        title: "保存相片到手机",
                        value: _controller.savePhotoStr.value,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) {
                                return BrnCommonActionSheet(
                                  actions: _controller.actions,
                                  clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                    _controller.savePhotoStr.value = actionEle.title;
                                    _controller.savePhoto.value = index;
                                  },
                                );
                              });
                        },
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextSelectFormItem(
                        title: "开启“工作拍照”",
                        value: _controller.openWorkPhotoStr.value,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) {
                                return BrnCommonActionSheet(
                                  actions: _controller.actions,
                                  clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                    _controller.openWorkPhotoStr.value = actionEle.title;
                                    _controller.openWorkPhoto.value = index;
                                  },
                                );
                              });
                        },
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextSelectFormItem(
                        title: "打卡时二次确认",
                        value: _controller.dkConfigStr.value,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) {
                                return BrnCommonActionSheet(
                                  actions: _controller.actions,
                                  clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                    _controller.dkConfigStr.value = actionEle.title;
                                    _controller.dkConfig.value = index;
                                  },
                                );
                              });
                        },
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextSelectFormItem(
                        title: "打卡时切换摄像头",
                        value: _controller.cameraConfigStr.value,
                        onTap: () {
                          showModalBottomSheet(
                              context: context,
                              backgroundColor: Colors.transparent,
                              builder: (BuildContext context) {
                                return BrnCommonActionSheet(
                                  actions: _controller.actionsCamera,
                                  clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                    _controller.cameraConfigStr.value = actionEle.title;
                                    _controller.cameraConfig.value = index;
                                  },
                                );
                              });
                        },
                      ),
                      Gaps.lineLeftMargin,
                      BrnTextSelectFormItem(
                        title: "显示企业品牌",
                        value: (_controller.companyInfo.value == null) ? '未设置' : '已设置',
                        onTap: () {
                          BoostNavigator.instance.push('WorkCameraCompanyLogoPage', arguments: {'company_info': _controller.companyInfo.value}).then((value) {
                            if (value is CameraConfig) {
                              _controller.companyInfo.value = value;
                            }
                          });
                        },
                      ),
                      Gaps.lineLeftMargin,
                      Container(
                        padding: const EdgeInsets.only(left: 20, right: 20, top: 10, bottom: 10),
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonUtils.getSimpleText(
                                  '显示APP信息',
                                  16,
                                  Colours.base_primary_text_title,
                                )
                              ],
                            )),
                            Obx(() => BrnSwitchButton(
                                  size: Size(40, 20),
                                  value: _controller.isShowAppInfo.value,
                                  // 使用新变量来表示Switch的状态
                                  onChanged: (bool value) {
                                    // 更新Switch的状态
                                    _controller.isShowAppInfo.value = value;
                                  },
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                ///功能开关
                Container(
                  color: Colors.white,
                  width: double.infinity,
                  margin: const EdgeInsets.only(bottom: 10),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
                        child: CommonUtils.getSimpleText('功能开关', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      ),
                      Gaps.lineLeftMargin,
                      Container(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonUtils.getSimpleText(
                                  '允许查看“我的考勤”',
                                  16,
                                  Colours.base_primary_text_title,
                                )
                              ],
                            )),
                            Obx(() => BrnSwitchButton(
                                  size: Size(40, 20),
                                  value: _controller.isSeekKQ.value,
                                  // 使用新变量来表示Switch的状态
                                  onChanged: (bool value) {
                                    // 更新Switch的状态
                                    _controller.isSeekKQ.value = value;
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.lineLeftMargin,
                      Container(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonUtils.getSimpleText(
                                  '允许查看“个人相册”',
                                  16,
                                  Colours.base_primary_text_title,
                                )
                              ],
                            )),
                            Obx(() => BrnSwitchButton(
                                  size: Size(40, 20),
                                  value: _controller.isSeekPhoto.value,
                                  // 使用新变量来表示Switch的状态
                                  onChanged: (bool value) {
                                    // 更新Switch的状态
                                    _controller.isSeekPhoto.value = value;
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.lineLeftMargin,
                      Container(
                        padding: const EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                CommonUtils.getSimpleText(
                                  '保洁员端极简模式',
                                  16,
                                  Colours.base_primary_text_title,
                                ),
                                CommonUtils.getSimpleText('极简模式下，保洁员端只能拍照，没有待办等', 14, Colors.grey),
                              ],
                            )),
                            Obx(() => BrnSwitchButton(
                                  size: Size(40, 20),
                                  value: _controller.isMinimalism.value,
                                  // 使用新变量来表示Switch的状态
                                  onChanged: (bool value) {
                                    // 更新Switch的状态
                                    _controller.isMinimalism.value = value;
                                  },
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                ///适用人群
                Visibility(
                  visible: !_controller.isDefault.value,
                  child: Container(
                    color: Colors.white,
                    child: Column(
                      children: [
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 16,
                          ),
                          child: Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  CommonUtils.getSimpleText('适用人群', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                  CommonUtils.getSimpleText('需同时满足如下条件', 14, Colours.base_primary_text_title),
                                ],
                              )),
                              InkWell(
                                child: CommonUtils.getSimpleText('+添加条件', 16, Colours.base_primary),
                                onTap: () {
                                  if (_controller.rangeUser.length >= 20) {
                                    Toast.show("最多添加20行数据");
                                    return;
                                  }
                                  _controller.addInItRules();
                                  _scrollToBottom();
                                },
                              ),
                            ],
                          ),
                        ),
                        Gaps.lineLeftMargin,
                        ListView.builder(
                            physics: const NeverScrollableScrollPhysics(),
                            itemCount: _controller.rangeUser.length,
                            shrinkWrap: true,
                            itemBuilder: (_, index) {
                              return WorkCameraRulesListItem(
                                position: index,
                                data: _controller.rangeUser[index],
                                onEdit: (action) {
                                  var rulesData = _controller.rangeUser[index];

                                  ///条件 2 要去选择内容
                                  if (action == 2) {
                                    ///在这里处理哈，这里有段逻辑是这样的， 如果条件是岗位/职务描述，那么就弹出输入框，让输入内容并改变内容，如果是部门（等于、不等于 *单选｜ 属于、不属于 *多选），如果是项目（等于、不等于 *单选｜ 属于、不属于 *多选），如果是角色（等于、不等于 *单选｜ 属于、不属于 *多选）
                                    handlerConditionThree(rulesData);
                                  } else {
                                    showModalBottomSheet(
                                        context: context,
                                        backgroundColor: Colors.transparent,
                                        builder: (BuildContext context) {
                                          return BrnCommonActionSheet(
                                            actions: action == 0
                                                ? _controller.rulesOne
                                                : (rulesData.type == '4')
                                                    ? _controller.rulesTwo
                                                    : _controller.rulesThree,
                                            clickCallBack: (int index, BrnCommonActionSheetItem actionEle) {
                                              if (action == 0) {
                                                rulesData.typeName = actionEle.title;
                                                rulesData.type = '${index + 1}';
                                              } else {
                                                rulesData.compareName = actionEle.title;
                                                rulesData.compare = _controller.getKeyByValue(rulesData.compareName!);
                                              }

                                              ///切换了这里，需要把数据中的条件 3 清空
                                              rulesData.valueList = [];
                                              _controller.rangeUser.refresh();
                                            },
                                          );
                                        });
                                  }
                                },
                                onDelete: () {
                                  _controller.rangeUser.removeAt(index);
                                  _controller.rangeUser.refresh();
                                },
                              );
                            })
                      ],
                    ),
                  ),
                ),

                Gaps.vGap10,
              ],
            ),
          )),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
        child: BrnBigMainButton(
          bgColor: Colours.base_primary_green,
          title: '提交',
          onTap: () {
            if (TextUtil.isEmpty(_controller.configName.value.text)) {
              Toast.show("请输入方案名称");
              return;
            }
            if (TextUtil.isEmpty(_controller.introduction.value.text)) {
              Toast.show("请输入描述");
              return;
            }
            var params = HashMap<String, dynamic>();
            if (!TextUtil.isEmpty(_controller.uuid.value)) {
              params['uuid'] = _controller.uuid.value;
            }
            //方案名称 configuration_name
            params['configuration_name'] = _controller.configName.value.text;
            //简介描述 introduction
            params['introduction'] = _controller.introduction.value.text;
            //状态是否开启 configuration_status 状态 1启用 2禁用
            params['configuration_status'] = _controller.isOpenStatus.value ? '1' : '2';
            //保存相片到手机 save_photo_mobile  1 默认开启允许用户设置 2默认关闭允许用户设置 3始终开启 4始终关闭
            params['save_photo_mobile'] = '${_controller.savePhoto.value + 1}';
            //开启工作拍照 open_work_photo 开启工作拍照 1 默认开启允许用户设置 2默认关闭允许用户设置 3始终开启 4始终关闭
            params['open_work_photo'] = '${_controller.openWorkPhoto.value + 1}';
            //开启打卡确认 dk_confirm 开启工作拍照 1 默认开启允许用户设置 2默认关闭允许用户设置 3始终开启 4始终关闭
            params['dk_confirm'] = '${_controller.dkConfig.value + 1}';
            //开启工作拍照 dk_switch_camera 打卡时切换摄像头 1默认前置，用户可自选 2默认后置，用户可自选 3强制用前置 4强制用后置
            params['dk_switch_camera'] = '${_controller.cameraConfig.value + 1}';
            //是否显示APP信息 1是 2否 is_show_app
            params['is_show_app'] = _controller.isShowAppInfo.value ? '1' : '2';
            //是否允许员工查看考勤 1是2否 is_see_kq
            params['is_see_kq'] = _controller.isSeekKQ.value ? '1' : '2';
            //是否允许员工查看个人相册 1是2否 is_see_photo
            params['is_see_photo'] = _controller.isSeekPhoto.value ? '1' : '2';
            //是否开启极简模式 1是2否 is_minimalism
            params['is_minimalism'] = _controller.isMinimalism.value ? '1' : '2';
            if (_controller.companyInfo.value != null) {
              //watermark_logo 水印的 logo
              params['watermark_logo'] = _controller.companyInfo.value!.logoPath;
              //watermark_logo_width 的宽
              params['watermark_logo_width'] = _controller.companyInfo.value!.logoWidth.toString();
              //watermark_logo_height 的高
              params['watermark_logo_height'] = _controller.companyInfo.value!.logoHeight.toString();
              //watermark_logo_scale 展示的大小比例
              params['watermark_logo_scale'] = '${_controller.companyInfo.value!.scale}';
              //watermark_logo_pos 的位置
              params['watermark_logo_pos'] = '${_controller.companyInfo.value!.logoPosition}';
            }

            ///range_user 规则组 天才的产品想的功能 如果可以请💥💥
            if (!_controller.isDefault.value && _controller.rangeUser.isNotEmpty) {
              params['range_user'] = json.encode(_controller.convertRulesData());
            }
            _presenter?.updateWorkRules(params);
          },
        ),
      ),
    );
  }

  ///处理条件三的内容
  ///如果条件是岗位/职务描述，那么就弹出输入框，让输入内容并改变内容，如果是部门（等于、不等于 *单选｜ 属于、不属于 *多选），如果是项目（等于、不等于 *单选｜ 属于、不属于 *多选），如果是角色（等于、不等于 *单选｜ 属于、不属于 *多选）
  void handlerConditionThree(CompanyCameraConfigOneRangeUser rulesData) {
    switch (rulesData.typeName) {
      case '部门':
        handlerDepartment(rulesData);
        break;
      case '项目':
        handlerProject(rulesData);
        break;
      case '角色':
        handlerRole(rulesData);
        break;
      case '岗位/职务描述':
        handlerWorkJob(rulesData);
        break;
    }
  }

  ///部门的处理
  void handlerDepartment(CompanyCameraConfigOneRangeUser rulesData) {
    ///默认是 false
    bool multiple = false;
    if (rulesData.compareName == '属于' || rulesData.compareName == '不属于') {
      multiple = true;
    }
    BoostNavigator.instance.push('companyChoiceAdministrationPage', arguments: {'multiple': multiple, "choice_uuids": rulesData.valueList!.map((e) => e.id ?? '').toList()}).then((value) {
      if (value != null) {
        List<CompanyCameraConfigOneRangeUserValueList> valueList = [];

        if (multiple) {
          List<DepartmentCompanyList> departmentCompanyList = value as List<DepartmentCompanyList>;
          valueList = departmentCompanyList.map((department) {
            return CompanyCameraConfigOneRangeUserValueList()
              ..id = department.uuid
              ..name = department.departmentName;
          }).toList();
        } else {
          DepartmentCompanyList departmentCompanyList = value as DepartmentCompanyList;
          valueList.add(CompanyCameraConfigOneRangeUserValueList()
            ..id = departmentCompanyList.uuid
            ..name = departmentCompanyList.departmentName);
        }

        rulesData.valueList = valueList;
        _controller.rangeUser.refresh();
      }
    });
  }

  ///项目的处理
  void handlerProject(CompanyCameraConfigOneRangeUser rulesData) {
    ///默认是 false
    bool multiple = false;
    if (rulesData.compareName == '属于' || rulesData.compareName == '不属于') {
      multiple = true;
    }

    BoostNavigator.instance.push('ProjectManagerPage', arguments: {'multiple': multiple, 'isSelected': true, "choice_uuids": rulesData.valueList!.map((e) => e.id ?? '').toList()}).then((value) {
      if (value != null) {
        List<CompanyCameraConfigOneRangeUserValueList> valueList = [];

        if (multiple) {
          List<ProjectManagerList> projectList = value as List<ProjectManagerList>;
          valueList = projectList.map((department) {
            return CompanyCameraConfigOneRangeUserValueList()
              ..id = department.uuid
              ..name = department.projectShortName;
          }).toList();
        } else {
          ProjectManagerList departmentCompanyList = value as ProjectManagerList;
          valueList.add(CompanyCameraConfigOneRangeUserValueList()
            ..id = departmentCompanyList.uuid
            ..name = departmentCompanyList.projectShortName);
        }

        rulesData.valueList = valueList;
        _controller.rangeUser.refresh();
      }
    });
  }

  ///酵色的处理
  void handlerRole(CompanyCameraConfigOneRangeUser rulesData) {
    ///默认是 false
    bool multiple = false;
    if (rulesData.compareName == '属于' || rulesData.compareName == '不属于') {
      multiple = true;
    }
    BoostNavigator.instance.push('RolePage', arguments: {
      'multiple': multiple,
      "choice_uuids": rulesData.valueList!.map((e) => e.id ?? '').toList(),
    }).then((value) {
      if (value != null) {
        List<CompanyCameraConfigOneRangeUserValueList> valueList = [];

        if (multiple) {
          List<PermissionData> projectList = value as List<PermissionData>;
          valueList = projectList.map((department) {
            return CompanyCameraConfigOneRangeUserValueList()
              ..id = department.id
              ..name = department.name;
          }).toList();
        } else {
          PermissionData departmentCompanyList = value as PermissionData;
          valueList.add(CompanyCameraConfigOneRangeUserValueList()
            ..id = departmentCompanyList.id
            ..name = departmentCompanyList.name);
        }
        rulesData.valueList = valueList;
        _controller.rangeUser.refresh();
      }
    });
  }

  ///岗位的处理 最简单 点击的时候弹窗 显示内容
  void handlerWorkJob(CompanyCameraConfigOneRangeUser rulesData) {
    CustomInputDialog(
      context: context,
      title: '请输入信息',
      inputText: rulesData.valueList?.first.name ?? '',
      onConfirmed: (input) {
        // 处理用户输入
        print('用户输入: $input');
        var data = CompanyCameraConfigOneRangeUserValueList();
        data.id = input;
        data.name = input;
        rulesData.valueList = [data];
        _controller.rangeUser.refresh();
      },
    ).show();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesCameraSettingPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workRulesSaveStatus() {
    CommonUtils.finishDelayedPage("操作成功");
  }

  @override
  viewRefresh() {
    setState(() {});
  }

  @override
  void dispose() {
    super.dispose();
    _scrollController.dispose(); // 释放控制器
  }
}
