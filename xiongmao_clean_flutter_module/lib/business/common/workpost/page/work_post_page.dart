import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../item/work_post_listview.dart';
import '../iview/work_post_iview.dart';
import '../presenter/work_post_persenter.dart';

/**
 * 岗位管理
 */
class WorkPostPage extends StatefulWidget {
  @override
  _WorkPostPageState createState() => _WorkPostPageState();
}

//集成分类 然后实现使用
class _WorkPostPageState extends State<WorkPostPage> with BasePageMixin<WorkPostPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkPostPage> implements WorkPostIView {
  WorkPostManagerPagePresenter? _presenter;

  List<WorkPostManagerList> datas = [];

  final WorkPostManagerPageController _controller = WorkPostManagerPageController();

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '岗位管理',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            padding: const EdgeInsets.all(10.0),
            hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
            itemBuilder: (_, index) {
              // return WorkPostManagerListItem(provider.list[index], provider, _player, index);
              return WorkPostManagerListItem(
                  data: _controller.list[index],
                  onDelete: () {
                    _presenter?.requestDeleteWork(_controller.list[index].uuid ?? "");
                  },
                  onEdit: () {
                    BoostNavigator.instance.push(
                      "addWorkPost",
                      arguments: {"uuid": _controller.list[index].uuid, "work_name": _controller.list[index].jobName, "work_salary": _controller.list[index].jobSalary},
                    ).then((value) => {_onRefresh()});
                  });
            },
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '添加岗位',
          onTap: () {
            BoostNavigator.instance.push("addWorkPost").then((value) => _onRefresh());
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkPostManagerPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  deleteWork() {
    _onRefresh();
  }

  @override
  bool get wantKeepAlive => false;
}
