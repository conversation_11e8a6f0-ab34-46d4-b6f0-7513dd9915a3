import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/role_custom_data.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_app_bar.dart';
import '../controller/work_rules_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../iview/work_post_save_iview.dart';
import '../iview/work_rules_role_permission_save_iview.dart';
import '../iview/work_rules_save_iview.dart';
import '../presenter/work_rules_persenter.dart';
import '../presenter/work_rules_role_permission_persenter.dart';

/// 各角色权限
class RolePermissionPage extends StatefulWidget {
  RolePermissionPage({Key? key}) : super(key: key);

  @override
  _RolePermissionPageState createState() => _RolePermissionPageState();
}

class _RolePermissionPageState extends State<RolePermissionPage> with BasePageMixin<RolePermissionPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RolePermissionPage> implements WorkRulesRolePermissionSaveIView {
  WorkRulesRolePermissionPagePresenter? _presenter;

  final WorkRulesRolePermissionPageController _controller = WorkRulesRolePermissionPageController();

  @override
  void initState() {
    super.initState();
    _presenter?.getWorkRulesInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '各角色权限',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: ListView.builder(
        shrinkWrap: true,
        padding: const EdgeInsets.only(bottom: 50),
        itemCount: _controller.rolePermissions.length,
        itemBuilder: (context, index) {
          RoleCustomData data = _controller.rolePermissions[index];

          RxString selectedAccessType = ''.obs;

          // 初始化选中的访问类型
          final roleConfig = _controller.roleList.firstWhereOrNull((element) => element.roleId == data.role_id.toString());
          if (roleConfig != null) {
            selectedAccessType.value = roleConfig.accessType!;
          } else {
            selectedAccessType.value = '1'; // 默认选中第一个选项
          }

          return Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    CommonUtils.getSimpleText(data.title, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(
                      '可操作权限',
                      14,
                      Colours.base_primary_text_title,
                    ),
                    CommonUtils.getSimpleText(data.functionDesc, 12, Colors.grey),
                  ],
                ),
              ),
              Gaps.line,
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                width: double.infinity,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(
                      '可操作数据',
                      14,
                      Colours.base_primary_text_title,
                    ),
                    Visibility(
                      child: CommonUtils.getSimpleText(data.dataDesc, 12, Colors.grey),
                      visible: (!TextUtil.isEmpty(data.dataDesc)),
                    ),
                    Visibility(
                      visible: (TextUtil.isEmpty(data.dataDesc)),
                      child: Container(
                        color: Colors.white,
                        width: double.infinity,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Gaps.vGap6,
                            Obx(() => BrnRadioButton(
                                  radioIndex: 1,
                                  isSelected: selectedAccessType.value == '1',
                                  child: CommonUtils.getSimpleText('能操作所有数据', 14, Colours.base_primary_text_caption),
                                  onValueChangedAtIndex: (radioIndex, value) {
                                    print('第一个 点击的下标 $radioIndex ; $value');
                                    selectedAccessType.value = (radioIndex).toString();
                                    _controller.updateAccessType(data.role_id, (radioIndex).toString());
                                    _presenter?.updateWorkRules();
                                    print(_controller.roleList.value);
                                  },
                                )),
                            Gaps.vGap4,
                            Obx(() => BrnRadioButton(
                                  radioIndex: 2,
                                  isSelected: selectedAccessType.value == '2',
                                  child: Expanded(
                                    child: CommonUtils.getSimpleText('只能操作行政组织中设置的管理范围内的数据', 14, Colours.base_primary_text_caption),
                                  ),
                                  onValueChangedAtIndex: (radioIndex, value) {
                                    print('第二个 点击的下标 $radioIndex ; $value');
                                    selectedAccessType.value = (radioIndex).toString();
                                    _controller.updateAccessType(data.role_id, (radioIndex).toString());
                                    print(_controller.roleList.value);
                                    _presenter?.updateWorkRules();
                                  },
                                )),
                          ],
                        ),
                      ),
                    )
                  ],
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesRolePermissionPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workRulesSaveStatus() {
    BrnToast.show('操作成功', context);
    _presenter?.getWorkRulesInfo();
  }

  @override
  viewRefresh() {
    setState(() {});
  }
}
