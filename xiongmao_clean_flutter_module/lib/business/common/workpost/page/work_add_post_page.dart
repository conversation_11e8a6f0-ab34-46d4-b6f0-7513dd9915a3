import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/main.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_app_bar.dart';
import '../iview/work_post_save_iview.dart';

/**
 * 添加岗位
 */
class WorkAddPostPage extends StatefulWidget {
  String? uuid = "";
  String? work_name = "";
  String? work_salary = "";

  WorkAddPostPage({Key? key, this.uuid = "", this.work_name = "", this.work_salary = ""}) : super(key: key);

  @override
  _WorkAddPostPageState createState() => _WorkAddPostPageState();
}

class _WorkAddPostPageState extends State<WorkAddPostPage> with BasePageMixin<WorkAddPostPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkAddPostPage> implements WorkPostSaveIView {
  WorkPostAddEditPagePresenter? _presenter;

  final _editNameControl = TextEditingController();
  final _editSalaryControl = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.work_name)) {
      _editNameControl.text = widget.work_name!;
    }
    if (!TextUtil.isEmpty(widget.work_salary)) {
      _editSalaryControl.text = widget.work_salary!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '添加岗位',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Container(
        margin: EdgeInsets.only(top: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BrnTextInputFormItem(
              title: "岗位名称",
              isRequire: true,
              hint: "请输入",
              controller: _editNameControl,
            ),
            Padding(
              padding: EdgeInsets.all(10),
              child: Text(
                "如：保洁员、文员等",
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ),
            BrnTextInputFormItem(
              title: "标准薪资",
              hint: "请输入",
              controller: _editSalaryControl,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 10,
                top: 10,
                right: 10,
              ),
              child: Text(
                "员工入职时，选择了该岗位的员工，默认等于该薪资（可修改），以减少入职登记的填写工作量。",
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '确定',
          onTap: () {
            _presenter?.requestWorkPostSave(widget.uuid, _editNameControl.text.toString(), _editSalaryControl.text.toString());
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkPostAddEditPagePresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workSaveStatus() {
    //不管新增还是新建都主动刷新上一层界面
    BrnToast.show("操作成功", context);
    BoostNavigator.instance.pop();
  }
}
