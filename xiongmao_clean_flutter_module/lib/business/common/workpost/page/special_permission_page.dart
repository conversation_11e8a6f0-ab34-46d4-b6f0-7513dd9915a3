import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/role_custom_data.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_refresh_list.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_app_bar.dart';
import '../controller/work_rules_controller.dart';
import '../controller/work_rules_role_permission_controller.dart';
import '../controller/work_rules_special_page_controller.dart';
import '../item/work_rules_special_listview.dart';
import '../iview/work_post_save_iview.dart';
import '../iview/work_rules_role_permission_save_iview.dart';
import '../iview/work_rules_save_iview.dart';
import '../iview/work_rules_special_permission_save_iview.dart';
import '../presenter/work_rules_persenter.dart';
import '../presenter/work_rules_role_permission_persenter.dart';
import '../presenter/work_rules_special_permission_persenter.dart';

/// 特殊数据权限
class SpecialPermissionPage extends StatefulWidget {
  SpecialPermissionPage({Key? key}) : super(key: key);

  @override
  _SpecialPermissionPageState createState() => _SpecialPermissionPageState();
}

class _SpecialPermissionPageState extends State<SpecialPermissionPage> with BasePageMixin<SpecialPermissionPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<SpecialPermissionPage> implements WorkRulesSpecialPermissionSaveIView {
  WorkRulesSpecialPermissionPagePresenter? _presenter;

  final WorkRulesSpecialManagerPageController _controller = WorkRulesSpecialManagerPageController();

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '特殊数据权限',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: WillPopScope(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
                child: CommonUtils.getSimpleText('此处设置指定员工能操作的数据范围', 14, Colours.base_primary_text_caption),
              ),
              Expanded(
                  child: Obx(() => MyRefreshListView(
                        itemCount: _controller.list.length,
                        onRefresh: _onRefresh,
                        loadMore: _loadMore,
                        hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                        itemBuilder: (_, index) {
                          return WorkRulesSpecialManagerListItem(
                              data: _controller.list[index],
                              onDelete: () {
                                DialogManager.showConfirmDialog(
                                  context: context,
                                  title: '提示',
                                  cancel: '取消',
                                  confirm: '确定',
                                  message: '是否删除该条数据？',
                                  onConfirm: () {
                                    _presenter?.delSpecial(_controller.list[index].uuid ?? "");
                                  },
                                  onCancel: () {},
                                );
                              },
                              onEdit: () {
                                BoostNavigator.instance.push('addSpecialPermissionPage', arguments: {"data": _controller.list[index]}).then((value) => _onRefresh());
                              });
                        },
                      ))),
            ],
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 54,
        child: Column(
          children: [
            Gaps.line,
            // 按钮
            Padding(
              padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 6),
              child: BrnBigMainButton(
                themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                title: '+ 添加',
                onTap: () {
                  BoostNavigator.instance.push('addSpecialPermissionPage').then((value) => _onRefresh());
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesSpecialPermissionPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  viewRefresh() {
    BrnToast.show('操作成功', context);
    _onRefresh();
  }
}
