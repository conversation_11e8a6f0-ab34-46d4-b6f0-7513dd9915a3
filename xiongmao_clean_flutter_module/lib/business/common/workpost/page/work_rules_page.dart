import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../project/bean/project_manager_entity.dart';
import '../controller/work_rules_controller.dart';
import '../iview/work_post_save_iview.dart';
import '../presenter/work_rules_persenter.dart';

/// 用工规则，直接写这了
class WorkRulesPage extends StatefulWidget {
  @override
  _WorkRulesPageState createState() => _WorkRulesPageState();
}

class _WorkRulesPageState extends State<WorkRulesPage> with BasePageMixin<WorkRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkRulesPage> implements WorkRulesSaveIView {
  WorkRulesPagePresenter? _presenter;

  final WorkRulesPageController _controller = WorkRulesPageController();

  var _index = 0; //记录当前选中的下标内容
  var _dateIndex = 0; //记录薪水结算日选中的下标内容

  @override
  void initState() {
    super.initState();
    _presenter?.getWorkRulesInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '更多企业设置',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Container(
          padding: EdgeInsets.only(bottom: 50),
          child: Column(
            children: [
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    const LoadAssetImage(
                      'common/icon_common_work_rules_in',
                      width: 20,
                      height: 20,
                    ),
                    Gaps.hGap4,
                    CommonUtils.getSimpleText('入职', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,

              ///用工年龄上线
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center, // 设置垂直居中对齐
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '用工年龄上限',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('超过上限的项目成员，将无法办理入职', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => InkWell(
                          onTap: () {
                            SingleColumnDataPickerView.showSingleColumnDataPicker(context, '用工年龄上限', _controller.ageList, _index, (index, selectedText) {
                              print('当前选择的下标：$index');
                              print('当前选择的文本内容：$selectedText');
                              _index = index;
                              _controller.updateMaxAge(selectedText.replaceAll("岁", ""));
                              saveWorkRules();
                            });
                          },
                          child: Wrap(
                            alignment: WrapAlignment.center, // 设置 Wrap 内部内容水平居中
                            children: [
                              Text(
                                "${_controller.maxAge}岁",
                                style: const TextStyle(fontSize: 14, color: Colors.black),
                              ),
                              Image.asset(
                                'assets/images/base/icon_base_gray_arrow.png',
                                width: 20,
                                height: 20,
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
              Gaps.lineLeftMargin,

              ///不允许补录之前的信息
              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '不允许补录今日之前的入职信息',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('开启后入职时间将不能选到今日之前的时间', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => BrnSwitchButton(
                          size: Size(40, 20),
                          value: _controller.isEntryBeforeToday.value,
                          // 使用新变量来表示Switch的状态
                          onChanged: (bool value) {
                            // 更新Switch的状态
                            _controller.isEntryBeforeToday.value = value;
                            saveWorkRules();
                          },
                        )),
                  ],
                ),
              ),
              Gaps.lineLeftMargin,

              ///拍照上传。无犯罪证明
              Container(
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '拍照上传“无犯罪证明”',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('关闭后，入职时将不显示“无犯罪证明”一栏', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => BrnSwitchButton(
                          size: Size(40, 20),
                          value: _controller.isUploadNotGuilty.value,
                          // 使用新变量来表示Switch的状态
                          onChanged: (bool value) {
                            // 更新Switch的状态
                            _controller.isUploadNotGuilty.value = value;
                            saveWorkRules();
                          },
                        )),
                  ],
                ),
              ),
              Gaps.lineLeftMargin,

              ///办理入职需要入职员工签字
              Container(
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '办理入职时需要入职的员工签字确认',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('入职时若签署签字合同则无需该步骤', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => BrnSwitchButton(
                          size: Size(40, 20),
                          value: _controller.isEntrySign.value,
                          // 使用新变量来表示Switch的状态
                          onChanged: (bool value) {
                            // 更新Switch的状态
                            _controller.isEntrySign.value = value;
                            saveWorkRules();
                          },
                        )),
                  ],
                ),
              ),
              // Container(
              //   margin: const EdgeInsets.only(top: 10),
              //   padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
              //   color: Colors.white,
              //   child: Row(
              //     children: [
              //       const LoadAssetImage(
              //         'common/icon_common_work_rules_contract',
              //         width: 20,
              //         height: 20,
              //       ),
              //       Gaps.hGap4,
              //       CommonUtils.getSimpleText('电子签相关', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
              //     ],
              //   ),
              // ),
              // Gaps.line,
              //
              // ///发薪日结算
              // Container(
              //   padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
              //   color: Colors.white,
              //   child: Row(
              //     crossAxisAlignment: CrossAxisAlignment.center, // 设置顶部对齐
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       Expanded(
              //           child: Column(
              //         crossAxisAlignment: CrossAxisAlignment.start,
              //         children: [
              //           CommonUtils.getSimpleText(
              //             '薪资结算日',
              //             14,
              //             Colours.base_primary_text_title,
              //           ),
              //           CommonUtils.getSimpleText('入职合同时，该日期可自动填写到合同中', 12, Colors.grey),
              //         ],
              //       )),
              //       Obx(() => InkWell(
              //             onTap: () {
              //               SingleColumnDataPickerView.showSingleColumnDataPicker(context, '薪资结算日', _controller.dateList, _dateIndex, (index, selectedText) {
              //                 print('当前选择的下标：$index');
              //                 print('当前选择的文本内容：$selectedText');
              //                 _dateIndex = index;
              //                 _controller.updateSalaryDate(selectedText.replaceAll("日", ""));
              //                 saveWorkRules();
              //               });
              //             },
              //             child: Wrap(
              //               children: [
              //                 CommonUtils.getSimpleText('${_controller.salaryDate}日', 14, Colours.base_primary_text_title),
              //                 Image.asset(
              //                   'assets/images/base/icon_base_gray_arrow.png',
              //                   width: 20,
              //                   height: 20,
              //                 ),
              //               ],
              //             ),
              //           )),
              //     ],
              //   ),
              // ),

              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    const LoadAssetImage(
                      'common/icon_common_work_rules_kq',
                      width: 20,
                      height: 20,
                    ),
                    Gaps.hGap4,
                    CommonUtils.getSimpleText('考勤相关', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,

              Container(
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center, // 设置顶部对齐
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText('允许“集体打卡”的项目', 14, Colours.base_primary_text_title),
                        CommonUtils.getSimpleText('无需员工自己打卡，由领班或主管统一拍照打卡', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => InkWell(
                          onTap: () {
                            BoostNavigator.instance.push('ProjectGeneralSelectionPage').then((value) {
                              _presenter?.getWorkRulesInfo();
                            });
                          },
                          child: Wrap(
                            children: [
                              CommonUtils.getSimpleText('${_controller.togetherClock.value}', 14, Colours.base_primary_text_title),
                              Image.asset(
                                'assets/images/base/icon_base_gray_arrow.png',
                                width: 20,
                                height: 20,
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
              Gaps.lineLeftMargin,
              Container(
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center, // 设置顶部对齐
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '打卡位置范围',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('员工只能在这个范围内进行“打卡”', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => InkWell(
                          onTap: () {
                            showDialogClockDistance();
                          },
                          child: Wrap(
                            children: [
                              CommonUtils.getSimpleText(_controller.distance.value, 14, Colours.base_primary_text_title),
                              Image.asset(
                                'assets/images/base/icon_base_gray_arrow.png',
                                width: 20,
                                height: 20,
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
              // Container(
              //   padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
              //   color: Colors.white,
              //   child: Row(
              //     crossAxisAlignment: CrossAxisAlignment.center, // 设置顶部对齐
              //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
              //     children: [
              //       Expanded(
              //           child: Column(
              //         crossAxisAlignment: CrossAxisAlignment.start,
              //         children: [
              //           CommonUtils.getSimpleText(
              //             '每月生成下个月排班表的日期',
              //             14,
              //             Colours.base_primary_text_title,
              //           ),
              //           CommonUtils.getSimpleText('提前生成排班表，才能添加请假记录', 12, Colors.grey),
              //         ],
              //       )),
              //       Obx(() => InkWell(
              //             onTap: () {
              //               SingleColumnDataPickerView.showSingleColumnDataPicker(context, '生成下个月排班表的日期', _controller.dateList, _dateIndex, (index, selectedText) {
              //                 print('当前选择的下标：$index');
              //                 print('当前选择的文本内容：$selectedText');
              //                 _dateIndex = index;
              //                 _controller.updateScheduleDate(selectedText.replaceAll("日", ""));
              //                 saveWorkRules();
              //               });
              //             },
              //             child: Wrap(
              //               children: [
              //                 CommonUtils.getSimpleText('${_controller.schedule}日', 14, Colours.base_primary_text_title),
              //                 Image.asset(
              //                   'assets/images/base/icon_base_gray_arrow.png',
              //                   width: 20,
              //                   height: 20,
              //                 ),
              //               ],
              //             ),
              //           )),
              //     ],
              //   ),
              // ),
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    const LoadAssetImage(
                      'common/icon_common_work_rules_d',
                      width: 20,
                      height: 20,
                    ),
                    Gaps.hGap4,
                    CommonUtils.getSimpleText('保洁端', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,
              Container(
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '极简模式',
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText('极简模式下，保洁员端只能拍照，没有待办等', 12, Colors.grey),
                      ],
                    )),
                    Obx(() => BrnSwitchButton(
                          size: Size(40, 20),
                          value: _controller.minimalism.value,
                          // 使用新变量来表示Switch的状态
                          onChanged: (bool value) {
                            // 更新Switch的状态
                            _controller.minimalism.value = value;
                            saveWorkRules();
                          },
                        )),
                  ],
                ),
              ),

              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    const LoadAssetImage(
                      'common/icon_common_work_rules_d',
                      width: 20,
                      height: 20,
                    ),
                    Gaps.hGap4,
                    CommonUtils.getSimpleText('功能权限', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,
              InkWell(
                child: Container(
                  padding: const EdgeInsets.only(left: 16, right: 16, top: 20, bottom: 20),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText(
                        '各角色权限',
                        14,
                        Colours.base_primary_text_title,
                      ),
                      Image.asset(
                        'assets/images/base/icon_base_gray_arrow.png',
                        width: 20,
                        height: 20,
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  BoostNavigator.instance.push('rolePermissionPage');
                },
              ),
              Gaps.line,
              InkWell(
                child: Container(
                  padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonUtils.getSimpleText(
                            '特殊数据权限',
                            14,
                            Colours.base_primary_text_title,
                          ),
                          CommonUtils.getSimpleText('默认的权限逻辑不满足需求时，在此做特殊设置', 12, Colors.grey),
                        ],
                      )),
                      Image.asset(
                        'assets/images/base/icon_base_gray_arrow.png',
                        width: 20,
                        height: 20,
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  BoostNavigator.instance.push('specialPermissionPage');
                },
              ),

              ///其他
              Container(
                margin: const EdgeInsets.only(top: 10),
                padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    const LoadAssetImage(
                      'common/icon_common_work_rules_d',
                      width: 20,
                      height: 20,
                    ),
                    Gaps.hGap4,
                    CommonUtils.getSimpleText('其他', 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold, height: 1),
                  ],
                ),
              ),
              Gaps.line,
              InkWell(
                child: Container(
                  padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                  color: Colors.white,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          CommonUtils.getSimpleText(
                            '相机等配置',
                            14,
                            Colours.base_primary_text_title,
                          ),
                          CommonUtils.getSimpleText('设置员工的相机参数、是否能查看自己的考勤、相册等', 12, Colors.grey),
                        ],
                      )),
                      Image.asset(
                        'assets/images/base/icon_base_gray_arrow.png',
                        width: 20,
                        height: 20,
                      ),
                    ],
                  ),
                ),
                onTap: () {
                  BoostNavigator.instance.push('workCameraMainPage');
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  ///显示选择的打卡距离
  void showDialogClockDistance() {
    List<BrnCommonActionSheetItem> actions = [
      BrnCommonActionSheetItem(
        '500米',
        actionStyle: BrnCommonActionSheetItemStyle.normal,
      ),
      BrnCommonActionSheetItem(
        '300米',
        actionStyle: BrnCommonActionSheetItemStyle.normal,
      ),
      BrnCommonActionSheetItem(
        '200米',
        actionStyle: BrnCommonActionSheetItemStyle.normal,
      ),
      BrnCommonActionSheetItem(
        '100米',
        actionStyle: BrnCommonActionSheetItemStyle.normal,
      )
    ];

    // 展示actionSheet
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: actions,
            clickCallBack: (
              int index,
              BrnCommonActionSheetItem actionEle,
            ) {
              String title = actionEle.title;
              _controller.distance.value = title;
              saveWorkRules();
            },
          );
        });
  }

  saveWorkRules() {
    _presenter?.updateWorkRules();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workRulesSaveStatus() {
    //重新那一边数据
    _presenter?.getWorkRulesInfo();
  }
}
