import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/my_app_bar.dart';
import '../controller/work_rules_camera_main_controller.dart';
import '../item/work_camera_main_listview.dart';
import '../iview/work_rules_role_permission_save_iview.dart';
import '../presenter/work_rules_camera_main_persenter.dart';

/// 相机等配置
class WorkCameraMainPage extends StatefulWidget {
  WorkCameraMainPage({Key? key}) : super(key: key);

  @override
  _WorkCameraMainPageState createState() => _WorkCameraMainPageState();
}

class _WorkCameraMainPageState extends State<WorkCameraMainPage> with BasePageMixin<WorkCameraMainPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkCameraMainPage> implements WorkRulesRolePermissionSaveIView {
  WorkRulesCameraMainPagePresenter? _presenter;

  final WorkRulesCameraMainPageController _controller = WorkRulesCameraMainPageController();

  @override
  void initState() {
    super.initState();

    ///获取相机配置
    onRefresh();
  }

  Future<void> onRefresh() async {
    _presenter?.getConfigInfo();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '相机等配置',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() {
        return ReorderableListView.builder(
          itemCount: _controller.list.value.length,
          itemBuilder: (context, index) {
            final item = _controller.list[index];
            return WorkCameraMainListItem(
              key: ValueKey(item.uuid), // 确保每个项有唯一的 key
              data: item,
              onDelete: () {
                DialogManager.showConfirmDialog(
                  context: context,
                  title: '提示',
                  cancel: '取消',
                  confirm: '确定',
                  message: '是否删除该方案？',
                  onConfirm: () {
                    _presenter?.deleteCompanyConfig(item.uuid ?? '');
                  },
                  onCancel: () {},
                );
              },
              onEdit: () {
                BoostNavigator.instance.push('WorkCameraSettingPage', arguments: {"uuid": item.uuid});
              },
            );
          },
          onReorder: (oldIndex, newIndex) {
            print('拖拽顺序 old -- ${_controller.list.value[oldIndex].configurationName} , new -- ${_controller.list.value[newIndex - 1].configurationName}');
            // 确保默认项不参与排序
            final oldItem = _controller.list.value[oldIndex];
            final newItem = _controller.list.value[newIndex - 1];

            if (oldItem.isDefault == "1" || newItem.isDefault == "1") {
              // 提示用户默认配置不支持换
              Toast.show('默认配置不支持换位置');
              return;
            }

            // 调整索引以便顺利交换位置
            if (newIndex > oldIndex) newIndex--;
            final item = _controller.list.value.removeAt(oldIndex);
            _controller.list.value.insert(newIndex, item);

            // 这里可以添加保存排序到服务器的逻辑
            _presenter?.sortCompanyConfig(_controller.list.value[newIndex].uuid ?? '', newIndex + 1);
          },
        );
      }),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
        child: BrnBigMainButton(
          bgColor: Colours.base_primary_green,
          title: '添加配置方案',
          onTap: () {
            BoostNavigator.instance.push('WorkCameraSettingPage').then((value) => onRefresh());
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkRulesCameraMainPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workRulesSaveStatus() {
    setState(() {
      onRefresh();
    });
  }

  @override
  viewRefresh() {
    setState(() {});
  }
}
