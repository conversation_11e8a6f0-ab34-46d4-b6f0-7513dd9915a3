import 'package:bruno/bruno.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/camera_config.dart';


class WorkRulesCameraLogoController extends GetxController {
  ///常规内容 水印LOGO图片位置 1左上 2左下 3居中 4右上 有logo则必传
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem('左上'),
    BrnCommonActionSheetItem('左下'),
    BrnCommonActionSheetItem('居中'),
    BrnCommonActionSheetItem('右上'),
  ];

  ///记录位置 默认是左下（这个是位置的信息，传递給后段，根据这个来计算）
  var positionPhoto = 2.obs;

  ///选择的照片，需要上传到七牛
  var localPath = ''.obs;

  ///图片的高
  var logoWidth = 0.obs;

  ///图片的宽
  var logoHeight = 0.obs;

  ///可调整的图片大小（这个是计算的基准，）
  var scalePhoto = 50.obs;

  ///再这里进行数据填充
  void fillData(CameraConfig config) {
    localPath.value = config.logoPath;
    logoWidth.value = config.logoWidth;
    logoHeight.value = config.logoHeight;
    scalePhoto.value = config.scale < 30 ? 30 : config.scale;
    positionPhoto.value = config.logoPosition;

    // 如果是网络图片且宽高为0，则自动获取图片尺寸
    if (_isNetworkImage(config.logoPath) &&
        (config.logoWidth == 0 || config.logoHeight == 0)) {
      // _getNetworkImageSize(config.logoPath);
    }
  }

  /// 判断是否为网络图片
  bool _isNetworkImage(String imagePath) {
    return imagePath.startsWith('http://') || imagePath.startsWith('https://');
  }

}
