import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../project/bean/project_manager_entity.dart';
import '../bean/company_camera_config_entity.dart';
import '../bean/role_custom_data.dart';
import '../bean/work_post_manager_entity.dart';
import '../bean/work_rules_config_entity.dart';

class WorkRulesCameraMainPageController extends GetxController {

  ///拿到返回的json配置
  var list = <CompanyCameraConfigList>[].obs;

  void initMyList(List<CompanyCameraConfigList>? value) {
    if (value != null) {
      list.value = value.toList();
    }
  }


}
