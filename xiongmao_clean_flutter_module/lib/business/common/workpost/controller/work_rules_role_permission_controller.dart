import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../project/bean/project_manager_entity.dart';
import '../bean/role_custom_data.dart';
import '../bean/work_post_manager_entity.dart';
import '../bean/work_rules_config_entity.dart';

class WorkRulesRolePermissionPageController extends GetxController {
  ///这里是各角色权限使用的数据
  List<RoleCustomData> rolePermissions = [
    RoleCustomData(role_id: HttpConfig.ROLE_SUPER_MANGER_ID, title: "超级管理员", functionDesc: "超管享有所有功能", dataDesc: '超管能操作所有数据'),
    RoleCustomData(role_id: HttpConfig.ROLE_MANGER_ID, title: "管理员", functionDesc: "管理员不能添加/编辑管理员/超管，其他功能都能操作"),
    RoleCustomData(role_id: HttpConfig.ROLE_HR_ID, title: "人事", functionDesc: "可以使用待办、花名册、离职管理、考勤统计、排班表、拍照、工作圈等，不可增删查改客户、项目"),
    RoleCustomData(role_id: HttpConfig.ROLE_REGIONAL_MANAGER_ID, title: "大区经理", functionDesc: "可管理其负责范围内的所有项目并查看汇报，但不可维护企业级的各种设置"),
    RoleCustomData(role_id: HttpConfig.ROLE_PROJECT_OWNER_ID, title: "项目负责人", functionDesc: "可管辖其所属项目的成员、考勤统计、考勤设置等", dataDesc: '用户所属项目下的数据'),
    RoleCustomData(role_id: HttpConfig.ROLE_LEADER_ID, title: "领班", functionDesc: "在保洁员的权限基础上增加领班打卡、修改团队成员的考勤结果的能力，但不能添加/删除团队成员", dataDesc: '自己所属项目下领班是用户自己，或没有设置领班的小组下的员工'),
    RoleCustomData(role_id: HttpConfig.ROLE_CLEAN_ID, title: "保洁员", functionDesc: "可处理其收到的待办、拍摄水印照片、查看所属项目的工作圈和通讯录（不可查看人员详情）、查看自己的考勤结果", dataDesc: '仅可操作和自己有关的数据'),
  ];

  ///拿到返回的json配置
  var roleList = <WorkRulesConfigRoleAccess>[].obs;

  void initRoleList(List<WorkRulesConfigRoleAccess>? value) {
    if (value != null) {
      roleList.value = value.toList();
    }
  }

  ///更新元数据中的json
  void updateAccessType(String roleId, String accessType) {
    final index = roleList.indexWhere((element) => element.roleId == roleId.toString());
    WorkRulesConfigRoleAccess data = WorkRulesConfigRoleAccess();
    data.roleId = roleId.toString();
    data.accessType = accessType.toString();
    if (index != -1) {
      roleList[index] = data;
    } else {
      roleList.add(data);
    }
  }
}
