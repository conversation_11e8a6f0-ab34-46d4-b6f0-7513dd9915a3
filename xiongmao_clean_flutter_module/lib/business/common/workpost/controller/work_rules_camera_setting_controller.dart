import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/company_camera_config_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../project/bean/project_manager_entity.dart';
import '../bean/camera_config.dart';
import '../bean/camera_rules_json.dart';
import '../bean/role_custom_data.dart';
import '../bean/work_post_manager_entity.dart';
import '../bean/work_rules_config_entity.dart';

class WorkRulesCameraSettingController extends GetxController {
  ///是否是默认系统设置，如果是系统设置，关闭适用人群
  var isDefault = false.obs;

  ///详情的 uuid
  var uuid = ''.obs;

  ///方案的名称
  var configName = TextEditingController().obs;

  ///描述
  var introduction = TextEditingController().obs;

  ///方案状态是否启用
  var isOpenStatus = false.obs;

  ///是否显示 App 信息
  var isShowAppInfo = false.obs;

  ///是否允许员工查看考勤
  var isSeekKQ = false.obs;

  ///是否允许查看相册
  var isSeekPhoto = false.obs;

  ///是否开启极简模式
  var isMinimalism = false.obs;

  ///企业品牌logo
  var companyInfo = Rxn<CameraConfig>();

  ///拿到返回的json配置
  var roleList = <WorkRulesConfigRoleAccess>[].obs;

  ///常规内容
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem('默认开启允许用户设置'),
    BrnCommonActionSheetItem('默认关闭允许用户设置'),
    BrnCommonActionSheetItem('始终开启'),
    BrnCommonActionSheetItem('始终关闭'),
  ];

  ///摄像头常规内容
  List<BrnCommonActionSheetItem> actionsCamera = [
    BrnCommonActionSheetItem('默认前置，用户可自选'),
    BrnCommonActionSheetItem('默认后置，用户可自选'),
    BrnCommonActionSheetItem('强制用前置'),
    BrnCommonActionSheetItem('强制用后置'),
  ];

  ///保存相片到手机
  var savePhoto = 0.obs;
  var savePhotoStr = '默认关闭允许用户设置'.obs;

  ///开启工作拍照
  var openWorkPhoto = 0.obs;
  var openWorkPhotoStr = '默认关闭允许用户设置'.obs;

  ///打卡二次确认
  var dkConfig = 0.obs;
  var dkConfigStr = '默认关闭允许用户设置'.obs;

  ///摄像头的内容
  var cameraConfig = 0.obs;
  var cameraConfigStr = '默认前置，用户可自选'.obs;

  ///条件组 条件1
  List<BrnCommonActionSheetItem> rulesOne = [
    BrnCommonActionSheetItem('部门'),
    BrnCommonActionSheetItem('项目'),
    BrnCommonActionSheetItem('角色'),
    BrnCommonActionSheetItem('岗位/职务描述'),
  ];

  ///条件组 条件2
  List<BrnCommonActionSheetItem> rulesTwo = [
    BrnCommonActionSheetItem('等于'),
    BrnCommonActionSheetItem('不等于'),
    BrnCommonActionSheetItem('包含'),
    BrnCommonActionSheetItem('不包含'),
    BrnCommonActionSheetItem('属于'),
    BrnCommonActionSheetItem('不属于'),
  ];

  List<BrnCommonActionSheetItem> rulesThree = [
    BrnCommonActionSheetItem('等于'),
    BrnCommonActionSheetItem('不等于'),
    BrnCommonActionSheetItem('属于'),
    BrnCommonActionSheetItem('不属于'),
  ];

  ///规则组
  var rangeUser = <CompanyCameraConfigOneRangeUser>[].obs;

  ///给组里增加数据
  void addInItRules() {
    var initData = CompanyCameraConfigOneRangeUser();
    initData.type = '1';
    initData.typeName = '部门';
    initData.compare = 'eq';
    initData.compareName = '等于';

    ///子的 item
    var initDataValue = CompanyCameraConfigOneRangeUserValueList();
    // initDataValue.id = httpConfig.project_uuid;
    // initDataValue.name = httpConfig.project_name;
    initData.valueList = [];
    rangeUser.add(initData);
    rangeUser.refresh();
  }

  ///对现有 rangeUser 数据组进行处理
  List<CameraRulesJson> convertRulesData() {
    var result = rangeUser.map((e) {
      var json = CameraRulesJson(
        type: e.type ?? '',
        compare: e.compare ?? '',
        value: e.valueList!.map((e) => e.id).toList().join(','),
      );
      return json;
    }).toList();
    print('传递给后端的 ${json.encode(result)}');
    return result;
  }

  ///默认的配置
  void fillData(CompanyCameraConfigOneEntity data) {
    configName.value.text = data.configurationName ?? '';
    introduction.value.text = data.introduction ?? '';
    //是否系统的设置
    if ('1' == data.isDefault) {
      isDefault.value = true;
    } else {
      isDefault.value = false;
    }
    //是否启用
    if ('1' == data.configurationStatus) {
      isOpenStatus.value = true;
    } else {
      isOpenStatus.value = false;
    }
    //保存相片到手机
    if (!TextUtil.isEmpty(data.savePhotoMobile)) {
      savePhoto.value = int.parse(data.savePhotoMobile!) - 1;
      savePhotoStr.value = actions[savePhoto.value].title;
    }
    //开启工作拍照
    if (!TextUtil.isEmpty(data.openWorkPhoto)) {
      openWorkPhoto.value = int.parse(data.openWorkPhoto!) - 1;
      openWorkPhotoStr.value = actions[openWorkPhoto.value].title;
    }
    //打卡时二次确认
    if (!TextUtil.isEmpty(data.dkConfirm)) {
      dkConfig.value = int.parse(data.dkConfirm!) - 1;
      dkConfigStr.value = actions[dkConfig.value].title;
    }
    //打卡切换摄像头
    if (!TextUtil.isEmpty(data.dkSwitchCamera)) {
      cameraConfig.value = int.parse(data.dkSwitchCamera!) - 1;
      cameraConfigStr.value = actionsCamera[cameraConfig.value].title;
    }
    //是否显示App信息
    if ('1' == data.isShowApp) {
      isShowAppInfo.value = true;
    } else {
      isShowAppInfo.value = false;
    }
    //是否允许员工查看考勤
    if ('1' == data.isSeeKq) {
      isSeekKQ.value = true;
    } else {
      isSeekKQ.value = false;
    }
    //是否允许查看相册
    if ('1' == data.isSeePhoto) {
      isSeekPhoto.value = true;
    } else {
      isSeekPhoto.value = false;
    }
    //是否开启极简模式
    if ('1' == data.isMinimalism) {
      isMinimalism.value = true;
    } else {
      isMinimalism.value = false;
    }
    //企业 logo
    var logoWidth = 0;
    var logoHeight = 0;
    var logoPos = 0;
    var logoScale = 0;
    if (!TextUtil.isEmpty(data.watermarkLogoWidth)) {
      logoWidth = int.parse(data.watermarkLogoWidth!);
    }
    if (!TextUtil.isEmpty(data.watermarkLogoHeight)) {
      logoHeight = int.parse(data.watermarkLogoHeight!);
    }
    if (!TextUtil.isEmpty(data.watermarkLogoPos)) {
      logoPos = int.parse(data.watermarkLogoPos!);
    }
    if (!TextUtil.isEmpty(data.watermarkLogoScale)) {
      logoScale = int.parse(data.watermarkLogoScale!);
      if (logoScale < 30) logoScale = 30;
    }

    //如果有 logo 的时候才去设置
    if (!TextUtil.isEmpty(data.watermarkLogo)) {
      companyInfo.value = CameraConfig(
        logoPath: data.watermarkLogo ?? '',
        logoWidth: logoWidth,
        logoHeight: logoHeight,
        logoPosition: logoPos,
        scale: logoScale,
      );
    }

    //条件数据组
    if (data.rangeUser != null && data.rangeUser!.isNotEmpty) {
      rangeUser.value = data.rangeUser!.map((e) {
        var item = CompanyCameraConfigOneRangeUser();
        item.type = e.type;
        item.typeName = e.typeName;
        item.compare = e.compare;
        item.compareName = operatorMap[e.compare];
        item.valueList = e.valueList;
        return item;
      }).toList();
    }
  }

  // 创建一个映射，将操作符与字符串关联
  Map<String, String> operatorMap = {
    'eq': '等于',
    'neq': '不等于',
    'contain': '包含',
    'uncontain': '不包含',
    'belong': '属于',
    'unbelong': '不属于',
  };

  // 通过 value 获取 key 的方法
  String? getKeyByValue(String value) {
    return operatorMap.entries
        .firstWhere((entry) => entry.value == value,
            orElse: () => const MapEntry('', ''))
        .key;
  }
}
