import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../bean/work_post_manager_entity.dart';

class WorkPostManagerPageController extends GetxController {


  var list = <WorkPostManagerList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<WorkPostManagerList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();
  }

  void updateMyList(List<WorkPostManagerList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }
}
