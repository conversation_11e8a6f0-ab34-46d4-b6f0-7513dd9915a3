import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../project/bean/project_manager_entity.dart';
import '../bean/role_custom_data.dart';
import '../bean/work_post_manager_entity.dart';
import '../bean/work_rules_config_entity.dart';

class WorkRulesPageController extends GetxController {
  var maxAge = "".obs;

  var salaryDate = "".obs;

  var distance = "".obs;

  void updateMaxAge(String? age) {
    maxAge.value = age ?? "70";
  }

  void updateClockDistance(String? d) {
    distance.value = d ?? "300米";
  }

  void updateSalaryDate(String? date) {
    salaryDate.value = date ?? "25日";
  }

  var isEntryBeforeToday = false.obs;

  void updateEntryBeforeToday(bool value) {
    isEntryBeforeToday.value = value;
  }

  var isUploadNotGuilty = false.obs;

  void updateUploadNotGuilty(bool value) {
    isUploadNotGuilty.value = value;
  }

  var isEntrySign = false.obs;

  void updateEntrySign(bool value) {
    isEntrySign.value = value;
  }

  ///我的考勤
  var myAttendance = false.obs;

  void updateMyAttendance(bool value) {
    myAttendance.value = value;
  }

  ///个人相册
  var myPhoto = false.obs;

  void updateMyPhoto(bool value) {
    myPhoto.value = value;
  }

  ///排班表
  var schedule = "15日".obs;

  void updateScheduleDate(String? s) {
    schedule.value = s ?? "15日";
  }

  ///极简模式
  var minimalism = false.obs;

  void updateMinimalism(bool value) {
    minimalism.value = value;
  }

  ///集体打卡
  var togetherClock = "0个".obs;

  void updateTogetherClock(String value) {
    togetherClock.value = value;
  }

  var list = <WorkRulesConfigProjectList>[].obs;

  var listClock = <ProjectManagerList>[].obs;

  void initTaskList(List<WorkRulesConfigProjectList>? value) {
    if (value != null) {
      list.value = value.toList();
    }
  }

  ///保存图片到手机
  var savePic = false.obs;

  void updateSavePic(bool value) {
    savePic.value = value;
  }

  ///开启工作拍照
  var openWorkCamera = false.obs;

  void updateOpenWorkCamera(bool value) {
    openWorkCamera.value = value;
  }

  ///保存图片到手机
  var clockReConfirm = false.obs;

  void updateClockReConfirm(bool value) {
    clockReConfirm.value = value;
  }

  final List<String> ageList = [
    '50岁',
    '51岁',
    '52岁',
    '53岁',
    '54岁',
    '55岁',
    '56岁',
    '57岁',
    '58岁',
    '59岁',
    '60岁',
    '61岁',
    '62岁',
    '63岁',
    '64岁',
    '65岁',
    '66岁',
    '67岁',
    '68岁',
    '69岁',
    '70岁',
    '71岁',
    '72岁',
    '73岁',
    '74岁',
    '75岁',
  ];

  final List<String> dateList = [
    '1日',
    '2日',
    '3日',
    '4日',
    '5日',
    '6日',
    '7日',
    '8日',
    '9日',
    '10日',
    '11日',
    '12日',
    '13日',
    '14日',
    '15日',
    '16日',
    '17日',
    '18日',
    '19日',
    '20日',
    '21日',
    '22日',
    '23日',
    '24日',
    '25日',
    '26日',
    '27日',
    '28日',
  ];

  ///这里是各角色权限使用的数据
  List<RoleCustomData> rolePermissions = [
    RoleCustomData(role_id: HttpConfig.ROLE_SUPER_MANGER_ID, title: "超级管理员", functionDesc: "超管享有所有功能", dataDesc: '超管能操作所有数据'),
    RoleCustomData(role_id: HttpConfig.ROLE_MANGER_ID, title: "管理员", functionDesc: "管理员不能添加/编辑管理员/超管，其他功能都能操作"),
    RoleCustomData(role_id: HttpConfig.ROLE_HR_ID, title: "人事", functionDesc: "可以使用待办、花名册、离职管理、考勤统计、排班表、拍照、工作圈等，不可增删查改客户、项目"),
    RoleCustomData(role_id: HttpConfig.ROLE_REGIONAL_MANAGER_ID, title: "大区经理", functionDesc: "可管理其负责范围内的所有项目并查看汇报，但不可维护企业级的各种设置"),
    RoleCustomData(role_id: HttpConfig.ROLE_PROJECT_OWNER_ID, title: "项目负责人", functionDesc: "可管辖其所属项目的成员、考勤统计、考勤设置等", dataDesc: '用户所属项目下的数据'),
    RoleCustomData(role_id: HttpConfig.ROLE_LEADER_ID, title: "领班", functionDesc: "在保洁员的权限基础上增加领班打卡、修改团队成员的考勤结果的能力，但不能添加/删除团队成员", dataDesc: '自己所属项目下领班是用户自己，或没有设置领班的小组下的员工'),
    RoleCustomData(role_id: HttpConfig.ROLE_CLEAN_ID, title: "保洁员", functionDesc: "可处理其收到的待办、拍摄水印照片、查看所属项目的工作圈和通讯录（不可查看人员详情）、查看自己的考勤结果", dataDesc: '仅可操作和自己有关的数据'),
  ];

  ///这里是各角色权限使用的数据
}
