import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../res/colors.dart';
import '../../../res/dimens.dart';
import '../../../res/gaps.dart';
import '../../../util/common_utils.dart';

class BasePageTitle extends StatefulWidget {
  final String label;
  final String icon;
  EdgeInsetsGeometry? padding;

  BasePageTitle({
    Key? key,
    required this.icon,
    required this.label,
    this.padding,
  }) : super(key: key);

  @override
  _BasePageTitleState createState() => _BasePageTitleState();
}

class _BasePageTitleState extends State<BasePageTitle> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: widget.padding ?? const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          LoadAssetImage(
            widget.icon,
            width: 24,
            height: 24,
          ),
          Gaps.hGap4,
          CommonUtils.getSimpleText(widget.label, 14, Colours.base_primary_text_caption)
        ],
      ),
    );
  }
}
