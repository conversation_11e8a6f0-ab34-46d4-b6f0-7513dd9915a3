import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/base/page/setting_proxy_page.dart';

import '../../../net/http_config.dart';

/// 设置代理
const settingProxyPage = "settingProxyPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> baseOtherMap = {
  settingProxyPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return SettingProxyPage();
        });
  },
};
