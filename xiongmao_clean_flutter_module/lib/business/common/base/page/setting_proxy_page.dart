import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/main.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../../mvp/base_page.dart';
import '../../../../../mvp/power_presenter.dart';
import '../../../../../res/colors.dart';
import '../../../../../util/brn_config_utils.dart';
import '../../../../../widgets/my_app_bar.dart';
import '../../workpost/iview/work_post_save_iview.dart';

/// 设置代理界面
class SettingProxyPage extends StatefulWidget {
  SettingProxyPage({Key? key}) : super(key: key);

  @override
  _SettingProxyPageState createState() => _SettingProxyPageState();
}

class _SettingProxyPageState extends State<SettingProxyPage> with BasePageMixin<SettingProxyPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<SettingProxyPage> implements WorkPostSaveIView {
  WorkPostAddEditPagePresenter? _presenter;

  final TextEditingController _proxyController = TextEditingController(); // 声明成员变量

  String mProxy = '';

  @override
  void initState() {
    super.initState();
    _loadStoredValue();
  }

  @override
  void dispose() {
    _proxyController.dispose(); // 释放资源
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '设置Flutter的调试代理',
        actionName: '清除代理',
        onPressed: () {
          _removeValue();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Container(
        margin: const EdgeInsets.only(top: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BrnTextInputFormItem(
              controller: _proxyController,
              isRequire: true,
              title: "代理",
              hint: "点击这里输入",
              subTitle: '请输入具体数据（192.168.18.90:8888）的数据',
              onChanged: (newValue) {
                mProxy = newValue;
              },
            ),
          ],
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '确定（点击后就生效哈）',
          onTap: () {
            if (TextUtil.isEmpty(mProxy)) {
              Toast.show("不是，你啥也不输入，你点啥呢");
              return;
            }
            if (!mProxy.contains(':')) {
              Toast.show("检查输入的内容，内容不对");
              return;
            }
            httpConfig.proxy = mProxy;
            _saveValue();
            _loadStoredValue();
            Toast.show("设置成功来，获取一下 ${_proxyController.text}");
            BoostNavigator.instance.pop();
          },
        ),
      ),
    );
  }

  Future<void> _saveValue() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.setString('flutter_proxy', mProxy);
    _loadStoredValue();
  }

  Future<void> _loadStoredValue() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    setState(() {
      mProxy = prefs.getString('flutter_proxy') ?? '';
      _proxyController.text = mProxy;
      httpConfig.proxy = mProxy;
    });
  }

  Future<void> _removeValue() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    await prefs.remove('flutter_proxy');
    _loadStoredValue(); // Reload the stored value
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkPostAddEditPagePresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  workSaveStatus() {}
}
