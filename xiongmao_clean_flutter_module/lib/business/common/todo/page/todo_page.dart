import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

/**
 * 待办
 */
class TodoPage extends StatefulWidget {
  @override
  _TodoPageState createState() => _TodoPageState();
}

class _TodoPageState extends State<TodoPage> with SingleTickerProviderStateMixin {
  late TabController tabController;
  List<BadgeTab> tabs = [];

  @override
  void initState() {
    super.initState();

    tabs.add(BadgeTab(text: "需求1", badgeNum: 26));
    tabs.add(BadgeTab(text: "需求2"));

    tabController = TabController(length: tabs.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Padding(
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
        child: Column(
          children: [
            BrnTabBar(
              controller: tabController,
              tabs: tabs,
              onTap: (state, index) {
                state.refreshBadgeState(index);
              },
            ),
            Expanded(
              child: Tab<PERSON><PERSON><PERSON>ie<PERSON>(
                controller: tabController,
                children: [
                  Center(child: Text('这是需求1页面')),
                  Center(child: Text('这是需求2页面')),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }
}
