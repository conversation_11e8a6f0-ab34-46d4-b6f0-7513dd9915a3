import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';

import '../../../net/http_config.dart';

/// 定义页面名称
const todoPage = "todoPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> rosterRouterMap = {
  todoPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return TodoPage();
        });
  },
};
