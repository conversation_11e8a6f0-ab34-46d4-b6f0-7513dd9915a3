import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

/**
 * 身份证验证
 */
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../credit_inquiry/bean/custom_credit_entity.dart';
import '../../credit_inquiry/item/custom_credit_list_Item.dart';

class IdentityPage extends StatelessWidget {
  final List<CustomListItemData> dataList = [
    CustomListItemData(title: '身份证验证', statusName: "免费", description: '数据直连官方身份证中心，覆盖全中国13亿人口，实时核查返回结果，保证100%的真实数据！', subDescription: "办理入职时，将自动进行身份验证，无需额外设置。"),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '身份核验',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: ListView.builder(
        itemCount: dataList.length,
        itemBuilder: (context, index) {
          return Container();
        },
      ),
    );
  }
}
