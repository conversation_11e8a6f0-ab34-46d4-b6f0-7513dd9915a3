import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/page/attendance_export_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/page/attendance_one_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/page/attendance_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/page/attendance_result_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/credit_inquiry/page/credit_inquiry_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/identity/page/identity_page.dart';

import '../../../net/http_config.dart';

/// 考勤统计
const attendancePage = "AttendancePage";

/// 考勤详情
const attendanceOnePage = "AttendanceOnePage";

/// 修改考勤班次
const attendanceResultRulesPage = "AttendanceResultRulesPage";

/// 考勤导出
const attendanceExportPage = "AttendanceExportPage";

/// 考勤统计
Map<String, FlutterBoostRouteFactory> attendanceRouterMap = {
  attendancePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          int showProject = map['show_project'] ?? 0;
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '';
          return AttendancePage(
            showProject: showProject,
            projectUuid: projectUuid,
            projectName: projectName,
          );
        });
  },

  ///考勤详情
  attendanceOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          //如果这个是null的那么就走默认当前用户的
          String userUuid = map['user_uuid'] ?? httpConfig.user_uuid;
          // String userUuid = map['user_uuid'] ?? '3187db13710269298678d1c9bf1c4f96';
          String userName = map['user_name'] ?? httpConfig.user_name;
          String projectUuid = map['project_uuid'] ?? '';
          String searchDayDate = map['search_day_date'] ?? '';
          return AttendanceOnePage(
            userUuid: userUuid,
            userName: userName,
            projectUuid: projectUuid,
            searchDayDate: searchDayDate,
          );
        });
  },
  attendanceResultRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          //如果这个是null的那么就走默认当前用户的
          String userName = map['user_name'] ?? httpConfig.user_name;
          String uuid = map['uuid'];
          bool hideMinutes = map['hideMinutes'] ?? false;
          return AttendanceResultRulesPage(
            userName: userName,
            uuid: uuid,
            hideMinutes: hideMinutes,
          );
        });
  },
  attendanceExportPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String projectUuid = map['project_uuid'] ?? '';
          String projectName = map['project_name'] ?? '';
          return AttendanceExportPage(
            projectUuid: projectUuid,
            projectName: projectName,
          );
        });
  },
};
