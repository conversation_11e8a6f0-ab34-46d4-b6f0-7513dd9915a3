class AttendanceUpdate {
  String uuid;
  int cdStatus;
  String cdLong;
  int ztStatus;
  String ztLong;

  AttendanceUpdate({
    required this.uuid,
    required this.cdStatus,
    required this.cdLong,
    required this.ztStatus,
    required this.ztLong,
  });

  // 从 JSON 创建对象
  factory AttendanceUpdate.fromJson(Map<String, dynamic> json) {
    return AttendanceUpdate(
      uuid: json['uuid'],
      cdStatus: json['cd_status'],
      cdLong: json['cd_long'],
      ztStatus: json['zt_status'],
      ztLong: json['zt_long'],
    );
  }

  // 转换对象为 JSON
  Map<String, dynamic> toJson() {
    return {
      'uuid': uuid,
      'cd_status': cdStatus,
      'cd_long': cdLong,
      'zt_status': ztStatus,
      'zt_long': ztLong,
    };
  }
}
