import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/attendance_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/attendance_data_entity.g.dart';

@JsonSerializable()
class AttendanceDataEntity {
	int? page;
	int? size;
	int? total;
	@JSONField(name: "all_total")
	int? allTotal;
	@JSONField(name: "cq_total")
	String? cqTotal;
	@JSONField(name: "actual_cq_total")
	String? actualCqTotal;
	@JSONField(name: "cd_total")
	String? cdTotal;
	@JSONField(name: "zt_total")
	String? ztTotal;
	@JSONField(name: "qk_total")
	String? qkTotal;
	@JSONField(name: "kg_total")
	String? kgTotal;
	@JSONField(name: "xj_total")
	String? xjTotal;
	@JSONField(name: "jb_total")
	String? jbTotal;
	List<AttendanceDataList>? list;

	AttendanceDataEntity();

	factory AttendanceDataEntity.fromJson(Map<String, dynamic> json) => $AttendanceDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AttendanceDataList {
	String? uuid;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "work_days")
	String? workDays;
	@JSONField(name: "actual_work_days")
	String? actualWorkDays;
	@JSONField(name: "xj_days")
	String? xjDays;
	@JSONField(name: "jb_hours")
	String? jbHours;
	@JSONField(name: "cd_time_long")
	String? cdTimeLong;
	@JSONField(name: "zt_time_long")
	String? ztTimeLong;
	@JSONField(name: "kg_days")
	String? kgDays;
	@JSONField(name: "qk_times")
	String? qkTimes;
	@JSONField(name: "entry_date")
	String? entryDate;
	@JSONField(name: "leave_date")
	String? leaveDate;
	@JSONField(name: "user_uuid")
	String? userUuid;
	@JSONField(name: "schedule_uuid")
	String? scheduleUuid;
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "class_uuid")
	String? classUuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "kq_day_uuid")
	String? kqDayUuid;
	@JSONField(name: "kq_date")
	String? kqDate;
	String? avatar;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "is_work_day")
	String? isWorkDay;

	AttendanceDataList();

	factory AttendanceDataList.fromJson(Map<String, dynamic> json) => $AttendanceDataListFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}