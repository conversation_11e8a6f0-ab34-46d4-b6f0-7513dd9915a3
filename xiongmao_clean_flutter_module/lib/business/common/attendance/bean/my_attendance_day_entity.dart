import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/my_attendance_day_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/my_attendance_day_entity.g.dart';

@JsonSerializable()
class MyAttendanceDayEntity {
	List<MyAttendanceDayList>? list;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "day_type")
	String? dayType;
	@JSONField(name: "class_type")
	String? classType;
	@JSONField(name: "class_uuid")
	String? classUuid;
	@JSONField(name: "schedule_uuid")
	String? scheduleUuid;
	@JSONField(name: "holiday_list")
	List<MyAttendanceDayHolidayList>? holidayList;
	@JSONField(name: "overtime_list")
	List<MyAttendanceDayOvertimeList>? overtimeList;
	@J<PERSON>NField(name: "operate_log_list")
	List<MyAttendanceDayOperateLogList>? operateLogList;

	MyAttendanceDayEntity();

	factory MyAttendanceDayEntity.fromJson(Map<String, dynamic> json) => $MyAttendanceDayEntityFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceDayEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MyAttendanceDayList {
	String? uuid;
	@JSONField(name: "in_title")
	String? inTitle;
	@JSONField(name: "in_status")
	String? inStatus;
	@JSONField(name: "in_status_name")
	String? inStatusName;
	@JSONField(name: "in_clock_time")
	String? inClockTime;
	@JSONField(name: "in_should_clock_time")
	String? inShouldClockTime;
	@JSONField(name: "in_actual_clock_time")
	String? inActualClockTime;
	@JSONField(name: "in_is_clock")
	String? inIsClock;
	@JSONField(name: "in_clock_type_name")
	String? inClockTypeName;
	@JSONField(name: "in_media_url")
	String? inMediaUrl;
	@JSONField(name: "in_pic_thumb")
	String? inPicThumb;
	@JSONField(name: "in_video_cover_url")
	String? inVideoCoverUrl;
	@JSONField(name: "in_origin_media_url")
	String? inOriginMediaUrl;
	@JSONField(name: "in_message_type")
	String? inMessageType;
	@JSONField(name: "in_media_type")
	String? inMediaType;
	@JSONField(name: "in_operate_name")
	String? inOperateName;
	@JSONField(name: "in_is_current_day")
	String? inIsCurrentDay;
	@JSONField(name: "out_title")
	String? outTitle;
	@JSONField(name: "out_status")
	String? outStatus;
	@JSONField(name: "out_status_name")
	String? outStatusName;
	@JSONField(name: "out_clock_time")
	String? outClockTime;
	@JSONField(name: "out_should_clock_time")
	String? outShouldClockTime;
	@JSONField(name: "out_actual_clock_time")
	String? outActualClockTime;
	@JSONField(name: "out_is_clock")
	String? outIsClock;
	@JSONField(name: "out_clock_type_name")
	String? outClockTypeName;
	@JSONField(name: "out_media_url")
	String? outMediaUrl;
	@JSONField(name: "out_pic_thumb")
	String? outPicThumb;
	@JSONField(name: "out_video_cover_url")
	String? outVideoCoverUrl;
	@JSONField(name: "out_origin_media_url")
	String? outOriginMediaUrl;
	@JSONField(name: "out_message_type")
	String? outMessageType;
	@JSONField(name: "out_media_type")
	String? outMediaType;
	@JSONField(name: "out_operate_name")
	String? outOperateName;
	@JSONField(name: "out_is_current_day")
	String? outIsCurrentDay;

	MyAttendanceDayList();

	factory MyAttendanceDayList.fromJson(Map<String, dynamic> json) => $MyAttendanceDayListFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceDayListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MyAttendanceDayHolidayList {
	String? introduction;
	String? uuid;
	@JSONField(name: "start_date")
	String? startDate;
	@JSONField(name: "start_type")
	String? startType;
	@JSONField(name: "start_type_name")
	String? startTypeName;
	@JSONField(name: "end_date")
	String? endDate;
	@JSONField(name: "end_type")
	String? endType;
	@JSONField(name: "end_type_name")
	String? endTypeName;
	@JSONField(name: "holiday_type")
	String? holidayType;
	@JSONField(name: "holiday_type_name")
	String? holidayTypeName;
	@JSONField(name: "time_long")
	String? timeLong;
	String? reason;

	MyAttendanceDayHolidayList();

	factory MyAttendanceDayHolidayList.fromJson(Map<String, dynamic> json) => $MyAttendanceDayHolidayListFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceDayHolidayListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MyAttendanceDayOvertimeList {
	String? uuid;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "overtime_long")
	String? overtimeLong;
	@JSONField(name: "overtime_date")
	String? overtimeDate;
	@JSONField(name: "overtime_type")
	String? overtimeType;
	@JSONField(name: "overtime_type_name")
	String? overtimeTypeName;
	String? reason;

	MyAttendanceDayOvertimeList();

	factory MyAttendanceDayOvertimeList.fromJson(Map<String, dynamic> json) => $MyAttendanceDayOvertimeListFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceDayOvertimeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MyAttendanceDayOperateLogList {
	String? uuid;
	@JSONField(name: "segment_uuid")
	String? segmentUuid;
	String? title;
	@JSONField(name: "sb_status")
	String? sbStatus;
	@JSONField(name: "sb_status_name")
	String? sbStatusName;
	@JSONField(name: "cd_time_long")
	String? cdTimeLong;
	@JSONField(name: "xb_status")
	String? xbStatus;
	@JSONField(name: "xb_status_name")
	String? xbStatusName;
	@JSONField(name: "zt_time_long")
	String? ztTimeLong;

	MyAttendanceDayOperateLogList();

	factory MyAttendanceDayOperateLogList.fromJson(Map<String, dynamic> json) => $MyAttendanceDayOperateLogListFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceDayOperateLogListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}