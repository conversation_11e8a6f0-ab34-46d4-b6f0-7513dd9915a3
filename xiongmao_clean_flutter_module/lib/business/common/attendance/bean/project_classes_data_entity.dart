import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/project_classes_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/project_classes_data_entity.g.dart';

@JsonSerializable()
class ProjectClassesDataEntity {
	List<ProjectClassesDataList>? list;

	ProjectClassesDataEntity();

	factory ProjectClassesDataEntity.fromJson(Map<String, dynamic> json) => $ProjectClassesDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $ProjectClassesDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ProjectClassesDataList {
	String? uuid;
	@JSONField(name: "class_name")
	String? className;
	@JSONField(name: "is_no_clock")
	String? isNoClock;
	@JSONField(name: "class_type")
	String? classType;
	@JSONField(name: "is_default_rest")
	String? isDefaultRest;
	@JSONField(name: "class_type_name")
	String? classTypeName;
	@JSONField(name: "is_no_clock_name")
	String? isNoClockName;
	@JSONField(name: "is_default_rest_name")
	String? isDefaultRestName;
	@JSONField(name: "class_time")
	String? classTime;
	@JSONField(name: "avg_line_time")
	String? avgLineTime;
	@JSONField(name: "avg_line_is_today")
	String? avgLineIsToday;
	@JSONField(name: "out_class_desc")
	String? outClassDesc;
	@JSONField(name: "in_class_desc")
	String? inClassDesc;

	ProjectClassesDataList();

	factory ProjectClassesDataList.fromJson(Map<String, dynamic> json) => $ProjectClassesDataListFromJson(json);

	Map<String, dynamic> toJson() => $ProjectClassesDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}