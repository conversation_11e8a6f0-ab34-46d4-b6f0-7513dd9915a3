import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/my_attendance_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/my_attendance_one_entity.g.dart';

@JsonSerializable()
class MyAttendanceOneEntity {
	@JSONField(name: "work_days")
	String? workDays;
	@JSONField(name: "actual_work_days")
	String? actualWorkDays;
	@JSONField(name: "xx_days")
	String? xxDays;
	@JSONField(name: "xj_days")
	String? xjDays;
	@JSONField(name: "jb_hours")
	String? jbHours;
	@JSONField(name: "cd_time_long")
	String? cdTimeLong;
	@JSONField(name: "zt_time_long")
	String? ztTimeLong;
	@JSONField(name: "kg_days")
	String? kgDays;
	@JSONField(name: "qk_times")
	String? qkTimes;
	@J<PERSON>NField(name: "user_uuid")
	String? userUuid;
	@J<PERSON><PERSON>ield(name: "user_name")
	String? userName;
	@JSONField(name: "avatar")
	String? avatar;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "project_name")
	String? projectName;
	List<MyAttendanceOneList>? list;

	MyAttendanceOneEntity();

	factory MyAttendanceOneEntity.fromJson(Map<String, dynamic> json) => $MyAttendanceOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class MyAttendanceOneList {
	String? uuid;
	@JSONField(name: "user_uuid")
	String? userUuid;
	@JSONField(name: "is_cq")
	String? isCq;
	@JSONField(name: "is_xx")
	String? isXx;
	@JSONField(name: "is_xj")
	String? isXj;
	@JSONField(name: "is_jb")
	String? isJb;
	@JSONField(name: "is_qk")
	String? isQk;
	@JSONField(name: "is_cd")
	String? isCd;
	@JSONField(name: "is_zt")
	String? isZt;
	@JSONField(name: "is_kg")
	String? isKg;
	@JSONField(name: "is_entry")
	String? isEntry;
	@JSONField(name: "is_leave")
	String? isLeave;
	@JSONField(name: "kq_date")
	String? kqDate;
	@JSONField(name: "kq_year")
	String? kqYear;
	@JSONField(name: "kq_month")
	String? kqMonth;
	@JSONField(name: "kq_day")
	String? kqDay;

	MyAttendanceOneList();

	factory MyAttendanceOneList.fromJson(Map<String, dynamic> json) => $MyAttendanceOneListFromJson(json);

	Map<String, dynamic> toJson() => $MyAttendanceOneListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}