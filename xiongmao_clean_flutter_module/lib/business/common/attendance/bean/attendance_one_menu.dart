class AttendanceOneMenu {
  String num;
  String sub;
  String title;
  String color;

  AttendanceOneMenu({
    required this.num,
    required this.sub,
    required this.title,
    required this.color,
  });

  // 从 JSON 创建对象
  factory AttendanceOneMenu.fromJson(Map<String, dynamic> json) {
    return AttendanceOneMenu(
      num: json['num'],
      sub: json['sub'],
      title: json['title'],
      color: json['color'],
    );
  }

  // 转换对象为 JSON
  Map<String, dynamic> toJson() {
    return {
      'num': num,
      'sub': sub,
      'title': title,
      'color': color,
    };
  }
}