import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../../../../util/common_utils.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_calendar_tag.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/attendance_one_menu.dart';
import '../bean/my_attendance_day_entity.dart';
import '../bean/my_attendance_one_entity.dart';
import '../bean/project_classes_data_entity.dart';

class AttendanceOneController extends GetxController {
  var userUuid = "".obs;

  ///当前显示的日期 一进来界面，默认显示当月
  var currentDate = "".obs;

  ///默认是那天
  var currentDateDay = "".obs;

  ///获取我的考勤的uuid
  var currentDayUserUuid = "".obs;

  ///用来记录有几个标签
  var maxSchemeCount = 0.obs;

  /// 创建一个 Map 来存储日期和 user_uuid 的映射
  Map<String, String> dateToUuidMap = {};

  ///当前日历的标签
  Map<DateTime, List<CalendarTag>> tags = {};

  ///当月的日历数据
  MyAttendanceOneEntity? myAttendanceOne;

  ///当天的考勤数据
  MyAttendanceDayEntity? myAttendanceDay;

  ///考勤的内容
  var attendanceTopList = [].obs;

  ///上下班的卡
  var statusList = [
    BaseChooseString("正常"),
    BaseChooseString("缺卡"),
  ].obs;

  var inWorkStatusIndex = 0.obs;
  var outWorkStatusIndex = 0.obs;

  ///考勤给默认的参数
  void getAttendanceStat(MyAttendanceOneEntity? data) {
    List<AttendanceOneMenu> attendanceList = [];

    if (data != null) {
      myAttendanceOne = data;
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.workDays).toString(), sub: "天", title: "应出勤", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.actualWorkDays).toString(), sub: "天", title: "实出勤", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.xxDays).toString(), sub: "天", title: "休息", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.xjDays).toString(), sub: "天", title: "请假", color: "#800080"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.jbHours).toString(), sub: "小时", title: "加班", color: "#4DA6FF"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.cdTimeLong).toString(), sub: "分钟", title: "迟到", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.ztTimeLong).toString(), sub: "分钟", title: "早退", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.kgDays).toString(), sub: "天", title: "旷工", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: CommonUtils.formatDecimal(data.qkTimes).toString(), sub: "次", title: "缺卡", color: "#FF4040"));
    } else {
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "天", title: "应出勤", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "天", title: "实出勤", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "天", title: "休息", color: "#000000"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "天", title: "请假", color: "#800080"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "小时", title: "加班", color: "#4DA6FF"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "分钟", title: "迟到", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "分钟", title: "早退", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "天", title: "旷工", color: "#FF4040"));
      attendanceList.add(AttendanceOneMenu(num: "0", sub: "次", title: "缺卡", color: "#FF4040"));
    }
    attendanceTopList.value = attendanceList.toList();
  }
}
