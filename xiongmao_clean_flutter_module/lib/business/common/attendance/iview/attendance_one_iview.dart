import '../../../../mvp/mvps.dart';
import '../bean/my_attendance_day_entity.dart';
import '../bean/my_attendance_one_entity.dart';
import '../bean/project_classes_data_entity.dart';

abstract class AttendanceOneIView extends IMvpView {
  void getMyAttendanceOne(MyAttendanceOneEntity data) {}

  void getMyAttendanceDayOne(MyAttendanceDayEntity data) {}

  void getClasses(ProjectClassesDataEntity dataEntity, String? userName, String? attendanceDate) {}

  ///用来回归到顶部
  void startGetMyAttendanceOne() {}
}
