import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';

class AttendanceMonthListItem extends StatelessWidget {
  final AttendanceDataList data;

  final Function onClick;

  AttendanceMonthListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(left: 16, right: 16, top: 10),
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                CustomAvatarView(
                  name: data.userName,
                  avatarUrl: data.avatar,
                ),
                Gaps.hGap10,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        CommonUtils.getSimpleText(data.userName, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                        Gaps.hGap4,
                        Padding(
                          padding: const EdgeInsets.only(
                            top: 4,
                          ),
                          child: CommonUtils.getSimpleText('${data.projectName ?? '-'}/${data.jobName ?? '-'}', 12, Colours.base_primary_jv),
                        ),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(top: 5),
                          child: CommonUtils.getSimpleText(getInfo(data), 12, Colours.base_primary_text_hint, textAlign: TextAlign.right),
                        )),
                      ],
                    ),
                    Gaps.vGap4,
                    CommonUtils.getSimpleText('应出勤:${CommonUtils.formatDecimal(data.workDays)}天 实出勤:${CommonUtils.formatDecimal(data.actualWorkDays)}天  请假:${CommonUtils.formatDecimal(data.xjDays)}天  加班:${CommonUtils.formatDecimal(data.jbHours)}小时\n迟到:${CommonUtils.formatDecimal(data.cdTimeLong)}分钟  早退:${CommonUtils.formatDecimal(data.ztTimeLong)}分钟  缺卡:${CommonUtils.formatDecimal(data.qkTimes)}次  旷工:${CommonUtils.formatDecimal(data.kgDays)}天', 12, Colours.base_primary_text_hint)
                  ],
                )),
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  getInfo(AttendanceDataList data) {
    if (!TextUtil.isEmpty(data.entryDate) && !TextUtil.isEmpty(data.leaveDate)) {
      return '${data.entryDate} 入职 - ${data.leaveDate} 离职';
    }
    if (!TextUtil.isEmpty(data.entryDate)) {
      return '${data.entryDate} 入职';
    }
    if (!TextUtil.isEmpty(data.leaveDate)) {
      return '${data.leaveDate} 离职';
    }
  }
}
