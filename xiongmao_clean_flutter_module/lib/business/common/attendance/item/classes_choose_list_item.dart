import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/project_classes_data_entity.dart';

class ClassesChooseListItem extends StatelessWidget {
  final ProjectClassesDataList data;

  String classUuid = '0';

  final Function onClick;

  ClassesChooseListItem({required this.data, required this.classUuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
            child: Row(
              children: [
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(data.className, 14, Colours.base_primary_text_title, decoration: TextDecoration.none, fontWeight: FontWeight.bold),
                    CommonUtils.getSimpleText(data.classTime, 13, Colours.base_primary_text_caption, decoration: TextDecoration.none),
                  ],
                )),
                LoadAssetImage(
                  classUuid == data.uuid ? 'icon_check' : "icon_uncheck",
                  width: 20,
                  height: 20,
                )
              ],
            ),
          ),
          Gaps.line,
        ],
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
