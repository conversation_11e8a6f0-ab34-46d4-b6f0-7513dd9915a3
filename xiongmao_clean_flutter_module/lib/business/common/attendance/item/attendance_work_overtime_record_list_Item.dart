import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/my_attendance_day_entity.dart';

class AttendanceWorkOvertimeRecordListItem extends StatelessWidget {
  final MyAttendanceDayOvertimeList data;

  final Function() onClick;

  AttendanceWorkOvertimeRecordListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: Colours.base_primary_bg_page,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('加班日期：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText(data.overtimeDate ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right, overflow: TextOverflow.ellipsis)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('加班类型：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText(data.overtimeTypeName ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('加班时段：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText('${data.startTime?.replaceAll("-", '.')}~${data.endTime?.replaceAll("-", ".")}', 15, Colours.base_primary_text_title, textAlign: TextAlign.right, overflow: TextOverflow.ellipsis)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('加班时长：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText('${CommonUtils.formatDecimal(data.overtimeLong) ?? '-'} 小时', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('加班原因：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText(data.reason ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          InkWell(
            child: CommonUtils.getSimpleText('查看详情 > ', 15, Colours.base_primary, textAlign: TextAlign.center),
            onTap: () {
              onClick();
            },
          ),
        ],
      ),
    );
  }
}
