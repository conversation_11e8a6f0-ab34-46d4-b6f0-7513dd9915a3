import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../bean/my_attendance_day_entity.dart';

class AttendanceHolidayRecordListItem extends StatelessWidget {
  final MyAttendanceDayHolidayList data;

  final Function onClick;

  AttendanceHolidayRecordListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: Colours.base_primary_bg_page,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ///顶部
          Gaps.vGap4,
          Row(
            children: [
              CommonUtils.getSimpleText('请假时段：', 15, Colours.base_primary_text_hint),
              Expanded(
                child: CommonUtils.getSimpleText('${data.startDate?.replaceAll("-", '.')}(${data.startTypeName}) ~ ${data.endDate?.replaceAll("-", ".")}(${data.endTypeName})', 15, Colours.base_primary_text_title, textAlign: TextAlign.right, overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('请假类型：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText(data.holidayTypeName ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('请假时长：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText(CommonUtils.formatDecimal(data.timeLong) ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          Row(
            children: [
              CommonUtils.getSimpleText('请假原因：', 15, Colours.base_primary_text_hint),
              Expanded(child: CommonUtils.getSimpleText((!TextUtil.isEmpty(data.reason) ? data.reason : '-') ?? '-', 15, Colours.base_primary_text_title, textAlign: TextAlign.right)),
            ],
          ),
          Gaps.vGap6,
          InkWell(
            child: CommonUtils.getSimpleText('查看详情 > ', 15, Colours.base_primary, textAlign: TextAlign.center),
            onTap: () {
              onClick();
            },
          ),
        ],
      ),
    );
  }
}
