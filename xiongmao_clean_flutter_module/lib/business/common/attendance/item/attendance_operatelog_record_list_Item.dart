import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../bean/my_attendance_day_entity.dart';

class AttendanceOperateLogRecordListItem extends StatelessWidget {
  final MyAttendanceDayOperateLogList data;

  final bool showDel;
  final Function onClick;

  AttendanceOperateLogRecordListItem({required this.data, required this.showDel, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 8,
        horizontal: 10,
      ),
      decoration: BoxDecoration(
        color: Colours.base_primary_bg_page,
        borderRadius: BorderRadius.circular(8),
      ),
      margin: const EdgeInsets.only(bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Gaps.vGap4,
          Row(
            children: [
              Expanded(child: CommonUtils.getSimpleText('${data.title}', 15, Colours.base_primary_text_title)),
              Visibility(
                visible: showDel,
                child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 15, Colours.red, textAlign: TextAlign.center),
                  onTap: () {
                    onClick();
                  },
                ),
              ),
            ],
          ),
          Gaps.vGap6,
          CommonUtils.getSimpleText('上班：${data.sbStatusName}', 14, Colours.base_primary_text_caption),
          CommonUtils.getSimpleText('下班：${data.xbStatusName}', 14, Colours.base_primary_text_caption),
        ],
      ),
    );
  }
}
