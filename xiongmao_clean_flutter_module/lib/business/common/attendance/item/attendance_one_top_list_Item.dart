import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/attendance_one_menu.dart';

class AttendanceOneTopListItem extends StatelessWidget {
  AttendanceOneMenu data;

  AttendanceOneTopListItem({required this.data});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        color: Colors.white,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.end,
              children: [
                CommonUtils.getSimpleText(CommonUtils.formatDecimal(data.num), 16, data.num == '0' ? Colours.base_primary_text_body : CommonUtils.hexStringToColor(data.color), fontWeight: FontWeight.bold, height: 0),
                CommonUtils.getSimpleText(data.sub, 12, Colours.base_primary_text_caption),
              ],
            ),
            CommonUtils.getSimpleText(data.title, 12, Colours.base_primary_text_caption),
          ],
        ),
      ),
      onTap: () {},
    );
  }
}
