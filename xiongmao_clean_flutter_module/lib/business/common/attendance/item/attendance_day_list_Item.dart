import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';

class AttendanceDayListItem extends StatelessWidget {
  final AttendanceDataList data;

  final Function(int) onClick;

  AttendanceDayListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 16, right: 16, top: 10),
        child: Column(
          children: [
            Row(
              children: [
                CustomAvatarView(
                  name: data.userName,
                  avatarUrl: data.avatar,
                ),
                Gaps.hGap10,
                Expanded(
                    child: Column(
                  children: [
                    Row(
                      children: [
                        CommonUtils.getSimpleText(data.userName, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                        Gaps.hGap4,
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: CommonUtils.getSimpleText('${data.projectName ?? '-'}/${data.jobName ?? '-'}', 12, Colours.base_primary_jv),
                        ),
                        Expanded(
                            child: Padding(
                          padding: const EdgeInsets.only(top: 0),
                          child: CommonUtils.getSimpleText(getInfo(data), 12, Colours.base_primary_text_hint, textAlign: TextAlign.right),
                        )),
                      ],
                    ),
                    Gaps.vGap4,
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.only(left: 4, right: 4),
                          decoration: BoxDecoration(
                            color: data.isWorkDay == '2' ? Colours.base_primary_select2 : Colours.base_primary_select,
                            borderRadius: BorderRadius.circular(4.0),
                            border: Border.all(color: Colors.white, width: 1),
                          ),
                          child: InkWell(
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                CommonUtils.getSimpleText((data.isWorkDay == '2' ? '休息' : data.className ?? ''), 12, data.isWorkDay == '2' ? Colours.base_primary_text_body : Colours.base_primary),
                                Gaps.hGap2,
                                const Padding(
                                  padding: EdgeInsets.only(top: 1),
                                  child: LoadAssetImage(
                                    'common/icon_attendance_edit',
                                    height: 14,
                                    width: 14,
                                  ),
                                ),
                              ],
                            ),
                            onTap: () {
                              onClick(0);
                            },
                          ),
                        ),
                        Gaps.hGap4,
                        Expanded(
                          child: RichText(
                              overflow: TextOverflow.ellipsis, // 超出部分使用省略号
                              maxLines: 1,
                              text: TextSpan(
                                children: getTextSpan(),
                              )),
                        ),
                      ],
                    ),
                  ],
                )),
                InkWell(
                  child: const LoadAssetImage(
                    'base/icon_base_edit',
                    height: 24,
                    width: 24,
                  ),
                  onTap: () {
                    onClick(1);
                  },
                )
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
          ],
        ),
      ),
      onTap: () {
        onClick(3);
      },
    );
  }

  List<TextSpan> getTextSpan() {
    List<TextSpan> spans = [];

    /// 迟到分钟数
    if (!TextUtil.isEmpty(data.cdTimeLong) && double.parse(data.cdTimeLong!) > 0) {
      spans.add(TextSpan(
        text: '迟到:${CommonUtils.formatDecimal(data.cdTimeLong)}分钟 ',
        style: const TextStyle(color: Colors.red),
      ));
    }

    /// 早退分钟数
    if (!TextUtil.isEmpty(data.ztTimeLong) && double.parse(data.ztTimeLong!) > 0) {
      spans.add(TextSpan(
        text: '早退:${CommonUtils.formatDecimal(data.ztTimeLong)}分钟 ',
        style: const TextStyle(color: Colors.red),
      ));
    }

    /// 旷工分钟数
    if (!TextUtil.isEmpty(data.kgDays) && double.parse(data.kgDays!) > 0.00) {
      spans.add(TextSpan(
        text: '旷工:${CommonUtils.formatDecimal(data.kgDays)}天 ',
        style: const TextStyle(color: Colors.red),
      ));
    }

    /// 缺卡分钟数
    if (!TextUtil.isEmpty(data.qkTimes) && double.parse(data.qkTimes!) > 0) {
      spans.add(TextSpan(
        text: '缺卡:${CommonUtils.formatDecimal(data.qkTimes)}次 ',
        style: const TextStyle(color: Colors.red),
      ));
    }

    /// 请假分钟数
    if (!TextUtil.isEmpty(data.xjDays) && double.parse(data.xjDays!) > 0.00) {
      spans.add(TextSpan(
        text: '请假:${CommonUtils.formatDecimal(data.xjDays)}天 ',
        style: const TextStyle(color: Colors.deepPurpleAccent),
      ));
    }

    /// 加班分钟数
    if (!TextUtil.isEmpty(data.jbHours) && double.parse(data.jbHours!) > 0.00) {
      spans.add(TextSpan(
        text: '加班:${CommonUtils.formatDecimal(data.jbHours)}小时 ',
        style: const TextStyle(color: Colors.blue),
      ));
    }

    // 如果添加的 spans 超过两个，添加省略号
    if (spans.length > 2) {
      spans = spans.sublist(0, 2); // 只保留前两个
      spans.add(const TextSpan(
        text: ' ...',
        style: TextStyle(color: Colors.grey), // 可根据需要更改颜色
      ));
    }

    return spans;
  }

  getInfo(AttendanceDataList data) {
    if (!TextUtil.isEmpty(data.entryDate) && !TextUtil.isEmpty(data.leaveDate)) {
      return '${data.entryDate} 入职 - ${data.leaveDate} 离职';
    }
    if (!TextUtil.isEmpty(data.entryDate)) {
      return '${data.entryDate} 入职';
    }
    if (!TextUtil.isEmpty(data.leaveDate)) {
      return '${data.leaveDate} 离职';
    }
  }
}
