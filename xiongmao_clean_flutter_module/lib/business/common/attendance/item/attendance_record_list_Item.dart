import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/my_attendance_day_entity.dart';

class AttendanceRecordListItem extends StatelessWidget {
  final MyAttendanceDayList data;

  final bool showBut;
  final bool showTitle;

  final int position;

  final Function(int) onClick;

  AttendanceRecordListItem({required this.data, required this.showBut, required this.position, required this.showTitle, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Visibility(
            visible: showTitle,
            child: Column(
              children: [
                Gaps.vGap10,
                Row(
                  children: [
                    Expanded(child: CommonUtils.getSimpleText('第${position + 1}段：', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold)),

                    ///InkWell取消点击背景色
                    Visibility(
                      visible: showBut,
                      child: InkWell(
                        splashColor: Colors.transparent, // 取消水波纹颜色
                        highlightColor: Colors.transparent, // 取消高亮背景色
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(4),
                          ),
                          padding: const EdgeInsets.symmetric(vertical: 4, horizontal: 6),
                          child: CommonUtils.getSimpleText('改打卡结果', 14, Colours.white),
                        ),
                        onTap: () {
                          onClick(1);
                        },
                      ),
                    ),
                  ],
                )
              ],
            )),
        Gaps.vGap6,
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: InkWell(
              child: Container(
                // margin: const EdgeInsets.only(top: 10, bottom: 10),
                padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                decoration: BoxDecoration(
                  color: Colours.base_primary_bg_page,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(child: CommonUtils.getSimpleText('${data.inTitle}', 18, Colours.base_primary_text_title)),
                        LoadImage(
                          'icon_base_arrow',
                          width: 6,
                          height: 6,
                        ),
                      ],
                    ),
                    Gaps.vGap10,
                    Row(
                      children: [
                        CommonUtils.getSimpleText('#${data.inStatusName}#', 16, getStatusColor(data.inStatus)),
                        Gaps.hGap6,
                        Expanded(child: CommonUtils.getSimpleText(buildTextConvert(data), 14, Colours.base_primary_text_body, overflow: TextOverflow.ellipsis)),
                      ],
                    ),
                    Visibility(
                        visible: (!TextUtil.isEmpty(data.inMediaUrl)),
                        child: InkWell(
                          child: Column(
                            children: [
                              Gaps.vGap10,
                              LoadImage(
                                data.inMediaUrl ?? '',
                                width: 100,
                                height: 100,
                              ),
                            ],
                          ),
                          onTap: () {
                            ImagePickers.previewImage(data.inMediaUrl ?? '');
                          },
                        )),
                  ],
                ),
              ),
              onTap: () {
                onClick(2);
              },
            )),
            Gaps.hGap10,
            Expanded(
                child: InkWell(
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 10, horizontal: 10),
                decoration: BoxDecoration(
                  color: Colours.base_primary_bg_page,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(child: CommonUtils.getSimpleText('${data.outTitle}', 18, Colours.base_primary_text_title)),
                        LoadImage(
                          'icon_base_arrow',
                          width: 6,
                          height: 6,
                        ),
                      ],
                    ),
                    Gaps.vGap10,
                    Row(
                      children: [
                        CommonUtils.getSimpleText('#${data.outStatusName}#', 16, getStatusColor(data.outStatus)),
                        Gaps.hGap6,
                        Expanded(child: CommonUtils.getSimpleText(buildoutTextConvert(data), 14, Colours.base_primary_text_body, overflow: TextOverflow.ellipsis)),
                      ],
                    ),
                    Visibility(
                        visible: (!TextUtil.isEmpty(data.outMediaUrl)),
                        child: InkWell(
                          child: Column(
                            children: [
                              Gaps.vGap10,
                              LoadImage(
                                data.outMediaUrl ?? '',
                                width: 100,
                                height: 100,
                              ),
                            ],
                          ),
                          onTap: () {
                            ImagePickers.previewImage(data.outMediaUrl ?? '');
                          },
                        )),
                  ],
                ),
              ),
              onTap: () {
                onClick(3);
              },
            )),
          ],
        ),
      ],
    );
  }

  ///获取当前的text的颜色
  Color getStatusColor(String? status) {
    switch (status) {
      case "0":
        return Color(0xFF8D8E99);
      case "1":
        return Color(0xFF09BE89);
      case "2":
      case "3":
      case "4":
      case "5":
        return Color(0xFFFF4040);
      case "6":
        return Color(0xFF800080);
      default:
        return Colors.black; // 默认颜色
    }
  }

  String buildTextConvert(MyAttendanceDayList data) {
    if (data.inClockTime != null && data.inClockTime!.isNotEmpty && data.inIsClock == "1") {
      if (data.inIsCurrentDay == "1") {
        return '${data.inClockTime} 已打卡';
      } else {
        return '次日 ${data.inClockTime} 已打卡';
      }
    } else {
      return '';
    }
  }

  String buildoutTextConvert(MyAttendanceDayList data) {
    if (data.outClockTime != null && data.outClockTime!.isNotEmpty && data.outIsClock == "1") {
      if (data.outIsCurrentDay == "1") {
        return '${data.outClockTime} 已打卡';
      } else {
        return '次日 ${data.outClockTime} 已打卡';
      }
    } else {
      return '';
    }
  }
}
