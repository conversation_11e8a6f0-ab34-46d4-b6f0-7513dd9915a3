import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../schedule/bean/schedule_export_data_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/my_attendance_day_entity.dart';
import '../bean/my_attendance_one_entity.dart';
import '../bean/project_classes_data_entity.dart';
import '../controller/attendance_controller.dart';
import '../controller/attendance_one_controller.dart';
import '../iview/attendance_iview.dart';
import '../iview/attendance_one_iview.dart';

/// 考勤详情
class AttendanceOnePresenter extends BasePagePresenter<AttendanceOneIView> with WidgetsBindingObserver {
  AttendanceOneController controller;

  AttendanceOnePresenter(this.controller);

  ///获取我的考勤
  Future<dynamic> getMyAttendanceOne() {
    view.startGetMyAttendanceOne();
    HashMap<String, String> params = HashMap<String, String>();
    params['search_month'] = controller.currentDate.value;
    params['user_uuid'] = controller.userUuid.value;
    return requestNetwork<MyAttendanceOneEntity>(Method.get, url: HttpApi.ATTENDANCE_MY_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getMyAttendanceOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取当日的考勤详情
  Future<dynamic> getMyAttendanceDayOne() {
    HashMap<String, String> params = HashMap<String, String>();
    params['search_date'] = controller.currentDateDay.value;
    params['user_uuid'] = controller.userUuid.value;
    return requestNetwork<MyAttendanceDayEntity>(Method.get, url: HttpApi.ATTENDANCE_MY_DAYS_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getMyAttendanceDayOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除该条修改记录
  Future<dynamic> deleteOperateLog(String? uuid) {
    HashMap<String, String> hashMap = HashMap();
    hashMap["uuid"] = '$uuid';
    return requestNetwork<Object>(Method.post, url: HttpApi.DELETE_ATTENDANCE_OPERATELOG, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      ///刷新我的考勤
      delayRefreshAttendance();
    });
  }

  ///获取当前项目的班次列表
  Future<dynamic> getClassesManager(String? userName, String? attendanceDate, String projectUuid) {
    HashMap<String, String> params = HashMap<String, String>();
    params['project_uuid'] = projectUuid;
    return requestNetwork<ProjectClassesDataEntity>(Method.get, url: HttpApi.GET_CLASSES_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        view.getClasses(data, userName, attendanceDate);
      } else {}
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///修改某个排班下的班次
  Future<dynamic> updateClassType(String uuid, String? class_uuid, String is_rest) {
    HashMap<String, String> params = HashMap<String, String>();
    params['uuid'] = uuid; //排班UUID
    if (!TextUtil.isEmpty(class_uuid)) {
      //班次UUID 非休息必传
      params['class_uuid'] = "$class_uuid";
    }
    params['is_rest'] = is_rest;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_SCHEDULE_UPDATE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      ///刷新我的考勤当天的数据
      delayRefreshAttendance();
    });
  }

  ///修改某个排班下的班次
  Future<dynamic> requestResetAttendance(String stat_uuid) {
    HashMap<String, String> params = HashMap<String, String>();
    params['stat_uuid'] = stat_uuid; //排班UUID
    return requestNetwork<Object>(Method.post, url: HttpApi.RESET_ATTENDANCE_OPERATELOG, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      ///刷新我的考勤当天的数据
      delayRefreshAttendance();
    });
  }

  ///修改考勤结果
  Future<dynamic> requestUpdateAttendance(HashMap<String, String> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.UPDATE_ATTENDANCE_OPERATELOG, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      ///刷新我的考勤当天的数据
      delayRefreshAttendance();
    });
  }

  void delayRefreshAttendance() {
    Toast.show('操作成功，即将重新拉取最新数据，请稍等...');
    Future.delayed(const Duration(seconds: 3), () {
      view.startGetMyAttendanceOne();
      getMyAttendanceDayOne();
    });
  }


  void delayRefreshAttendanceOne() {
    Toast.show('操作成功，即将重新拉取最新数据，请稍等...');
    Future.delayed(const Duration(seconds: 3), () {
      getMyAttendanceOne();
    });
  }
}
