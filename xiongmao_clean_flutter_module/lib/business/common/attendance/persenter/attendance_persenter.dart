import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../schedule/bean/schedule_export_data_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../bean/project_classes_data_entity.dart';
import '../controller/attendance_controller.dart';
import '../iview/attendance_iview.dart';

/// 考勤报表的
class AttendancePresenter extends BasePagePresenter<AttendanceIView> with WidgetsBindingObserver {
  AttendanceController controller;

  AttendancePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getAttendanceListManager();
  }

  void loadMore() {
    _page++;
    getAttendanceListManager();
  }

  ///获取考勤列表
  Future<dynamic> getAttendanceListManager() {
    HashMap<String, String> params = HashMap<String, String>();
    params['search_date'] = controller.searchDay.value;
    params['search_month'] = controller.searchMonth.value;
    params['stat_status'] = controller.status.value;
    params['project_uuid'] = controller.projectUuid.value;
    params['is_stat_total'] = '1';
    params["page"] = "$_page";
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<AttendanceDataEntity>(Method.get, url: (controller.reportType == 0) ? HttpApi.ATTENDANCE_DAY_LIST : HttpApi.ATTENDANCE_MONTH_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.data.value = data;
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取当前项目的班次列表
  Future<dynamic> getClassesManager(String? classUuid, String? scheduleUuid, String? projectUuid, String? user_name, String? attendance_date) {
    HashMap<String, String> params = HashMap<String, String>();
    params['project_uuid'] = '$projectUuid';
    return requestNetwork<ProjectClassesDataEntity>(Method.get, url: HttpApi.GET_CLASSES_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        view.getClasses(data, classUuid, scheduleUuid, user_name, attendance_date);
      } else {}
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///修改某个排班下的班次类型
  Future<dynamic> updateClassType(String? scheduleUuid, String? class_uuid, String is_rest) {
    HashMap<String, String> params = HashMap<String, String>();
    params['uuid'] = '$scheduleUuid'; //排班UUID
    if (!TextUtil.isEmpty(class_uuid)) {
      //班次UUID 非休息必传
      params['class_uuid'] = "$class_uuid";
    }
    params['is_rest'] = is_rest;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_SCHEDULE_UPDATE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      onRefresh();
    });
  }

  ///修改打卡记录
  Future<dynamic> updateClockType(String? stat_uuid, int sb_status, String? cd_time_long, int xb_status, String? zt_time_long) {
    HashMap<String, String> params = HashMap<String, String>();
    params['stat_uuid'] = '$stat_uuid'; //排班UUID
    params['sb_status'] = '$sb_status'; //上班卡状态 1正常 2缺卡 3迟到
    if (3 == sb_status) {
      params['cd_time_long'] = '$cd_time_long'; //迟到时长 迟到必传
    }
    params['xb_status'] = '$xb_status'; //下班卡状态 1正常 2缺卡 3早退
    if (3 == xb_status) {
      params['zt_time_long'] = '$zt_time_long'; //早退时长 早退必传
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_SCHEDULE_UPDATE_CLOCK, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      onRefresh();
    });
  }

  ///统计导出
  Future<dynamic> exportAttendance(String projectUuid, String date, String type) {
    HashMap<String, String> params = HashMap<String, String>();
    params['search_month'] = date.replaceAll("年", "-").replaceAll("月", "");
    if (!TextUtil.isEmpty(projectUuid)) {
      params['project_uuid'] = projectUuid;
    }
    params['type'] = type;
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleExportDataEntity>(Method.post, url: HttpApi.GET_ATTENDANCE_EXPORT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.export(data.downloadUrl ?? '', date);
      }
    });
  }

  ///统计导出
  Future<dynamic> exportAttendanceResult(String projectUuid, String date) {
    HashMap<String, String> params = HashMap<String, String>();
    params['search_month'] = date.replaceAll("年", "-").replaceAll("月", "");
    if (!TextUtil.isEmpty(projectUuid)) {
      params['project_uuid'] = projectUuid;
    }

    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleExportDataEntity>(Method.post, url: HttpApi.GET_ATTENDANCE_EXPORT_RESULT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.export(data.downloadUrl ?? '', date);
      }
    });
  }
}
