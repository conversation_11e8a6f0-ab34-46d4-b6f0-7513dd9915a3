import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../util/common_utils.dart';
import '../../approve/bean/base_choose_string.dart';

/// 修改考勤结果
class CustomAttendanceResultDialog extends StatefulWidget {
  final Function(int, String, int, String) onClick;

  const CustomAttendanceResultDialog({super.key, required this.onClick});

  @override
  _CustomAttendanceResultDialogState createState() => _CustomAttendanceResultDialogState();
}

class _CustomAttendanceResultDialogState extends State<CustomAttendanceResultDialog> {
  List<BaseChooseString> tagList = [
    BaseChooseString("正常"),
    BaseChooseString("缺卡"),
  ];

  //记录工作情况下 来改变状态
  int workLongClick = 1;
  int workOffLongClick = 1;

  String lateMinute = '_'; //迟到分钟数
  String leaveMinute = '_'; //早退分钟数

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: 400,
        // 自定义高度
        padding: const EdgeInsets.all(16.0),
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Stack(
              children: [
                CommonUtils.getSimpleText('修改考勤结果', 18, Colours.base_primary_text_title, decoration: TextDecoration.none),
                Positioned(
                  right: 0,
                  top: 2,
                  child: InkWell(
                    child: const LoadAssetImage(
                      'icon_base_round_close',
                      width: 20,
                      height: 20,
                    ),
                    onTap: () {
                      Navigator.pop(context); // 关闭对话框
                    },
                  ),
                ),
              ],
            ),
            CommonUtils.getSimpleText('上班卡', 18, Colours.base_primary_text_title, decoration: TextDecoration.none),
            Gaps.vGap10,
            Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                      top: 8,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: (workLongClick == 1) ? Colours.base_primary_select : Colours.base_primary_un_select,
                      borderRadius: BorderRadius.circular(4.0),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: CommonUtils.getSimpleText('正常', 14, (workLongClick == 1) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
                  ),
                  onTap: () {
                    setState(() {
                      workLongClick = 1;
                    });
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                      top: 8,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: (workLongClick == 2) ? Colours.base_primary_select : Colours.base_primary_un_select,
                      borderRadius: BorderRadius.circular(4.0),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: CommonUtils.getSimpleText('缺卡', 14, (workLongClick == 2) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
                  ),
                  onTap: () {
                    setState(() {
                      workLongClick = 2;
                    });
                  },
                )),
              ],
            ),
            Gaps.vGap10,
            InkWell(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.only(
                  top: 8,
                  bottom: 8,
                ),
                decoration: BoxDecoration(
                  color: (workLongClick == 3) ? Colours.base_primary_select : Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4.0),
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: CommonUtils.getSimpleText('设置迟到${lateMinute}分钟数', 14, (workLongClick == 3) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
              ),
              onTap: () {
                setState(() {
                  workLongClick = 3;
                  BrnMiddleInputDialog(
                      title: '设置迟到分钟数',
                      hintText: '输入迟到分钟数',
                      cancelText: '取消',
                      confirmText: '确定',
                      maxLength: 100,
                      maxLines: 1,
                      barrierDismissible: false,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onConfirm: (value) {
                        Navigator.pop(context);
                        setState(() {
                          lateMinute = value;
                        });
                      },
                      onCancel: () {
                        Navigator.pop(context);
                      }).show(context);
                });
              },
            ),
            Gaps.vGap10,
            CommonUtils.getSimpleText('下班卡', 18, Colours.base_primary_text_title, decoration: TextDecoration.none),
            Gaps.vGap10,
            Row(
              children: [
                Expanded(
                    child: InkWell(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                      top: 8,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: (workOffLongClick == 1) ? Colours.base_primary_select : Colours.base_primary_un_select,
                      borderRadius: BorderRadius.circular(4.0),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: CommonUtils.getSimpleText('正常', 14, (workOffLongClick == 1) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
                  ),
                  onTap: () {
                    setState(() {
                      workOffLongClick = 1;
                    });
                  },
                )),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    width: double.infinity,
                    padding: const EdgeInsets.only(
                      top: 8,
                      bottom: 8,
                    ),
                    decoration: BoxDecoration(
                      color: (workOffLongClick == 2) ? Colours.base_primary_select : Colours.base_primary_un_select,
                      borderRadius: BorderRadius.circular(4.0),
                      border: Border.all(color: Colors.white, width: 1),
                    ),
                    child: CommonUtils.getSimpleText('缺卡', 14, (workOffLongClick == 2) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
                  ),
                  onTap: () {
                    setState(() {
                      workOffLongClick = 2;
                    });
                  },
                )),
              ],
            ),
            Gaps.vGap10,
            InkWell(
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.only(
                  top: 8,
                  bottom: 8,
                ),
                decoration: BoxDecoration(
                  color: (workOffLongClick == 3) ? Colours.base_primary_select : Colours.base_primary_un_select,
                  borderRadius: BorderRadius.circular(4.0),
                  border: Border.all(color: Colors.white, width: 1),
                ),
                child: CommonUtils.getSimpleText('设置早退${leaveMinute}分钟数', 14, (workOffLongClick == 3) ? Colours.base_primary : Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
              ),
              onTap: () {
                setState(() {
                  workOffLongClick = 3;
                  BrnMiddleInputDialog(
                      title: '设置早退分钟数',
                      hintText: '输入早退分钟数',
                      cancelText: '取消',
                      confirmText: '确定',
                      maxLength: 100,
                      maxLines: 1,
                      barrierDismissible: false,
                      inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      onConfirm: (value) {
                        Navigator.pop(context);
                        setState(() {
                          leaveMinute = value;
                        });
                      },
                      onCancel: () {
                        Navigator.pop(context);
                      }).show(context);
                });
              },
            ),
            Gaps.vGap20,

            ///底部按钮，如果是自由下班卡，就不显示底部按钮
            Row(
              children: [
                InkWell(
                  child: Container(
                    padding: EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      border: Border.all(color: Colours.base_primary, width: 1),
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CommonUtils.getSimpleText('重置', 14, Colours.base_primary, decoration: TextDecoration.none),
                  ),
                  onTap: () {},
                ),
                Gaps.hGap10,
                Expanded(
                    child: InkWell(
                  child: Container(
                    padding: EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                    decoration: BoxDecoration(
                      color: Colours.base_primary,
                      borderRadius: BorderRadius.circular(4.0),
                    ),
                    child: CommonUtils.getSimpleText('确定', 14, Colours.white, textAlign: TextAlign.center, decoration: TextDecoration.none),
                  ),
                  onTap: () {
                    widget.onClick(workLongClick, lateMinute, workOffLongClick, lateMinute);
                    Navigator.pop(context);
                  },
                ))
              ],
            )
          ],
        ),
      ),
    );
  }
}
