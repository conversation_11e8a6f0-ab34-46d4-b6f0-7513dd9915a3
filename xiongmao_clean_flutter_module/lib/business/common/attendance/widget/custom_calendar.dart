import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../bean/attendance_calendar_tag.dart';

class CustomCalendar extends StatefulWidget {
  final ValueChanged<DateTime>? onDateSelected;
  final Map<DateTime, List<CalendarTag>> initialTags;
  final DateTime initialDate;
  final int schemeCount;

  CustomCalendar({this.onDateSelected, required this.initialDate, this.schemeCount = 0, this.initialTags = const {}});

  @override
  _CustomCalendarState createState() => _CustomCalendarState();
}

class _CustomCalendarState extends State<CustomCalendar> {
  late DateTime _currentDate;
  DateTime? _selectedDate;
  Map<DateTime, List<CalendarTag>> _tags = {};

  @override
  void initState() {
    super.initState();
    _currentDate = widget.initialDate;
    _tags = widget.initialTags; // 初始化标签
    _selectedDate = _currentDate; // 默认选中今天
  }

  @override
  void didUpdateWidget(CustomCalendar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.initialDate != widget.initialDate) {
      _currentDate = widget.initialDate;
      _selectedDate = DateTime(_currentDate.year, _currentDate.month, _currentDate.day); // 更新为本月的1号
    }
  }

  void _onDaySelected(DateTime day) {
    setState(() {
      _selectedDate = day;
    });
    if (widget.onDateSelected != null) {
      widget.onDateSelected!(day);
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Column(
        children: [
          Gaps.vGap12,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: [
              for (var day in ['一', '二', '三', '四', '五', '六', '日'])
                Expanded(
                  child: Center(
                    child: CommonUtils.getSimpleText(day, 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                  ),
                ),
            ],
          ),
          Gaps.vGap10,
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 7,
              childAspectRatio: buildTagSizeFloat(),
            ),
            itemCount: _daysInMonth(_currentDate.year, _currentDate.month) + _firstDayOfMonth(_currentDate.year, _currentDate.month).weekday - 1,
            itemBuilder: (BuildContext context, int index) {
              if (index < _firstDayOfMonth(_currentDate.year, _currentDate.month).weekday - 1) {
                return Container(); // Days before the first day of the month
              } else {
                final day = DateTime(
                  _currentDate.year,
                  _currentDate.month,
                  index - _firstDayOfMonth(_currentDate.year, _currentDate.month).weekday + 2,
                );
                final isSelected = _selectedDate != null && _selectedDate!.year == day.year && _selectedDate!.month == day.month && _selectedDate!.day == day.day;
                return GestureDetector(
                  onTap: () => _onDaySelected(day),
                  child: Container(
                    decoration: BoxDecoration(
                      color: isSelected ? Colours.half_transparent_44 : Colors.transparent,
                      borderRadius: BorderRadius.circular(2.0),
                    ),
                    margin: const EdgeInsets.all(2),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText('${day.day}', 14, isSelected ? Colours.base_primary_text_title : Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                        ..._tags[day]?.map((tag) {
                              return CommonUtils.getSimpleText(tag.description, 12, tag.color, height: 1);
                            }).toList() ??
                            [],
                      ],
                    ),
                  ),
                );
              }
            },
          ),
        ],
      ),
    );
  }

  int _daysInMonth(int year, int month) {
    if ([1, 3, 5, 7, 8, 10, 12].contains(month)) {
      return 31;
    }
    if ([4, 6, 9, 11].contains(month)) {
      return 30;
    }
    if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
      return 29; // Leap year
    }
    return 28;
  }

  DateTime _firstDayOfMonth(int year, int month) {
    return DateTime(year, month, 1);
  }

  double buildTagSizeFloat() {
    print('tag大小 ${widget.schemeCount}');
    switch (widget.schemeCount) {
      case 0:
      case 1:
        return 1.2;
      case 2:
        return 1.1;
      case 3:
        return 0.86;
      default:
        return 0.75;
    }
  }
}
