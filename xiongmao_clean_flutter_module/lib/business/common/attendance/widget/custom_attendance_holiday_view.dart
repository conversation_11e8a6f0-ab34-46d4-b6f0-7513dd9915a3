import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../util/common_utils.dart';
import '../bean/project_classes_data_entity.dart';
import '../item/classes_choose_list_item.dart';

///修改考勤结果，只不过是休息日，无需修改
class CustomAttendanceHolidayDialog extends StatelessWidget {
  final Function() onClick;

  CustomAttendanceHolidayDialog({super.key, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: 500,
        // 自定义高度
        padding: const EdgeInsets.all(16.0),
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            Stack(
              children: [
                CommonUtils.getSimpleText('修改考勤结果', 18, Colours.base_primary_text_title, decoration: TextDecoration.none),
                Positioned(
                  right: 0,
                  top: 6,
                  child: InkWell(
                    child: const LoadAssetImage(
                      'icon_base_round_close',
                      width: 20,
                      height: 20,
                    ),
                    onTap: () {
                      Navigator.pop(context); // 关闭对话框
                      onClick();
                    },
                  ),
                ),
              ],
            ),
            const Padding(
              padding: EdgeInsets.only(top: 50),
              child: LoadAssetImage(
                'no_data',
                width: 150,
                height: 150,
              ),
            ),
            CommonUtils.getSimpleText('休息日不需要打卡，不会有异常，无需修改', 14, Colours.base_primary_text_title, textAlign: TextAlign.center, decoration: TextDecoration.none),
          ],
        ),
      ),
    );
  }
}
