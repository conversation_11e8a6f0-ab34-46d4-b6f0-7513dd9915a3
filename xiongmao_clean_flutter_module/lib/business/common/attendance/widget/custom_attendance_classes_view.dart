import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../../../util/common_utils.dart';
import '../bean/project_classes_data_entity.dart';
import '../item/classes_choose_list_item.dart';

///修改班次
class CustomAttendanceClassesDialog extends StatelessWidget {
  ProjectClassesDataEntity dataEntity;

  String? userName;
  String? attendanceDate;
  String classUuid;

  final Function(ProjectClassesDataList?) onClick;

  CustomAttendanceClassesDialog({required this.dataEntity, required this.classUuid, required this.onClick, this.userName, this.attendanceDate});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.6, //设置高度为屏幕高度的一半
        decoration: const BoxDecoration(
          color: Colors.white,
        ),
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 16, right: 16, top: 16, bottom: 16),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        CommonUtils.getSimpleText(
                          '选择班次',
                          16,
                          Colours.base_primary_text_title,
                          decoration: TextDecoration.none,
                          fontWeight: FontWeight.bold,
                        ),
                        CommonUtils.getSimpleText(
                          '$userName  ${getWeekday(attendanceDate)}',
                          14,
                          Colours.base_primary_text_title,
                          decoration: TextDecoration.none,
                        ),
                      ],
                    ),
                  ),
                  // 使用 Container 来代替 Positioned
                  Container(
                    margin: const EdgeInsets.only(left: 16.0),
                    child: InkWell(
                      child: const LoadAssetImage(
                        'base/icon_base_close',
                        width: 20,
                        height: 20,
                      ),
                      onTap: () {
                        onClick(null);
                      },
                    ),
                  ),
                ],
              ),
            ),
            Gaps.line,
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: dataEntity.list?.length,
                itemBuilder: (context, index) {
                  return ClassesChooseListItem(
                    data: dataEntity.list![index],
                    classUuid: classUuid,
                    onClick: () {
                      print('点击这里了 ${dataEntity.list![index].uuid}');
                      onClick(dataEntity.list![index]);
                    },
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  String getWeekday(String? dateString) {
    if (!TextUtil.isEmpty(dateString)) {
      DateTime dateTime = DateTime.parse(dateString!);
      List<String> weekdays = ['星期一', '星期二', '星期三', '星期四', '星期五', '星期六', '星期日'];
      // weekday 返回的是 1 (Monday) 到 7 (Sunday)
      int index = dateTime.weekday - 1;
      return '$dateString (${weekdays[index]})';
    }
    return '';
  }
}
