import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/project_classes_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/loading_util.dart';
import '../../../../widgets/custom_report_selector.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/custom_tag_number_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../credit_inquiry/bean/credit_inquiry_info_entity.dart';
import '../bean/attendance_data_entity.dart';
import '../controller/attendance_controller.dart';
import '../item/attendance_day_list_Item.dart';
import '../item/attendance_month_list_Item.dart';
import '../iview/attendance_iview.dart';
import '../persenter/attendance_persenter.dart';
import '../widget/custom_attendance_classes_view.dart';
import '../widget/custom_attendance_holiday_view.dart';
import '../widget/custom_attendance_result_view.dart';

/// 报表
class AttendancePage extends StatefulWidget {
  int showProject;
  String projectUuid;
  String projectName;

  AttendancePage({super.key, this.showProject = 0, required this.projectUuid, required this.projectName});

  @override
  _AttendancePageState createState() => _AttendancePageState();
}

class _AttendancePageState extends State<AttendancePage> with BasePageMixin<AttendancePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AttendancePage> implements AttendanceIView {
  AttendancePresenter? _presenter;

  final AttendanceController _controller = AttendanceController();

  DateTime currentMonth = DateTime.now();
  DateTime currentDay = DateTime.now();

  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectUuid.value = widget.projectUuid;
    _controller.projectName.value = widget.projectName;

    //等于1  获取当前项目的
    if (widget.showProject == 1 || (httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID)) {
      _controller.projectUuid.value = httpConfig.project_uuid;
      _controller.projectName.value = httpConfig.project_name;
    }

    // 格式化年份和月份
    _controller.searchDay.value = getFormattedDate(currentDay);
    _controller.searchMonth.value = getFormattedDate(currentMonth);

    _onRefresh();

    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
        _onRefresh();

        ///如果是点击了安卓 并且是全部项目的情况下，就跳转到考勤管理
        if (_controller.gotoAttendance.value) {
          BoostNavigator.instance.push('ProjectAttendanceManagerRulesPage', arguments: {
            'project_uuid': _controller.projectUuid.value,
            'project_name': _controller.projectName.value,
          });
          return;
        }
      });
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '考勤报表',
            centerSubTitle: _controller.projectName.value,
            actionWidget: Row(
              children: [
                InkWell(
                  onTap: () {
                    _controller.gotoAttendance.value = false;
                    BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                      "method": "goto_show_project_dialog",
                      "isChangeAppProject": CommonUtils.checkRoleHeadOffice(),
                      'isNeedAll': CommonUtils.checkRoleHeadOffice(),
                      'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                      'project_uuid': _controller.projectUuid.value,
                    });
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: LoadAssetImage(
                      "icon_change",
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    _controller.gotoAttendance.value = false;
                    BoostNavigator.instance.push('AttendanceExportPage', arguments: {'project_uuid': _controller.projectUuid.value, 'project_name': _controller.projectName.value});
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: LoadAssetImage(
                      "base/icon_base_share",
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
                InkWell(
                  onTap: () {
                    ///如果是全部项目的话，先去选择项目
                    if ('全部项目' == _controller.projectName.value) {
                      _controller.gotoAttendance.value = true;
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog", "isChangeAppProject": false, 'project_uuid': _controller.projectUuid.value});
                    } else {
                      _controller.gotoAttendance.value = false;
                      BoostNavigator.instance.push('ProjectAttendanceManagerRulesPage', arguments: {
                        'project_uuid': _controller.projectUuid.value,
                        'project_name': _controller.projectName.value,
                      });
                    }
                  },
                  child: const Padding(
                    padding: EdgeInsets.only(left: 10, right: 10),
                    child: LoadAssetImage(
                      "common/icon_attendance_group",
                      width: 24,
                      height: 24,
                    ),
                  ),
                ),
              ],
            ),
          ),
          body: WillPopScope(
              child: Column(
                children: [
                  Container(
                    padding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                    color: Colors.white,
                    child: Row(
                      children: [
                        Expanded(
                            child: CustomReportSelector(
                          options: const ['日报', '月报'],
                          onSelectedIndexChanged: (index) {
                            setState(() {
                              _controller.reportType = index;
                              if (index == 0) {
                                _controller.searchDay.value = getFormattedDate(currentDay);
                              } else {
                                _controller.searchMonth.value = getFormattedDate(currentMonth);
                              }
                              print('切换  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
                            });
                            _onRefresh();
                          },
                        )),
                        Gaps.hGap10,
                        Expanded(
                            flex: 2,
                            child: CustomSelectedArrowView(
                              dateText: (_controller.reportType == 0) ? _controller.searchDay.value : _controller.searchMonth.value,
                              onDateTextPressed: () {
                                BrnDatePicker.showDatePicker(
                                  themeData: BrnPickerConfig(
                                    pickerHeight: 300,
                                  ),
                                  context,
                                  initialDateTime: (_controller.reportType == 0) ? currentDay : currentMonth,
                                  pickerMode: BrnDateTimePickerMode.date,
                                  dateFormat: (_controller.reportType == 1) ? 'yyyy年,MMMM月' : 'yyyy年,MMMM月,dd日',
                                  onConfirm: (dateTime, list) {
                                    if (_controller.reportType == 0) {
                                      _controller.searchDay.value = DateUtil.formatDate(dateTime, format: (_controller.reportType == 1) ? 'yyyy-MM' : 'yyyy-MM-dd');
                                      currentDay = dateTime;
                                    } else {
                                      _controller.searchMonth.value = DateUtil.formatDate(dateTime, format: (_controller.reportType == 1) ? 'yyyy-MM' : 'yyyy-MM-dd');
                                      currentMonth = dateTime;
                                    }
                                    _onRefresh();
                                  },
                                );
                              },
                              onNextDayPressed: () {
                                _nextMonth();
                              },
                              onPreviousDayPressed: () {
                                _previousMonth();
                              },
                            )),
                      ],
                    ),
                  ),
                  Gaps.line,
                  Obx(() => CustomTagNumberView(
                        items: getTagData(_controller.data.value),
                        onItemSelected: (index) {
                          _controller.status.value = '$index';
                          _onRefresh();
                        },
                      )),
                  Gaps.vGap10,
                  Expanded(
                      child: MyRefreshListView(
                    itemCount: _controller.list.length,
                    onRefresh: _onRefresh,
                    loadMore: _loadMore,
                    padding: const EdgeInsets.all(0),
                    hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                    itemBuilder: (_, index) {
                      return _controller.reportType == 0
                          ? AttendanceDayListItem(
                              data: _controller.list[index],
                              onClick: (position) {
                                if (position == 0) {
                                  //修改班次 要查询班次
                                  _presenter?.getClassesManager(
                                    (_controller.list[index].isWorkDay == '2') ? "0" : _controller.list[index].classUuid,
                                    _controller.list[index].scheduleUuid,
                                    _controller.list[index].projectUuid,
                                    _controller.list[index].userName,
                                    _controller.list[index].kqDate,
                                  );
                                } else if (position == 1) {
                                  gotoAttendanceOne(
                                    _controller.list[index].userUuid ?? '',
                                    _controller.list[index].userName ?? '',
                                    _controller.projectUuid.value,
                                    _controller.searchDay.value,
                                  );
                                } else {
                                  gotoAttendanceOne(
                                    _controller.list[index].userUuid ?? '',
                                    _controller.list[index].userName ?? '',
                                    _controller.projectUuid.value,
                                    _controller.searchDay.value,
                                  );
                                }
                              },
                            )
                          : AttendanceMonthListItem(
                              data: _controller.list[index],
                              onClick: () {
                                gotoAttendanceOne(
                                  _controller.list[index].userUuid ?? '',
                                  _controller.list[index].userName ?? '',
                                  _controller.projectUuid.value,
                                  _controller.searchMonth.value,
                                );
                              },
                            );
                    },
                  )),
                ],
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
        ));
  }

  List<GridTagNumItem> getTagData(AttendanceDataEntity data) {
    List<GridTagNumItem> items = [
      GridTagNumItem(title: '全部', backgroundColor: Colors.grey, num: '${data.allTotal ?? 0}'),
      GridTagNumItem(title: '应出勤', backgroundColor: Colors.grey, num: '${data.cqTotal ?? 0}'),
      GridTagNumItem(title: '实出勤', backgroundColor: Colors.grey, num: '${data.actualCqTotal ?? 0}'),
      GridTagNumItem(title: '迟到', backgroundColor: Colors.red, num: '${data.cdTotal ?? 0}'),
      GridTagNumItem(title: '早退', backgroundColor: Colors.red, num: '${data.ztTotal ?? 0}'),
      GridTagNumItem(title: '缺卡', backgroundColor: Colors.red, num: '${data.qkTotal ?? 0}'),
      GridTagNumItem(title: '旷工', backgroundColor: Colors.red, num: '${data.kgTotal ?? 0}'),
      GridTagNumItem(title: '请假', backgroundColor: Colors.deepPurpleAccent, num: '${data.xjTotal ?? 0}'),
      GridTagNumItem(title: '加班', backgroundColor: Colors.blue, num: '${data.jbTotal ?? 0}'),
    ];
    return items;
  }

  ///上一天
  void _previousMonth() {
    if (_controller.reportType == 0) {
      currentDay = DateTime(currentDay.year, currentDay.month, currentDay.day - 1);
    } else {
      currentMonth = DateTime(currentMonth.year, currentMonth.month - 1);
    }
    _updateSearchValues();
    print('切换上  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
    _onRefresh();
  }

  ///下一天
  void _nextMonth() {
    if (_controller.reportType == 0) {
      currentDay = DateTime(currentDay.year, currentDay.month, currentDay.day + 1);
    } else {
      currentMonth = DateTime(currentMonth.year, currentMonth.month + 1);
    }
    _updateSearchValues();
    print('切换下  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
    _onRefresh();
  }

  void _updateSearchValues() {
    _controller.searchDay.value = getFormattedDate(currentDay);
    _controller.searchMonth.value = getFormattedDate(currentMonth);
  }

  String getFormattedDate(DateTime date) {
    if (_controller.reportType == 0) {
      return "${date.year.toString()}-${(date.month.toString().padLeft(2, '0'))}-${(date.day.toString().padLeft(2, '0'))}";
    } else {
      return "${date.year.toString()}-${(date.month.toString().padLeft(2, '0'))}";
    }
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AttendancePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void searchUser() {}

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? classUuid, String? scheduleUuid, String? user_name, String? attendance_date) {
    DialogManager.setDialogShowing(true);

    //往数据中增加一个休息的班次
    ProjectClassesDataList data = ProjectClassesDataList();
    data.uuid = '0';
    data.className = "休息日";
    data.classTime = "该日无需上班";
    dataEntity.list?.insert(0, data);
    print("提交之前的 $classUuid");
    //弹出dialog
    showModalBottomSheet(
      context: context,
      builder: (BuildContext dialogContext) {
        return CustomAttendanceClassesDialog(
          dataEntity: dataEntity,
          classUuid: classUuid ?? '0',
          userName: user_name,
          attendanceDate: attendance_date,
          onClick: (data) {
            DialogManager.setDialogShowing(false);
            Navigator.of(dialogContext).pop(); // 使用 dialogContext 关闭对话框
            if (data !=null ) {
              _presenter?.updateClassType(scheduleUuid, data.uuid, (data.uuid == '0') ? '1' : '2');
            }
          },
        );
      },
    );
  }

  @override
  void export(String downloadUrl, String exportDate) {
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': downloadUrl, 'fileName': '考勤报表-$exportDate'});
  }

  void gotoAttendanceOne(String userUuid, String userName, String projectUuid, String searchDay) {
    BoostNavigator.instance.push('AttendanceOnePage', arguments: {
      'user_uuid': userUuid,
      'user_name': userName,
      'project_uuid': projectUuid,
      'search_day_date': _controller.reportType == 0 ? searchDay : "",
    }).then((value) => _onRefresh());
  }
}
