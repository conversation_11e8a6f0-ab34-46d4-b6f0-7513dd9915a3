import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_day_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/my_attendance_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/attendance/bean/project_classes_data_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/gaps.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/attendance_update.dart';
import '../controller/attendance_one_controller.dart';
import '../iview/attendance_one_iview.dart';
import '../persenter/attendance_one_persenter.dart';

/// 考勤详情 - 修改考勤结果
class AttendanceResultRulesPage extends StatefulWidget {
  String? userName;
  String? uuid;
  bool hideMinutes;

  AttendanceResultRulesPage({super.key, this.userName, this.uuid, required this.hideMinutes});

  @override
  _AttendanceResultRulesPageState createState() => _AttendanceResultRulesPageState();
}

class _AttendanceResultRulesPageState extends State<AttendanceResultRulesPage> with BasePageMixin<AttendanceResultRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AttendanceResultRulesPage> implements AttendanceOneIView {
  AttendanceOnePresenter? _presenter;

  final AttendanceOneController _controller = AttendanceOneController();

  final TextEditingController _inWorkController = TextEditingController(); // 创建控制器
  final FocusNode _inWorkFocusNode = FocusNode(); // 创建 FocusNode

  final TextEditingController _outWorkController = TextEditingController(); // 创建控制器
  final FocusNode _outWorkFocusNode = FocusNode(); // 创建 FocusNode

  @override
  void dispose() {
    _inWorkFocusNode.dispose(); // 释放 FocusNode
    _inWorkController.dispose(); // 释放控制器
    _outWorkController.dispose(); // 释放 FocusNode
    _outWorkFocusNode.dispose(); // 释放控制器
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '修改考勤结果',
        centerSubTitle: '${widget.userName}',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Container(
            color: Colors.white,
            height: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Gaps.vGap20,
                CommonUtils.getSimpleText('上班卡', 15, Colours.base_primary_text_title),
                SelectTabWidget(
                  key: ValueKey('inWorkStatus-${_controller.inWorkStatusIndex.value}'),
                  // 确保唯一
                  _controller.statusList.value,
                  multiSelect: false,
                  crossAxisCount: 2,
                  hideMore: false,
                  paddingBottom: 0,
                  paddingTop: 6,
                  tabFontSize: 15,
                  defaultSelectedIndex: _controller.inWorkStatusIndex.value == -1 ? [] : [_controller.inWorkStatusIndex.value],
                  lastIsAddOne: false,
                  selectedColor: Colours.base_primary,
                  bgSelectedColor: Colours.base_primary_select,
                  bgUnSelectedColor: Colours.base_primary_un_select,
                  childAspectRatio: 8 / 2,
                  itemClickCallback: (List<int> indexs) {
                    LogUtil.e("indexs = $indexs");
                    _controller.inWorkStatusIndex.value = (indexs[0]).toInt();
                    // 取消焦点
                    FocusScope.of(context).unfocus();
                  },
                ),
                Visibility(
                  visible: widget.hideMinutes,
                  child: InkWell(
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: _controller.inWorkStatusIndex.value == 2 ? Colours.base_primary_select : Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CommonUtils.getSimpleText('设置迟到', 15, _controller.inWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title),
                          Gaps.hGap4,
                          SizedBox(
                            width: 30,
                            child: TextField(
                              maxLength: 3,
                              focusNode: _inWorkFocusNode,
                              // 绑定 FocusNode
                              style: TextStyle(
                                fontSize: 14,
                                height: 1.5,
                                color: _controller.inWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title,
                              ),
                              decoration: const InputDecoration(
                                border: InputBorder.none, // 保留下划线
                                hintText: '0',
                                hintStyle: TextStyle(color: Colors.grey),
                                counterText: '',
                              ),
                              keyboardType: TextInputType.number,
                              controller: _inWorkController,
                              onTap: () {
                                _controller.inWorkStatusIndex.value = 2;
                                FocusScope.of(context).requestFocus(_inWorkFocusNode); // 获取焦点
                              },
                            ),
                          ),
                          Gaps.hGap4,
                          CommonUtils.getSimpleText('分钟数', 15, _controller.inWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title),
                        ],
                      ),
                    ),
                    onTap: () {
                      _controller.inWorkStatusIndex.value = 2;
                      FocusScope.of(context).requestFocus(_inWorkFocusNode); // 获取焦点
                    },
                  ),
                ),

                ///下班卡
                Gaps.vGap20,
                CommonUtils.getSimpleText('下班卡', 15, Colours.base_primary_text_title),
                SelectTabWidget(
                  key: ValueKey('outWorkStatus-${_controller.outWorkStatusIndex.value}'),
                  // 确保唯一

                  _controller.statusList.value,
                  multiSelect: false,
                  crossAxisCount: 2,
                  hideMore: false,
                  paddingBottom: 0,
                  paddingTop: 6,
                  tabFontSize: 15,
                  defaultSelectedIndex: _controller.outWorkStatusIndex.value == -1 ? [] : [_controller.outWorkStatusIndex.value],
                  lastIsAddOne: false,
                  selectedColor: Colours.base_primary,
                  bgSelectedColor: Colours.base_primary_select,
                  bgUnSelectedColor: Colours.base_primary_un_select,
                  childAspectRatio: 8 / 2,
                  itemClickCallback: (List<int> indexs) {
                    LogUtil.e("indexs = $indexs");
                    _controller.outWorkStatusIndex.value = (indexs[0]).toInt();
                    // 取消焦点
                    FocusScope.of(context).unfocus();
                  },
                ),
                Visibility(
                  child: InkWell(
                    child: Container(
                      height: 40,
                      decoration: BoxDecoration(
                        color: _controller.outWorkStatusIndex.value == 2 ? Colours.base_primary_select : Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CommonUtils.getSimpleText('设置早退', 15, _controller.outWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title),
                          Gaps.hGap4,
                          SizedBox(
                            width: 30,
                            child: TextField(
                              maxLength: 3,
                              focusNode: _outWorkFocusNode,
                              // 绑定 FocusNode
                              style: TextStyle(
                                fontSize: 14,
                                height: 1.5,
                                color: _controller.outWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title,
                              ),
                              decoration: const InputDecoration(
                                border: InputBorder.none, // 保留下划线
                                hintText: '0',
                                hintStyle: TextStyle(color: Colors.grey),
                                counterText: '',
                              ),
                              keyboardType: TextInputType.number,
                              controller: _outWorkController,
                              onTap: () {
                                _controller.outWorkStatusIndex.value = 2;
                                FocusScope.of(context).requestFocus(_outWorkFocusNode); // 获取焦点
                              },
                            ),
                          ),
                          Gaps.hGap4,
                          CommonUtils.getSimpleText('分钟数', 15, _controller.outWorkStatusIndex.value == 2 ? Colours.base_primary : Colours.base_primary_text_title),
                        ],
                      ),
                    ),
                    onTap: () {
                      _controller.outWorkStatusIndex.value = 2;
                      FocusScope.of(context).requestFocus(_outWorkFocusNode); // 获取焦点
                    },
                  ),
                  visible: widget.hideMinutes,
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 80,
        child: Column(
          children: [
            Gaps.line,
            Gaps.vGap10,
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: Row(
                children: [
                  InkWell(
                    child: Container(
                      padding: const EdgeInsets.only(left: 30, right: 30, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.transparent,
                        border: Border.all(color: Colours.base_primary, width: 1),
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: Column(
                        children: [
                          CommonUtils.getSimpleText('取消修改', 16, Colours.base_primary, fontWeight: FontWeight.bold),
                          CommonUtils.getSimpleText('以打卡记录为准算考勤', 13, Colours.base_primary),
                        ],
                      ),
                    ),
                    onTap: () {
                      BoostNavigator.instance.pop(widget.uuid);
                    },
                  ),
                  Gaps.hGap10,
                  Expanded(
                      child: InkWell(
                    child: Container(
                      padding: EdgeInsets.only(left: 20, right: 20, top: 16, bottom: 18),
                      decoration: BoxDecoration(
                        color: Colours.base_primary,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('确定', 20, Colours.white, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      var update = AttendanceUpdate(
                        uuid: widget.uuid ?? '',
                        cdStatus: _controller.inWorkStatusIndex.value,
                        cdLong: (!TextUtil.isEmpty(_inWorkController.text.toString()) ? _inWorkController.text.toString() : '0'),
                        ztStatus: _controller.outWorkStatusIndex.value,
                        ztLong: (!TextUtil.isEmpty(_outWorkController.text.toString()) ? _outWorkController.text.toString() : '0'),
                      );
                      BoostNavigator.instance.pop(update);
                    },
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AttendanceOnePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? userName, String? attendanceDate) {}

  @override
  void getMyAttendanceDayOne(MyAttendanceDayEntity data) {}

  @override
  void getMyAttendanceOne(MyAttendanceOneEntity data) {}

  @override
  void startGetMyAttendanceOne() {}
}
