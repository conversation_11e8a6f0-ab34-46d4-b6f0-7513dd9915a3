import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../attendance/bean/project_classes_data_entity.dart';
import '../controller/attendance_controller.dart';
import '../iview/attendance_iview.dart';
import '../persenter/attendance_persenter.dart';

class AttendanceExportPage extends StatefulWidget {
  String projectUuid;
  String projectName;

  AttendanceExportPage({super.key, required this.projectUuid, required this.projectName});

  @override
  _AttendanceExportPageState createState() => _AttendanceExportPageState();
}

class _AttendanceExportPageState extends State<AttendanceExportPage> with BasePageMixin<AttendanceExportPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<AttendanceExportPage> implements AttendanceIView {
  AttendancePresenter? _presenter;

  final AttendanceController _controller = AttendanceController();
  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();

    _controller.projectExportUuid.value = widget.projectUuid;
    _controller.projectExportName.value = widget.projectName;

    if ((httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID)) {
      _controller.projectExportUuid.value = httpConfig.project_uuid;
      _controller.projectExportName.value = httpConfig.project_name;
    }

    ///监听切换项目
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectExportUuid.value = project_uuid;
        _controller.projectExportName.value = project_name;
      });
    });

    ///默认获取本月
    DateTime now = DateTime.now();
    _controller.projectExportDate.value = '${now.year}年${CommonUtils.formatToTwoDigits(now.month)}月';
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          backgroundColor: Colours.base_primary_bg_page,
          appBar: MyAppBar(
            centerTitle: '导出考勤统计',
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          body: Column(
            children: [
              BrnTextSelectFormItem(
                isRequire: true,
                value: _controller.projectExportName.value,
                title: "导出项目",
                onTap: () {
                  BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                    "method": "goto_show_project_dialog",
                    "isChangeAppProject": false,
                    'isNeedAll': CommonUtils.checkRoleHeadOffice(),
                    'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                    'project_uuid': _controller.projectExportUuid.value,
                  });
                },
              ),
              Gaps.line,
              BrnTextSelectFormItem(
                isRequire: true,
                value: _controller.projectExportDate.value,
                title: "日期范围",
                onTap: () {
                  DateTime now = DateTime.now();
                  DateTime startDate = DateTime(now.year, now.month - 8);
                  DateTime endDate = DateTime(now.year, now.month);

                  if (startDate.month < 1) {
                    startDate = DateTime(startDate.year - 1, startDate.month + 12);
                  }
                  if (endDate.month > 12) {
                    endDate = DateTime(endDate.year + 1, endDate.month - 12);
                  }

                  List<String> monthsInRange = _controller.getMonthsInRange(startDate, endDate);
                  int currentIndex = _controller.getCurrentMonthIndex(monthsInRange);

                  SingleColumnDataPickerView.showSingleColumnDataPicker(context, '导出考勤统计', monthsInRange, currentIndex, (index, selectedText) {
                    print('当前选择的下标：$index');
                    print('当前选择的文本内容：$selectedText');

                    // 解析选中的年份和月份
                    String yearStr = selectedText.substring(0, 4);
                    String monthStr = selectedText.substring(5, 7);
                    int year = int.parse(yearStr);
                    int month = int.parse(monthStr);

                    // 计算第一天和最后一天
                    DateTime firstDayOfMonth = DateTime(year, month);
                    DateTime lastDayOfMonth = DateTime(year, month + 1).subtract(const Duration(days: 1));

                    print('选择的日期：$firstDayOfMonth - $lastDayOfMonth');
                    _controller.projectExportDate.value = selectedText;
                  });
                },
              ),
              Gaps.line,
              Container(
                color: Colors.white,
                padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                    Expanded(child: Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("报表类型", 16, Colours.base_primary_text_title))),
                    Expanded(
                        flex: 2,
                        child: Container(
                          margin: const EdgeInsets.only(left: 0, right: 0),
                          child: SelectTabWidget(
                            _controller.exportTypeList.value,
                            multiSelect: false,
                            crossAxisCount: 2,
                            hideMore: false,
                            paddingBottom: 0,
                            paddingTop: 0,
                            tabFontSize: 15,
                            defaultSelectedIndex: [0],
                            lastIsAddOne: false,
                            selectedColor: Colours.base_primary,
                            bgSelectedColor: Colours.base_primary_select,
                            bgUnSelectedColor: Colours.base_primary_un_select,
                            childAspectRatio: 6 / 2,
                            itemClickCallback: (List<int> indexs) {
                              LogUtil.e("indexs = $indexs");
                              _controller.projectExportType.value = "${(indexs[0])}";
                              LogUtil.e("indexs = ${_controller.projectExportType.value}");
                            },
                          ),
                        )),
                  ],
                ),
              ),
              Container(
                height: 60,
                margin: EdgeInsets.only(top: 10),
                padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
                child: BrnBigMainButton(
                  bgColor: Colours.base_primary_green,
                  title: '确定',
                  onTap: () {
                    if (_controller.projectExportType.value == '0') {
                      _presenter?.exportAttendanceResult(_controller.projectExportUuid.value, _controller.projectExportDate.value);
                    } else {
                      _presenter?.exportAttendance(_controller.projectExportUuid.value, _controller.projectExportDate.value, _controller.projectExportType.value);
                    }
                  },
                ),
              )
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AttendancePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void onReset() {}

  @override
  void export(String downloadUrl, String exportDate) {
    String typeName = "考勤结果";
    if (_controller.projectExportType.value == '0') {
      typeName = '考勤结果';
    } else if (_controller.projectExportType.value == '1') {
      typeName = '有效打卡';
    } else if (_controller.projectExportType.value == '2') {
      typeName = '原始打卡';
    } else {
      typeName = '签字表';
    }
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': downloadUrl, 'fileName': '考勤表-$typeName-$exportDate'});
  }

  @override
  void searchUser() {}

  @override
  void getClasses(ProjectClassesDataEntity dataEntity, String? classUuid, String? scheduleUuid, String? userName, String? attendanceDate) {}
}
