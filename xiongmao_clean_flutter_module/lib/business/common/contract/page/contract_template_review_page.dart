import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_template_review_page_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_template_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/persenter/contract_template_review_page_persenter.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_button.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/gaps.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/toast_utils.dart';
import '../../../../widgets/my_bottom_button_one.dart';
import '../controller/contract_main_info_controller.dart';
import '../controller/contract_template_page_controller.dart';

class ContractTemplateReviewPage extends StatefulWidget {
  String url;
  int index;

  ContractTemplateReviewPage({this.url = "https://media.jiazhengye.cn/pdf/20240104006389-policy-65964acda8c86.pdf", required this.index});

  @override
  _ContractTemplateReviewPageState createState() => _ContractTemplateReviewPageState();
}

class _ContractTemplateReviewPageState extends State<ContractTemplateReviewPage> with BasePageMixin<ContractTemplateReviewPage, PowerPresenter<dynamic>> implements ContractTemplateReviewPageIView {
  late WebViewController webViewController;
  String _title = "详情";
  late ContractTemplateReviewPagePresenter _presenter;

  @override
  void initState() {
    super.initState();
    webViewController = WebViewController()
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..loadRequest(Uri.parse(widget.url));

    // 监听页面加载完成获取标题
    webViewController.setNavigationDelegate(
      NavigationDelegate(
        onPageFinished: (url) {
          webViewController.getTitle().then((title) {
            setState(() {
              _title = title ?? "详情";
            });
          });

          // var string = SpUtil.getString(Constant.UserAgent);
          // if(string==null||string==""){
          //   controller.runJavaScriptReturningResult('navigator.userAgent').then((userAgent){
          //     print('User Agent--------:'+userAgent.toString().substring(1,userAgent.toString().length-1));
          //     SpUtil.putString(Constant.UserAgent, userAgent.toString().substring(1,userAgent.toString().length-1));
          //   });
          // }
        },
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    var controller = Get.find<ContractTemplatePageController>();
    var value = controller.contractList.value;
    var item = value[widget.index];
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: _title,
      ),
      body: Column(
        children: [
          Expanded(
              child: WebViewWidget(
            controller: webViewController,
          )),
          Visibility(
            visible: value[widget.index].isSelf == "1" && value[widget.index].templateStatus == "1",
            child: Padding(
              padding: EdgeInsets.symmetric(vertical: 6, horizontal: 6),
              child: Row(
                children: [
                  Expanded(
                      flex: 2,
                      child: MyButton(
                          minHeight: 40,
                          text: "删除",
                          radius: 4,
                          side: BorderSide(width: 1, color: Colours.base_primary),
                          textColor: Colours.base_primary,
                          backgroundColor: Colors.white,
                          onPressed: () {
                            BrnDialogManager.showConfirmDialog(context, title: "提示", message: '确定要删除该合同模版吗?', cancel: '取消', confirm: '确定', barrierDismissible: false, onConfirm: () {
                              Get.back();
                              _presenter.deleteContractTemplate(value[widget.index].uuid ?? "");
                            }, onCancel: () {
                              Get.back();
                            });
                          })),
                  Gaps.hGap12,
                  Expanded(
                      flex: 5,
                      child: MyBottomButtonOne(
                          title: "编辑",
                          horizontalPadding: 0,
                          isNeedDivideLine: false,
                          onPressed: () {
                            var selectTagIndex = (controller.type.value == TYPE_ENTRY ? controller.selectTagIndex.value : 0);
                            var contract_type = (controller.type.value == TYPE_ENTRY ? "入职-" : "离职-") + (controller.contractTagList.value[selectTagIndex].labelName ?? "");
                            var subLabelCode = controller.contractTagList.value[selectTagIndex].code;
                            var uuid = value[widget.index].uuid;
                            var template_title = value[widget.index].templateTitle;
                            var template_url = value[widget.index].templateUrl;
                            BoostNavigator.instance.push("addContractTemplatePage", arguments: {"contract_type": contract_type, "sub_label_code": subLabelCode, "template_title": template_title, "template_url": template_url, "uuid": uuid}).then((value) {
                              Toast.show("编辑成功");
                              BoostNavigator.instance.pop();
                            });
                          })),
                ],
              ),
            ),
          ) //已审核
        ],
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractTemplateReviewPagePresenter();
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void deleteContractTemplate() {
    Toast.show("删除成功");
    BoostNavigator.instance.pop();
  }
}
