import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_template_page_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../iview/add_contract_template_iview.dart';
import '../persenter/add_contract_template_persenter.dart';

/**
 * 添加合同模版
 */
class AddContractTemplatePage extends StatefulWidget {
  String? contract_type = "";
  String? sub_label_code = "";
  String? uuid = "";
  String? template_title = "";
  String? template_url = "";

  AddContractTemplatePage({Key? key, this.contract_type = "", this.sub_label_code = "", this.uuid = "", this.template_title, this.template_url}) : super(key: key);

  @override
  _AddContractTemplatePageState createState() => _AddContractTemplatePageState();
}

class _AddContractTemplatePageState extends State<AddContractTemplatePage> with BasePageMixin<AddContractTemplatePage, PowerPresenter<dynamic>> implements AddContractTemplatePageIView {
  AddContractTemplatePagePresenter? _presenter;

  final _contractTemplateControl = TextEditingController();
  final _contractNameControl = TextEditingController();

  late VoidCallback addListener;
  var fileName = "";

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.contract_type)) {
      _contractTemplateControl.text = widget.contract_type!;
    }
    if (widget.template_title != null && widget.template_title != "") {
      _contractNameControl.text = widget.template_title ?? "";
    }
    addListener = BoostChannel.instance.addEventListener("ChooseNativeFile", (key, arguments) async {
      var filePath = arguments["filePath"];
      fileName = arguments["fileName"];
      if ((fileName as String).contains(".")) {
        var split = (fileName as String).split(".");
        _contractNameControl.text = split[0];
      } else {
        _contractNameControl.text = fileName;
      }
      LogUtil.e("object--filePath-----" + filePath.toString() + "======" + fileName);
      BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "uploadFileToQiniu", "filePath": filePath});
    });

    BoostChannel.instance.addEventListener("UploadFile", (key, arguments) async {
      var url = arguments["url"];
      widget.template_url = url;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: (widget.uuid != null && widget.uuid != "") ? '编辑合同模版' : '添加合同模版',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Container(
        margin: EdgeInsets.only(top: 10),
        child: Column(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Opacity(
                    opacity: 0.6,
                    child: BrnTextInputFormItem(
                      title: "合同类型",
                      isRequire: false,
                      hint: "请输入",
                      isEdit: false,
                      controller: _contractTemplateControl,
                    ),
                  ),
                  Gaps.vBlock12,
                  Opacity(
                    opacity: (widget.uuid != null && widget.uuid != "") ? 0.6 : 1.0,
                    child: BrnTextSelectFormItem(
                      title: "上传合同文件",
                      hint: "请选择",
                      value: (fileName == "") ? ((widget.template_url != null && widget.template_url != "") ? '已上传' : "请选择") : fileName,
                      isEdit: (widget.uuid != null && widget.uuid != "") ? false : true,
                      onTap: () {
                        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoChooseNativeFile"});
                      },
                    ),
                  ),
                  Visibility(
                    child: Padding(
                      padding: EdgeInsets.only(left: 16, right: 16, top: 4, bottom: 4),
                      child: CommonUtils.getSimpleText("可选择1个doc或docx文件", 12, Colours.base_primary_text_title),
                    ),
                    visible: (widget.uuid == null || widget.uuid == ""),
                  ),
                  Gaps.vGap12,
                  BrnTextInputFormItem(
                    title: "合同模版名称",
                    hint: "请输入",
                    isRequire: true,
                    controller: _contractNameControl,
                  ),
                ],
              ),
            ),
            Container(
              color: Colors.white,
              padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Visibility(
                    child: CommonUtils.getSimpleText("电子合同模板内容审核及制作需要3-5个工作日", 10, Colours.base_primary),
                    visible: (widget.uuid == null || widget.uuid == ""),
                  ),
                  Visibility(
                    child: Gaps.vGap4,
                    visible: (widget.uuid == null || widget.uuid == ""),
                  ),
                  BrnBigMainButton(
                    themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                    title: '确定',
                    onTap: () {
                      if (_contractNameControl.text.toString() == "") {
                        Toast.show("请输入合同模版名称");
                        return;
                      }
                      HashMap<String, String> params = HashMap();
                      if (widget.template_url != "" && widget.template_url != null) {
                        params["template_url"] = widget.template_url ?? "";
                      } else {
                        Toast.show("请上传合同文件");
                        return;
                      }
                      params["template_title"] = _contractNameControl.text.toString();
                      params["sub_label_code"] = widget.sub_label_code ?? "";
                      if (widget.uuid != "" && widget.uuid != null) {
                        params["uuid"] = widget.uuid ?? "";
                      }
                      _presenter?.requestEditContractTemplate(params);
                    },
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddContractTemplatePagePresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  requestEditContractTemplate() {
    //不管新增还是新建都主动刷新上一层界面
    BrnToast.show("操作成功", context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
