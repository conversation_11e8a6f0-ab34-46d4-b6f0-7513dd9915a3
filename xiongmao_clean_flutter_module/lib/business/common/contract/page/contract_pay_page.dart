import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../controller/contract_pay_info_controller.dart';
import '../item/contract_pay_info_grid_view.dart';
import '../iview/contract_pay_info_iview.dart';
import '../persenter/contract_pay_persenter.dart';

/**
 * 电子合同-购买电子合同
 */
class ContractPaylPage extends StatefulWidget {
  @override
  ContractPayPageState createState() => ContractPayPageState();
}

class ContractPayPageState extends State<ContractPaylPage> with BasePageMixin<ContractPaylPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ContractPaylPage> implements ContractPayInfoIView {
  ContractPayInfoPresenter? _presenter;

  final ContractPayInfoController _controller = ContractPayInfoController();

  int selectedIndex = 0;

  String? currentNumber;

  @override
  void initState() {
    super.initState();
    _presenter?.getContractInfo();
    Get.put(this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '购买电子合同',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Column(
          children: [
            //顶部信息
            Container(
              margin: EdgeInsets.only(top: 10),
              padding: EdgeInsets.only(top: 16, bottom: 16, right: 16, left: 16),
              color: Colors.white,
              width: double.infinity,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '购买的电子合同，将用于公司下全部项目使用',
                    style: TextStyle(color: Colors.grey, fontSize: 13),
                  ),
                  Gaps.vGap10,
                  Obx(() => ContractPayInfoGridView(
                      items: _controller.list.value,
                      onItemSelected: (value) {
                        currentNumber = _controller.list.value[value].number;
                        _controller.updatePrice(_controller.list.value[value].price ?? "", _controller.list.value[value].amount ?? "");
                      }))
                ],
              ),
            ),
            Gaps.vGap10,
            Padding(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: Text(
                '小贴士\n1.电子合同签章服务涉及到企业/个人认证服务、企业/个人CA证书申请服务、合同固化防篡改服务等，签署过程中自动完成，保证签约安全性。\n2.购买后，公司下全部项目均可使用消耗。\n3.提交入离职申请后，将自动扣除合同份数。\n4.当合同份数不足时，将无法发起签署，充值后将为您自动补发。',
                style: TextStyle(color: Colors.grey, fontSize: 13),
              ),
            )
          ],
        ),
      ),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        padding: EdgeInsets.only(left: 16, right: 16, top: 10, bottom: 10),
        child: Row(
          children: [
            Expanded(
                child: Obx(() => Text(
                      "共计：${_controller.price.value}元",
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
                    ))),
            Expanded(
                child: BrnBigMainButton(
              bgColor: Colours.base_primary_green,
              title: '立即支付',
              onTap: () {
                SpUtil.putString(Constant.buyContractAmount, _controller.amount.value);
                //调用原生支付
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "pay_native", "number": currentNumber ?? (_controller.list[selectedIndex].number ?? "")});
              },
            )),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractPayInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  void finishPage() {
    BoostNavigator.instance.pop();
  }
}
