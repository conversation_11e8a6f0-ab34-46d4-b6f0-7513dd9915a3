import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_main_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/constant.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_border_text_view.dart';
import '../item/contract_listview.dart';
import '../item/contract_pay_info_grid_view.dart';
import 'contract_pay_page.dart';

/**
 * 电子合同-购买电子合同成功
 */
class ContractPaySuccessPage extends StatefulWidget {
  @override
  _ContractPaySuccessPageState createState() => _ContractPaySuccessPageState();
}

class _ContractPaySuccessPageState extends State<ContractPaySuccessPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var string = SpUtil.getString(Constant.buyContractAmount);
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '支付结果',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: BrnAbnormalStateWidget(
        img: Image.asset(
          'assets/images/no_data.png',
          scale: 3.0,
        ),
        title: "支付成功",
        content: '成功购买' + (string ?? "") + '份电子合同',
        operateAreaType: OperateAreaType.singleButton,
        operateTexts: ["返回"],
        action: (_) {
          // BrnToast.show("第$_个按钮被点击了", context);
          // Get.until((route) => route.settings.name=="ContractPage");
          BoostNavigator.instance.pop();
        },
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
    var find = Get.find<ContractPayPageState>();
    find.finishPage();
  }
}
