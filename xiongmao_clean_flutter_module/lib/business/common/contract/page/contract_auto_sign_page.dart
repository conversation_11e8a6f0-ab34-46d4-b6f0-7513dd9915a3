import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/persenter/contract_edit_seal_persenter.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/add_signature_result_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/log_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/xm_webview/xm_js_msg_call.dart';
import '../../../../widgets/xm_webview/xm_web_controller.dart';
import '../../../../widgets/xm_webview/xm_webview.dart';
import '../iview/contract_edit_seal_iview.dart';

/// 电子合同-自动签署
class ContractAutoSignPage extends StatefulWidget {
  @override
  _ContractAutoSignPageState createState() => _ContractAutoSignPageState();

  final String? url;

  ContractAutoSignPage({this.url});
}

class _ContractAutoSignPageState extends State<ContractAutoSignPage> {
  late XmWebController controller;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '电子合同',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Expanded(
                child: XmWebView(
              needUserAgent: false,
              onXmJsMsgCall: (XmJsMsgCall msgCall) {
                var decode = json.decode(msgCall.message);
                var name = decode["name"];
                var data = decode["data"];
                switch (name) {
                  case "WebViewJavascriptBridge_navigateBack":
                    BoostNavigator.instance.pop();
                    break;
                }
              },
              controllerSettingBack: (value) {
                controller = value;
                LogUtil.e("object----url---" + (widget.url ?? ""));
                controller.loadRequest(Uri.parse(widget.url ?? ""));
              },
              domainM2: false,
            ))
          ],
        ),
      ),
    );
  }
}
