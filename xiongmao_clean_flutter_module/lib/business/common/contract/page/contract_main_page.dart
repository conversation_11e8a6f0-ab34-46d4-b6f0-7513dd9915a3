import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/scheduler/ticker.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_electronic_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_pay_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_template_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/log_utils.dart';
import '../../../../util/permission_util.dart';
import '../../../../widgets/custom_border_text_view.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../../../widgets/wrap_content_height_sliver_header.dart';
import '../../base/base_web_page.dart';
import '../../company/bean/department_company_entity.dart';
import '../controller/contract_main_info_controller.dart';
import '../iview/contract_main_info_iview.dart';
import '../persenter/contract_main_persenter.dart';
import 'contract_edit_seal_page.dart';
import 'contract_history_page.dart';

/// 电子签设置
class ContractPage extends StatefulWidget {
  @override
  _ContractPageState createState() => _ContractPageState();
}

class _ContractPageState extends State<ContractPage> with BasePageMixin<ContractPage, PowerPresenter<dynamic>>, SingleTickerProviderStateMixin implements ContractMainInfoIView {
  ContractMainInfoPresenter? _presenter;
  final ContractMainInfoController _controller = Get.put(ContractMainInfoController());
  final _outerRefreshIndicatorKey = GlobalKey<RefreshIndicatorState>();
  final _scrollViewController = ScrollController();

  late TabController _tabController;
  List tabs = ["入职合同模板", "离职合同模板"];
  final _pageController = PageController();

  late ContractTemplatePage entryPage;
  late ContractTemplatePage dimissionPage;

  void initState() {
    super.initState();

    _tabController = TabController(vsync: this, length: tabs.length);
    _tabController.addListener(() {
      _handleTabSelection();
    });
    //获取合同类型
    _presenter?.getContractMainInfo();

    entryPage = ContractTemplatePage(TYPE_ENTRY);
    dimissionPage = ContractTemplatePage(TYPE_SEPARATION);
  }

  void _handleTabSelection() {
    FocusScope.of(context).requestFocus(FocusNode());
    if (_tabController.indexIsChanging) {
      // 当前选中的选项卡发生变化
      MyLog.e('Selected tab: ${_tabController.index}');
      _pageController.jumpToPage(_tabController.index);
      _controller.updateCurrentTabIndex(_tabController.index);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: true,
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
          centerTitle: '电子签设置',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      body: SafeArea(
          child: Column(
        children: [
          Expanded(
              child: Container(
            height: 500,
            child: RefreshIndicator(
              key: _outerRefreshIndicatorKey,
              onRefresh: () async {
                // notifyData();
              },
              child: NestedScrollView(
                controller: _scrollViewController,
                headerSliverBuilder: (BuildContext context, bool innerBoxIsScrolled) {
                  return [
                    ///顶部的其他信息
                    WrapContentHeightSliverHeader(
                        child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        //顶部信息
                        Container(
                          padding: const EdgeInsets.only(left: 16, right: 10, top: 10, bottom: 10),
                          color: Colors.white,
                          child: Row(
                            children: [
                              //左边的内容
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        _controller.infoEntity.value.contractCount ?? "0",
                                        style: const TextStyle(color: Colours.base_primary, fontWeight: FontWeight.bold, fontSize: 21),
                                      ),
                                      const Text(
                                        " 份",
                                        style: TextStyle(color: Colours.base_primary_text_body, fontSize: 14),
                                      ),
                                    ],
                                  ),
                                  const Text(
                                    "电子合同剩余可用份数",
                                    style: TextStyle(color: Colours.base_primary_text_body, fontSize: 14),
                                  )
                                ],
                              )),
                              //右边的内容
                              Row(
                                // mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  BrnSmallOutlineButton(
                                    title: "历史记录",
                                    fontSize: 14,
                                    themeData: BrnButtonConfig(bigButtonHeight: 27),
                                    textColor: Colours.base_primary,
                                    lineColor: Colours.base_primary,
                                    onTap: () {
                                      BoostNavigator.instance.push("contractHistoryPage");
                                    },
                                  ),
                                  Gaps.hGap8,
                                  BrnSmallMainButton(
                                    title: '购买',
                                    fontSize: 14,
                                    width: 60,
                                    textColor: Colours.white,
                                    bgColor: Colours.base_primary,
                                    onTap: () {
                                      // BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "pay_native"});
                                      BoostNavigator.instance.push("contractPayPage").then((value) => _presenter?.getContractMainInfo());
                                    },
                                  )
                                ],
                              ),
                            ],
                          ),
                        ),
                        //合同签署方式介绍
                        Container(
                          color: Colors.white,
                          margin: const EdgeInsets.only(top: 10),
                          padding: const EdgeInsets.all(16.0),
                          child: const Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Expanded(
                                    child: Text(
                                      "合同签署方式",
                                      style: TextStyle(fontSize: 15, fontWeight: FontWeight.bold, color: Colours.base_primary_text_title),
                                    ),
                                  ),
                                  Text(
                                    "实名认证签署",
                                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colours.base_primary_text_title),
                                  ),
                                ],
                              ),
                              Gaps.vGap8,
                              Text(
                                "1.该方式的电子合同，符合国家《民法典》对电子合同的要求，具有完整的法律效力。\n2.签署过程需要签署方实名信息认证，对接国家认证权威机构颁发的CA证书，作签署信息存案。\n3.每发起一次签署，消耗一次合同份数。",
                                style: TextStyle(color: Colours.base_primary_text_body, fontSize: 13),
                              ),
                            ],
                          ),
                        ),
                        Gaps.vBlock12,

                        /// 入职、离职的配置
                        Container(
                          padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0, top: 16.0),
                          color: Colors.white,
                          child: Row(
                            children: [
                              const Expanded(
                                  child: Text(
                                "应用场景",
                                style: TextStyle(
                                  fontSize: 14,
                                  fontWeight: FontWeight.bold,
                                ),
                              )),
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.0),
                                  border: Border.all(color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.entryContract ?? false) ? Colours.base_primary : Colours.base_primary_line_b),
                                  color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.entryContract ?? false) ? Colours.base_primary_green_trans : Colours.white,
                                ),
                                padding: EdgeInsets.only(left: 20.0, right: 20, top: 4, bottom: 4),
                                child: InkWell(
                                  child: Center(
                                    child: Text(
                                      "入职",
                                      style: TextStyle(
                                        color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.entryContract ?? false) ? Colours.base_primary : Colours.base_primary_text_body,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  onTap: () {
                                    _presenter?.getUpdateContractStatus('{"EntryContract":${!(_controller.infoEntity.value.signSetting?.labelEnabledStatus!.entryContract ?? false)}}');
                                  },
                                ),
                              ),
                              Gaps.hGap10,
                              Container(
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(30.0),
                                  border: Border.all(color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.separationContract ?? false) ? Colours.base_primary : Colours.base_primary_line_b),
                                  color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.separationContract ?? false) ? Colours.base_primary_green_trans : Colours.white,
                                ),
                                padding: EdgeInsets.only(left: 20.0, right: 20, top: 4, bottom: 4),
                                child: InkWell(
                                  child: Center(
                                    child: Text(
                                      "离职",
                                      style: TextStyle(
                                        color: (_controller.infoEntity.value.signSetting?.labelEnabledStatus?.separationContract ?? false) ? Colours.base_primary : Colours.base_primary_text_body,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  onTap: () {
                                    _presenter?.getUpdateContractStatus('{"SeparationContract":${!(_controller.infoEntity.value.signSetting?.labelEnabledStatus?.separationContract ?? false)}}');
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        Gaps.line,
                        Obx(() => InkWell(
                              child: Container(
                                padding: const EdgeInsets.only(left: 16.0, right: 16.0, bottom: 16.0, top: 16.0),
                                color: Colors.white,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.center,
                                  children: [
                                    const Expanded(
                                        child: Text(
                                      "适用部门",
                                      style: TextStyle(
                                        fontSize: 14,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    )),
                                    CommonUtils.getSimpleText(_controller.department.value, 14, Colours.base_primary_text_title),
                                    Gaps.hGap4,
                                    const LoadAssetImage(
                                      'base/icon_base_gray_arrow',
                                      width: 14,
                                      height: 14,
                                    ),
                                  ],
                                ),
                              ),
                              onTap: () {
                                ///跳转到其他界面选择上级部门
                                BoostNavigator.instance.push('companyChoiceAdministrationPage', arguments: {'multiple': true, "choice_uuids": _controller.companyUuids}).then((value) {
                                  if (value != null) {
                                    List<DepartmentCompanyList> departmentCompanyList = value as List<DepartmentCompanyList>;
                                    _controller.companyUuids.value = departmentCompanyList.map((e) => e.uuid ?? '').toList();

                                    /// 提取 departmentName 并拼接成逗号分隔的字符串（限制最多显示两个部门名称）
                                    var shownDepartments = departmentCompanyList.take(2).map((e) => e.departmentName ?? '');
                                    var departmentNames = shownDepartments.join(',');

                                    if (departmentCompanyList.length > 2) {
                                      departmentNames += '...'; // 如果超过两个部门，添加省略号
                                    }

                                    // 始终显示“等X个部门”
                                    _controller.department.value = '$departmentNames${(departmentCompanyList.length > 2 ? '等' : '')}${departmentCompanyList.length}个部门';
                                    if (departmentCompanyList.isNotEmpty) {
                                      ///更改状态
                                      _presenter?.getUpdateContractStatus(null);
                                    }
                                  } else {
                                    _controller.companyUuids.value = [];
                                    _presenter?.getUpdateContractStatus(null);
                                  }
                                });
                              },
                            )),
                        Gaps.vBlock12,
                      ],
                    )),

                    ///入离职合同模版
                    WrapContentHeightSliverHeader(
                        pinned: true,
                        child: Visibility(
                            visible: true,
                            child: Container(
                              color: Colours.white,
                              width: double.maxFinite,
                              child: TabBar(
                                //下面isScrollable和padding是为了tab居左对齐 去掉则为居中
                                // isScrollable: true,
                                // padding: EdgeInsets.only(left: 0),
                                controller: _tabController,
                                labelColor: Colours.base_primary_text_title,
                                unselectedLabelColor: Colours.base_primary_text_caption,
                                labelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                                unselectedLabelStyle: TextStyle(fontSize: 15, fontWeight: FontWeight.bold),
                                indicatorColor: Colours.base_primary,
                                indicatorSize: TabBarIndicatorSize.label,
                                // indicatorPadding:
                                // const EdgeInsets.only(left: 15, right: 15),
                                tabs: tabs.map((e) => Tab(text: e)).toList(),
                              ),
                            ))),
                  ];
                },
                body: PageView(
                  controller: _pageController,
                  children: [entryPage, dimissionPage],
                  onPageChanged: (index) {
                    _tabController.animateTo(index);
                    _controller.updateCurrentTabIndex(_tabController.index);
                  },
                ),
              ),
            ),
          )),
        ],
      )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractMainInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void dispose() {
    super.dispose();
  }

  void getContractRegisterUser(bool enforce) {
    if (enforce) {
      BrnDialogManager.showConfirmDialog(context, title: "提示", message: '以前的认证信息都将清除，是否确认执行？', cancel: '取消', confirm: '确定', barrierDismissible: false, onConfirm: () {
        Get.back();
        _presenter?.getContractRegisterUser(enforce);
      }, onCancel: () {
        Get.back();
      });
    } else {
      _presenter?.getContractRegisterUser(enforce);
    }
  }

  @override
  void getContractMainInfo(ContractMainElectronicEntity data) {
    _controller.updateContractMainInfoEntity(data);
    setState(() {});
  }
}
