import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_obx_widget.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_history_iview.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_border_text_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/contract_history_controller.dart';
import '../item/contract_listview.dart';
import '../item/contract_pay_info_grid_view.dart';
import '../persenter/contract_history_presenter.dart';

/**
 * 电子合同-电子合同历史记录
 */
class ContractHistorylPage extends StatefulWidget {
  @override
  _ContractHistoryPageState createState() => _ContractHistoryPageState();
}

class _ContractHistoryPageState extends State<ContractHistorylPage> with BasePageMixin<ContractHistorylPage, PowerPresenter<dynamic>> implements ContractHistoryIView {
  late ContractHistoryPresenter _presenter;
  final ContractHistoryController _controller = ContractHistoryController();

  @override
  void initState() {
    super.initState();
    onRefresh();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '历史记录',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SafeArea(
        child: Column(
          children: [
            Expanded(child: Obx(() {
              return MyRefreshListView(
                padding: EdgeInsets.only(top: 10),
                itemCount: _controller.list.value.length,
                itemBuilder: (context, index) {
                  return _itemBuild(_controller.list.value[index]);
                },
                onRefresh: onRefresh,
                hasMore: _controller.totalNumber.value.isEmpty ? false : _controller.list.value.length < int.parse(_controller.totalNumber.value),
                loadMore: loadMore,
              );
            })),
            Gaps.vGap8,
          ],
        ),
      ),
    );
  }

  Future<void> onRefresh() async {
    _presenter.onRefresh();
  }

  Future<void> loadMore() async {
    _presenter.loadMore();
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractHistoryPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  _itemBuild(ContractHistoryList item) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16),
      color: Colours.white,
      child: Column(
        children: [
          Gaps.vGap12,
          Row(
            children: [
              Expanded(child: CommonUtils.getSimpleText(item.remark, 16, Colours.base_primary_text_title)),
              Gaps.hGap32,
              CommonUtils.getSimpleText("${item.consumeType == "1" ? "+" : "-"}${item.amount ?? ""}份", 16, item.consumeType == "1" ? Colours.base_primary : Colours.base_primary_warning),
            ],
          ),
          Gaps.vGap10,
          Row(
            children: [
              Expanded(child: CommonUtils.getSimpleText(item.createTime, 14, Colours.base_primary_text_caption)),
              CommonUtils.getSimpleText("余量${item.afterAmount ?? ""}份", 14, Colours.base_primary_text_body),
            ],
          ),
          Gaps.vGap12,
          Gaps.line,
        ],
      ),
    );
  }
}
