import 'dart:collection';
import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/src/scheduler/ticker.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_electronic_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_pay_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/custom_border_text_view.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../controller/contract_main_info_controller.dart';
import '../item/contract_listview.dart';
import '../iview/contract_main_info_iview.dart';
import '../persenter/contract_main_persenter.dart';
import '../widghts/contract_tag_grid_view.dart';
import 'contract_edit_seal_page.dart';
import 'contract_history_page.dart';

/**
 * 入职界面
 */
class ContractHirePage extends StatefulWidget {
  @override
  _ContractHirePageState createState() => _ContractHirePageState();
}

class _ContractHirePageState extends State<ContractHirePage> with BasePageMixin<ContractHirePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ContractHirePage> implements ContractMainInfoIView, TickerProvider {
  ContractMainInfoPresenter? _presenter;

  final ContractMainInfoController _controller = ContractMainInfoController();

  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => SingleChildScrollView(
            child: Column(
              children: [
                ///入职合同配置
                Container(
                  color: Colors.white,
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start, // 设置顶部对齐
                        children: [
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "1、请按合同类型设置模板，主管在给保洁员办理入职时，会根据合同类型自动使用您设置的模板；\n2、以下合同模板经过专业律师认证，请放心使用。",
                                style: TextStyle(color: Colors.grey, fontSize: 10.0),
                              ),
                            ],
                          )),
                        ],
                      ),
                      // ContractTagListView(
                      //   items: _controller.contractTagList.value,
                      //   selectedColor: Colors.green,
                      //   unselectedColor: Colors.grey,
                      //   isRounded: true,
                      //   onSelect: (item) {
                      //     _presenter?.getContractList("EntryContract", item.code);
                      //   },
                      // ),
                      // CustomContractListView(
                      //   dataList: _controller.contractList.value,
                      //   onRadioStateChanged: (index, newState) {
                      //     // 在这里处理Radio状态变化后的逻辑
                      //     var map = HashMap();
                      //     map['${_controller.contractList.value[index].subLabelCode}'] = 'true';
                      //     _presenter?.getUpdateContractSubStatus(jsonEncode(map));
                      //   },
                      // ),
                    ],
                  ),
                )
              ],
            ),
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractMainInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  Ticker createTicker(TickerCallback onTick) {
    return Ticker(onTick);
  }

  @override
  void getContractMainInfo(ContractMainElectronicEntity data) {
    // TODO: implement getContractMainInfo
  }


}
