import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../credit_inquiry/persenter/credit_persenter.dart';
import '../../workpost/item/work_post_listview.dart';
import '../../workpost/iview/work_post_iview.dart';
import '../../workpost/presenter/work_post_persenter.dart';
import '../controller/contract_my_controller.dart';
import '../item/contract_records_item_view.dart';
import '../iview/contract_my_iview.dart';
import '../persenter/contract_my_persenter.dart';

class ContractRecordPage extends StatefulWidget {
  String? uuid = "";
  String? user_name = "";

  ContractRecordPage({Key? key, this.uuid = "", this.user_name});

  @override
  _ContractRecordPageState createState() => _ContractRecordPageState();
}

//集成分类 然后实现使用
class _ContractRecordPageState extends State<ContractRecordPage> with BasePageMixin<ContractRecordPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ContractRecordPage> implements ContractMyPageIView {
  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '添加劳动协议',
      desc: '劳动合同、劳务合同、退休返聘合同',
      descStyle: const TextStyle(
        fontSize: 14,
        color: Colours.base_primary_text_caption,
      ),
    ),
    BrnCommonActionSheetItem(
      '添加离职协议',
    ),
  ];

  ContractMyPresenter? _presenter;

  List<WorkPostManagerList> datas = [];

  final ContractMyController _controller = ContractMyController();

  VoidCallback? addListener;

  @override
  void initState() {
    super.initState();
    _onRefresh();
    //监听回掉
    addListener ??= BoostChannel.instance.addEventListener("refresh_contract_records", (key, arguments) async {
      _onRefresh();
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh(widget.uuid ?? "");
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore(widget.uuid ?? "");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '合同记录',
        centerSubTitle: widget.user_name,
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => MyRefreshListView(
            itemCount: _controller.list.length,
            onRefresh: _onRefresh,
            loadMore: _loadMore,
            hasMore: _controller.totalAll.value > _controller.list.length,
            padding: const EdgeInsets.all(10.0),
            itemBuilder: (_, index) {
              return ContractRecordsItemView(
                data: _controller.list[index],
                onEdit: () {
                  //如果是电子合同，那么就跳转预览 纸质合同可以编辑，电子合同不允许编辑。【合同已经发出去了，编辑了也无济于事】
                  if (_controller.list[index].contractType == "1") {
                    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_contract_preview", "url": "${_controller.list[index].eleContractUrl}", "share_title": "${widget.user_name}的${_controller.list[index].contractTitle}"});
                  } else {
                    BoostNavigator.instance.push('contractStaffPage', arguments: {'uuid': '${_controller.list[index].uuid}', 'user_uuid': widget.uuid}).then((value) => _onRefresh());
                  }
                },
                onDelete: () {
                  BrnDialogManager.showConfirmDialog(context, title: "删除", cancel: '取消', confirm: '删除', message: "是否确认删除？", barrierDismissible: false, onConfirm: () {
                    Navigator.of(context, rootNavigator: true).pop();
                    _presenter?.deleteMyContract(_controller.list[index].uuid ?? "");
                  }, onCancel: () {
                    Navigator.of(context, rootNavigator: true).pop();
                  });
                },
              );
            },
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '添加',
          onTap: () {
            BoostNavigator.instance.push('contractStaffPage', arguments: {'user_uuid': '${widget.uuid}'}).then((value) => _onRefresh());
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractMyPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  deleteWork() {
    _onRefresh();
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void updateList() {
    _onRefresh();
  }

  @override
  void dispose() {
    super.dispose();
    addListener?.call();
  }
}
