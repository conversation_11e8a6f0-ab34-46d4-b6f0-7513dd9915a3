import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_template_page.dart';

import '../bean/contract_all_list_entity.dart';
import '../bean/contract_info_entity.dart';
import '../bean/contract_main_info_entity.dart';
import '../bean/contract_tag_list_entity.dart';
import '../bean/contract_tag_one_entity.dart';

class ContractTemplatePageController extends GetxController {
  ContractTemplatePageState? state;
  // ContractTemplatePageController(this.chooseAuntState);

  var selectTagIndex = 0.obs;
  void updateSelectTagIndex(int value) {
    selectTagIndex.value = value;
  }

  var contractTagList = <ContractTagOneSubLabelList>[].obs;
  void updateContractTagList(List<ContractTagOneSubLabelList> value) {
    contractTagList.value = value.toList();
  }

  var contractList = <ContractAllListList>[].obs;
  void updateContactList(List<ContractAllListList> value) {
    contractList.value = value.toList();
  }

  var type = 0.obs;
  void updateType(int value) {
    type.value = value;
  }

  void notifyData() {
    state?.notifyData();
  }

  void setState(ContractTemplatePageState contractTemplatePageState) {
    this.state = contractTemplatePageState;
  }
}
