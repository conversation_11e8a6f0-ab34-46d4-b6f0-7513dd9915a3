import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

class ContractHistoryController extends GetxController{
  var list = <ContractHistoryList>[].obs;
  void updateList(List<ContractHistoryList> value) {
    list.value = value;
  }

  var totalNumber = "0".obs;
  void updateTotal(String? total) {
    totalNumber.value = total ?? "";
  }
}