import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/contract_all_list_entity.dart';
import '../bean/contract_info_entity.dart';
import '../bean/contract_main_electronic_entity.dart';
import '../bean/contract_main_info_entity.dart';
import '../bean/contract_tag_list_entity.dart';
import '../bean/contract_tag_one_entity.dart';

class ContractMainInfoController extends GetxController {
  var contract_status = "".obs;

  void updateContractStatus(String? status) {
    contract_status.value = status.toString();
  }

  var currentTabIndex = 0.obs;

  void updateCurrentTabIndex(int index) {
    currentTabIndex.value = index;
  }

  var infoEntity = ContractMainElectronicEntity().obs;

  void updateContractMainInfoEntity(ContractMainElectronicEntity data) {
    infoEntity.value = data;

    ///都有那些适用公司
    if (data.departmentList != null && data.departmentList!.isNotEmpty) {
      ///拿到uuids
      companyUuids.value = data.departmentList!.map((e) => e.uuid ?? '').toList();

      var shownDepartments = data.departmentList!.take(2).map((dept) => dept.departmentName);

      var departmentNames = shownDepartments.join(',');
      if (data.departmentList!.length > 2) {
        departmentNames += '...';
      }

      if (data.departmentList!.length == 1) {
        department.value = departmentNames; // 只有一个部门时，直接显示部门名称
      } else {
        department.value = '$departmentNames${(departmentNames.length > 2 ? '等' : '')}${data.departmentList?.length}个部门';
      }
    } else {
      department.value = "全公司"; // 如果没有部门列表，默认显示"全公司"
    }
  }

  ///适用部门
  var department = "全公司".obs;

  ///用来记录多选的uuids
  var companyUuids = <String>[].obs;
}
