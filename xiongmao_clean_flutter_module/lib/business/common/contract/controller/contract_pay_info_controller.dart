import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../bean/contract_info_entity.dart';

class ContractPayInfoController extends GetxController {
  var list = <ContractInfoList>[].obs;

  var price = "0".obs;
  var amount = "0".obs;

  void initMyList(List<ContractInfoList> value) {
    list.value = value.toList();
  }

  void updatePrice(String currentPrice,String amountValue) {
    price.value = currentPrice;
    amount.value = amountValue;
  }
}
