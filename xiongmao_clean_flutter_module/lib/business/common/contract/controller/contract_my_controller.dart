import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

import '../bean/contract_my_entity.dart';
import '../bean/contract_user_entity.dart';

class ContractMyController extends GetxController {
  var list = <ContractMyList>[].obs;
  var totalAll = 0.obs;

  void updateList(List<ContractMyList> value) {
    list.value = value;
  }

  void updateTotal(int total) {
    totalAll.value = total;
  }
}
