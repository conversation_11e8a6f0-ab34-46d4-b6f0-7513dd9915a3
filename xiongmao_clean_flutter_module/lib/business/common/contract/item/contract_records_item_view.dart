import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/load_image.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/contract_my_entity.dart';

class ContractRecordsItemView extends StatelessWidget {
  ContractMyList data;
  final Function onDelete;
  final Function onEdit;

  ContractRecordsItemView({required this.data, required this.onDelete, required this.onEdit});

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8.0),
      ),
      margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonUtils.getSimpleText((data.contractTitle), 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
              CommonUtils.getSimpleText((data.contractType == "1" ? "电子合同" : "纸质合同"), 14, Colours.base_primary_text_title),
            ],
          ),
          Gaps.vGap12,
          Visibility(
            visible: (data.mainLabelCode != "EntryContract"),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('离职类型', 14, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.leftReasonName}", 14, Colours.base_primary_text_title),
              ],
            ),
          ),
          Visibility(
            visible: (data.mainLabelCode != "EntryContract"),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('备注', 14, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText("${data.remark}", 14, Colours.base_primary_text_title),
              ],
            ),
          ),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonUtils.getSimpleText((data.mainLabelCode == "EntryContract") ? "合约期限" : '离职日期', 14, Colours.base_primary_text_caption),
              CommonUtils.getSimpleText((data.mainLabelCode == "EntryContract") ? '${data.startTime} - ${data.endTime} ' : '${data.startTime}', 14, Colours.base_primary_text_title),
            ],
          ),
          Gaps.vGap8,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonUtils.getSimpleText('创建人', 14, Colours.base_primary_text_caption),
              CommonUtils.getSimpleText("${data.createUserName}", 14, Colours.base_primary_text_title),
            ],
          ),
          Gaps.vGap8,
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              CommonUtils.getSimpleText('创建时间', 14, Colours.base_primary_text_caption),
              CommonUtils.getSimpleText("${data.createTime}", 14, Colours.base_primary_text_title),
            ],
          ),
          Gaps.vGap8,
          Visibility(
            visible: (data.contractType == "1"),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('签署状态', 14, Colours.base_primary_text_caption),
                CommonUtils.getSimpleText(data.contractStatusName, 14, Colours.base_primary_text_title),
              ],
            ),
          ),
          Visibility(
            visible: (data.contractType == "2") || data.contractPicList!.isNotEmpty,
            child: CommonUtils.getSimpleText('合同照片', 14, Colours.base_primary_text_caption),
          ),
          Visibility(
            visible: (data.contractType == "2") || data.contractPicList!.isNotEmpty,
            child: CustomImageGridView(
              imageUrls: getImageUrls(data.contractPicList!),
              maxImageCount: 4,
              showAddButton: false,
              showDelButton: false,
              includeEdge: false,
            ),
          ),
          Gaps.vGap8,
          Gaps.line,
          Gaps.vGap20,
          Row(
            children: [
              Visibility(
                visible: (data.mainLabelCode == "EntryContract"),
                child: Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText('删除', 16, Colours.red, textAlign: TextAlign.center),
                  onTap: () {
                    onDelete();
                  },
                )),
              ),
              Visibility(
                visible: (data.contractType == '2' || (data.contractStatus == "2" || data.contractStatus == "3")),
                child: Expanded(
                    child: InkWell(
                  child: CommonUtils.getSimpleText((data.contractStatus == "2" || data.contractStatus == "3") ? '预览' : '编辑', 16, Colours.base_primary, textAlign: TextAlign.center),
                  onTap: () {
                    onEdit();
                  },
                )),
              ),
            ],
          )
        ],
      ),
    );
  }

  List<BaseMediaEntity> getImageUrls(List<String> contractPicList) {
    List<BaseMediaEntity> imageUrls = [];

    for (int i = 0; i < contractPicList.length; i++) {
      BaseMediaEntity data = BaseMediaEntity();
      data.media_url = contractPicList[i];
      data.media_type = '1';
      imageUrls.add(data);
    }

    return imageUrls;
  }
}
