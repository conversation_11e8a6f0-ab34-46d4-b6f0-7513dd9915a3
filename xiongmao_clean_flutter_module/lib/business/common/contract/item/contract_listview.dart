import 'dart:ffi';

import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../bean/contract_all_list_entity.dart';
import '../controller/contract_template_page_controller.dart';
import '../page/contract_template_page.dart';

typedef void OnRadioStateChanged(int index, bool newState);
typedef void OnItemClick(int index);

class CustomContractListView extends StatefulWidget {
  final List<ContractAllListList> dataList;
  final OnRadioStateChanged onRadioStateChanged;
  final OnItemClick onItemClick;
  var selectedIndex;
  CustomContractListView({ required this.dataList, required this.onRadioStateChanged,required this.onItemClick,required this.selectedIndex, required Key key})
      :super(key: key);

  @override
  _CustomContractListViewState createState() => _CustomContractListViewState();
}

class _CustomContractListViewState extends State<CustomContractListView> {
  var selectedIndex=0;
  @override
  void initState() {
    super.initState();
    selectedIndex = widget.selectedIndex;
  }
  @override
  Widget build(BuildContext context) {
    MyLog.e("msg---dataList-----"+widget.dataList.toString());
    return ListView.builder(
      physics: NeverScrollableScrollPhysics(),
      padding: EdgeInsets.symmetric(vertical: 8),
      shrinkWrap: true,
      itemCount: widget.dataList.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            widget.onItemClick.call(index);
            var item = widget.dataList[index];
            LogUtil.e("object--templateStatus---"+(item.templateStatus??""));
            //   //template_status 审核状态 1已审核 2未审核 3-生成中
            if(item.templateStatus=="1"){
              setState(() {
                selectedIndex = index;
              });
              widget.onRadioStateChanged(index,selectedIndex!=index);
            }else{
              Toast.show("合同审核中，暂时不能选中");
            }
          },
          child: Container(
            padding: EdgeInsets.symmetric(vertical: 6),
            child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Text(
                (widget.dataList[index].isSelf == "2") ? "#系统#" : "#公司#",
                style: TextStyle(fontSize: 14, color: (widget.dataList[index].isSelf == "2") ? Colours.text_gray : Colours.base_primary),
              ),
              Gaps.hGap8,
              ConstrainedBox(constraints: BoxConstraints(maxWidth: 160),child: Text(
                "${widget.dataList[index].templateTitle}",
                style: TextStyle(fontSize: 14),
              ),),
              Gaps.hGap8,
              Expanded(child: InkWell(
                onTap: () {
                  var item = widget.dataList[index];
                  LogUtil.e("object--templateStatus---"+(item.templateStatus??""));
                  //   //template_status 审核状态 1已审核 2未审核 3-生成中
                  if(item.templateStatus=="1"){
                    // 处理预览点击逻辑
                    BoostNavigator.instance.push("contractTemplateReviewPage", arguments: {"url": "${widget.dataList[index].templateUrl}","index":index}).then((value){
                      var controller = Get.find<ContractTemplatePageController>();
                      controller.notifyData();
                    });
                  }else{
                    Toast.show("合同审核中，暂时不能预览");
                  }
                },
                child: Text(
                  "预览>>",
                  style: TextStyle(color: Colors.blue),
                ),
              )),
              Gaps.hGap8,
              Opacity(opacity: (widget.dataList[index].templateStatus=="1")?1.0:0.4,child: LoadAssetImage(selectedIndex==index?"icon_check":"icon_uncheck",width: 20,height: 20,),),
            ],
          ),),
        );
      },
    );
  }
}
