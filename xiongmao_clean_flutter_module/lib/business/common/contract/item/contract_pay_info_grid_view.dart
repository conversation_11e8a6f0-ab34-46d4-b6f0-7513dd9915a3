import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';

import '../bean/contract_info_entity.dart';

class ContractPayInfoGridView extends StatefulWidget {
  final List<ContractInfoList> items;
  final Function(int) onItemSelected;

  ContractPayInfoGridView({required this.items, required this.onItemSelected});

  @override
  _ContractPayInfoViewState createState() => _ContractPayInfoViewState();
}

class _ContractPayInfoViewState extends State<ContractPayInfoGridView> {
  int _selectedIndex = 0;

  @override
  Widget build(BuildContext context) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
      ),
      itemCount: widget.items.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _selectedIndex = index;
              widget.onItemSelected(_selectedIndex);
            });
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.all(Radius.circular(10)),
              color: _selectedIndex == index ? Colours.base_primary : Colors.transparent,
              border: Border.all(
                color: _selectedIndex == index ? Colours.base_primary : Colors.black12,
                width: 2,
              ),
            ),
            padding: EdgeInsets.all(8),
            child: Stack(
              children: [
                Center(
                  // 让Column中的内容居中
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "${widget.items[index].amount}份",
                        style: TextStyle(color: _selectedIndex == index ? Colours.white : Colours.base_primary, fontSize: 18),
                      ),
                      Text(
                        "(${widget.items[index].unitPrice}元/份)",
                        style: TextStyle(color: _selectedIndex == index ? Colours.white : Colours.base_primary_text_title, fontSize: 10),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
