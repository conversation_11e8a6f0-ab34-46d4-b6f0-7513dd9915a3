import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_history_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_pay_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_pay_success_page.dart';
import '../../../net/http_config.dart';

//购买界面
const contractPayPage = "contractPayPage";
//购买成功
const contractPaySuccessPage = "contractPaySuccessPage";
//历史界面
const contractHistoryPage = "contractHistoryPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractHistoryMap = {
  contractHistoryPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractHistorylPage();
        });
  },
};

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractPaySuccessMap = {
  contractPaySuccessPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractPaySuccessPage();
        });
  },
};
/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractPayRouterMap = {
  contractPayPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractPaylPage();
        });
  },
};
