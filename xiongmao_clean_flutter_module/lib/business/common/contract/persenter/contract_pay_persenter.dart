import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/contract_info_entity.dart';
import '../controller/contract_pay_info_controller.dart';
import '../iview/contract_pay_info_iview.dart';

/**
 * 合同购买的支付内容
 */
class ContractPayInfoPresenter extends BasePagePresenter<ContractPayInfoIView> with WidgetsBindingObserver {


  ContractPayInfoController controller;

  ContractPayInfoPresenter(this.controller);


  //获取用户规则
  Future<dynamic> getContractInfo() {
    var params = <String, String>{};
    return requestNetwork<ContractInfoEntity>(Method.get, url: HttpApi.GET_CONTRACT_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
          controller.initMyList(data.list ?? []);
          if(data.list!.isNotEmpty){
            controller.updatePrice(data.list![0].price??"",data.list![0].amount??"");
          }
      }
    }, onError: (_, __) {});
  }
}
