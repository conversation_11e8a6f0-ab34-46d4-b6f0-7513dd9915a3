import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_fdd_verify_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_register_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/controller/contract_template_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_template_page_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_template_review_page_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/contract_auto_sign_entity.dart';
import '../bean/contract_info_entity.dart';
import '../bean/contract_tag_list_entity.dart';
import '../controller/contract_main_info_controller.dart';
import '../controller/contract_pay_info_controller.dart';
import '../iview/contract_main_info_iview.dart';
import '../iview/contract_pay_info_iview.dart';
import '../page/contract_template_page.dart';

class ContractTemplateReviewPagePresenter extends BasePagePresenter<ContractTemplateReviewPageIView> with WidgetsBindingObserver {
  ///删除合同模版
  Future<dynamic> deleteContractTemplate(String uuid) {
    var params = <String, String>{};
    params['uuid'] = uuid;
    return requestNetwork<Object>(Method.get, url: HttpApi.DELETE_CONTRACT_TEMPALTE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.deleteContractTemplate();
      }
    }, onError: (_, __) {});
  }
}
