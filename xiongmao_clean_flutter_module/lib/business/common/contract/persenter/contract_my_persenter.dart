import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/contract_my_entity.dart';
import '../bean/contract_user_entity.dart';
import '../controller/contract_my_controller.dart';
import '../iview/contract_my_iview.dart';

/**
 * 获取我的合同列表
 */
class ContractMyPresenter extends BasePagePresenter<ContractMyPageIView> with WidgetsBindingObserver {
  ContractMyController controller;

  ContractMyPresenter(this.controller);

  int _page = 1;

  void onRefresh(String uuid) {
    _page = 1;
    requestMyContract(uuid);
  }

  void loadMore(String uuid) {
    _page++;
    requestMyContract(uuid);
  }

  //获取我的合同列表
  Future<dynamic> requestMyContract(String uuid) {
    HashMap<String, String> params = HashMap<String, String>();
    params['user_uuid'] = '$uuid';
    params['_page'] = '$_page';
    params['size'] = '20';
    return requestNetwork<ContractMyEntity>(Method.get, url: HttpApi.CONTACT_MY_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateList(data.list!);
        controller.updateTotal(data.total ?? 0);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  //获取我的合同列表
  Future<dynamic> deleteMyContract(String uuid) {
    HashMap<String, String> params = HashMap<String, String>();
    params['uuid'] = '$uuid';
    return requestNetwork<Object>(Method.post, url: HttpApi.CONTRCT_DELETE_MY, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.updateList();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
