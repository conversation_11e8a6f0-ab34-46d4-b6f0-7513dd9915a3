import 'dart:collection';

import 'package:dio/dio.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_edit_seal_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/add_signature_result_entity.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/contract_info_entity.dart';
import '../controller/contract_pay_info_controller.dart';
import '../iview/contract_pay_info_iview.dart';


class ContractEditSealPresenter extends BasePagePresenter<ContractEditSealIView> with WidgetsBindingObserver {
  //印章上传
  Future<dynamic> addSignature(HashMap<String, dynamic> params) {
    return requestNetwork<AddSignatureResultEntity>(Method.post, url: HttpApi.ADD_SIGNATURE, params: params, isShow: true, isClose: true,onSuccess: (data) {
      if (data != null) {
       view.addSignatureResultEntity(data);
      }
    }, onError: (code, msg) {
      print('请求失败【 $code 】 $msg');
    });
  }
}
