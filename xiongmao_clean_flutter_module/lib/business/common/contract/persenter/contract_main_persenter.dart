import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_fdd_verify_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_register_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_auto_sign_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_rules_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../base/base_web_page.dart';
import '../bean/contract_auto_sign_entity.dart';
import '../bean/contract_info_entity.dart';
import '../bean/contract_main_electronic_entity.dart';
import '../bean/contract_tag_list_entity.dart';
import '../controller/contract_main_info_controller.dart';
import '../controller/contract_pay_info_controller.dart';
import '../iview/contract_main_info_iview.dart';
import '../iview/contract_pay_info_iview.dart';

/**
 * 获取合同的信息
 */
class ContractMainInfoPresenter extends BasePagePresenter<ContractMainInfoIView> with WidgetsBindingObserver {
  ContractMainInfoController controller;

  ContractMainInfoPresenter(this.controller);

  ///获取合同配置信息
  Future<dynamic> getContractMainInfo() {
    var params = <String, String>{};
    params['customer_type'] = "2";
    return requestNetwork<ContractMainElectronicEntity>(Method.get, url: HttpApi.GET_ELECTRONIC_CONTRACT, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getContractMainInfo(data);
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同第三方帐户注册
  Future<dynamic> getContractRegisterUser(bool enforce) {
    var params = <String, String>{};
    params['customer_type'] = "2";
    if (enforce) {
      params['enforce'] = "1";
    }
    return requestNetwork<ContractRegisterInfoEntity>(Method.get, url: HttpApi.GET_CONTRACT_REGISTER, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        getContractFddUrl(data.data);
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同第三方帐户认证的链接 未认证的，则直接走法大大的获取认证的接口
  Future<dynamic> getContractFddUrl(String? customer_id) {
    var params = <String, String>{};
    params['customer_id'] = "$customer_id";
    return requestNetwork<ContractFddVerifyEntity>(Method.get, url: HttpApi.GET_VERIFY_URL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        LogUtil.e("object-------1111-----" + (data.url ?? ""));
        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoWebPage", "url": data.url ?? ""});
      }
    }, onError: (_, __) {});
  }

  ///自动授权接口
  Future<dynamic> getAutoSignUrl(String? customer_id) {
    var params = <String, String>{};
    params['customer_id'] = "$customer_id";
    return requestNetwork<ContractAutoSignEntity>(Method.get, url: HttpApi.GET_AUTO_SIGN, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        LogUtil.e("-----authUrl----" + (data.authUrl ?? ""));
        BoostNavigator.instance.push("contractAutoSignPage", arguments: {"url": data.authUrl}).then((value) => getContractMainInfo());
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同列表标签详情 比如入职合同下的内容、离职合同下的内容
  Future<dynamic> getContractTagOneList(String code) {
    var params = <String, String>{};
    params['code'] = code; //标签编码
    params['n_sub_label_list'] = "true"; //是否获取子数据
    return requestNetwork<ContractTagOneEntity>(Method.get, url: HttpApi.GET_CONTRACT_TAG_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        // controller.updateContractTagList(data.subLabelList ?? []);
        if (data.subLabelList?.isNotEmpty ?? false) {
          getContractList("EntryContract", data.subLabelList![0].code);
        }
      }
    }, onError: (_, __) {});
  }

  ///获取电子合同列表
  Future<dynamic> getContractList(String main_label_code, String? sub_label_code) {
    var params = <String, String>{};
    params['main_label_code'] = "$main_label_code"; //一级标签code
    params['sub_label_code'] = "$sub_label_code"; ////二级标签code
    return requestNetwork<ContractAllListEntity>(Method.get, url: HttpApi.GET_CONTRACT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        // controller.updateContactList(data.list ?? []);
      }
    }, onError: (_, __) {});
  }

  ///编辑入离职的合同状态 修改默认值也可以改
  Future<dynamic> getUpdateContractStatus(String? label_enabled_status) {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(label_enabled_status)) {
      params['label_enabled_status'] = "$label_enabled_status"; //电子合同开启标签【一级标签】
    }
    //适用部门
    if (controller.companyUuids.value.isNotEmpty) {
      params['department_uuid_list'] = controller.companyUuids.value.join(','); //电子合同开启标签【二级标签】
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CONTRACT_UPDATE_STATUS, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        getContractMainInfo();
      }
    }, onError: (_, __) {});
  }
}
