import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../iview/add_contract_template_iview.dart';

/**
 *
 */
class AddContractTemplatePagePresenter extends BasePagePresenter<AddContractTemplatePageIView> with WidgetsBindingObserver {
  //编辑-新建岗位
  Future<dynamic> requestEditContractTemplate(HashMap<String,String> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.EDIT_CONTRACT_TEMPALTE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.requestEditContractTemplate();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
