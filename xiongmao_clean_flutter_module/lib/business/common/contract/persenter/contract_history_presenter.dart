import 'dart:convert';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';
import '../../../../mvp/base_page_presenter.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../controller/contract_history_controller.dart';
import '../iview/contract_history_iview.dart';

class ContractHistoryPresenter extends BasePagePresenter<ContractHistoryIView> {
  ContractHistoryController controller;

  ContractHistoryPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    _getList();
  }

  void loadMore() {
    _page++;
    _getList();
  }

  Future<dynamic> _getList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    return requestNetwork<ContractHistoryEntity>(Method.get, url: HttpApi.GET_CONTRACT_HISTORY_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        MyLog.e("========加载==_page===$_page");
        if (_page == 1) {
          controller.updateList(data.list?.toList() ?? []);
        } else {
          var value = controller.list.value;
          value.addAll(data.list ?? []);
          controller.updateList(value.toList());
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========加载失败========");
      if (_page > 1) {
        _page--;
      }
    });
  }
}
