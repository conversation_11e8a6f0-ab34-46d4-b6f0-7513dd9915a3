import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_fdd_verify_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_register_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/controller/contract_template_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/iview/contract_template_page_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/contract_auto_sign_entity.dart';
import '../bean/contract_info_entity.dart';
import '../bean/contract_tag_list_entity.dart';
import '../controller/contract_main_info_controller.dart';
import '../controller/contract_pay_info_controller.dart';
import '../iview/contract_main_info_iview.dart';
import '../iview/contract_pay_info_iview.dart';
import '../page/contract_template_page.dart';

/**
 * 获取合同的信息
 */
class ContractTemplatePagePresenter extends BasePagePresenter<ContractTemplatePageIView> with WidgetsBindingObserver {
  ContractTemplatePageController controller;
  ContractTemplatePagePresenter(this.controller);

  ///获取电子合同列表标签详情 比如入职合同下的内容、离职合同下的内容
  Future<dynamic> getContractTagOneList(int type) {
    String code =  type==TYPE_ENTRY ? "EntryContract" : "SeparationContract";
    var params = <String, String>{};
    params['code'] = code; //标签编码
    params['n_sub_label_list'] = "true"; //是否获取子数据
    return requestNetwork<ContractTagOneEntity>(Method.get, url: HttpApi.GET_CONTRACT_TAG_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateContractTagList(data.subLabelList ?? []);
        if (data.subLabelList?.isNotEmpty ?? false) {
          getContractList(code, data.subLabelList![0].code);
        }
      }
    }, onError: (_, __) {});
  }

  ///编辑入离职的合同状态 修改默认值也可以改
  Future<dynamic> getUpdateContractSubStatus(String? default_sign_label_code) {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(default_sign_label_code)) {
      params['default_sign_label_code'] = "$default_sign_label_code"; //电子合同标签默认模版【二级标签】
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CONTRACT_UPDATE_STATUS, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        Toast.show("操作成功");
      }
    }, onError: (_, __) {});
  }


  ///获取电子合同列表
  Future<dynamic> getContractList(String main_label_code, String? sub_label_code) {
    var params = <String, String>{};
    params['main_label_code'] = "$main_label_code"; //一级标签code
    params['sub_label_code'] = "$sub_label_code"; ////二级标签code
    params['n_setting'] = "1"; //获取模版设置
    return requestNetwork<ContractAllListEntity>(Method.get, url: HttpApi.GET_CONTRACT_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateContactList(data.list ?? []);
      }
    }, onError: (_, __) {});
  }
}
