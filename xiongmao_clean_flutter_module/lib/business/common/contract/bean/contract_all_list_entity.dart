import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_all_list_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_all_list_entity.g.dart';

@JsonSerializable()
class ContractAllListEntity {
	int? page;
	int? size;
	int? total;
	List<ContractAllListList>? list;

	ContractAllListEntity();

	factory ContractAllListEntity.fromJson(Map<String, dynamic> json) => $ContractAllListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractAllListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractAllListList {
	String? id;
	String? uuid;
	@JSONField(name: "main_label_code")
	String? mainLabelCode;
	@JSONField(name: "sub_label_code")
	String? subLabelCode;
	@JSONField(name: "company_id")
	String? companyId;
	@JSONField(name: "template_title")
	String? templateTitle;
	@JSONField(name: "template_url")
	String? templateUrl;
	@JSONField(name: "template_status")
	String? templateStatus;
	@JSONField(name: "is_self")
	String? isSelf;
	@JSONField(name: "is_delete")
	String? isDelete;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;
	ContractAllListListSetting? setting;

	ContractAllListList();

	factory ContractAllListList.fromJson(Map<String, dynamic> json) => $ContractAllListListFromJson(json);

	Map<String, dynamic> toJson() => $ContractAllListListToJson(this);
}

@JsonSerializable()
class ContractAllListListSetting {
	@JSONField(name: "label_default_sign_template")
	bool? labelDefaultSignTemplate;

	ContractAllListListSetting();

	factory ContractAllListListSetting.fromJson(Map<String, dynamic> json) => $ContractAllListListSettingFromJson(json);

	Map<String, dynamic> toJson() => $ContractAllListListSettingToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}