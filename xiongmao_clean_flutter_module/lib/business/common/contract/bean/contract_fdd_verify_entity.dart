import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_fdd_verify_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_fdd_verify_entity.g.dart';

@JsonSerializable()
class ContractFddVerifyEntity {
	String? transactionNo;
	String? url;

	ContractFddVerifyEntity();

	factory ContractFddVerifyEntity.fromJson(Map<String, dynamic> json) => $ContractFddVerifyEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractFddVerifyEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}