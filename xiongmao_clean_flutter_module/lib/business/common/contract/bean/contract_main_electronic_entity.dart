import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_main_electronic_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_main_electronic_entity.g.dart';

@JsonSerializable()
class ContractMainElectronicEntity {
	@JSONField(name: "sign_setting")
	ContractMainElectronicSignSetting? signSetting;
	@JSONField(name: "contract_count")
	String? contractCount;
	@JSONField(name: "department_list")
	List<ContractMainElectronicDepartmentList>? departmentList;

	ContractMainElectronicEntity();

	factory ContractMainElectronicEntity.fromJson(Map<String, dynamic> json) => $ContractMainElectronicEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainElectronicEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainElectronicSignSetting {
	@JSONField(name: "label_enabled_status")
	ContractMainElectronicSignSettingLabelEnabledStatus? labelEnabledStatus;
	@JSONField(name: "default_sign_label_code")
	ContractMainElectronicSignSettingDefaultSignLabelCode? defaultSignLabelCode;

	ContractMainElectronicSignSetting();

	factory ContractMainElectronicSignSetting.fromJson(Map<String, dynamic> json) => $ContractMainElectronicSignSettingFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainElectronicSignSettingToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainElectronicSignSettingLabelEnabledStatus {
	@JSONField(name: "EntryContract")
	bool? entryContract;
	@JSONField(name: "SeparationContract")
	bool? separationContract;

	ContractMainElectronicSignSettingLabelEnabledStatus();

	factory ContractMainElectronicSignSettingLabelEnabledStatus.fromJson(Map<String, dynamic> json) => $ContractMainElectronicSignSettingLabelEnabledStatusFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainElectronicSignSettingLabelEnabledStatusToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainElectronicSignSettingDefaultSignLabelCode {
	@JSONField(name: "EntryContract_Rehire")
	String? entrycontractRehire;
	@JSONField(name: "EntryContract_Service")
	String? entrycontractService;
	@JSONField(name: "SeparationContract_Agreement")
	String? separationcontractAgreement;
	@JSONField(name: "EntryContract_Labor")
	String? entrycontractLabor;
	@JSONField(name: "EntryContract_PartTime")
	String? entrycontractParttime;

	ContractMainElectronicSignSettingDefaultSignLabelCode();

	factory ContractMainElectronicSignSettingDefaultSignLabelCode.fromJson(Map<String, dynamic> json) => $ContractMainElectronicSignSettingDefaultSignLabelCodeFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainElectronicSignSettingDefaultSignLabelCodeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainElectronicDepartmentList {
	String? uuid;
	@JSONField(name: "department_name")
	String? departmentName;

	ContractMainElectronicDepartmentList();

	factory ContractMainElectronicDepartmentList.fromJson(Map<String, dynamic> json) => $ContractMainElectronicDepartmentListFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainElectronicDepartmentListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}