import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_tag_list_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_tag_list_entity.g.dart';

@JsonSerializable()
class ContractTagListEntity {
	int? page;
	int? size;
	int? total;
	List<ContractTagListList>? list;

	ContractTagListEntity();

	factory ContractTagListEntity.fromJson(Map<String, dynamic> json) => $ContractTagListEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractTagListEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractTagListList {
	String? id;
	String? pid;
	@JSONField(name: "label_name")
	String? labelName;
	@JSONField(name: "is_delete")
	String? isDelete;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;

	ContractTagListList();

	factory ContractTagListList.fromJson(Map<String, dynamic> json) => $ContractTagListListFromJson(json);

	Map<String, dynamic> toJson() => $ContractTagListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}