import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_tag_one_entity.g.dart';
import 'dart:convert';

import '../../../../widgets/select_tab/select_tab_data.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_tag_one_entity.g.dart';

@JsonSerializable()
class ContractTagOneEntity {
	String? id;
	String? pid;
	String? code;
	@JSONField(name: "label_name")
	String? labelName;
	@JSONField(name: "is_delete")
	String? isDelete;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;
	@JSONField(name: "sub_label_list")
	List<ContractTagOneSubLabelList>? subLabelList;

	ContractTagOneEntity();

	factory ContractTagOneEntity.fromJson(Map<String, dynamic> json) => $ContractTagOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractTagOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractTagOneSubLabelList implements SelectTabData{
	String? id;
	String? pid;
	String? code;
	@JSONField(name: "label_name")
	String? labelName;
	@JSONField(name: "is_delete")
	String? isDelete;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;

	ContractTagOneSubLabelList();

	factory ContractTagOneSubLabelList.fromJson(Map<String, dynamic> json) => $ContractTagOneSubLabelListFromJson(json);

	Map<String, dynamic> toJson() => $ContractTagOneSubLabelListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}

  @override
  String? getTabMark() {
   return null;
  }

  @override
  String? getTabText() {
    return labelName;
  }
}