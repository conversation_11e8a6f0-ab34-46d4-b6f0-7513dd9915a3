import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_register_info_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_register_info_entity.g.dart';

@JsonSerializable()
class ContractRegisterInfoEntity {
	int? code;
	String? data;
	String? msg;

	ContractRegisterInfoEntity();

	factory ContractRegisterInfoEntity.fromJson(Map<String, dynamic> json) => $ContractRegisterInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractRegisterInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}