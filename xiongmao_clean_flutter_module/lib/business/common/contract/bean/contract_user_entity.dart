import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_user_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_user_entity.g.dart';

@JsonSerializable()
class ContractUserEntity {
	List<ContractUserList>? list;

	ContractUserEntity();

	factory ContractUserEntity.fromJson(Map<String, dynamic> json) => $ContractUserEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractUserEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractUserList {
	String? uuid;
	@JSONField(name: "work_start_time")
	String? workStartTime;
	@JSONField(name: "work_end_time")
	String? workEndTime;
	@JSONField(name: "create_user_name")
	String? createUserName;
	@JSONField(name: "create_time")
	String? createTime;
	@J<PERSON>NField(name: "label_code")
	String? labelCode;
	@JSONField(name: "label_name")
	String? labelName;
	@JSONField(name: "contract_sign_type")
	String? contractSignType;
	@JSONField(name: "contract_status")
	String? contractStatus;
	@JSONField(name: "contract_status_name")
	String? contractStatusName;
	@JSONField(name: "contract_sign_type_name")
	String? contractSignTypeName;
	@JSONField(name: "contract_pic_list")
	List<ContractUserListContractPicList>? contractPicList;

	ContractUserList();

	factory ContractUserList.fromJson(Map<String, dynamic> json) => $ContractUserListFromJson(json);

	Map<String, dynamic> toJson() => $ContractUserListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractUserListContractPicList {
	@JSONField(name: "source_url")
	String? sourceUrl;

	ContractUserListContractPicList();

	factory ContractUserListContractPicList.fromJson(Map<String, dynamic> json) => $ContractUserListContractPicListFromJson(json);

	Map<String, dynamic> toJson() => $ContractUserListContractPicListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}