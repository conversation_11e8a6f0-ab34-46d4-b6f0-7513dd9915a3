import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_info_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_info_entity.g.dart';

@JsonSerializable()
class ContractInfoEntity {
	List<ContractInfoList>? list;

	ContractInfoEntity();

	factory ContractInfoEntity.fromJson(Map<String, dynamic> json) => $ContractInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractInfoList {
	String? number;
	@JSONField(name: "unit_price")
	String? unitPrice;
	String? price;
	String? amount;
	@JSONField(name: "is_default")
	String? isDefault;

	ContractInfoList();

	factory ContractInfoList.fromJson(Map<String, dynamic> json) => $ContractInfoListFromJson(json);

	Map<String, dynamic> toJson() => $ContractInfoListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}