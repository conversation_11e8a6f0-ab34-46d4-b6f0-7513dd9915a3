import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_my_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_my_entity.g.dart';

@JsonSerializable()
class ContractMyEntity {
	int? page;
	int? size;
	int? total;
	List<ContractMyList>? list;

	ContractMyEntity();

	factory ContractMyEntity.fromJson(Map<String, dynamic> json) => $ContractMyEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractMyEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMyList {
	String? uuid;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "main_label_code")
	String? mainLabelCode;
	@JSONField(name: "sub_label_code")
	String? subLabelCode;
	@JSONField(name: "contract_title")
	String? contractTitle;
	@JSONField(name: "create_user_name")
	String? createUserName;
	@JSONField(name: "contract_type")
	String? contractType;
	@JSONField(name: "contract_status")
	String? contractStatus;
	@JSONField(name: "contract_status_name")
	String? contractStatusName;
	@JSONField(name: "contract_type_name")
	String? contractTypeName;
	@JSONField(name: "contract_pic_list")
	List<String>? contractPicList;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "ele_contract_uuid")
	String? eleContractUuid;
	@JSONField(name: "ele_contract_url")
	String? eleContractUrl;
	@JSONField(name: "left_reason")
	String? leftReason;
	@JSONField(name: "left_reason_name")
	String? leftReasonName;
	String? remark;

	ContractMyList();

	factory ContractMyList.fromJson(Map<String, dynamic> json) => $ContractMyListFromJson(json);

	Map<String, dynamic> toJson() => $ContractMyListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

