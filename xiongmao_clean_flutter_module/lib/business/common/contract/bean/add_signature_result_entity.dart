import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/add_signature_result_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/add_signature_result_entity.g.dart';

@JsonSerializable()
class AddSignatureResultEntity {
	@JSONField(name: "signature_id")
	String? signatureId;
	@JSONField(name: "seal_cdn_url")
	String? sealCdnUrl;

	AddSignatureResultEntity();

	factory AddSignatureResultEntity.fromJson(Map<String, dynamic> json) => $AddSignatureResultEntityFromJson(json);

	Map<String, dynamic> toJson() => $AddSignatureResultEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}