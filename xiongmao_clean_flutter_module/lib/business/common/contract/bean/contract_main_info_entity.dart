import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_main_info_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_main_info_entity.g.dart';

@JsonSerializable()
class ContractMainInfoEntity {
	String? id;
	@JSONField(name: "company_id")
	String? companyId;
	@JSONField(name: "customer_id")
	String? customerId;
	@JSONField(name: "customer_id_card")
	String? customerIdCard;
	@JSONField(name: "customer_name")
	String? customerName;
	@JSONField(name: "customer_mobile")
	String? customerMobile;
	@JSONField(name: "customer_type")
	String? customerType;
	@JSONField(name: "customer_status")
	String? customerStatus;
	ContractMainInfoCert? cert;
	@JSONField(name: "seal_cdn_url")
	String? sealCdnUrl;
	@JSONField(name: "seal_auto_sign_status")
	String? sealAutoSignStatus;
	@JSONField(name: "sign_setting")
	ContractMainInfoSignSetting? signSetting;
	@JSONField(name: "contract_count")
	String? contractCount;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "update_time")
	String? updateTime;
	@JSONField(name: "seal_no")
	String? sealNo;

	ContractMainInfoEntity();

	factory ContractMainInfoEntity.fromJson(Map<String, dynamic> json) => $ContractMainInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainInfoCert {
	@JSONField(name: "company_name")
	String? companyName;
	String? organization;
	@JSONField(name: "customer_name")
	String? customerName;
	@JSONField(name: "customer_mobile")
	String? customerMobile;
	@JSONField(name: "customer_id_card")
	String? customerIdCard;

	ContractMainInfoCert();

	factory ContractMainInfoCert.fromJson(Map<String, dynamic> json) => $ContractMainInfoCertFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainInfoCertToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainInfoSignSetting {
	@JSONField(name: "label_enabled_status")
	ContractMainInfoSignSettingLabelEnabledStatus? labelEnabledStatus;
	@JSONField(name: "default_sign_label_code")
	ContractMainInfoSignSettingDefaultSignLabelCode? defaultSignLabelCode;

	ContractMainInfoSignSetting();

	factory ContractMainInfoSignSetting.fromJson(Map<String, dynamic> json) => $ContractMainInfoSignSettingFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainInfoSignSettingToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainInfoSignSettingLabelEnabledStatus {
	@JSONField(name: "EntryContract")
	bool? entryContract;
	@JSONField(name: "SeparationContract")
	bool? separationContract;

	ContractMainInfoSignSettingLabelEnabledStatus();

	factory ContractMainInfoSignSettingLabelEnabledStatus.fromJson(Map<String, dynamic> json) => $ContractMainInfoSignSettingLabelEnabledStatusFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainInfoSignSettingLabelEnabledStatusToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class ContractMainInfoSignSettingDefaultSignLabelCode {
	@JSONField(name: "EntryContract_Rehire")
	String? entrycontractRehire;
	@JSONField(name: "EntryContract_Service")
	String? entrycontractService;
	@JSONField(name: "SeparationContract_Agreement")
	String? separationcontractAgreement;

	ContractMainInfoSignSettingDefaultSignLabelCode();

	factory ContractMainInfoSignSettingDefaultSignLabelCode.fromJson(Map<String, dynamic> json) => $ContractMainInfoSignSettingDefaultSignLabelCodeFromJson(json);

	Map<String, dynamic> toJson() => $ContractMainInfoSignSettingDefaultSignLabelCodeToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}