import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_auto_sign_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_auto_sign_entity.g.dart';

@JsonSerializable()
class ContractAutoSignEntity {
	String? transactionNo;
	String? authUrl;

	ContractAutoSignEntity();

	factory ContractAutoSignEntity.fromJson(Map<String, dynamic> json) => $ContractAutoSignEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractAutoSignEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}