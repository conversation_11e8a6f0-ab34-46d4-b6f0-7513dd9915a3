import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import '../bean/contract_tag_one_entity.dart';

class ContractTagListView extends StatefulWidget {
  final List<ContractTagOneSubLabelList> items;
  final Color selectedColor;
  final Color unselectedColor;
  final Color selectedBorderColor;
  final Color unselectedBorderColor;

  final bool isRounded;
  final void Function(int index, ContractTagOneSubLabelList item)? onSelect;

  ContractTagListView({
    Key? key, // 添加 Key 参数
    required this.items,
    this.selectedColor = Colors.green,
    this.unselectedColor = Colors.grey,
    this.selectedBorderColor = Colours.transparent,
    this.unselectedBorderColor = Colours.base_primary_line_b,
    this.isRounded = false,
    this.onSelect,
  }) : super(key: key);

  @override
  ContractTagListViewState createState() => ContractTagListViewState();
}

class ContractTagListViewState extends State<ContractTagListView> {
  int _selectedIndex = 0;

  int get selectedIndex => _selectedIndex; // Getter

  @override
  Widget build(BuildContext context) {
    return ListView.builder(
      shrinkWrap: true,
      itemCount: (widget.items.length / 3).ceil(),
      itemBuilder: (context, row) {
        int startIndex = row * 3;
        int endIndex = (row + 1) * 3;
        return Padding(
          padding: EdgeInsets.only(bottom: 10),
          child: Row(
            children: List.generate(3, (index) {
              int itemIndex = startIndex + index;
              bool isSelected = itemIndex == _selectedIndex;

              if (itemIndex < widget.items.length) {
                ContractTagOneSubLabelList item = widget.items[itemIndex];

                return GestureDetector(
                  onTap: () {
                    if (widget.onSelect != null) {
                      setState(() {
                        _selectedIndex = itemIndex;
                      });
                      widget.onSelect!(itemIndex, item);
                    }
                  },
                  child: Container(
                    margin: EdgeInsets.only(right: 8),
                    // margin: EdgeInsets.symmetric(vertical: 8.0,horizontal: 8.0),
                    height: 32,
                    decoration: BoxDecoration(color: isSelected ? widget.selectedColor : widget.unselectedColor, borderRadius: widget.isRounded ? BorderRadius.circular(16.0) : null, border: Border.all(color: isSelected ? widget.selectedBorderColor : widget.unselectedBorderColor, width: 1)),
                    padding: EdgeInsets.symmetric(vertical: 3.0, horizontal: 8.0),
                    child: Center(
                      child: Text(
                        "${item.labelName}",
                        style: TextStyle(color: isSelected ? Colours.base_primary : Colours.base_primary_text_body),
                      ),
                    ),
                  ),
                );
              } else {
                return SizedBox(); // 不添加空白占位符
              }
            }),
          ),
        );
      },
    );
  }
}
