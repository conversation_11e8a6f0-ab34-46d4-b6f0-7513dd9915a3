import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/add_contract_template_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_auto_sign_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_edit_seal_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_record_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/page/contract_template_review_page.dart';
import '../../../net/http_config.dart';

//电子合同的界面
const contractPage = "contractPage";
const contractAutoSignPage = "contractAutoSignPage";
const contractEditSealPage = "contractEditSealPage";
const addContractTemplatePage = "addContractTemplatePage";
const contractTemplateReviewPage = "contractTemplateReviewPage";

///合同记录
const contractRecordPage = "contractRecordPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractRouterMap = {
  contractPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractPage();
        });
  },
};

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractAutoSignPageMap = {
  contractAutoSignPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractAutoSignPage(
            url: map["url"],
          );
        });
  },
};

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> contractEditSealPageMap = {
  contractEditSealPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractEditSealPage(
            companyName: map["companyName"],
            customer_id: map["customer_id"],
            organization: map["organization"],
            sealCdnUrl: map["sealCdnUrl"],
          );
        });
  },
};

Map<String, FlutterBoostRouteFactory> addContractTemplatePageMap = {
  addContractTemplatePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return AddContractTemplatePage(
            contract_type: map["contract_type"],
            sub_label_code: map["sub_label_code"],
            uuid: map["uuid"],
            template_title: map["template_title"],
            template_url: map['template_url'],
          );
        });
  },
};

Map<String, FlutterBoostRouteFactory> contractTemplateReviewPageMap = {
  contractTemplateReviewPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractTemplateReviewPage(url: map["url"], index: map["index"]);
        });
  },
};

Map<String, FlutterBoostRouteFactory> contractRecordPageMap = {
  contractRecordPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractRecordPage(
            uuid: map['uuid'] ?? 'a79aeccc9518e13e64839882bab50341',
            user_name: map['user_name'] ?? '',
          );
        });
  },
};
