import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/custom_manager_entity.dart';
import '../controller/custom_manager_controller.dart';
import '../iview/custom_manager_iview.dart';

class CustomManagerPresenter extends BasePagePresenter<CustomMangerIView> with WidgetsBindingObserver {
  CustomManagerController controller;

  CustomManagerPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    requestCustomManagerList();
  }

  void loadMore() {
    _page++;
    requestCustomManagerList();
  }

  ///获取客户管理列表
  Future<dynamic> requestCustomManagerList() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["keyword"] = controller.searchText.value;
    return requestNetwork<CustomManagerEntity>(Method.post, url: HttpApi.GET_CUSTOM_MANGET_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.listTotal.value = "${data.total ?? '0'}";
        if (_page == 1) {
          controller.initTaskList(data.total ?? 0, data.list!);
        } else {
          controller.updateTaskList(data.list!);
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取客户详情
  Future<dynamic> requestCustomOne(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    return requestNetwork<CustomManagerList>(Method.post, url: HttpApi.CUSTOM_INFO_ONE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateCustomOne(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取项目计划列表
  Future<dynamic> requestProjectManagerList(String custom_uuid) {
    var params = <String, String>{};
    params["page"] = "1";
    params["size"] = "1000";
    params["custom_uuid"] = custom_uuid;
    return requestNetwork<ProjectManagerEntity>(Method.post, url: HttpApi.PROJECT_MANAGER_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        if ("$_page" == '1') {
          controller.initOneList(data.list!);
        } else {
          controller.updateOneList(data.list!);
        }
        controller.listTotal.value = "${data.total ?? '0'}";
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除客户管理
  Future<dynamic> delCustom(String custom_uuid) {
    var params = <String, String>{};
    params["uuid"] = custom_uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.CUSTOM_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.saveStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
