import 'dart:collection';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../res/constant.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../../staff/bean/city_data_entity.dart';
import '../bean/custom_manager_entity.dart';
import '../controller/custom_manager_controller.dart';
import '../controller/custom_save_controller.dart';
import '../iview/custom_manager_iview.dart';
import '../iview/custom_save_iview.dart';

class CustomSavePresenter extends BasePagePresenter<CustomSaveIView> with WidgetsBindingObserver {
  CustomSaveController controller;

  CustomSavePresenter(this.controller);

  ///保存客户的信息
  Future<dynamic> saveCustomInfo() {
    var params = <String, String>{};
    if (!TextUtil.isEmpty(controller.uuid.value)) {
      params['uuid'] = controller.uuid.value;
    }
    params['custom_name'] = controller.customShortName.value;
    params['custom_full_name'] = controller.customFullName.value;
    params['area_id'] = controller.areaID.value;
    params['address'] = controller.address.value;
    params['contact_person'] = controller.contactPerson.value;
    params['contact_mobile'] = controller.contactMobile.value;
    params['remark'] = controller.remark.value;
    params['manager_user_uuid'] = controller.managerUserUuid.value;

    return requestNetwork<CustomManagerEntity>(Method.post, url: HttpApi.CUSTOM_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.saveStatus();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取城市的元数据
  Future<dynamic> getCityMetaData() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<CityDataEntity>(Method.get, url: HttpApi.GET_CITY_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        SpUtil.putString(Constant.CITY_META_DATA, json.encode(data));
      }
    });
  }
}
