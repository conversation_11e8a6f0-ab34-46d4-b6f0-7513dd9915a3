import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_avatar_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/custom_manager_entity.dart';

class CustomManagerListView extends StatelessWidget {
  CustomManagerList data;
  String choose_uuid;
  bool isSelected;

  final Function onClick;

  CustomManagerListView({required this.data, required this.isSelected, required this.choose_uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Container(
        decoration: BoxDecoration(
          color: Colours.white,
          borderRadius: BorderRadius.circular(4.0),
        ),
        margin: const EdgeInsets.only(
          left: 10,
          right: 10,
          bottom: 10,
        ),
        padding: const EdgeInsets.only(
          top: 10,
          left: 16,
          right: 16,
          bottom: 10,
        ), // 设置内边距为16.0
        child: Column(
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(data.customName, 17, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    Visibility(
                      child: CommonUtils.getSimpleText(data.customFullName, 14, Colours.base_primary_text_caption),
                      visible: !TextUtil.isEmpty(data.customFullName),
                    ),
                  ],
                )),
                Visibility(
                  visible: isSelected,
                  child: LoadAssetImage(
                    (data.uuid == choose_uuid) ? "icon_check" : "icon_uncheck",
                    width: 20,
                    height: 20,
                  ),
                ),
                Visibility(
                  visible: !isSelected,
                  child: Container(
                    padding: const EdgeInsets.only(
                      left: 4,
                      right: 4,
                    ),
                    margin: const EdgeInsets.only(
                      top: 4,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(2.0),
                      border: Border.all(color: Colours.base_primary, width: 1),
                    ),
                    child: CommonUtils.getSimpleText('全部项目${data.projectNum}个', 14, Colours.base_primary),
                  ),
                )
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,

            ///关键人
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('关键人：', 15, Colours.base_primary_text_caption),
                Gaps.hGap10,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.contactPerson) ? data.contactPerson : '-', 15, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis),
              ],
            ),
            Gaps.vGap4,

            ///关键人电话
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText('关键人电话：', 15, Colours.base_primary_text_caption)),
                Gaps.hGap10,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.contactMobile) ? maskPhoneNumber(data.contactMobile!) : '-', 15, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis),
              ],
            ),
            Gaps.vGap4,

            ///城市/区域
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('城市/区域：', 15, Colours.base_primary_text_caption),
                Gaps.hGap10,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.cityName) ? '${data.provinceName}${data.cityName}' : '-', 15, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis),
              ],
            ),
            Gaps.vGap4,

            ///详细地址
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('详细地址：', 15, Colours.base_primary_text_caption),
                Gaps.hGap10,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.address) ? data.address : '-', 15, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis),
              ],
            ),
            Gaps.vGap4,

            ///备注
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('备注：', 15, Colours.base_primary_text_caption),
                Gaps.hGap10,
                Expanded(child: CommonUtils.getSimpleText(!TextUtil.isEmpty(data.remark) ? data.remark : '-', 15, Colours.base_primary_text_title)),
              ],
            ),
            Gaps.vGap4,

            ///备注
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText('市场负责人：', 15, Colours.base_primary_text_caption),
                Gaps.hGap10,
                CommonUtils.getSimpleText(!TextUtil.isEmpty(data.managerUserName) ? data.managerUserName : '-', 15, Colours.base_primary_text_title, overflow: TextOverflow.ellipsis),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length != 11) {
      throw ArgumentError('Phone number must be 11 digits long');
    }
    // 使用正则表达式替换中间四位
    return phoneNumber.replaceRange(3, 7, '****');
  }

  Future<void> _makePhoneCall(String phoneNumber) async {
    final Uri launchUri = Uri(
      scheme: 'tel',
      path: phoneNumber,
    );

    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      throw 'Could not launch $launchUri';
    }
  }
}
