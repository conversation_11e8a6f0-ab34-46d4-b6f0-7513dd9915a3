import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../project/item/project_manager_item_listview.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../../staff/bean/city_data_entity.dart';
import '../bean/custom_manager_entity.dart';

class CustomSaveController extends GetxController {
  var uuid = ''.obs; //客户的uuid
  var customShortName = ''.obs; //简称
  var customFullName = ''.obs; //全称
  var areaID = ''.obs; //区域ID
  var cityName = ''.obs; //城市
  var address = ''.obs; //详细地址
  var contactPerson = ''.obs; //关键人
  var contactMobile = ''.obs; //关键人电话
  var remark = ''.obs; //备注
  var managerUserName = '${httpConfig.user_name}'.obs; //市场负责人  默认是当前用户
  var managerUserUuid = '${httpConfig.user_uuid}'.obs; //市场负责人  默认是当前用户

  var homeTown = ''.obs; //选择组件的使用 城市



  ///城市的列表
  List<CityDataList> cityList = [];
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;

}
