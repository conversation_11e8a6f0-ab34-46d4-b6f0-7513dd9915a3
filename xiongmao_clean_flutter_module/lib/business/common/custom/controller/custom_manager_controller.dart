import 'package:bruno/bruno.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../project/item/project_manager_item_listview.dart';
import '../../quality_service/bean/clean_plan_task_entity.dart';
import '../bean/custom_manager_entity.dart';

class CustomManagerController extends GetxController {
  var oneDelName = ''.obs;

  var searchText = "".obs;

  var listTotal = "0".obs;

  ///客户管理的列表
  var listTask = <CustomManagerList>[].obs;

  void initTaskList(int total, List<CustomManagerList> value) {
    listTotal.value = "$total";
    listTask.value = value.toList();
  }

  void updateTaskList(List<CustomManagerList> value) {
    listTask.value.addAll(value);
    listTask.value = listTask.value.toList();
  }

  ///详情的项目列表
  var listOne = <ProjectManagerList>[].obs;

  void initOneList(List<ProjectManagerList> value) {
    listOne.value = value.toList();
    if (listOne.value.isEmpty) {
      oneDelName.value = '删除';
    }
  }

  void updateOneList(List<ProjectManagerList> value) {
    listOne.value.addAll(value);
    listOne.value = listOne.value.toList();
  }

  ///详情
  var data = CustomManagerList().obs;

  void updateCustomOne(CustomManagerList entity) {
    data.value = entity;
  }
}
