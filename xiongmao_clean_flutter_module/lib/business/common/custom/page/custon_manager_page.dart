import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/custom_manager_controller.dart';
import '../item/custom_manager_item_listview.dart';
import '../iview/custom_manager_iview.dart';
import '../persenter/custom_manager_persenter.dart';

/// 客户管理
class CustomManagerPage extends StatefulWidget {
  String? choose_uuid = "";

  bool isSelected = false;

  CustomManagerPage({Key? key, this.choose_uuid = "", required this.isSelected}) : super(key: key);

  @override
  _CustomManagerPageState createState() => _CustomManagerPageState();
}

class _CustomManagerPageState extends State<CustomManagerPage> with BasePageMixin<CustomManagerPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CustomManagerPage> implements CustomMangerIView {
  CustomManagerPresenter? _presenter;

  final CustomManagerController _controller = CustomManagerController();

  @override
  void initState() {
    super.initState();
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: '客户管理',
        actionName: widget.isSelected ? '添加' : '',
        onPressed: () {
          BoostNavigator.instance.push('customSavePage').then((value) => _onRefresh());
        },
      ),
      body: Column(
        children: [
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
            child: CustomSearchView(
              hint: '请输入内容查找客户',
              onTextChanged: (text) {
                _controller.searchText.value = text;
                _onRefresh();
              },
            ),
          ),

          ///列表
          Obx(() => Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.listTask.value.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.listTotal.value) > _controller.listTask.value.length,
                padding: const EdgeInsets.only(top: 10),
                itemBuilder: (_, index) {
                  return CustomManagerListView(
                    data: _controller.listTask[index],
                    isSelected: widget.isSelected,
                    choose_uuid: widget.choose_uuid ?? '',
                    onClick: () {
                      if (widget.isSelected) {
                        BoostNavigator.instance.pop(_controller.listTask[index]);
                        return;
                      }
                      BoostNavigator.instance.push('customOnePage', arguments: {'uuid': _controller.listTask[index].uuid}).then((value) => _onRefresh());
                    },
                  );
                },
              )))
        ],
      ),

      ///这里是编辑 添加项目
      bottomNavigationBar: Visibility(
        visible: !widget.isSelected,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
            title: '+ 添加客户',
            onTap: () {
              BoostNavigator.instance.push('customSavePage').then((value) => _onRefresh());
            },
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CustomManagerPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void saveStatus() {}
}
