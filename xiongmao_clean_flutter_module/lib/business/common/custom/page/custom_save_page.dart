import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';

import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../project/item/project_manager_item_listview.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../staff/bean/city_data_entity.dart';
import '../../staff/widght/brn2_city_row_delegate.dart';
import '../../staff/widght/brn3_city_row_delegate.dart';
import '../bean/custom_manager_entity.dart';
import '../controller/custom_manager_controller.dart';
import '../controller/custom_save_controller.dart';
import '../item/custom_manager_item_listview.dart';
import '../iview/custom_manager_iview.dart';
import '../iview/custom_save_iview.dart';
import '../persenter/custom_manager_persenter.dart';
import '../persenter/custom_save_persenter.dart';

/// 添加、新增客户
class CustomSavePage extends StatefulWidget {
  CustomManagerList? customOne;

  CustomSavePage({Key? key, this.customOne}) : super(key: key);

  @override
  _CustomSavePageState createState() => _CustomSavePageState();
}

class _CustomSavePageState extends State<CustomSavePage> with BasePageMixin<CustomSavePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CustomSavePage> implements CustomSaveIView {
  CustomSavePresenter? _presenter;

  final CustomSaveController _controller = CustomSaveController();

  @override
  void initState() {
    super.initState();

    ///获取城市列表
    String? cityJson = SpUtil.getString(Constant.CITY_META_DATA);
    if (TextUtil.isEmpty(cityJson)) {
      _presenter?.getCityMetaData();
    }

    if (widget.customOne != null) {
      _controller.uuid.value = widget.customOne!.uuid ?? '';
      _controller.managerUserName.value = widget.customOne!.managerUserName ?? '';
      _controller.customShortName.value = widget.customOne!.customName ?? '';
      _controller.customFullName.value = widget.customOne!.customFullName ?? '';
      _controller.areaID.value = widget.customOne!.areaId ?? '';
      _controller.cityName.value = '${widget.customOne!.provinceName}${widget.customOne!.cityName}';
      _controller.address.value = widget.customOne!.address ?? '';
      _controller.contactPerson.value = widget.customOne!.contactPerson ?? '';
      _controller.contactMobile.value = widget.customOne!.contactMobile ?? '';
      _controller.remark.value = widget.customOne!.remark ?? '';
      _controller.managerUserUuid.value = widget.customOne!.managerUserUuid ?? '';
      _controller.homeTown.value = widget.customOne!.provinceName ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
        centerTitle: (widget.customOne == null) ? '添加客户' : '编辑客户',
      ),
      body: Obx(() => SingleChildScrollView(
            child: Column(
              children: [
                BrnTextInputFormItem(
                  title: "公司简称",
                  isRequire: true,
                  hint: "请输入",
                  controller: TextEditingController()..text = _controller.customShortName.value,
                  onChanged: (newValue) {
                    _controller.customShortName.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "公司全称",
                  hint: "请输入",
                  controller: TextEditingController()..text = _controller.customFullName.value,
                  onChanged: (newValue) {
                    _controller.customFullName.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "市场负责人",
                  hint: "请输入",
                  isRequire: true,
                  value: _controller.managerUserName.value,
                  onTap: () {
                    ///选择市场负责人
                    BoostNavigator.instance.push('selectPersonnelPage', arguments: {'title': '选择运营负责人', 'status': '1', 'is_head_office': '1', 'uuids': _controller.managerUserUuid.value}).then((value) {
                      if (value != null) {
                        List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                        if (data != null && data.isNotEmpty) {
                          _controller.managerUserName.value = data[0].userName ?? '';
                          _controller.managerUserUuid.value = data[0].uuid ?? '';
                        }
                      }
                    });
                  },
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "城市/区域",
                  value: _controller.cityName.value,
                  onTap: () {
                    if (_controller.cityList.isEmpty) {
                      _controller.cityList = CommonUtils.getCityList();
                    }
                    BrnMultiDataPicker(
                      context: context,
                      title: '选择城市/区域',
                      delegate: Brn2CityRowDelegate(
                        firstSelectedIndex: _controller.firstSelectedIndex,
                        secondSelectedIndex: _controller.secondSelectedIndex,
                        cityData: _controller.cityList,
                      ),
                      confirmClick: (selectedItemLists) {
                        if (selectedItemLists.isNotEmpty) {
                          _controller.firstSelectedIndex = selectedItemLists[0];
                          _controller.secondSelectedIndex = selectedItemLists[1];
                          // 获取选中的省对象
                          CityDataList province = _controller.cityList[_controller.firstSelectedIndex];
                          // 获取选中的城市对象
                          CityDataListList city = province.list![_controller.secondSelectedIndex];
                          //区域
                          CityDataListListList area = city.list![_controller.thirdSelectedIndex];

                          ///赋值給参数
                          _controller.cityName.value = '${province.name}-${city.name}';
                          _controller.areaID.value = area.id ?? '';
                          // 显示选中的省和市的信息
                          print(
                            "Selected Province: ${province.name} (${province.id}), Selected City: ${city.name} (${city.id})",
                          );
                        }
                      },
                    ).show();
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "详细地址",
                  hint: "请输入",
                  controller: TextEditingController()..text = _controller.address.value,
                  onChanged: (newValue) {
                    _controller.address.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "关键人",
                  hint: "请输入",
                  controller: TextEditingController()..text = _controller.contactPerson.value,
                  onChanged: (newValue) {
                    _controller.contactPerson.value = newValue;
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "关键人电话",
                  hint: "请输入",
                  controller: TextEditingController()..text = _controller.contactMobile.value,
                  onChanged: (newValue) {
                    _controller.contactMobile.value = newValue;
                  },
                ),
                Gaps.line,
                Container(
                  padding: const EdgeInsets.only(left: 20, top: 10, bottom: 10),
                  width: double.infinity,
                  child: CommonUtils.getSimpleText('备注', 14, Colours.base_primary_text_title),
                  color: Colours.white,
                ),
                Container(
                  padding: const EdgeInsets.only(left: 20, right: 20, bottom: 20),
                  color: Colours.white,
                  child: BrnInputText(
                    maxHeight: 200,
                    minHeight: 100,
                    borderRadius: 4.0,
                    minLines: 1,
                    textString: _controller.remark.value,
                    maxLength: 150,
                    bgColor: Colours.base_primary_bg_page,
                    autoFocus: false,
                    textInputAction: TextInputAction.newline,
                    hint: '请输入150字以内的备注信息',
                    padding: const EdgeInsets.fromLTRB(20, 10, 20, 14),
                    onTextChange: (text) {
                      _controller.remark.value = text;
                    },
                    onSubmit: (text) {},
                  ),
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '保存',
          onTap: () {
            if (TextUtil.isEmpty(_controller.customShortName.value)) {
              BrnToast.show('请输入项目简称', context);
              return;
            }
            if (TextUtil.isEmpty(_controller.managerUserUuid.value)) {
              BrnToast.show('请选择市场负责人', context);
              return;
            }
            _presenter?.saveCustomInfo();
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CustomSavePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void saveStatus() {
    BrnToast.show(widget.customOne == null ? '创建成功' : '编辑成功', context);
    BoostNavigator.instance.pop();
  }

  @override
  void dispose() {
    super.dispose();
  }
}
