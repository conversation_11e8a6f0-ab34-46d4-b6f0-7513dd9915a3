import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/custom_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/custom_manager_entity.g.dart';

@JsonSerializable()
class CustomManagerEntity {
  int? page;
  int? size;
  int? total;
  List<CustomManagerList>? list;

  CustomManagerEntity();

  factory CustomManagerEntity.fromJson(Map<String, dynamic> json) => $CustomManagerEntityFromJson(json);

  Map<String, dynamic> toJson() => $CustomManagerEntityToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}

@JsonSerializable()
class CustomManagerList {
  String? uuid;
  @JSONField(name: "custom_full_name")
  String? customFullName;
  @JSONField(name: "province_name")
  String? provinceName;
  @JSONField(name: "city_name")
  String? cityName;
  @JSONField(name: "city_id")
  String? cityId;
  @JSONField(name: "area_id")
  String? areaId;
  @JSONField(name: "area_name")
  String? areaName;
  String? address;
  @JSONField(name: "contact_person")
  String? contactPerson;
  @JSONField(name: "contact_mobile")
  String? contactMobile;
  String? remark;
  @JSONField(name: "project_num")
  String? projectNum;
  @JSONField(name: "custom_name")
  String? customName;
  @JSONField(name: "manager_user_name")
  String? managerUserName;
  @JSONField(name: "manager_user_uuid")
  String? managerUserUuid;

  CustomManagerList();

  factory CustomManagerList.fromJson(Map<String, dynamic> json) => $CustomManagerListFromJson(json);

  Map<String, dynamic> toJson() => $CustomManagerListToJson(this);

  @override
  String toString() {
    return jsonEncode(this);
  }
}
