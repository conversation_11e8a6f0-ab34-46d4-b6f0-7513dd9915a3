import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/custom/page/custom_save_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/custom/page/custon_manager_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/custom/page/custon_one_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/monitoring_work_order_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/quality_monitoring_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/risk_monitoring/page/risk_page.dart';

import '../../../net/http_config.dart';
import 'bean/custom_manager_entity.dart';

/// 客户管理
const customManagerPage = "customManagerPage";

/// 客户详情
const customOnePage = "customOnePage";

/// 新建客户
const customSavePage = "customSavePage";

/// 客户路由
Map<String, FlutterBoostRouteFactory> customRouterMap = {
  customManagerPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String choose_uuid = map['choose_uuid'] ?? "";
          bool isSelected = map['isSelected'] ?? false;
          return CustomManagerPage(
            choose_uuid: choose_uuid,
            isSelected: isSelected,
          );
        });
  },

  ///客户详情
  customOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map['uuid'] ?? "";
          return CustomOnePage(uuid: uuid);
        });
  },

  ///新增编辑客户
  customSavePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          CustomManagerList? customOne = map['custom_one'];
          return CustomSavePage(customOne: customOne);
        });
  },
};
