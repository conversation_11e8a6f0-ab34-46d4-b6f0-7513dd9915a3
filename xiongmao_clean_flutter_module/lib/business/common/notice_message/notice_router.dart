import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_history_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_market_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/page/qr_login_web_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/page/scan_qr_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/notice_message/page/notice_message_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';

import '../../../net/http_config.dart';

/// 系统通知显示的内容
const noticeMessagePage = "noticeMessagePage";

///二维码扫描
const scanQrPage = "ScanQrPage";

///二维码登录 Web pc 端
const scanLoginWebPage = "ScanLoginWebPage";

/// 系统通知
Map<String, FlutterBoostRouteFactory> noticeMessageRouterMap = {
  ///系统通知
  noticeMessagePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return NoticeMessagePage();
        });
  },

  ///二维码扫描
  scanQrPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ScanQrPage();
        });
  },

  ///登录 web 扫描
  scanLoginWebPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map["uuid"] ?? "";
          return ScanLoginWebPage(
            code: uuid,
          );
        });
  },
};
