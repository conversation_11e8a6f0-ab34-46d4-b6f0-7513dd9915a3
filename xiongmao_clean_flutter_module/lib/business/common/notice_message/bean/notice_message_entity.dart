import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/notice_message_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/notice_message_entity.g.dart';

@JsonSerializable()
class NoticeMessageEntity {
	int? page;
	int? size;
	int? total;
	List<NoticeMessageList>? list;

	NoticeMessageEntity();

	factory NoticeMessageEntity.fromJson(Map<String, dynamic> json) => $NoticeMessageEntityFromJson(json);

	Map<String, dynamic> toJson() => $NoticeMessageEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class NoticeMessageList {
	@JSONField(name: "notice_type")
	String? noticeType;
	@JSONField(name: "notice_type_name")
	String? noticeTypeName;
	String? content;
	@JSONField(name: "create_time")
	String? createTime;

	NoticeMessageList();

	factory NoticeMessageList.fromJson(Map<String, dynamic> json) => $NoticeMessageListFromJson(json);

	Map<String, dynamic> toJson() => $NoticeMessageListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}