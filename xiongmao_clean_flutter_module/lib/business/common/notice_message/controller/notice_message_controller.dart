import 'package:get/get.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/notice_message_entity.dart';

/**
 * 系统通知的保单
 */
class NoticeMessagePageController extends GetxController {
  var totalNumber = "0".obs;

  ///系统通知list
  var list = <NoticeMessageList>[].obs;

  void initMyList(List<NoticeMessageList> value) {
    list.value = value.toList();
  }

  void updateMyList(List<NoticeMessageList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }
}
