import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_zxing/flutter_zxing.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/my_app_bar.dart';
import '../controller/scan_qr_controller.dart';
import '../iview/scan_qr_iview.dart';
import '../presenter/scan_qr_persenter.dart';

///扫描登录
class ScanLoginWebPage extends StatefulWidget {
  String code;

  ScanLoginWebPage({super.key, required this.code});

  @override
  State<ScanLoginWebPage> createState() => _ScanLoginWebPageState();
}

class _ScanLoginWebPageState extends State<ScanLoginWebPage> with BasePageMixin<ScanLoginWebPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ScanLoginWebPage> implements ScanQrIView {
  ScanQrPresenter? _presenter;

  final ScanQrController _controller = ScanQrController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '扫码登录',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Container(
        width: double.infinity,
        child: Column(
          children: [
            const SizedBox(
              height: 130,
            ),
            CommonUtils.getSimpleText('您正在扫码登录电脑端', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
            const SizedBox(
              height: 20,
            ),
            CommonUtils.getSimpleText('是否确定登录？', 18, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
            const SizedBox(
              height: 50,
            ),
            Padding(
              padding: EdgeInsets.symmetric(horizontal: 30),
              child: BrnBigMainButton(
                title: '确定',
                onTap: () {
                  _presenter?.loginWeb(widget.code);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ScanQrPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void success() {
    BoostNavigator.instance.pop();
  }
}
