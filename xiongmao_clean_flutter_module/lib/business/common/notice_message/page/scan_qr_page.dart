import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_zxing/flutter_zxing.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/permission_util.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/my_app_bar.dart';
import '../controller/scan_qr_controller.dart';
import '../iview/scan_qr_iview.dart';
import '../presenter/scan_qr_persenter.dart';

///二维码扫描
class ScanQrPage extends StatefulWidget {
  const ScanQrPage({super.key});

  @override
  State<ScanQrPage> createState() => _ScanQrPageState();
}

class _ScanQrPageState extends State<ScanQrPage> with BasePageMixin<ScanQrPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<ScanQrPage> implements ScanQrIView {
  ScanQrPresenter? _presenter;

  final ScanQrController _controller = ScanQrController();

  Uint8List? createdCodeBytes;

  Code? result;
  bool showDebugInfo = true;
  int successScans = 0;
  int failedScans = 0;

  @override
  void initState() {
    super.initState();
    permissionUtil.requestPermission(Permission.camera, tipMsg: "扫描二维码需要获取您的相机权限", requestSuccessFun: () {});
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '扫一扫',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      body: ReaderWidget(
        onScan: _onScanSuccess,
        onScanFailure: _onScanFailure,
        onControllerCreated: _onControllerCreated,
        tryInverted: true,
        scanDelay: const Duration(milliseconds: 500),
        resolution: ResolutionPreset.high,
        lensDirection: CameraLensDirection.back,
        showFlashlight: false,
        showGallery: false,
        showToggleCamera: false,
        scannerOverlay: const FixedScannerOverlay(
          borderColor: Colours.base_primary,
          overlayColor: Colors.black45,
          borderRadius: 1,
          borderLength: 16,
          borderWidth: 8,
          cutOutSize: 200,
        ),
      ),
    );
  }

  void _onControllerCreated(_, Exception? error) {
    if (error != null) {
      // Handle permission or unknown errors
      _showMessage(context, 'Error: $error');
    }
  }

  _onScanSuccess(Code? code) {
    print("扫描成功_onScanSuccess---> ${code?.text}");
    if (code == null || TextUtil.isEmpty(code.text)) {
      Toast.show('扫描异常');
      return;
    }
    // 解析 JSON
    Map<String, dynamic> jsonMap = jsonDecode(code.text!);

    // 获取 uuid
    String uuid = jsonMap['uuid'];

    ///如果扫描出来特性的标识，那么跳转到 特性详情页面 web 登录界面
    BoostNavigator.instance.push('ScanLoginWebPage', arguments: {'uuid': uuid});
  }

  _onScanFailure(Code? code) {
    print("扫描失败--->_onScanFailure  ${code?.error}");
    setState(() {
      failedScans++;
      result = code;
    });
    if (code?.error?.isNotEmpty == true) {
      _showMessage(context, 'Error: ${code?.error}');
    }
  }

  _showMessage(BuildContext context, String message) {
    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text(message)),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ScanQrPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;

  @override
  void success() {}
}
