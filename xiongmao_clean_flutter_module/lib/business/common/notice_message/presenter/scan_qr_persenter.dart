import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_history_one_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/notice_message_entity.dart';
import '../controller/notice_message_controller.dart';
import '../controller/scan_qr_controller.dart';
import '../iview/notice_messsage_iview.dart';
import '../iview/scan_qr_iview.dart';

/// 二维码扫描
class ScanQrPresenter extends BasePagePresenter<ScanQrIView> with WidgetsBindingObserver {
  ScanQrController controller;

  ScanQrPresenter(this.controller);

  //请求登录 web 接口
  Future<dynamic> loginWeb(String code) {
    var params = <String, dynamic>{};
    params['code'] = code;
    return requestNetwork<Object>(Method.post, url: HttpApi.QR_CODE_LOGIN_WEB, params: params, isShow: true, isClose: true, onSuccess: (data) {
      view.success();
    }, onError: (_, __) {});
  }
}
