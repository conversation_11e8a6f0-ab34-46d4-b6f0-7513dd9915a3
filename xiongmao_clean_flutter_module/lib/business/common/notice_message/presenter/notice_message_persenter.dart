import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get_rx/src/rx_types/rx_types.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/iview/insure_scheme_history_one_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/iview/work_rules_save_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/notice_message_entity.dart';
import '../controller/notice_message_controller.dart';
import '../iview/notice_messsage_iview.dart';

/**
 * 系统的消息
 */
class NoticeMessagePagePresenter extends BasePagePresenter<NoticeMessageIView> with WidgetsBindingObserver {
  NoticeMessagePageController controller;

  NoticeMessagePagePresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getInsureHistoryActionRecord();
  }

  void loadMore() {
    _page++;
    getInsureHistoryActionRecord();
  }

  //获取详情的操作记录
  Future<dynamic> getInsureHistoryActionRecord() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "100";
    return requestNetwork<NoticeMessageEntity>(Method.get, url: HttpApi.GET_NOTICE_MESSAGE_GETLIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        controller.totalNumber.value = "${data.total ?? 0}";
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {});
  }

  //获取详情的操作记录
  Future<dynamic> sendRead() {
    var params = <String, String>{};
    return requestNetwork<Object>(Method.get, url: HttpApi.GET_NOTICE_MESSAGE_READ, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {}, onError: (_, __) {});
  }
}
