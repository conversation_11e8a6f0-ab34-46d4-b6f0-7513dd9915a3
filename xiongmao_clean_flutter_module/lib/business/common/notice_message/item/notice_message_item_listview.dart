import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/insure/page/Insure_details_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/web/page/web_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../insure/bean/insure_one_record_entity.dart';
import '../bean/notice_message_entity.dart';

/**
 * 系统通知的列表
 */
class NoticeMessageItemListView extends StatelessWidget {
  NoticeMessageList data;

  NoticeMessageItemListView({required this.data});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {},
      child: Container(
        margin: EdgeInsets.only(top: 10, left: 14, right: 14),
        child: Column(
          children: [
            CommonUtils.getSimpleText(data.createTime, 14, Colours.base_primary_text_caption, textAlign: TextAlign.center),
            Container(
              padding: EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
              width: double.infinity,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary_text_hint), // 绿色边框
                borderRadius: BorderRadius.circular(8.0), // 设置圆角半径为10.0
                color: Colors.white, // 设置背景颜色为灰色
              ),
              child: RichText(
                text: TextSpan(
                  style: TextStyle(color: Colors.black),
                  children: [
                    TextSpan(text: '${data.noticeTypeName ?? '提示'}：', style: TextStyle(color: Colors.red)),
                    TextSpan(text: data.content ?? ""),
                  ],
                ),
              ),
            )
          ],
        ),
      ),
    );
  }
}
