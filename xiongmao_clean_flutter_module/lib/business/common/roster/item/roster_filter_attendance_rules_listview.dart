import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import '../../../../res/colors.dart';
import '../../../../widgets/load_image.dart';
import '../bean/attendance_manager_entity.dart';

class RosterFilterAttendanceRulesListItem extends StatelessWidget {
  AttendanceManagerList data;

  String? uuid;

  Function onClick;

  RosterFilterAttendanceRulesListItem({super.key, required this.data, required this.uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        margin: const EdgeInsets.only(bottom: 10, top: 10),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText(data.groupName, 15, Colours.base_primary_text_title)),
                LoadAssetImage(
                  (uuid == data.uuid) ? "icon_check" : "icon_uncheck",
                  width: 20,
                  height: 20,
                ),
              ],
            ),
            Gaps.vGap20,
            Gaps.line,
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
