import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/gaps.dart';
import '../../project/bean/project_manager_entity.dart';

class RosterFilterAttendanceRulesRowListItem extends StatelessWidget {
  ProjectManagerList data;
  String uuid;

  Function onClick;

  RosterFilterAttendanceRulesRowListItem({super.key, required this.data, required this.uuid, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 4.0),
        color: (uuid == data.uuid) ? Colours.white : Colours.transparent,
        child: Row(
          children: [
            Container(
              width: 4.0,
              height: 30.0,
              margin: const EdgeInsets.only(left: 4.0, right: 10.0, top: 6.0, bottom: 6.0),
              decoration: BoxDecoration(
                color: (uuid == data.uuid) ? Colours.base_primary : Colours.transparent, // Replace with your color
                borderRadius: const BorderRadius.only(
                  topRight: Radius.circular(10.0),
                  bottomRight: Radius.circular(10.0),
                ),
              ),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  CommonUtils.getSimpleText((!TextUtil.isEmpty(data.projectShortName) ? data.projectShortName : data.projectName), 14, (uuid == data.uuid) ? Colours.base_primary : Colours.base_primary_text_title),
                ],
              ),
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }
}
