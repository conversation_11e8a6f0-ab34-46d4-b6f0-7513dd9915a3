import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../project/bean/project_manager_entity.dart';
import '../bean/attendance_manager_entity.dart';
import '../bean/roster_filter_data.dart';

///花名册的筛选
class RosterFilterRulesController extends GetxController {
  ///性别的列表
  var sexList = [
    BaseChooseString("男"),
    BaseChooseString("女"),
  ].obs;

  ///状态的列表
  var statusList = [
    BaseChooseString("已完善"),
    BaseChooseString("未完善"),
  ].obs;

  ///状态的列表
  var insureStatusList = [
    BaseChooseString("无需购买"),
    BaseChooseString("待购买"),
    BaseChooseString("已购买"),
  ].obs;

  ///参保方式的列表
  var insuredStatusList = [
    BaseChooseString("不参保"),
    BaseChooseString("社保"),
    BaseChooseString("商业保险"),
  ].obs;

  ///正常的列表
  var normalStatusList = [
    BaseChooseString("正常"),
    BaseChooseString("异常"),
  ].obs;

  ///角色的列表
  var roleList = [
    BaseChooseString("管理员"),
    BaseChooseString("人事"),
    BaseChooseString("大区经理"),
    BaseChooseString("项目负责人"),
    BaseChooseString("领班"),
    BaseChooseString("保洁"),
  ].obs;

  ///入职的日期
  var entryDateList = [
    BaseChooseString("上周"),
    BaseChooseString("本周"),
    BaseChooseString("本月"),
    BaseChooseString("上月"),
  ].obs;

  ///性别的位置
  var sexIndex = (-1).obs;

  ///年龄范围
  var ageMin = '最低·不限制'.obs;
  var ageMax = '最高·不限制'.obs;

  ///年龄最低
  var ageMinIndex = 0.obs;

  ///年龄最高
  var ageMaxIndex = 0.obs;

  ///年龄的
  List<String> ageList = List.generate(70 - 18 + 1, (i) => (i + 18).toString());

  ///入职日期
  var customEntryDate = '自定义日期范围'.obs;
  var customEntryStartDate = ''.obs;
  var customEntryEndDate = ''.obs;
  DateTime customEntryStartTime = DateTime.now();
  DateTime customEntryEndTime = DateTime.now();

  ///自定义日期的
  var entryIndex = (-1).obs;

  ///角色
  var roleIndex = (-1).obs;

  ///参保类型
  var insureTypeIndex = (-1).obs;

  ///头像
  var headIndex = (-1).obs;

  ///身份证
  var idPicIndex = (-1).obs;

  ///保险
  var insureIndex = (-1).obs;

  ///劳动协议
  var isHasIndex = (-1).obs;

  ///信用查询
  var creditInquiryIndex = (-1).obs;

  ///银行卡
  var bankIndex = (-1).obs;

  ///健康证明
  var healthIndex = (-1).obs;

  ///无犯罪证明
  var recodeIndex = (-1).obs;

  ///考勤规则
  var groupUuid = ''.obs;
  var groupName = '请选择'.obs;

  ///反选
  void invertData(RosterFilterData filterData) {
    if (filterData.isReset == true) {
      return;
    }
    //性别
    if (!TextUtil.isEmpty(filterData.sex)) {
      sexIndex.value = int.parse(filterData.sex!) - 1;
    }
    //年龄范围
    if (!TextUtil.isEmpty(filterData.minAge)) {
      ageMin.value = '最低·${filterData.minAge}岁';
    }
    if (!TextUtil.isEmpty(filterData.maxAge)) {
      ageMax.value = '最高·${filterData.maxAge}岁';
    }
    //入职日期
    customEntryDate.value = invertEntryDate(filterData.startDate ?? '');
    //角色
    invertRole(filterData.roleId ?? '');
    //考勤规则
    groupUuid.value = filterData.groupUuid ?? '';
    groupName.value = filterData.groupName ?? '';
    //参保方式
    if (!TextUtil.isEmpty(filterData.insuranceType)) {
      insureTypeIndex.value = int.parse(filterData.insuranceType!) - 1;
    }
    //头像
    if (!TextUtil.isEmpty(filterData.picStatus)) {
      headIndex.value = int.parse(filterData.picStatus!) - 1;
    }
    //身份证照片
    if (!TextUtil.isEmpty(filterData.isHasIdCard)) {
      idPicIndex.value = int.parse(filterData.isHasIdCard!) - 1;
    }
    //保险
    if (!TextUtil.isEmpty(filterData.insureStatus)) {
      insureIndex.value = int.parse(filterData.insureStatus!) - 1;
    }
    //劳动协议
    if (!TextUtil.isEmpty(filterData.isHasContract)) {
      isHasIndex.value = int.parse(filterData.isHasContract!) - 1;
    }
    //信用查询
    if (!TextUtil.isEmpty(filterData.creditInquiryStatus)) {
      creditInquiryIndex.value = int.parse(filterData.creditInquiryStatus!) - 1;
    }
    //银行卡
    if (!TextUtil.isEmpty(filterData.bankStatus)) {
      bankIndex.value = int.parse(filterData.bankStatus!) - 1;
    }
    //健康证明
    if (!TextUtil.isEmpty(filterData.healthStatus)) {
      healthIndex.value = int.parse(filterData.healthStatus!) - 1;
    }
    //无犯罪证明
    if (!TextUtil.isEmpty(filterData.recodeCheck)) {
      recodeIndex.value = int.parse(filterData.recodeCheck!) - 1;
    }
  }

  ///自定义日期范围
  String invertEntryDate(String value) {
    if (value.isEmpty) {
      return '自定义日期范围';
    } else {
      if (value.contains('#')) {
        return value;
      } else {
        switch (value) {
          case 'lastWeek':
            entryIndex.value = 0;
            return '自定义日期范围';
          case 'thisWeek':
            entryIndex.value = 1;
            return '自定义日期范围';
          case 'thisMonth':
            entryIndex.value = 2;
            return '自定义日期范围';
          case 'lastMonth':
            entryIndex.value = 3;
            return '自定义日期范围';
          default:
            return '自定义日期范围';
        }
      }
    }
  }

  ///角色的反选
  void invertRole(String role) {
    switch (role) {
      case '1':
        roleIndex.value = 0;
        break;
      case '3':
        roleIndex.value = 1;
        break;
      case '6':
        roleIndex.value = 2;
        break;
      case '2':
        roleIndex.value = 3;
        break;
      case '4':
        roleIndex.value = 4;
        break;
      case '5':
        roleIndex.value = 5;
        break;
    }
  }
}
