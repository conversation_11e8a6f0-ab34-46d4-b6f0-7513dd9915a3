import 'dart:convert';

class RosterFilterData {
  String? sex;
  String? minAge;
  String? maxAge;
  String? startDate;
  String? endDate;
  String? contractType;
  String? picStatus;
  String? bankStatus;
  String? creditInquiryStatus;
  String? insureStatus;
  String? contractStatus;
  String? healthStatus;
  String? recodeCheck;
  String? insuranceType;
  String? insuranceStatus;
  String? isHasIdCard;
  String? isHasContract;
  String? roleId;
  String? groupUuid;
  String? groupName;
  bool? isReset;

  RosterFilterData({
    this.sex = '',
    this.minAge = '',
    this.maxAge = '',
    this.startDate = '',
    this.endDate = '',
    this.contractType = '',
    this.picStatus = '',
    this.bankStatus = '',
    this.creditInquiryStatus = '',
    this.insureStatus = '',
    this.contractStatus = '',
    this.healthStatus = '',
    this.recodeCheck = '',
    this.insuranceType = '',
    this.insuranceStatus = '',
    this.isHasIdCard = '',
    this.isHasContract = '',
    this.roleId = '',
    this.groupUuid = '',
    this.groupName = '',
    this.isReset = false,
  });

  factory RosterFilterData.fromJson(Map<String, dynamic> json) {
    return RosterFilterData(
      sex: json['sex'],
      minAge: json['min_age'],
      maxAge: json['max_age'],
      startDate: json['start_date'],
      endDate: json['end_date'],
      contractType: json['contract_type'],
      picStatus: json['pic_status'],
      bankStatus: json['bank_status'],
      creditInquiryStatus: json['credit_inquiry_status'],
      insureStatus: json['insure_status'],
      contractStatus: json['contract_status'],
      healthStatus: json['health_status'],
      recodeCheck: json['recode_check'],
      insuranceType: json['insurance_type'],
      insuranceStatus: json['insurance_status'],
      isHasIdCard: json['is_has_id_card'],
      isHasContract: json['is_has_contract'],
      roleId: json['role_id'],
      groupUuid: json['group_uuid'],
      groupName: json['group_name'],
      isReset: json['isReset'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'sex': sex,
      'min_age': minAge,
      'max_age': maxAge,
      'start_date': startDate,
      'end_date': endDate,
      'contract_type': contractType,
      'pic_status': picStatus,
      'bank_status': bankStatus,
      'credit_inquiry_status': creditInquiryStatus,
      'insure_status': insureStatus,
      'contract_status': contractStatus,
      'health_status': healthStatus,
      'recode_check': recodeCheck,
      'insurance_type': insuranceType,
      'insurance_status': insuranceStatus,
      'is_has_id_card': isHasIdCard,
      'is_has_contract': isHasContract,
      'role_id': roleId,
      'group_uuid': groupUuid,
      'group_name': groupName,
      'isReset': isReset,
    };
  }
}
