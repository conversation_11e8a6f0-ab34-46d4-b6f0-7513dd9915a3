import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/attendance_manager_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/attendance_manager_entity.g.dart';

@JsonSerializable()
class AttendanceManagerEntity {
	List<AttendanceManagerList>? list;

	AttendanceManagerEntity();

	factory AttendanceManagerEntity.fromJson(Map<String, dynamic> json) => $AttendanceManagerEntityFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceManagerEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class AttendanceManagerList {
	String? uuid;
	@JSONField(name: "group_name")
	String? groupName;
	@JSONField(name: "work_day_desc")
	String? workDayDesc;
	@JSONField(name: "in_class_desc")
	String? inClassDesc;
	@JSONField(name: "address_list")
	List<String>? addressList;
	@JSONField(name: "attendance_method_name")
	String? attendanceMethodName;

	AttendanceManagerList();

	factory AttendanceManagerList.fromJson(Map<String, dynamic> json) => $AttendanceManagerListFromJson(json);

	Map<String, dynamic> toJson() => $AttendanceManagerListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}