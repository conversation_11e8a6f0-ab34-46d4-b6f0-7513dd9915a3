import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/roster/page/roster_filter_attendance_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/roster/page/roster_filter_rules_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/todo/page/todo_page.dart';

import '../../../net/http_config.dart';

/// 花名册筛选
const rosterFilterRulesPage = "RosterFilterRulesPage";

/// 花名册筛选 - 考勤规则
const rosterFilterAttendanceRulesPage = "RosterFilterAttendanceRulesPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> todoRouterMap = {
  rosterFilterRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? filterData = map["filterData"] ?? '';
          return RosterFilterRulesPage(
            filterData: filterData,
          );
        });
  },
  rosterFilterAttendanceRulesPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"];
          return RosterFilterAttendanceRulesPage(
            uuid: uuid,
          );
        });
  },
};
