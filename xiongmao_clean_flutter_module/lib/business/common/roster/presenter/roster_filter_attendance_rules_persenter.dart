import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../bean/attendance_manager_entity.dart';
import '../controller/roster_filter_attendance_rules_controller.dart';
import '../iview/roster_filter_attendance_rules_iview.dart';

/// 花名册筛选-考勤规则
class RosterFilterAttendanceRulesPresenter extends BasePagePresenter<RosterFilterAttendanceRulesIView> with WidgetsBindingObserver {
  RosterFilterAttendanceRulesController controller;

  RosterFilterAttendanceRulesPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getAttendanceListManager();
  }

  void loadMore() {
    _page++;
    getAttendanceListManager();
  }

  ///获取考勤规则的列表
  Future<dynamic> getAttendanceListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params["project_uuid"] = controller.projectUuid.value;
    }
    if (!TextUtil.isEmpty(controller.keyword.value)) {
      params["keyword"] = controller.keyword.value;
    }
    params['map_code'] = 'gao_de';
    return requestNetwork<AttendanceManagerEntity>(Method.get, url: HttpApi.GET_ATTENDANCE_MANAGER_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取考勤规则的row
  Future<dynamic> getAttendanceRowListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["project_uuid"] = httpConfig.project_uuid;
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ProjectManagerEntity>(Method.get, url: HttpApi.GET_GENERAL_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initRowMyList(data.list ?? []);

          ///计算
          if (data.list != null && data.list!.isNotEmpty) {
            controller.projectUuid.value = data.list![0].uuid!;
            onRefresh();
          }
        } else {
          controller.updateRowMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
