import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../bean/attendance_manager_entity.dart';
import '../controller/roster_filter_attendance_rules_controller.dart';
import '../controller/roster_filter_rules_controller.dart';
import '../iview/roster_filter_attendance_rules_iview.dart';
import '../iview/roster_filter_rules_iview.dart';

/// 花名册筛选-考勤规则
class RosterFilterRulesPresenter extends BasePagePresenter<RosterFilterRulesIView> with WidgetsBindingObserver {
  RosterFilterRulesController controller;

  RosterFilterRulesPresenter(this.controller);


}
