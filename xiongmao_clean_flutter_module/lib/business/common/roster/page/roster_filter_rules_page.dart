import 'dart:convert';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../bean/attendance_manager_entity.dart';
import '../bean/roster_filter_data.dart';
import '../controller/roster_filter_attendance_rules_controller.dart';
import '../controller/roster_filter_rules_controller.dart';
import '../item/roster_filter_attendance_rules_listview.dart';
import '../item/roster_filter_attendance_rules_row_listview.dart';
import '../iview/roster_filter_attendance_rules_iview.dart';
import '../iview/roster_filter_rules_iview.dart';
import '../presenter/roster_filter_attendance_rules_persenter.dart';
import '../presenter/roster_filter_rules_persenter.dart';

/// 花名册筛选
class RosterFilterRulesPage extends StatefulWidget {
  String? filterData = '';

  RosterFilterRulesPage({super.key, required this.filterData});

  @override
  _RosterFilterRulesPageState createState() => _RosterFilterRulesPageState();
}

class _RosterFilterRulesPageState extends State<RosterFilterRulesPage> with BasePageMixin<RosterFilterRulesPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RosterFilterRulesPage> implements RosterFilterRulesIView {
  RosterFilterRulesPresenter? _presenter;

  final RosterFilterRulesController _controller = RosterFilterRulesController();

  @override
  void initState() {
    super.initState();
    //这里是反选
    if (!TextUtil.isEmpty(widget.filterData)) {
      Map<String, dynamic> data = json.decode(widget.filterData!);
      RosterFilterData filterData = RosterFilterData.fromJson(data);
      print('拿到了 -- ${filterData.sex}');
      _controller.invertData(filterData);

      ///强制刷新
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '花名册筛选',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ///性别
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('性别', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      SelectTabWidget(
                        key: ValueKey(_controller.sexIndex.value),
                        _controller.sexList.value,
                        multiSelect: false,
                        crossAxisCount: 2,
                        hideMore: false,
                        paddingBottom: 0,
                        paddingTop: 6,
                        tabFontSize: 15,
                        defaultSelectedIndex: [_controller.sexIndex.value],
                        lastIsAddOne: false,
                        selectedColor: Colours.base_primary,
                        bgSelectedColor: Colours.base_primary_select,
                        bgUnSelectedColor: Colours.base_primary_un_select,
                        childAspectRatio: 8 / 2,
                        itemClickCallback: (List<int> indexs) {
                          LogUtil.e("indexs = $indexs");
                          if (_controller.sexIndex.value == (indexs[0]).toInt()) {
                            _controller.sexIndex.value = -1;
                          } else {
                            _controller.sexIndex.value = (indexs[0]).toInt();
                          }
                        },
                      )
                    ],
                  ),
                ),

                ///年龄范围
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('年龄范围', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      Gaps.vGap10,
                      Row(
                        children: [
                          Expanded(
                              child: GestureDetector(
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                color: (_controller.ageMin.value == '最低·不限制') ? Colours.base_primary_un_select : Colours.base_primary_select,
                                border: (_controller.ageMin.value == '最低·不限制') ? Border.all(color: Colours.transparent, width: 0) : Border.all(color: Colours.base_primary, width: 1),
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: CommonUtils.getSimpleText(_controller.ageMin.value, 15, (_controller.ageMin.value == '最低·不限制') ? Colours.base_primary_text_title : Colours.base_primary, textAlign: TextAlign.center),
                            ),
                            onTap: () {
                              if (_controller.ageMin.value != '最低·不限制') {
                                _controller.ageMin.value = '最低·不限制';
                                _controller.ageMinIndex.value = 0;
                              } else {
                                SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择最小年龄', _controller.ageList, _controller.ageMinIndex.value, (index, selectedText) {
                                  print('当前选择的下标：$index');
                                  print('当前选择的文本内容：$selectedText');
                                  _controller.ageMin.value = "最低·$selectedText岁";
                                  _controller.ageMinIndex.value = index;
                                });
                              }
                            },
                          )),
                          Gaps.hGap10,
                          CommonUtils.getSimpleText('~', 15, Colours.base_primary_text_title),
                          Gaps.hGap10,
                          Expanded(
                              child: GestureDetector(
                            child: Container(
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              decoration: BoxDecoration(
                                color: (_controller.ageMax.value == '最高·不限制') ? Colours.base_primary_un_select : Colours.base_primary_select,
                                border: (_controller.ageMax.value == '最高·不限制') ? Border.all(color: Colours.transparent, width: 0) : Border.all(color: Colours.base_primary, width: 1),
                                borderRadius: BorderRadius.circular(4.0),
                              ),
                              child: CommonUtils.getSimpleText(_controller.ageMax.value, 15, (_controller.ageMax.value == '最高·不限制') ? Colours.base_primary_text_title : Colours.base_primary, textAlign: TextAlign.center),
                            ),
                            onTap: () {
                              if (_controller.ageMax.value != '最高·不限制') {
                                _controller.ageMax.value = '最高·不限制';
                                _controller.ageMaxIndex.value = 0;
                              } else {
                                SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择最高年龄', _controller.ageList, _controller.ageMaxIndex.value, (index, selectedText) {
                                  print('当前选择的下标：$index');
                                  print('当前选择的文本内容：$selectedText');
                                  _controller.ageMax.value = "最高·$selectedText岁";
                                  _controller.ageMaxIndex.value = index;
                                });
                              }
                            },
                          )),
                        ],
                      ),
                    ],
                  ),
                ),

                ///入职日期
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('入职日期', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      SelectTabWidget(
                        key: ValueKey(_controller.entryIndex.value),
                        // 使用 entryIndex 作为 key
                        _controller.entryDateList.value,
                        multiSelect: false,
                        crossAxisCount: 4,
                        hideMore: false,
                        paddingBottom: 0,
                        paddingTop: 6,
                        tabFontSize: 15,
                        defaultSelectedIndex: (_controller.entryIndex.value == -1) ? [] : [_controller.entryIndex.value],
                        lastIsAddOne: false,
                        selectedColor: Colours.base_primary,
                        bgSelectedColor: Colours.base_primary_select,
                        bgUnSelectedColor: Colours.base_primary_un_select,
                        childAspectRatio: 8 / 4,
                        itemClickCallback: (List<int> indexs) {
                          LogUtil.e("indexs = $indexs");
                          if (_controller.entryIndex.value == (indexs[0]).toInt()) {
                            _controller.entryIndex.value = -1;
                            _controller.customEntryDate.value = '自定义日期范围';
                          } else {
                            _controller.entryIndex.value = (indexs[0]).toInt();
                            _controller.customEntryDate.value = '自定义日期范围';
                          }
                        },
                      ),
                      Gaps.vGap10,
                      InkWell(
                        child: Container(
                          width: double.infinity,
                          padding: const EdgeInsets.symmetric(vertical: 6),
                          decoration: BoxDecoration(
                            color: (_controller.customEntryDate.value == '自定义日期范围') ? Colours.base_primary_un_select : Colours.base_primary_select,
                            border: (_controller.customEntryDate.value == '自定义日期范围') ? Border.all(color: Colours.transparent, width: 0) : Border.all(color: Colours.base_primary, width: 1),
                            borderRadius: BorderRadius.circular(4.0),
                          ),
                          child: CommonUtils.getSimpleText(_controller.customEntryDate.value, 15, (_controller.customEntryDate.value == '自定义日期范围') ? Colours.base_primary_text_title : Colours.base_primary, textAlign: TextAlign.center),
                        ),
                        onTap: () {
                          if (_controller.customEntryDate.value != '自定义日期范围') {
                            _controller.customEntryDate.value = '自定义日期范围';
                          } else {
                            String format = 'yyyy年-MM月-dd日';
                            BrnPickerTitleConfig pickerTitleConfig = const BrnPickerTitleConfig(titleContent: "选择自定义时间范围");
                            BrnDateRangePicker.showDatePicker(
                              context,
                              isDismissible: false,
                              minDateTime: DateTime(2020, 01, 01, 00, 00, 00),
                              maxDateTime: DateTime(2029, 12, 30, 23, 59, 59),
                              pickerMode: BrnDateTimeRangePickerMode.date,
                              minuteDivider: 10,
                              pickerTitleConfig: pickerTitleConfig,
                              dateFormat: format,
                              initialStartDateTime: _controller.customEntryStartTime,
                              initialEndDateTime: _controller.customEntryEndTime,
                              onConfirm: (startDateTime, endDateTime, startlist, endlist) {
                                _controller.customEntryStartTime = startDateTime;
                                _controller.customEntryEndTime = endDateTime;
                                _controller.customEntryStartDate.value = '${startDateTime.year}.${CommonUtils.formatToTwoDigits(startDateTime.month)}.${CommonUtils.formatToTwoDigits(startDateTime.day)}';
                                _controller.customEntryEndDate.value = '${endDateTime.year}.${CommonUtils.formatToTwoDigits(endDateTime.month)}.${CommonUtils.formatToTwoDigits(endDateTime.day)}';
                                _controller.customEntryDate.value = '${_controller.customEntryStartDate.value} ~ ${_controller.customEntryEndDate.value}';
                                _controller.entryIndex.value = -1;
                              },
                              onClose: () {
                                print("onClose");
                              },
                              onCancel: () {
                                print("onCancel");
                              },
                              onChange: (startDateTime, endDateTime, startlist, endlist) {},
                            );
                          }
                        },
                      )
                    ],
                  ),
                ),

                ///角色
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('角色', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      SelectTabWidget(
                        key: ValueKey(_controller.roleIndex.value),
                        _controller.roleList.value,
                        multiSelect: false,
                        crossAxisCount: 4,
                        hideMore: false,
                        paddingBottom: 0,
                        paddingTop: 6,
                        tabFontSize: 15,
                        defaultSelectedIndex: _controller.roleIndex.value == -1 ? [] : [_controller.roleIndex.value],
                        lastIsAddOne: false,
                        selectedColor: Colours.base_primary,
                        bgSelectedColor: Colours.base_primary_select,
                        bgUnSelectedColor: Colours.base_primary_un_select,
                        childAspectRatio: 8 / 4,
                        itemClickCallback: (List<int> indexs) {
                          LogUtil.e("indexs = $indexs");
                          if (_controller.roleIndex.value == (indexs[0]).toInt()) {
                            _controller.roleIndex.value = -1;
                          } else {
                            _controller.roleIndex.value = (indexs[0]).toInt();
                          }
                        },
                      )
                    ],
                  ),
                ),

                ///考勤规则
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('考勤规则', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      Gaps.vGap10,
                      InkWell(
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            CommonUtils.getSimpleText(_controller.groupName.value, 15, _controller.groupName.value == '请选择' ? Colours.base_primary_text_caption : Colours.base_primary_text_title),
                            LoadImage(
                              'base/icon_base_gray_arrow',
                              height: 20,
                              width: 20,
                            ),
                          ],
                        ),
                        onTap: () {
                          BoostNavigator.instance.push('RosterFilterAttendanceRulesPage', arguments: {'uuid': _controller.groupUuid.value}).then((value) {
                            if (value != null) {
                              if (value is String) {
                                _controller.groupName.value = '不限制';
                                _controller.groupUuid.value = '';
                              } else if (value is AttendanceManagerList) {
                                AttendanceManagerList data = value;
                                _controller.groupName.value = data.groupName ?? '';
                                _controller.groupUuid.value = data.uuid ?? '';
                              }
                            }
                          });
                        },
                      )
                    ],
                  ),
                ),

                ///参保方式
                Container(
                  margin: EdgeInsets.only(top: 10),
                  color: Colors.white,
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('参保方式', 15, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      SelectTabWidget(
                        key: ValueKey(_controller.insureTypeIndex.value),
                        _controller.insuredStatusList.value,
                        multiSelect: false,
                        crossAxisCount: 4,
                        hideMore: false,
                        paddingBottom: 0,
                        paddingTop: 6,
                        tabFontSize: 15,
                        defaultSelectedIndex: [_controller.insureTypeIndex.value],
                        lastIsAddOne: false,
                        selectedColor: Colours.base_primary,
                        bgSelectedColor: Colours.base_primary_select,
                        bgUnSelectedColor: Colours.base_primary_un_select,
                        childAspectRatio: 8 / 4,
                        itemClickCallback: (List<int> indexs) {
                          LogUtil.e("indexs = $indexs");
                          if (_controller.insureTypeIndex.value == (indexs[0]).toInt()) {
                            _controller.insureTypeIndex.value = -1;
                          } else {
                            _controller.insureTypeIndex.value = (indexs[0]).toInt();
                          }
                        },
                      )
                    ],
                  ),
                ),

                ///标签
                Container(
                  margin: EdgeInsets.only(top: 10, bottom: 100),
                  color: Colors.white,
                  width: double.infinity,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 16),
                        child: CommonUtils.getSimpleText('标签', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      ),

                      ///头像
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('头像', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  _controller.statusList.value,
                                  key: ValueKey(_controller.headIndex.value),
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.headIndex.value == -1 ? [] : [_controller.headIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.headIndex.value == (indexs[0]).toInt()) {
                                      _controller.headIndex.value = -1;
                                    } else {
                                      _controller.headIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///身份证照片
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('身份证照片', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  _controller.statusList.value,
                                  key: ValueKey(_controller.idPicIndex.value),
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.idPicIndex.value == -1 ? [] : [_controller.idPicIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.idPicIndex.value == (indexs[0]).toInt()) {
                                      _controller.idPicIndex.value = -1;
                                    } else {
                                      _controller.idPicIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///保险
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('保险', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  _controller.insureStatusList.value,
                                  key: ValueKey(_controller.insureIndex.value),
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.insureIndex.value == -1 ? [] : [_controller.insureIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.insureIndex.value == (indexs[0]).toInt()) {
                                      _controller.insureIndex.value = -1;
                                    } else {
                                      _controller.insureIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///劳动协议
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('劳动协议', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.isHasIndex.value),
                                  _controller.statusList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.isHasIndex.value == -1 ? [] : [_controller.isHasIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.isHasIndex.value == (indexs[0]).toInt()) {
                                      _controller.isHasIndex.value = -1;
                                    } else {
                                      _controller.isHasIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///信用查询
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('信用查询', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.creditInquiryIndex.value),
                                  _controller.normalStatusList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.creditInquiryIndex.value == -1 ? [] : [_controller.creditInquiryIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.creditInquiryIndex.value == (indexs[0]).toInt()) {
                                      _controller.creditInquiryIndex.value = -1;
                                    } else {
                                      _controller.creditInquiryIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///银行卡
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('银行卡', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.bankIndex.value),
                                  _controller.statusList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.bankIndex.value == -1 ? [] : [_controller.bankIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.bankIndex.value == (indexs[0]).toInt()) {
                                      _controller.bankIndex.value = -1;
                                    } else {
                                      _controller.bankIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///健康证明
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('健康证明', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.healthIndex.value),
                                  _controller.statusList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.healthIndex.value == -1 ? [] : [_controller.healthIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.healthIndex.value == (indexs[0]).toInt()) {
                                      _controller.healthIndex.value = -1;
                                    } else {
                                      _controller.healthIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                      Gaps.line,

                      ///无犯罪证明
                      Padding(
                        padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                        child: Row(
                          children: [
                            Expanded(child: CommonUtils.getSimpleText('无犯罪证明', 15, Colours.base_primary_text_title)),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.recodeIndex.value),
                                  _controller.statusList.value,
                                  multiSelect: false,
                                  crossAxisCount: 3,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.recodeIndex.value == -1 ? [] : [_controller.recodeIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 3,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    if (_controller.recodeIndex.value == (indexs[0]).toInt()) {
                                      _controller.recodeIndex.value = -1;
                                    } else {
                                      _controller.recodeIndex.value = (indexs[0]).toInt();
                                    }
                                  },
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        color: Colors.white,
        height: 60,
        child: Column(
          children: [
            Gaps.line,
            Gaps.vGap10,
            Padding(
              padding: const EdgeInsets.only(
                left: 16,
                right: 16,
              ),
              child: Row(
                children: [
                  InkWell(
                    child: Container(
                      padding: const EdgeInsets.only(left: 30, right: 30, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('重置', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      var filterData = RosterFilterData();
                      filterData.isReset = true;
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                        "method": "roster_filter_rules",
                        "filterData": json.encode(filterData),
                      });
                      BoostNavigator.instance.pop();
                    },
                  ),
                  Gaps.hGap10,
                  Expanded(
                      child: InkWell(
                    child: Container(
                      padding: const EdgeInsets.only(left: 20, right: 20, top: 8, bottom: 8),
                      decoration: BoxDecoration(
                        color: Colours.base_primary,
                        borderRadius: BorderRadius.circular(4.0),
                      ),
                      child: CommonUtils.getSimpleText('确定', 16, Colours.white, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      ///传递给原生的参数
                      var filterData = RosterFilterData();
                      //性别
                      filterData.sex = _controller.sexIndex.value == -1 ? '' : '${_controller.sexIndex.value + 1}';
                      //年龄范围
                      filterData.minAge = _controller.ageMin.value.contains('不限制') ? '' : _controller.ageMin.value.replaceAll('岁', '').replaceAll('最低·', '');
                      filterData.maxAge = _controller.ageMax.value.contains('不限制') ? '' : _controller.ageMax.value.replaceAll('岁', '').replaceAll('最高·', '');
                      //入职日期
                      filterData.startDate = getDateSelect((_controller.entryIndex.value == -1) ? '' : '${_controller.entryDateList[_controller.entryIndex.value].name}', _controller.customEntryDate.value.replaceAll("~", "#"));
                      //角色
                      filterData.roleId = updateRoleId(_controller.roleIndex.value);
                      //考勤规则
                      filterData.groupUuid = _controller.groupUuid.value;
                      filterData.groupName = _controller.groupName.value;
                      //参保方式
                      filterData.insuranceType = _controller.insureTypeIndex.value == -1 ? '' : '${_controller.insureTypeIndex.value + 1}';
                      //头像
                      filterData.picStatus = _controller.headIndex.value == -1 ? '' : '${_controller.headIndex.value + 1}';
                      //身份证照片
                      filterData.isHasIdCard = _controller.idPicIndex.value == -1 ? '' : '${_controller.idPicIndex.value + 1}';
                      //保险
                      filterData.insureStatus = _controller.insureIndex.value == -1 ? '' : '${_controller.insureIndex.value + 1}';
                      //劳动协议
                      filterData.isHasContract = _controller.isHasIndex.value == -1 ? '' : '${_controller.isHasIndex.value + 1}';
                      //信用查询
                      filterData.creditInquiryStatus = _controller.creditInquiryIndex.value == -1 ? '' : '${_controller.creditInquiryIndex.value + 1}';
                      //银行卡
                      filterData.bankStatus = _controller.bankIndex.value == -1 ? '' : '${_controller.bankIndex.value + 1}';
                      //健康证明
                      filterData.healthStatus = _controller.healthIndex.value == -1 ? '' : '${_controller.healthIndex.value + 1}';
                      //无犯罪证明
                      filterData.recodeCheck = _controller.recodeIndex.value == -1 ? '' : '${_controller.recodeIndex.value + 1}';
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                        "method": "roster_filter_rules",
                        "filterData": json.encode(filterData),
                      });
                      BoostNavigator.instance.pop();
                    },
                  ))
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String updateRoleId(int singleSelect) {
    if (singleSelect == -1) {
      return ""; // 返回空字符串
    } else {
      switch (singleSelect) {
        case 0:
          return "1";
        case 1:
          return "3";
        case 2:
          return "6";
        case 3:
          return "2";
        case 4:
          return "4";
        case 5:
          return "5";
        default:
          return ""; // 默认返回空字符串
      }
    }
  }

  String getDateSelect(String date, String customDate) {
    String mSelectDate = "";

    if (date.isNotEmpty) {
      switch (date) {
        case "上周":
          mSelectDate = "lastWeek";
          break;
        case "本周":
          mSelectDate = "thisWeek";
          break;
        case "本月":
          mSelectDate = "thisMonth";
          break;
        case "上月":
          mSelectDate = "lastMonth";
          break;
      }
    } else if (customDate != "自定义日期范围") {
      mSelectDate = customDate;
    }

    return mSelectDate;
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = RosterFilterRulesPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;
}
