import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/credit_inquiry/page/credit_inquiry_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/identity/page/identity_page.dart';

import '../../../net/http_config.dart';

/// 信用查询
const creditInquiryPage = "creditInquiryPage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> creditInquiryRouterMap = {
  creditInquiryPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String uuid = map['uuid'];
          String userName = map['user_name'];
          String idNumber = map['id_number'];
          String type = map['type'];
          return CreditInquiryPage(
            id_number: idNumber,
            userName: userName,
            uuid: uuid,
            type: type,
          );
        });
  },
};
