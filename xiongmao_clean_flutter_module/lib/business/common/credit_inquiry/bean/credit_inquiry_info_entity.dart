import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/credit_inquiry_info_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/credit_inquiry_info_entity.g.dart';

@JsonSerializable()
class CreditInquiryInfoEntity {
	String? status;
	List<CreditInquiryInfoList>? list;

	CreditInquiryInfoEntity();

	factory CreditInquiryInfoEntity.fromJson(Map<String, dynamic> json) => $CreditInquiryInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $CreditInquiryInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CreditInquiryInfoList {
	String? item;
	@JSONField(name: "credit_type")
	int? creditType;
	int? status;
	String? title;
	String? message;
	@JSONField(name: "img_url")
	String? imgUrl;
	@JSONField(name: "search_time")
	String? searchTime;
	String? explain;

	CreditInquiryInfoList();

	factory CreditInquiryInfoList.fromJson(Map<String, dynamic> json) => $CreditInquiryInfoListFromJson(json);

	Map<String, dynamic> toJson() => $CreditInquiryInfoListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}