import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/credit_inquiry_info_entity.dart';
import '../controller/credit_controller.dart';
import '../iview/credit_iview.dart';

/**
 * 信用查询的列表
 */
class CreditPagePresenter extends BasePagePresenter<CreditPageIView> with WidgetsBindingObserver {
  CreditController controller;

  CreditPagePresenter(this.controller);

  //获取列表
  Future<dynamic> requestCreditResult(String id_number, bool isShow) {
    HashMap<String, String> params = HashMap<String, String>();
    params['id_number'] = '$id_number';
    return requestNetwork<CreditInquiryInfoEntity>(Method.get, url: HttpApi.CREDIT_RESULT, queryParameters: params, loadingNotice: '查询中', isShow: isShow, isClose: isShow, onSuccess: (data) {
      if (data != null) {
        controller.updateList(data.list!);
        controller.updateStatus(data.status!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  //查询信用查询
  Future<dynamic> requestSearchUserId(String uuid, String type, bool isShow) {
    HashMap<String, String> params = HashMap<String, String>();
    params['uuid'] = '$uuid';
    params['type'] = '$type';
    return requestNetwork<Object>(Method.get, url: HttpApi.CREDIT_SEARCH_RESULT, queryParameters: params, isShow: isShow, isClose: isShow, onSuccess: (data) {
      if (data != null) {
        view.searchUser();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
