import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../widgets/load_image.dart';
import '../bean/credit_inquiry_info_entity.dart';

class CustomCreditListItem extends StatelessWidget {
  final CreditInquiryInfoList data;

  int position;

  CustomCreditListItem({required this.data, required this.position});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8.0),
          ),
          margin: const EdgeInsets.only(top: 10, left: 10, right: 10),
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Wrap(
                children: [
                  Container(
                    padding: EdgeInsets.only(right: 10),
                    decoration: BoxDecoration(
                      color: Colours.base_primary_trans,
                      borderRadius: BorderRadius.circular(50), // 设置圆角半径为10
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 30,
                          height: 30,
                          decoration: const BoxDecoration(
                            shape: BoxShape.circle,
                            color: Colours.base_primary, // 设置内层Container的背景颜色为蓝色
                          ),
                          child: CircleAvatar(
                            backgroundColor: Colors.transparent,
                            child: CommonUtils.getSimpleText('${(position + 1)}', 16, Colours.white),
                          ),
                        ),
                        Gaps.hGap4,
                        CommonUtils.getSimpleText(data.item, 16, Colours.base_primary),
                      ],
                    ),
                  ),
                ],
              ),
              Gaps.vGap8,
              Gaps.line,
              Gaps.vGap8,
              CommonUtils.getSimpleText(data.searchTime, 14, Colors.grey),
              Gaps.vGap4,
              CommonUtils.getSimpleText(data.title, 16, (1 == data.status) ? Colours.red : Colours.base_primary, fontWeight: FontWeight.bold),
              Gaps.vGap4,
              CommonUtils.getSimpleText((1 == data.status) ? data.message : data.explain, 12, Colours.base_primary_text_title),
            ],
          ),
        ),
        Visibility(
          child: Positioned(
            right: 10,
            top: 10,
            child: LoadImage(
              data.imgUrl ?? "",
              width: 100,
              height: 100,
              fit: BoxFit.scaleDown,
            ),
          ),
          visible: data.status != 0,
        ),
      ],
    );
  }
}
