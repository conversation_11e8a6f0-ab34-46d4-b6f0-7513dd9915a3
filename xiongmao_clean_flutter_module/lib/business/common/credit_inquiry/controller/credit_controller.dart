import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/generated/contract_history_entity.dart';

  import '../bean/credit_inquiry_info_entity.dart';

class CreditController extends GetxController {
  var status = "".obs;

  var statusName = "查询".obs;

  var list = <CreditInquiryInfoList>[].obs;

  void updateList(List<CreditInquiryInfoList> value) {
    list.value = value;
  }

  void updateStatus(String value) {
    status.value = value;
    switch (value) {
      case "0":
        statusName.value = "查询";
        break;
      case "1":
        statusName.value = "查询中";
        break;
      case "2":
        statusName.value = "重新查询";
        break;
    }
  }
}
