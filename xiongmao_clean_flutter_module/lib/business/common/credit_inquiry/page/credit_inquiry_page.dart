import 'dart:async';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';

import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_bottom_button_one.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/loading_util.dart';
import '../controller/credit_controller.dart';
import '../item/custom_credit_list_Item.dart';
import '../iview/credit_iview.dart';
import '../persenter/credit_persenter.dart';

/**
 * 信用查询
 */
class CreditInquiryPage extends StatefulWidget {
  String? id_number = ""; //查看结果
  String? userName = ""; //查看结果
  String? uuid = ""; //查询的时候需要
  String? type = "1"; //类型 1入职查询 2员工查询

  CreditInquiryPage({super.key, required this.id_number, required this.userName, required this.uuid, required this.type});

  @override
  _CreditInquiryPagePageState createState() => _CreditInquiryPagePageState();
}

class _CreditInquiryPagePageState extends State<CreditInquiryPage> with BasePageMixin<CreditInquiryPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<CreditInquiryPage> implements CreditPageIView {
  CreditPagePresenter? _presenter;

  final CreditController _controller = CreditController();

  // 创建一个全局变量来存储定时器
  Timer? _timer;

  void startTimer() {
    // 每3秒执行一次请求
    _timer = Timer.periodic(Duration(seconds: 1), (timer) {
      // 在这里执行您的请求操作
      print("执行定时器来刷新来");
      if (_controller.status.value == '2') {
        stopTimer();
        Loading.dismiss();
        print("我停止了 定时器");
      } else {
        print("我来查询结果怼");
        Loading.show(msg: '查询中');
        _presenter?.requestCreditResult(widget.id_number ?? "", false);
      }
    });
  }

  void stopTimer() {
    // 手动关闭定时器
    if (_timer != null) {
      _timer!.cancel();
      _timer = null;
    }
  }

  @override
  void initState() {
    super.initState();
    _presenter?.requestCreditResult(widget.id_number ?? "", true);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      appBar: MyAppBar(
          centerTitle: '信用查询',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      body: Obx(() => ListView(
            children: [
              Container(
                margin: EdgeInsets.only(left: 10, right: 10, top: 10),
                padding: EdgeInsets.all(10),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.0),
                ),
                child: Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        CommonUtils.getSimpleText(
                          "姓名",
                          14,
                          Colours.base_primary_text_title,
                        ),
                        CommonUtils.getSimpleText(
                          widget.userName,
                          14,
                          Colours.base_primary_text_title,
                          fontWeight: FontWeight.bold,
                        )
                      ],
                    ),
                    Gaps.vGap10,
                    Row(
                      children: [
                        CommonUtils.getSimpleText(
                          "身份证号",
                          14,
                          Colours.base_primary_text_title,
                        ),
                        Expanded(child: CommonUtils.getSimpleText(widget.id_number, 14, Colours.base_primary_text_title, textAlign: TextAlign.right, fontWeight: FontWeight.bold))
                      ],
                    ),
                    Gaps.vGap10,
                    MyBottomButtonOne(
                        title: _controller.statusName.value,
                        isNeedDivideLine: false,
                        horizontalPadding: 0,
                        onPressed: () {
                          if (_controller.statusName.value == '重新查询') {
                            _controller.status.value = '0';
                            _presenter?.requestSearchUserId(widget.uuid ?? "", widget.type ?? "", false);
                          } else {
                            _presenter?.requestSearchUserId(widget.uuid ?? "", widget.type ?? "", true);
                          }
                        }),
                  ],
                ),
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10, top: 10),
                    child: CommonUtils.getSimpleText(
                      "普通查询",
                      16,
                      Colours.base_primary_text_title,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(right: 10, top: 10),
                    child: InkWell(
                      child: CommonUtils.getSimpleText(
                        "刷新",
                        16,
                        Colours.base_primary,
                        fontWeight: FontWeight.bold,
                      ),
                      onTap: () {
                        _presenter?.requestCreditResult(widget.id_number ?? "", true);
                      },
                    ),
                  ),
                ],
              ),
              ListView.builder(
                shrinkWrap: true,
                itemCount: _controller.list.length,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  return Container(
                      child: CustomCreditListItem(
                    data: _controller.list[index],
                    position: index,
                  ));
                },
              ),
            ],
          )),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = CreditPagePresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void searchUser() {
    startTimer();
    // BrnToast.show("该查询结果需要3-5分钟左右，请您稍后再来查看", context);
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    stopTimer();
  }
}
