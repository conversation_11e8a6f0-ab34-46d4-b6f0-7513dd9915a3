import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

class CustomEntryStaffDialog extends StatelessWidget {
  final String title;
  final String content;
  final List<String> items; // 外部传入的列表
  final int coloredItemIndex; // 指定颜色的下标
  final ValueChanged<int> onItemSelected; // 选择项回调

  CustomEntryStaffDialog({
    required this.title,
    required this.content,
    required this.items,
    required this.coloredItemIndex, // 新增参数
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(0.0),
      ),
      child: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(top: 16, bottom: 6),
        width: 300, // 设置对话框宽度
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            CommonUtils.getSimpleText(title, 20, Colours.base_primary_text_title, textAlign: TextAlign.center),
            const SizedBox(height: 12),
            Padding(
              padding: const EdgeInsets.only(
                right: 16,
                left: 16,
              ),
              child: CommonUtils.getSimpleText(content, 14, Colours.base_primary_text_title, textAlign: TextAlign.center),
            ),
            Gaps.vGap20,
            ListView.builder(
              physics: NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemCount: items.length,
              itemBuilder: (context, index) {
                return Column(
                  children: [
                    Gaps.line,
                    InkWell(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(vertical: 12),
                        child: CommonUtils.getSimpleText(
                          items[index],
                          15,
                          index == coloredItemIndex ? Colours.base_primary : Colours.base_primary_text_title, // 指定颜色
                          textAlign: TextAlign.center,
                        ),
                      ),
                      onTap: () {
                        Navigator.of(context).pop(index); // 关闭对话框并返回下标
                        onItemSelected(index); // 调用回调
                      },
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
