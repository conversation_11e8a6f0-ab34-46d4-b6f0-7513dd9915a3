import 'package:bruno/bruno.dart';

import '../bean/city_data_entity.dart';

class Brn3CityRowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;
  final List<CityDataList> cityData;

  Brn3CityRowDelegate({this.firstSelectedIndex = 0, this.secondSelectedIndex = 0, this.thirdSelectedIndex = 0, required this.cityData});

  @override
  int numberOfComponent() {
    return 3;
  }

  @override
  int numberOfRowsInComponent(int component) {
    if (component == 0) {
      return cityData.length;
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?.length ?? 0;
    } else if (component == 2) {
      return cityData[firstSelectedIndex].list?[secondSelectedIndex].list?.length ?? 0;
    }
    return 0;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    if (component == 0) {
      return cityData[index].name ?? '';
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?[index].name ?? '';
    } else if (component == 2) {
      return cityData[firstSelectedIndex].list?[secondSelectedIndex].list?[index].name ?? '';
    }
    return '';
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    if (component == 0) {
      firstSelectedIndex = row;
    } else if (component == 1) {
      secondSelectedIndex = row;
    } else if (component == 2) {
      thirdSelectedIndex = row;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (component == 0) {
      return firstSelectedIndex;
    } else if (component == 1) {
      return secondSelectedIndex;
    } else if (component == 2) {
      return thirdSelectedIndex;
    }
    return 0;
  }

  @override
  Object getSelectedItemForComponent(int component) {
    if (component == 0) {
      return cityData[firstSelectedIndex];
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?[secondSelectedIndex] ?? CityDataList();
    } else if (component == 2) {
      return cityData[firstSelectedIndex].list?[secondSelectedIndex].list?[thirdSelectedIndex] ?? CityDataListListList();
    }
    return CityDataList();
  }
}