import 'package:bruno/bruno.dart';
import '../bean/city_data_entity.dart';

class Brn2CityRowDelegate implements BrnMultiDataPickerDelegate {
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  final List<CityDataList> cityData;

  Brn2CityRowDelegate({this.firstSelectedIndex = 0, this.secondSelectedIndex = 0, required this.cityData});

  @override
  int numberOfComponent() {
    return 2;
  }

  @override
  int numberOfRowsInComponent(int component) {
    if (component == 0) {
      return cityData.length;
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?.length ?? 0;
    }
    return 0;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    if (component == 0) {
      return cityData[index].name ?? '';
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?[index].name ?? '';
    }
    return '';
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    if (component == 0) {
      firstSelectedIndex = row;
    } else if (component == 1) {
      secondSelectedIndex = row;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    if (component == 0) {
      return firstSelectedIndex;
    } else if (component == 1) {
      return secondSelectedIndex;
    }
    return 0;
  }

  @override
  Object getSelectedItemForComponent(int component) {
    if (component == 0) {
      return cityData[firstSelectedIndex];
    } else if (component == 1) {
      return cityData[firstSelectedIndex].list?[secondSelectedIndex] ?? CityDataList();
    }
    return CityDataList();
  }
}