import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import '../../../../res/colors.dart';
import '../../../../widgets/custom_avatar_view.dart';
import '../../../../widgets/load_image.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';

class StaffResignMainListItem extends StatelessWidget {
  ProjectArchivesList data;

  String? type;

  final Function onClick;

  StaffResignMainListItem({required this.data, required this.onClick});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: Column(
        children: [
          Gaps.line,
          Container(
            color: Colors.white,
            padding: const EdgeInsets.only(
              top: 10,
              left: 16,
              right: 16,
              bottom: 10,
            ), // 设置内边距为16.0
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                CustomAvatarView(
                  name: data.userName,
                  avatarUrl: data.avatar,
                ),
                Gaps.hGap10,
                Expanded(
                    child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    CommonUtils.getSimpleText(data.userName, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    CommonUtils.getSimpleText(buildStringFromItem(data), 13, Colours.erji),
                  ],
                )),
                Gaps.hGap10,
              ],
            ),
          ),
          // Gaps.line
        ],
      ),
      onTap: () {
        onClick();
      },
    );
  }

  String buildStringFromItem(ProjectArchivesList item) {
    StringBuffer stringBuffer = StringBuffer();

    // if (item. != null && item.sexName.isNotEmpty) {
    //   stringBuffer.write(item.sexName);
    // }

    if (!TextUtil.isEmpty(item.jobName)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write(item.jobName);
    }

    if (!TextUtil.isEmpty(item.age)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.age}岁");
    }

    if (!TextUtil.isEmpty(item.workAge)) {
      if (stringBuffer.isNotEmpty) {
        stringBuffer.write(" / ");
      }
      stringBuffer.write("${item.workAge}年");
    }

    return stringBuffer.toString();
  }
}
