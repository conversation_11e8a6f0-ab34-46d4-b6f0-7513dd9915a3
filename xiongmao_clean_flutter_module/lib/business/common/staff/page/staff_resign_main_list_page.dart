import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/custom_search_view.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/staff_resign_main_list_controller.dart';
import '../item/staff_resign_main_listview.dart';
import '../item/staff_resign_main_row_listview.dart';
import '../iview/staff_resign_main_list_iview.dart';
import '../persenter/staff_resign_main_list_persenter.dart';

/// 选择离职员工去离职
class StaffResignMainListPage extends StatefulWidget {
  StaffResignMainListPage({super.key});

  @override
  _StaffResignMainListPageState createState() => _StaffResignMainListPageState();
}

class _StaffResignMainListPageState extends State<StaffResignMainListPage> with BasePageMixin<StaffResignMainListPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<StaffResignMainListPage> implements StaffResignManListIView {
  StaffResignMainListPresenter? _presenter;

  final StaffResignMainListController _controller = StaffResignMainListController();

  @override
  void initState() {
    super.initState();
    _presenter?.getProjectRowListManager();
  }

  Future<dynamic> _onRefresh() async {
    _presenter?.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter?.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '选择要离职的人员',
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Obx(() => Column(
            children: [
              Expanded(
                  child: Row(
                children: [
                  Expanded(
                    child: Container(
                      color: Colours.base_primary_bg_page,
                      height: double.infinity,
                      child: Column(
                        children: [
                          // 自定义框
                          InkWell(
                            child: Container(
                              padding: const EdgeInsets.only(left: 10, right: 10, top: 6, bottom: 6),
                              margin: const EdgeInsets.only(left: 4, right: 4, top: 10, bottom: 10),
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.circular(4),
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.grey.withOpacity(0.0),
                                    spreadRadius: 2,
                                    blurRadius: 5,
                                    offset: const Offset(0, 2),
                                  ),
                                ],
                              ),
                              width: double.infinity,
                              child: Row(
                                children: [
                                  Expanded(child: CommonUtils.getSimpleText(_controller.searchProjectName.value, 14, (_controller.searchProjectName.value == '搜项目') ? Colours.base_primary_text_caption : Colours.base_primary_text_title)),
                                  Visibility(
                                    visible: (_controller.searchProjectName.value != '搜项目'),
                                    child: const LoadAssetImage(
                                      'base/icon_base_close',
                                      width: 16,
                                      height: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            onTap: () {
                              //弹窗来提示问题
                              if (_controller.searchProjectName.value != '搜项目') {
                                _controller.searchProjectName.value = '搜项目';
                                _controller.filterProject();
                                _onRefresh();
                              } else {
                                _showCustomDialog(context, _controller);
                              }
                            },
                          ),
                          // ListView
                          Expanded(
                            child: ListView.builder(
                              shrinkWrap: true,
                              itemCount: _controller.listRow.length,
                              padding: const EdgeInsets.only(bottom: 20),
                              itemBuilder: (context, index) {
                                return StaffResignMainRowListItem(
                                  data: _controller.listRow[index],
                                  uuid: _controller.projectUuid.value,
                                  onClick: () {
                                    _controller.projectUuid.value = _controller.listRow[index].uuid ?? '';
                                    _onRefresh();
                                  },
                                );
                              },
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  Expanded(
                    flex: 2,
                    child: Column(
                      children: [
                        Container(
                          color: Colors.white,
                          padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                          child: CustomSearchView(
                            hint: '输入内容查找人员',
                            onTextChanged: (text) {
                              _controller.keyword.value = text;
                              _onRefresh();
                            },
                          ),
                        ),
                        Expanded(
                            child: Container(
                          color: Colours.white,
                          child: MyRefreshListView(
                            itemCount: _controller.list.length,
                            onRefresh: _onRefresh,
                            loadMore: _loadMore,
                            padding: const EdgeInsets.all(10.0),
                            hasMore: _controller.totalNumber.value > _controller.list.length,
                            //没分页，所以把禁止滑动
                            itemBuilder: (_, index) {
                              return StaffResignMainListItem(
                                data: _controller.list[index],
                                onClick: () {
                                  ///去离职
                                  BoostNavigator.instance.push('quickDeparkPage', arguments: {'uuid': _controller.list[index].uuid}).then((value) => _onRefresh());
                                },
                              );
                            },
                          ),
                        )),
                      ],
                    ),
                  ),
                ],
              )),
            ],
          )),
    );
  }

  void _showCustomDialog(BuildContext context, StaffResignMainListController controller) {
    final TextEditingController _controller = TextEditingController();

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Padding(
          padding: MediaQuery.of(context).viewInsets,
          child: Container(
            color: Colors.white,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 10),
                  decoration: BoxDecoration(
                    color: Colors.grey[200],
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: TextField(
                    controller: _controller,
                    decoration: const InputDecoration(
                      hintText: '请输入关键字',
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.all(10.0),
                    ),
                    autofocus: true, // 自动获得焦点
                  ),
                ),
                Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          _controller.clear(); // 清空输入框
                          controller.searchProjectName.value = '搜项目';
                          controller.filterProject();
                          _onRefresh();
                          Navigator.of(context).pop(); // 关闭对话框
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colors.red[100],
                            borderRadius: BorderRadius.circular(0.0),
                          ),
                          padding: const EdgeInsets.all(10.0),
                          child: CommonUtils.getSimpleText('清除条件', 16, Colours.red, textAlign: TextAlign.center),
                        ),
                      ),
                    ),
                    Expanded(
                      child: GestureDetector(
                        onTap: () {
                          // 处理搜索逻辑
                          String inputText = _controller.text;
                          print("搜索: $inputText");
                          controller.searchProjectName.value = inputText;
                          controller.filterProject();
                          _onRefresh();
                          Navigator.of(context).pop(); // 关闭对话框
                        },
                        child: Container(
                          decoration: BoxDecoration(
                            color: Colours.base_primary,
                            borderRadius: BorderRadius.circular(0.0),
                          ),
                          padding: const EdgeInsets.all(10.0),
                          child: CommonUtils.getSimpleText('搜索', 16, Colours.white, textAlign: TextAlign.center),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    ).whenComplete(() {
      // 在关闭对话框后，确保输入框失去焦点
      FocusScope.of(context).unfocus();
    });
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = StaffResignMainListPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;
}
