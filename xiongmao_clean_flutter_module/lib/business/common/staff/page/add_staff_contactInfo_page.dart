import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_contact_Info_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_contact_Info_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_contact_Info_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';

/// 添加联系人信息
class AddStaffContactInfoPage extends StatefulWidget {
  String? uuid = "";
  String? applicationNo = "";
  int channel;
  EntryConditionEntity? entryCondition;

  final Function(String) onNext;
  final Function onPrevious;

  AddStaffContactInfoPage({
    Key? key,
    this.uuid = "",
    this.applicationNo = "",
    required this.entryCondition,
    required this.channel,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  _AddStaffContactInfoPageState createState() => _AddStaffContactInfoPageState();
}

class _AddStaffContactInfoPageState extends State<AddStaffContactInfoPage> with BasePageMixin<AddStaffContactInfoPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffContactInfoIView {
  AddStaffContactInfoPresenter? _presenter;
  final AddStaffContactInfoController _controller = AddStaffContactInfoController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';

    ///拿到关系的数据
    String? jsonString = SpUtil.getString(Constant.META_DATA);
    print('从本地拿的数据：$jsonString');
    if (!TextUtil.isEmpty(jsonString)) {
      Map<String, dynamic> jsonMap = json.decode(jsonString!);
      GetMetaEntity metaData = GetMetaEntity.fromJson(jsonMap);
      // 现在可以使用 metaData 对象
      if (metaData.exigencyUserRelationList != null && metaData.exigencyUserRelationList!.isNotEmpty) {
        _controller.relationList.value = metaData.exigencyUserRelationList!;
        for (GetMetaExigencyUserRelationList item in metaData.exigencyUserRelationList!) {
          _controller.contactList.add(BaseChooseString(item.name));
        }
      } else {
        _presenter?.getMetaData();
      }
    } else {
      _presenter?.getMetaData();
    }

    ///在这里判断要去获取档案详情还是草稿详情 默认是获取草稿详情
    var isDraft = true;
    if (widget.entryCondition != null && !TextUtil.isEmpty(widget.entryCondition!.uuid)) {
      print('来请求联系人了 entryCondition');
      if (widget.entryCondition!.beforeEntry) {
        ///档案详情
        isDraft = false;
      } else {
        ///草稿详情
        isDraft = true;
      }

      ///请求详情
      _presenter?.getStaffOne(true, isDraft, widget.entryCondition!.uuid);
    } else {
      print('来请求联系人了 ');

      isDraft = widget.channel == 3 ? false : true;

      ///请求详情
      _presenter?.getStaffOne(false, isDraft, _controller.uuid.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Obx(() => Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 10),
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        child: CommonUtils.getSimpleText('本人', 16, Colours.base_primary_text_title),
                      ),
                      BrnTextInputFormItem(
                        isRequire: true,
                        title: "手机号",
                        hint: "请输入",
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(11), // 限制输入长度为20个字符
                        ],
                        inputType: BrnInputType.phone,
                        controller: _controller.phoneController,
                      ),
                    ],
                  ),
                ),
                Gaps.vGap10,
                Container(
                  width: double.infinity,
                  color: Colors.white,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                        child: CommonUtils.getSimpleText('紧急联系人', 16, Colours.base_primary_text_title),
                      ),
                      BrnTextInputFormItem(
                        title: "姓名",
                        hint: "请输入",
                        controller: _controller.contactNameController,
                      ),
                      Gaps.line,
                      BrnTextInputFormItem(
                        title: "手机/电话",
                        hint: "请输入",
                        inputFormatters: [
                          LengthLimitingTextInputFormatter(11), // 限制输入长度为20个字符
                        ],
                        inputType: BrnInputType.phone,
                        controller: _controller.contactPhoneController,
                      ),
                      Gaps.line,
                      Padding(
                        padding: const EdgeInsets.only(left: 18, right: 16, bottom: 10, top: 10),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Expanded(
                              child: Padding(
                                padding: const EdgeInsets.only(
                                  top: 10,
                                ),
                                child: CommonUtils.getSimpleText('关系', 15, Colours.base_primary_text_title),
                              ),
                            ),
                            Expanded(
                                flex: 3,
                                child: SelectTabWidget(
                                  key: ValueKey(_controller.contactIndex.value),
                                  _controller.contactList.value,
                                  multiSelect: false,
                                  crossAxisCount: 4,
                                  hideMore: false,
                                  paddingBottom: 0,
                                  paddingTop: 0,
                                  tabFontSize: 15,
                                  defaultSelectedIndex: _controller.contactIndex.value == -1 ? [] : [_controller.contactIndex.value],
                                  lastIsAddOne: false,
                                  selectedColor: Colours.base_primary,
                                  bgSelectedColor: Colours.base_primary_select,
                                  bgUnSelectedColor: Colours.base_primary_un_select,
                                  childAspectRatio: 6 / 4,
                                  itemClickCallback: (List<int> indexs) {
                                    LogUtil.e("indexs = $indexs");
                                    _controller.contactIndex.value = (indexs[0]).toInt();
                                  },
                                )),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            )),
      ),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        child: Column(
          children: [
            Gaps.line,
            Row(
              children: [
                Visibility(
                    visible: (widget.channel != 3 && widget.channel != 4),
                    child: Expanded(
                        child: InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.only(
                          top: 8,
                          left: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colours.base_primary_un_select,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CommonUtils.getSimpleText('上一步', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        widget.onPrevious();
                      },
                    ))),
                Gaps.hGap10,
                Expanded(
                    flex: 2,
                    child: InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.only(
                          top: 8,
                          right: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colours.base_primary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CommonUtils.getSimpleText((widget.channel == 3 || widget.channel == 4) ? '提交' : '下一步', 16, Colours.white, fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        if (TextUtil.isEmpty(_controller.phoneController.text)) {
                          Toast.show('请输入手机号');
                          return;
                        }
                        HashMap<String, String> hashMap = HashMap();
                        hashMap['op_type'] = 'contact';
                        hashMap['uuid'] = _controller.uuid.value;
                        hashMap['mobile'] = _controller.phoneController.text;
                        if (!TextUtil.isEmpty(_controller.contactNameController.text)) {
                          hashMap['exigency_user_name'] = _controller.contactNameController.text;
                        }
                        if (!TextUtil.isEmpty(_controller.contactPhoneController.text)) {
                          hashMap['exigency_user_mobile'] = _controller.contactPhoneController.text;
                        }
                        if (!TextUtil.isEmpty(_controller.relationId)) {
                          hashMap['exigency_user_relation'] = _controller.relationId;
                        }
                        print('${widget.channel} 什么东西');
                        _presenter?.updateStaffInfo(widget.channel == 3 ? false : true, hashMap);
                      },
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }

  @override
  bool get wantKeepAlive => true; // 保持活跃

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffContactInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onUpdate(CreateStaffEntity data) {
    widget.onNext(data.uuid ?? '');
  }

  @override
  void onGetMeta(GetMetaEntity data) {
    if (data.exigencyUserRelationList != null && data.exigencyUserRelationList!.isNotEmpty) {
      _controller.relationList.value = data.exigencyUserRelationList!;
      for (GetMetaExigencyUserRelationList item in data.exigencyUserRelationList!) {
        _controller.contactList.add(BaseChooseString(item.name));
      }
    }
  }
}
