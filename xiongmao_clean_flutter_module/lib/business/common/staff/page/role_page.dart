import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/controller/permission_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/dimens.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/role_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/permiss_iview.dart';
import '../iview/role_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/permission_persenter.dart';
import '../persenter/role_persenter.dart';

/// 全局选择角色的地方， 支持多选、单选 主要用于配置界面
class RolePage extends StatefulWidget {
  ///判断是多选 还是 单选
  bool multiple;

  ///别的洁面拿到的数据来做反选
  List<String>? choice_uuids;

  RolePage({Key? key, required this.multiple, this.choice_uuids});

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<RolePage> with BasePageMixin<RolePage, PowerPresenter<dynamic>> implements RoleIView {
  final RoleController _controller = RoleController();
  List<String> list = [];

  late RolePresenter _presenter;

  @override
  void initState() {
    super.initState();

    ///反选的uuid
    _controller.invertUuids.value = widget.choice_uuids ?? [];

    _presenter.requestPermission();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: "选择系统操作权限",
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SafeArea(
        child: Obx(() {
          return ListView.builder(
              padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 12),
              shrinkWrap: true,
              itemCount: _controller.list.value.length,
              itemBuilder: (context, index) {
                var value = _controller.list.value[index];
                return InkWell(
                  child: Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: BrnShadowCard(
                      blurRadius: 0,
                      color: Colours.white,
                      circular: 10,
                      child: Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Gaps.vGap6,
                                CommonUtils.getSimpleText(value.name, 14, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                                Gaps.vGap6,
                                CommonUtils.getSimpleText(value.role_desc, 14, Colours.base_primary_text_title),
                                Gaps.vGap6,
                              ],
                            )),
                            Visibility(
                              visible: value.isSelected,
                              child: const LoadAssetImage(
                                "icon_base_selected",
                                width: 30,
                                height: 30,
                              ),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  onTap: () {
                    if (widget.multiple) {
                      value.isSelected = !value.isSelected;
                      _controller.list.refresh();
                      return;
                    }
                    BoostNavigator.instance.pop(value);
                  },
                );
              });
        }),
      ),
      bottomNavigationBar: Visibility(
        visible: widget.multiple,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
            title: '确定',
            onTap: () {
              BoostNavigator.instance.pop(_controller.getSelectedItems());
            },
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = RolePresenter();
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }

  @override
  void requestPermissionData(PermissionEntity data) {
    _controller.updatePermissionData(data.list ?? []);
  }
}
