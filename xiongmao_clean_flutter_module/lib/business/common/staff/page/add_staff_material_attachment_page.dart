import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_image_grid_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/permission_util.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../controller/add_staff_material_Info_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_material_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../persenter/add_staff_material_Info_persenter.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';

/// 材料附件
class AddStaffMaterialAttachmentPage extends StatefulWidget {
  String? uuid = "";
  String? applicationNo = "";
  int channel;
  EntryConditionEntity? entryCondition;

  final Function(String) onNext;
  final Function onPrevious;

  AddStaffMaterialAttachmentPage({
    Key? key,
    this.uuid = "",
    this.applicationNo = "",
    required this.entryCondition,
    required this.channel,
    required this.onNext,
    required this.onPrevious,
  });

  @override
  _AddStaffMaterialAttachmentPageState createState() => _AddStaffMaterialAttachmentPageState();
}

class _AddStaffMaterialAttachmentPageState extends State<AddStaffMaterialAttachmentPage> with BasePageMixin<AddStaffMaterialAttachmentPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffMaterialAttachmentIView {
  AddStaffMaterialAttachmentInfoPresenter? _presenter;
  final AddStaffMaterialAttachmentInfoController _controller = AddStaffMaterialAttachmentInfoController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';

    ///拿到关系的数据
    String? jsonString = SpUtil.getString(Constant.META_DATA);
    if (!TextUtil.isEmpty(jsonString)) {
      Map<String, dynamic> jsonMap = json.decode(jsonString!);
      GetMetaEntity metaData = GetMetaEntity.fromJson(jsonMap);
      // 现在可以使用 metaData 对象
      if (metaData.bankList != null && metaData.bankList!.isNotEmpty) {
        _controller.bankOriginalList.value = metaData.bankList!;
        _controller.bankList.value = metaData.bankList!
            .map((e) => e.name)
            .where((name) => name != null) // 过滤掉 null 值
            .cast<String>() // 转换为 List<String>
            .toList();
      }
    }

    ///请求详情
    var isDraft = true;
    if (widget.entryCondition != null && !TextUtil.isEmpty(widget.entryCondition!.uuid)) {
      if (widget.entryCondition!.beforeEntry) {
        ///档案详情 延用以前的详情
        isDraft = false;
      } else {
        ///草稿详情
        isDraft = true;
      }

      ///请求详情
      _presenter?.getStaffOne(true, isDraft, widget.entryCondition!.uuid);
    } else {
      isDraft = widget.channel == 3 ? false : true;

      ///请求详情
      _presenter?.getStaffOne(false, isDraft, _controller.uuid.value);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.white,
      body: Obx(() => SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  width: double.infinity,
                  height: 10,
                  color: Colours.base_primary_bg_page,
                ),
                Gaps.vGap10,
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                  ),
                  child: CommonUtils.getSimpleText('银行卡照片', 16, Colours.base_primary_text_title),
                ),
                Container(
                  margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                  color: Colours.base_primary_bg_page,
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            CommonUtils.getSimpleText('点击拍摄/上传卡号面', 12, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                            CommonUtils.getSimpleText('将为您自动填写卡号信息', 10, Colours.base_primary_text_title),
                          ],
                        ),
                      ),
                      Expanded(
                          child: InkWell(
                        child: LoadImage(
                          (TextUtil.isEmpty(_controller.bankPic.value)) ? 'common/icon_bank_ocr' : _controller.bankPic.value,
                          height: 100,
                        ),
                        onTap: () {
                          openPhoto();
                        },
                      )),
                    ],
                  ),
                ),
                BrnTextInputFormItem(
                  title: "银行卡号",
                  hint: "请输入",
                  inputFormatters: [
                    LengthLimitingTextInputFormatter(20), // 限制输入长度为20个字符
                  ],
                  inputType: BrnInputType.number,
                  controller: _controller.bankNoController,
                ),
                Gaps.line,
                BrnTextSelectFormItem(
                  title: "开户行",
                  value: _controller.bankName.value,
                  onTap: () {
                    SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择开户行', _controller.bankList.value, _controller.bankIndex.value, (index, selectedText) {
                      print('当前选择的下标：$index');
                      print('当前选择的文本内容：$selectedText');
                      _controller.bankName.value = selectedText;
                      _controller.bankIndex.value = index;
                      _controller.bankCode.value = _controller.bankOriginalList.value[_controller.bankIndex.value].code ?? '';
                    });
                  },
                ),
                Gaps.line,
                BrnTextInputFormItem(
                  title: "支行名称",
                  hint: "请输入",
                  controller: _controller.banNameController,
                ),
                Container(
                  width: double.infinity,
                  height: 10,
                  color: Colours.base_primary_bg_page,
                ),
                Gaps.vGap16,
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText('头像', 16, Colours.base_primary_text_title),
                      CommonUtils.getSimpleText('仅限一张，用于花名册展示', 16, Colours.base_primary_text_title),
                    ],
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(left: 10, right: 10),
                  child: CustomImageGridView(
                    maxImageCount: 1,
                    imageUrls: _controller.headImages.value,
                    showDelButton: true,
                    includeEdge: true,
                    onImageUrlsChanged: (images) {
                      _controller.headImages.value = images;
                      requestUpdate();
                    },
                  ),
                ),
                Container(
                  width: double.infinity,
                  height: 10,
                  color: Colours.base_primary_bg_page,
                ),
                Gaps.vGap16,
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText('入职登记表', 16, Colours.base_primary_text_title),
                      CommonUtils.getSimpleText('可上传多张', 16, Colours.base_primary_text_title),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: CustomImageGridView(
                    maxImageCount: 20,
                    imageUrls: _controller.entryImages.value,
                    showAddButton: true,
                    showDelButton: true,
                    includeEdge: true,
                    onImageUrlsChanged: (images) {
                      _controller.entryImages.value = images;
                      requestUpdate();
                    },
                  ),
                ),
                Container(
                  width: double.infinity,
                  height: 10,
                  color: Colours.base_primary_bg_page,
                ),
                Gaps.vGap16,
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText('健康证明', 16, Colours.base_primary_text_title),
                      CommonUtils.getSimpleText('可上传多张', 16, Colours.base_primary_text_title),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: CustomImageGridView(
                    maxImageCount: 20,
                    imageUrls: _controller.healthImages.value,
                    showAddButton: true,
                    showDelButton: true,
                    includeEdge: true,
                    onImageUrlsChanged: (images) {
                      _controller.healthImages.value = images;
                      requestUpdate();
                    },
                  ),
                ),
                Container(
                  width: double.infinity,
                  height: 10,
                  color: Colours.base_primary_bg_page,
                ),
                Gaps.vGap16,
                Padding(
                  padding: const EdgeInsets.only(left: 20, right: 20),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      CommonUtils.getSimpleText('无犯罪证明', 16, Colours.base_primary_text_title),
                      CommonUtils.getSimpleText('可上传多张', 16, Colours.base_primary_text_title),
                    ],
                  ),
                ),
                Padding(
                  padding: EdgeInsets.only(left: 10, right: 10),
                  child: CustomImageGridView(
                    maxImageCount: 20,
                    imageUrls: _controller.noCrimeImages.value,
                    showAddButton: true,
                    showDelButton: true,
                    includeEdge: true,
                    onImageUrlsChanged: (images) {
                      _controller.noCrimeImages.value = images;
                      requestUpdate();
                    },
                  ),
                ),
                Container(
                  width: double.infinity,
                  height: 50,
                  color: Colours.base_primary_bg_page,
                ),
              ],
            ),
          )),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        child: Column(
          children: [
            Gaps.line,
            Row(
              children: [
                Visibility(
                  visible: (widget.channel != 3 && widget.channel != 4),
                  child: Expanded(
                      child: InkWell(
                    child: Container(
                      alignment: Alignment.center,
                      width: double.infinity,
                      height: 44,
                      margin: const EdgeInsets.only(
                        top: 8,
                        left: 10,
                      ),
                      decoration: BoxDecoration(
                        color: Colours.base_primary_un_select,
                        borderRadius: BorderRadius.circular(4),
                      ),
                      child: CommonUtils.getSimpleText('上一步', 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                    ),
                    onTap: () {
                      widget.onPrevious();
                    },
                  )),
                ),
                Gaps.hGap10,
                Expanded(
                    flex: 2,
                    child: InkWell(
                      child: Container(
                        alignment: Alignment.center,
                        width: double.infinity,
                        height: 44,
                        margin: const EdgeInsets.only(
                          top: 8,
                          right: 10,
                        ),
                        decoration: BoxDecoration(
                          color: Colours.base_primary,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: CommonUtils.getSimpleText((widget.channel == 3 || widget.channel == 4) ? '提交' : '下一步', 16, Colours.white, fontWeight: FontWeight.bold),
                      ),
                      onTap: () {
                        _controller.commit.value = true;
                        requestUpdate();
                      },
                    )),
              ],
            )
          ],
        ),
      ),
    );
  }

  void openPhoto() {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: _controller.actions,
            clickCallBack: (
              int index,
              BrnCommonActionSheetItem actionEle,
            ) async {
              Permission permission;
              String tipMsg;

              if (index == 0) {
                permission = Permission.camera;
                tipMsg = '拍摄照片需要用到相机权限';
              } else {
                permission = await CommonUtils.getRequiredPhotosPermission();
                tipMsg = '选择照片需要用到相册权限';
              }
              permissionUtil.requestPermission(permission, tipMsg: tipMsg, requestSuccessFun: () {
                selectImage((index == 0) ? 1 : 0);
              });
            },
          );
        });
  }

  //0是拍照，1是相册选
  Future<void> selectImage(int imageSource) async {
    Media? photo;
    if (imageSource == 0) {
      List<Media> pickerPaths = await ImagePickers.pickerPaths();
      if (pickerPaths.isNotEmpty) {
        photo = pickerPaths[0];
      }
    } else {
      photo = await ImagePickers.openCamera();
    }
    if (photo == null) {
      return;
    }
    print('拿到图片了' + photo.path.toString());
    //拿到图进行上传的操作
    BrnToast.show("识别中，请稍等", context);
    QiNiuUtils(photo.path, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, successCallback: (keyUrl, hashUrl) {
      print("七牛上传成功--$keyUrl");
      _controller.bankPic.value = keyUrl!;
      _presenter?.ocrBankCard();
    }, errorCallback: (error) {
      print("七牛上传失败--$error");
      BrnToast.show("上传失败", context);
    }).upload();
  }

  @override
  bool get wantKeepAlive => true; // 保持活跃

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffMaterialAttachmentInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onUpdate(CreateStaffEntity data) {
    if (_controller.commit.value) {
      widget.onNext(data.uuid ?? '');
    }
  }

  void requestUpdate() {
    HashMap<String, String> hashMap = HashMap();
    hashMap["op_type"] = "media";
    hashMap["uuid"] = _controller.uuid.value ?? "";
    if (_controller.headImages.isNotEmpty) {
      hashMap["avatar"] = _controller.headImages[0].media_url ?? '';
    }

    if (_controller.healthImages.isNotEmpty) {
      hashMap["healthy_pic"] = json.encode(_controller.healthImages.value.map((e) => e.media_url).toList());
    }

    if (_controller.noCrimeImages.isNotEmpty) {
      hashMap["no_crime_pic"] = json.encode(_controller.noCrimeImages.value.map((e) => e.media_url).toList());
    }

    if (_controller.entryImages.isNotEmpty) {
      hashMap["entry_registration_pic"] = json.encode(_controller.entryImages.value.map((e) => e.media_url).toList());
    }

    if (!TextUtil.isEmpty(_controller.bankPic.value)) {
      hashMap["bank_card_front"] = _controller.bankPic.value;
    }

    hashMap["bank_no"] = _controller.bankNoController.text;
    hashMap["bank_code"] = _controller.bankCode.value;
    hashMap["bank"] = _controller.bankName.value;
    hashMap["bank_name"] = _controller.banNameController.text;

    _presenter?.updateStaffInfo(widget.channel == 3 ? false : true, hashMap);
  }
}
