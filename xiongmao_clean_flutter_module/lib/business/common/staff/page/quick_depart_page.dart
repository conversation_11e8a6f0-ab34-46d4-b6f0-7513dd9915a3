import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/base_media_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../../contract/bean/contract_main_electronic_entity.dart';
import '../../widget/base_page_title.dart';
import '../bean/ele_status_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/quick_depart_page_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/quick_depart_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/quick_depart_page_persenter.dart';

/// 离职申请
class QuickDeparkPage extends StatefulWidget {
  String uuid = "";
  String application_no = "";

  QuickDeparkPage({Key? key, required this.uuid, required this.application_no});

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<QuickDeparkPage> with BasePageMixin<QuickDeparkPage, PowerPresenter<dynamic>> implements QuickDepartPageIView {
  QuickDepartPagePresenter? _presenter;
  final QuickDepartPageController _controller = QuickDepartPageController();
  final _noticeController = TextEditingController();
  final _nameController = TextEditingController();
  final _projectController = TextEditingController();
  final _positionController = TextEditingController();
  final _startDateController = TextEditingController();
  var types = ["自愿离职", "处罚离职", "病休离职", "事假离职"];
  final _remarkController = TextEditingController();

  VoidCallback? addListener;

  @override
  void initState() {
    super.initState();
    //监听回掉
    addListener ??= BoostChannel.instance.addEventListener("refresh_contract_records", (key, arguments) async {
      BoostNavigator.instance.pop();
    });

    DateTime now = DateTime.now();
    _controller.departDate.value = '${now.year}-${'${CommonUtils.formatToTwoDigits(now.month)}-${CommonUtils.formatToTwoDigits(now.day)}'}';
    _noticeController.text = "可上传多张";

    HashMap<String, String> hashMap = HashMap();
    hashMap['is_work_info'] = "1";
    hashMap['is_identity'] = "1";
    if (!TextUtil.isEmpty(widget.uuid)) {
      hashMap['uuid'] = widget.uuid;
      _presenter?.requestStaffDetail(hashMap); //员工详情
    }
    if (!TextUtil.isEmpty(widget.application_no)) {
      hashMap['application_no'] = widget.application_no;
      _presenter?.requestDepartDetail(hashMap); //审批详情
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: "离职申请",
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              BasePageTitle(
                icon: 'icon_depart_info',
                label: '人员信息',
                padding: const EdgeInsets.only(
                  top: 10,
                  left: 16,
                ),
              ),
              BrnTextInputFormItem(
                title: "姓名",
                isEdit: false,
                controller: _nameController,
              ),
              Gaps.line,
              BrnTextInputFormItem(
                title: "项目",
                isEdit: false,
                controller: _projectController,
              ),
              Gaps.line,
              BrnTextInputFormItem(
                title: "岗位/职务描述",
                isEdit: false,
                controller: _positionController,
              ),
              Gaps.line,
              BrnTextInputFormItem(
                title: "用工开始日期",
                isEdit: false,
                controller: _startDateController,
              ),
              Gaps.vBlock12,
              BasePageTitle(
                icon: 'icon_depart_user',
                label: '离职信息',
                padding: const EdgeInsets.only(
                  top: 10,
                  left: 16,
                ),
              ),
              Obx(() => BrnTextSelectFormItem(
                    isRequire: true,
                    title: "离职日期",
                    hint: "请选择",
                    value: _controller.departDate.value,
                    onTap: () {
                      BrnDatePicker.showDatePicker(
                        themeData: BrnPickerConfig(
                          pickerHeight: 300,
                        ),
                        context,
                        pickerTitleConfig: BrnPickerTitleConfig.Default,
                        pickerMode: BrnDateTimePickerMode.datetime,
                        dateFormat: 'yyyy年,MMMM月,dd日',
                        initialDateTime: DateUtil.getDateTime(_controller.departDate.value),
                        onConfirm: (dateTime, list) {
                          if (dateTime != null) {
                            final formattedDate = DateUtil.formatDate(dateTime, format: 'yyyy-MM-dd');
                            _controller.updateDepartDate(formattedDate);
                          }
                        },
                      );
                    },
                  )),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    isRequire: true,
                    title: "离职类型",
                    hint: "请选择",
                    value: _controller.departType.value,
                    onTap: () {
                      BrnBottomPicker.show(context,
                          title: "请选择离职类型",
                          showTitle: false,
                          contentWidget: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Container(
                                alignment: Alignment.center,
                                height: 56,
                                child: CommonUtils.getSimpleText("请选择离职类型", 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),
                              ),
                              Gaps.line,
                              ListView.builder(
                                  padding: EdgeInsets.zero,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemCount: 4,
                                  itemBuilder: (context, index) {
                                    return InkWell(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.center,
                                        children: [
                                          Container(
                                            alignment: Alignment.center,
                                            height: 50,
                                            child: CommonUtils.getSimpleText(types[index], 16, _controller.departType.value.contains(types[index]) ? Colours.base_primary : Colours.base_primary_text_title),
                                          ),
                                          Gaps.line
                                        ],
                                      ),
                                      onTap: () {
                                        _controller.updateDepartType(types[index]);
                                        _controller.reason.value = index + 1;
                                        Navigator.of(context, rootNavigator: true).pop();
                                      },
                                    );
                                  })
                            ],
                          ));
                    },
                  )),
              Gaps.line,
              Container(
                padding: EdgeInsets.only(left: 16),
                child: CommonUtils.getSimpleText("备注", 14, Colours.base_primary_text_title),
                height: 40,
                alignment: Alignment.centerLeft,
              ),
              BrnShadowCard(
                circular: 10,
                borderWidth: 0,
                blurRadius: 0,
                color: Colours.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0, horizontal: 16),
                  child: BrnInputText(
                    minLines: 3,
                    autoFocus: false,
                    textEditingController: _remarkController,
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    bgColor: Colours.base_primary_bg_page,
                    maxLength: 150,
                    hint: "请输入150字以内的备注信息",
                    borderRadius: 10,
                  ),
                ),
              ),
              Obx(
                () => Visibility(
                  visible: _controller.isSeparationContract.value,
                  child: BrnRadioInputFormItem(
                    title: "签署方式",
                    isRequire: true,
                    options: const [
                      "电子合同",
                      "纸质合同",
                    ],
                    value: _controller.signType.value,
                    onChanged: (oldValue, newValue) {
                      _controller.updateSignType(newValue ?? '');
                      if (newValue == "纸质合同") {
                        _controller.updateBottomBtnTitle("提交");
                      }
                      if (newValue == "电子合同") {
                        _controller.updateBottomBtnTitle("预览电子合同");
                      }
                    },
                  ),
                ),
              ),
              Gaps.line,
              Obx(() => Visibility(
                    visible: _controller.signType.value == "纸质合同",
                    child: Column(
                      children: [
                        BrnTextInputFormItem(
                          title: "离职协议",
                          isEdit: false,
                          controller: _noticeController,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: CustomImageGridView(
                            imageUrls: _controller.imagesList.value,
                            maxImageCount: 20,
                            showAddButton: true,
                            showDelButton: true,
                          ),
                        ),
                      ],
                    ),
                  )),
              Gaps.vBlock12,
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: Obx(() {
          return BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 14),
            title: _controller.bottomBtnTitle.value,
            onTap: () {
              var departDate = _controller.departDate.value;
              var departType = _controller.departType.value;
              if (TextUtil.isEmpty(departDate)) {
                Toast.show("请选择离职日期");
                return;
              }
              if (TextUtil.isEmpty(departType)) {
                Toast.show("请选择离职类型");
                return;
              }
              var signType = _controller.signType.value;
              if (signType == "") {
                Toast.show("请选择合同签署方式");
                return;
              } else {
                if (signType == "纸质合同") {
                  _presenter?.requestMembersDepart(getHashMap());
                } else {
                  if (TextUtil.isEmpty(_controller.customerIdCard.value)) {
                    Toast.show("该员工的身份证信息为空，无法进行电子签署，请先完善员工身份证信息");
                    return;
                  }
                  _presenter?.requestContractTagList("SeparationContract");
                }
              }
            },
          );
        }),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = QuickDepartPagePresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestDepartDetail(StaffDetailEntity data) {
    widget.uuid = data.uuid ?? "";
    fillBaseInfo(data);
  }

  ///员工详情
  void fillBaseInfo(StaffDetailEntity data) {
    ///获取个人日志
    _presenter?.getContractMainInfo(widget.uuid ?? '');

    _controller.staffDetailEntity.value = data;
    _nameController.text = data.userName ?? "";
    _projectController.text = (data.projectName == null || data.projectShortName == "") ? data.projectName ?? "" : data.projectShortName!;
    if (data.workInfo != null) {
      _positionController.text = (data.workInfo!.jobName == null || data.workInfo!.jobName == "") ? ("-") : data.workInfo!.jobName!;
    }
    _startDateController.text = data.workInfo?.workStartTime ?? "-";

    //从审批过来的话，才去取这些值，负责不取
    if (!TextUtil.isEmpty(widget.application_no)) {
      _controller.updateDepartDate(data.workInfo?.workEndTime ?? "-");
      _controller.updateDepartType(data.leftRemark ?? "-");

      for (int i = 0; i < types.length; i++) {
        if (types[i] == data.leftContract?.leftReasonName) {
          _controller.reason.value = (i + 1);
        }
      }
    }

    _remarkController.text = data.leftContract?.leftReasonRemark ?? "";

    var leftPicList = data.leftContract?.picList ?? [];
    List<BaseMediaEntity> list = [];
    leftPicList.forEach((element) {
      list.add(BaseMediaEntity(media_type: "1", media_url: element));
    });
    _controller.updateImagesList(list);

    /**
     * 1、先获取当前企业账号的配置
     * 2、在获取当前个人的账号信息 customer_id
     * 3、获取系统合同模板列表
     * 4、创建电子合同
     */
    if (data.identity != null && !TextUtil.isEmpty(data.identity?.idNumber)) {
      _controller.customerIdCard.value = data.identity?.idNumber ?? "";
      if (!TextUtil.isEmpty(data.identity?.idNumber)) {
        _presenter?.requestContractInfo("1", data.identity?.idNumber ?? "");
      }
    }
  }

  @override
  void requestStaffDetail(StaffDetailEntity data) {
    widget.uuid = data.uuid ?? '';
    fillBaseInfo(data);
  }

  HashMap<String, String> getHashMap() {
    HashMap<String, String> hashMap = HashMap<String, String>();
    if (widget.uuid.isNotEmpty) {
      hashMap["uuid"] = widget.uuid;
    }
    hashMap["work_end_time"] = _controller.departDate.value;
    hashMap["reason"] = _controller.reason.value.toString();
    hashMap["remark"] = _remarkController.text.toString();
    hashMap["contract_sign_type"] = isSeparationContract() ? (_controller.signType.value == "纸质合同" ? "2" : "1") : "2";

    var value = _controller.imagesList.value;
    if (value.isNotEmpty) {
      hashMap["pic_list"] = json.encode(value.map((e) => e.media_url!).toList());
    }
    return hashMap;
  }

  bool isSeparationContract() {
    // 是否可以电子签1是 2否
    return (_controller.contractPageInfo.value.status == 1);
  }

  @override
  void requestContractTagList(ContractTagOneEntity data) {
    //在这里直接拿到默认的合同类型，直接去请求子合同
    data.subLabelList?.forEach((element) {
      _presenter?.requestContractList("SeparationContract", element.code);
    });
  }

  @override
  void requestMembersDepart(Object data) {
    Toast.show("操作成功");
    Future.delayed(Duration(seconds: 1), () {
      BoostNavigator.instance.pop();
    });
  }

  @override
  void contractInfo(ContractMainInfoEntity data) {
    //根据customer_status 判断
    if ("2" == data.customerStatus) {
      ////如果是空的，那么就去注册
      _presenter?.requestContractRegister("1", "2", _controller.customerIdCard.value); //
      return;
    }
    _controller.customerId.value = data.customerId ?? ""; ////合同签署的时候需要
  }

  @override
  void contractSettingInfo(EleStatusEntity data) {
    _controller.contractPageInfo.value = data;
    _controller.updateIsSeparationContract(isSeparationContract());
    if (!isSeparationContract()) {
      _controller.updateBottomBtnTitle("提交");
    } else {
      _controller.updateBottomBtnTitle("预览电子合同");
    }
  }

  @override
  void requestContractList(ContractAllListEntity data) {
    data.list?.forEach((element) {
      if (element.setting?.labelDefaultSignTemplate ?? false) {
        var hashMap = HashMap<String, String>();
        hashMap["template_uuid"] = element.uuid ?? "";
        hashMap["customer_id_card"] = _controller.customerIdCard.value ?? "";
        hashMap["left_date"] = _controller.departDate.value ?? "";
        _presenter?.requestContractCreate(hashMap);
      }
    });
  }

  @override
  void requestContractCreate(ContractCreateEntity contractCreateEntity) {
    var customerId = _controller.customerId.value;
    if (customerId == null || customerId!.isEmpty) {
      Toast.show("用户信息异常，无法签署");
      return;
    }

    // 创建参数
    Map<String, dynamic> params = {
      "type": 200,
      "url": contractCreateEntity.contractUrl,
      "customer_id": customerId,
      "contract_uuid": contractCreateEntity.contractUuid,
      "hashMap": getHashMap()..putIfAbsent("contract_uuid", () => contractCreateEntity.contractUuid ?? ""),
    };
    if (_nameController.text.toString().isNotEmpty) {
      params["share_title"] = "${_nameController.text.toString()}的离职合同";
    }
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoContractSignWebActivity", "params": params});
  }

  @override
  void requestContractRegister(ContractRegisterEntity data) {
    if (1 == data.code) {
      _controller.customerId.value = data.data ?? "";
      LogUtil.e("注册成功了，id --》 " + _controller.customerId.value);
    } else {
      Toast.show(data.msg);
    }
  }

  @override
  void dispose() {
    super.dispose();
    addListener?.call();
  }
}
