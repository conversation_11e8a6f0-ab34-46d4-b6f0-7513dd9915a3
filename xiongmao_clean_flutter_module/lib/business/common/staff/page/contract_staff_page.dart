import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/base_media_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_staff_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../company/bean/department_company_entity.dart';
import '../../contract/bean/contract_main_electronic_entity.dart';
import '../../widget/base_page_title.dart';
import '../bean/ele_status_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/contract_staff_controller.dart';
import '../controller/quick_depart_page_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/contract_staff_iview.dart';
import '../iview/quick_depart_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/contract_staff_persenter.dart';
import '../persenter/quick_depart_page_persenter.dart';

/// 员工合同
class ContractStaffPage extends StatefulWidget {
  String uuid = ""; //合同id
  String user_uuid = ""; //员工的id

  ContractStaffPage({Key? key, required this.uuid, required this.user_uuid});

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<ContractStaffPage> with BasePageMixin<ContractStaffPage, PowerPresenter<dynamic>> implements ContractStaffIView {
  ContractStaffPresenter? _presenter;
  final ContractStaffController _controller = ContractStaffController();
  final _noticeController = TextEditingController();
  final _nameController = TextEditingController();
  final _projectController = TextEditingController();
  final _positionController = TextEditingController();
  final _startDateController = TextEditingController();
  final _remarkController = TextEditingController();

  VoidCallback? addListener;

  @override
  void initState() {
    super.initState();

    //监听回掉
    addListener ??= BoostChannel.instance.addEventListener("refresh_contract_records", (key, arguments) async {
      BoostNavigator.instance.pop();
    });

    _noticeController.text = "可上传多张";
    HashMap<String, String> hashMap = HashMap();
    hashMap['is_work_info'] = "1";
    hashMap['is_identity'] = "1";
    if (!TextUtil.isEmpty(widget.user_uuid)) {
      hashMap['uuid'] = widget.user_uuid;
      _presenter?.requestStaffDetail(hashMap); //员工详情
    }

    ///如果uuid 不是空的，那么查询合同详情
    _presenter?.getContractMainInfo(widget.user_uuid ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: (!TextUtil.isEmpty(widget.uuid)) ? '编辑劳动协议' : '添加劳动协议',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.white,
      body: SafeArea(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                    Expanded(child: Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("合同类型", 16, Colours.base_primary_text_title))),
                    Expanded(
                        flex: 2,
                        child: Obx(() => SelectTabWidget(
                              key: ValueKey(_controller.workTypeIndex.value),
                              //强制重构UI
                              // 动态 Key
                              _controller.workTypeList.value,
                              multiSelect: false,
                              crossAxisCount: 2,
                              hideMore: false,
                              paddingBottom: 0,
                              paddingTop: 0,
                              tabFontSize: 15,
                              defaultSelectedIndex: [_controller.workTypeIndex.value],
                              lastIsAddOne: false,
                              selectedColor: Colours.base_primary,
                              bgSelectedColor: Colours.base_primary_select,
                              bgUnSelectedColor: Colours.base_primary_un_select,
                              childAspectRatio: 6 / 2.2,
                              itemClickCallback: (List<int> indexs) {
                                LogUtil.e("indexs = $indexs");
                                _controller.workTypeIndex.value = (indexs[0]).toInt();
                                switch (indexs[0]) {
                                  case 0:
                                    _controller.workTypeName.value = '退休返聘合同';
                                    break;
                                  case 1:
                                    _controller.workTypeName.value = '劳务合同';
                                    break;
                                  case 2:
                                    _controller.workTypeName.value = '劳动合同';
                                    break;
                                  case 3:
                                    _controller.workTypeName.value = '兼职合同';
                                    break;
                                }
                                LogUtil.e("indexs = ${_controller.workTypeIndex.value}");
                              },
                            ))),
                  ],
                ),
              ),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    isRequire: true,
                    title: "合约期限",
                    hint: "请选择",
                    value: _controller.contractRangeDate.value,
                    onTap: () {
                      String format = 'yyyy年-MM月-dd日';
                      BrnPickerTitleConfig pickerTitleConfig = const BrnPickerTitleConfig(titleContent: "选择时间范围");
                      BrnDateRangePicker.showDatePicker(
                        context,
                        isDismissible: false,
                        minDateTime: DateTime(2020, 01, 01, 00, 00, 00),
                        maxDateTime: DateTime(2029, 12, 30, 23, 59, 59),
                        pickerMode: BrnDateTimeRangePickerMode.date,
                        minuteDivider: 10,
                        pickerTitleConfig: pickerTitleConfig,
                        dateFormat: format,
                        initialStartDateTime: _controller.startDateTime,
                        initialEndDateTime: _controller.endDateTime,
                        onConfirm: (startDateTime, endDateTime, startlist, endlist) {
                          _controller.startDateTime = startDateTime;
                          _controller.endDateTime = endDateTime;
                          _controller.workStartDate.value = '${startDateTime.year}.${CommonUtils.formatToTwoDigits(startDateTime.month)}.${CommonUtils.formatToTwoDigits(startDateTime.day)}';
                          _controller.workEndDate.value = '${endDateTime.year}.${CommonUtils.formatToTwoDigits(endDateTime.month)}.${CommonUtils.formatToTwoDigits(endDateTime.day)}';
                          _controller.contractRangeDate.value = '${_controller.workStartDate.value} ~ ${_controller.workEndDate.value}';
                        },
                        onClose: () {
                          print("onClose");
                        },
                        onCancel: () {
                          print("onCancel");
                        },
                        onChange: (startDateTime, endDateTime, startlist, endlist) {},
                      );
                    },
                  )),
              Gaps.line,
              Obx(
                () => Visibility(
                  visible: _controller.isEntryContract.value,
                  child: BrnRadioInputFormItem(
                    title: "签署方式",
                    isRequire: true,
                    options: (TextUtil.isEmpty(widget.uuid))
                        ? [
                            "电子合同",
                            "纸质合同",
                          ]
                        : ['纸质合同'],
                    value: _controller.signType.value,
                    onChanged: (oldValue, newValue) {
                      _controller.updateSignType(newValue ?? '');
                      if (newValue == "纸质合同") {
                        _controller.updateBottomBtnTitle("提交");
                      }
                      if (newValue == "电子合同") {
                        _controller.updateBottomBtnTitle("预览电子合同");
                      }
                    },
                  ),
                ),
              ),
              Gaps.line,
              Obx(() => Visibility(
                    visible: _controller.signType.value == "纸质合同",
                    child: Column(
                      children: [
                        BrnTextInputFormItem(
                          title: "劳动协议",
                          isEdit: false,
                          controller: _noticeController,
                        ),
                        Padding(
                          padding: const EdgeInsets.only(left: 10, right: 10),
                          child: CustomImageGridView(
                            imageUrls: _controller.imagesList.value,
                            maxImageCount: 20,
                            showAddButton: true,
                            showDelButton: true,
                          ),
                        ),
                      ],
                    ),
                  )),
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: Obx(() {
          return BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 14),
            title: _controller.bottomBtnTitle.value,
            onTap: () {
              ///提交合同记录
              if (TextUtil.isEmpty(_controller.contractRangeDate.value)) {
                Toast.show('请选择合约期限');
                return;
              }
              var hashMap = HashMap<String, String>();
              print('点击的下标 ${_controller.workTypeIndex.value}');

              ///判断是电子合同，还是纸质合同
              if (_controller.signType.value == "纸质合同") {
                ///现在只有劳动协议，没有离职协议 后续如果有再去增加
                hashMap["start_time"] = _controller.workStartDate.value.replaceAll(".", '-') ?? "";
                hashMap["end_time"] = _controller.workEndDate.value.replaceAll(".", '-') ?? "";
                hashMap["sub_label_code"] = _controller.getSubLabelCode(); //获取code
                var value = _controller.imagesList.value;
                print('上传之前 contract_pic ${json.encode(value.map((e) => e.media_url!).toList())}');
                if (value.isNotEmpty) {
                  hashMap["contract_pic"] = json.encode(value.map((e) => e.media_url!).toList());
                }

                ///合同uuid
                if (!TextUtil.isEmpty(widget.uuid)) {
                  hashMap["uuid"] = widget.uuid;
                }

                /// 用户 user_uuid 添加时候必须传
                if (!TextUtil.isEmpty(widget.user_uuid)) {
                  hashMap["user_uuid"] = widget.user_uuid;
                }
                _presenter?.requestPaperContractSave(hashMap);
              } else {
                ///走电子合同
                _presenter?.requestContractList("EntryContract", _controller.getSubLabelCode());
              }
            },
          );
        }),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = ContractStaffPresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestContractOne(ContractStaffOneEntity data) {
    ///拿到信息来设置信息

    _controller.workTypeIndex.value = _controller.getSubLabelIndexByCode(data.subLabelCode ?? '');

    _controller.workStartDate.value = data.startTime ?? "";
    _controller.workEndDate.value = data.endTime ?? "";
    _controller.contractRangeDate.value = '${_controller.workStartDate.value} ~ ${_controller.workEndDate.value}';

    ///底部按钮
    _controller.signType.value = '纸质合同';
    _controller.updateBottomBtnTitle("提交");

    ///图片
    List<BaseMediaEntity> list = [];
    data.contractPicList?.forEach((element) {
      list.add(BaseMediaEntity(media_type: "1", media_url: element));
    });
    _controller.updateImagesList(list);
  }

  @override
  void requestDepartDetail(StaffDetailEntity data) {
    widget.uuid = data.uuid ?? "";
    fillBaseInfo(data);
  }

  ///员工详情
  void fillBaseInfo(StaffDetailEntity data) {
    _controller.staffDetailEntity.value = data;
    _nameController.text = data.userName ?? "";
    _projectController.text = (data.projectName == null || data.projectName == "") ? data.projectName ?? "" : data.projectName!;
    _positionController.text = (data.jobName == null || data.jobName == "") ? ("-") : data.jobName!;

    ///入职的日期
    // _startDateController.text = data.workInfo?.workStartTime ?? "-";
    // 控制器

    // 解析 startDateTime
    DateTime defaultStartDateTime = _controller.parseWorkStartTime(data.workInfo?.workStartTime) ?? DateTime.now();
    _controller.startDateTime = defaultStartDateTime;

    // 计算 endDateTime
    DateTime endDateTime;
    if (data.workInfo?.workStartTime != null && data.workInfo!.workStartTime!.isNotEmpty) {
      DateTime workStartTime = defaultStartDateTime;
      endDateTime = DateTime(
        workStartTime.year + 1,
        workStartTime.month,
        workStartTime.day - 1,
      );
    } else {
      // 如果 workStartTime 为空，则使用当前时间作为默认值
      DateTime defaultStartTime = DateTime.now();
      endDateTime = DateTime(
        defaultStartTime.year + 1,
        defaultStartTime.month,
        defaultStartTime.day - 1,
      );
    }

    _controller.endDateTime = endDateTime;

    _remarkController.text = data.leftContract?.leftReasonRemark ?? "";

    var leftPicList = data.leftContract?.picList ?? [];
    List<BaseMediaEntity> list = [];
    leftPicList?.forEach((element) {
      list.add(BaseMediaEntity(media_type: "1", media_url: element));
    });
    _controller.updateImagesList(list);

    /**
     * 1、先获取当前企业账号的配置
     * 2、在获取当前个人的账号信息 customer_id
     * 3、获取系统合同模板列表
     * 4、创建电子合同
     */
    if (data.identity != null) {
      _controller.customerIdCard.value = data.identity?.idNumber ?? "";
      _presenter?.requestContractInfo("1", data.identity?.idNumber ?? "");
    }
  }

  @override
  void requestStaffDetail(StaffDetailEntity data) {
    fillBaseInfo(data);
  }

  HashMap<String, String> getHashMap() {
    HashMap<String, String> hashMap = HashMap<String, String>();
    if (!TextUtil.isEmpty(widget.uuid)) {
      hashMap["uuid"] = widget.uuid;
    }
    hashMap["work_end_time"] = _controller.contractRangeDate.value;
    hashMap["reason"] = _controller.reason.value.toString();
    hashMap["remark"] = _remarkController.text.toString();
    hashMap["contract_sign_type"] = isEntryContract() ? (_controller.signType.value == "纸质合同" ? "2" : "1") : "2";

    var value = _controller.imagesList.value;
    print('上传之前 pic_list ${json.encode(value.map((e) => e.media_url!).toList())}');
    if (value.isNotEmpty) {
      hashMap["pic_list"] = json.encode(value.map((e) => e.media_url!).toList());
    }
    return hashMap;
  }

  ///查看是否开启入职电子签
  bool isEntryContract() {
    // 获取合同信息
    return (1 == _controller.contractSettingInfo.value.status);
  }

  @override
  void requestContractTagList(ContractTagOneEntity data) {
    //在这里直接拿到默认的合同类型，直接去请求子合同
    _controller.workTypeList.value.clear();
    _controller.contractSubEntity.value = data;
    data.subLabelList?.forEach((element) {
      _controller.workTypeList.value.add(BaseChooseString(element.labelName));
    });

    ///获取合同详情
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.requestPaperContractOne(widget.uuid);
    }
  }

  @override
  void requestMembersDepart(Object data) {
    Toast.show("操作成功");
  }

  @override
  void contractInfo(ContractMainInfoEntity data) {
    //根据customer_status 判断
    if ("2" == data.customerStatus) {
      ////如果是空的，那么就去注册
      _presenter?.requestContractRegister("1", "2", _controller.customerIdCard.value); //
      return;
    }
    _controller.customerId.value = data.customerId ?? ""; ////合同签署的时候需要
  }

  @override
  void contractSettingInfo(EleStatusEntity data) {
    _controller.contractSettingInfo.value = data;
    _controller.updateIsEntryContract(isEntryContract());
    if (!isEntryContract()) {
      _controller.updateBottomBtnTitle("提交");
    } else {
      _controller.updateBottomBtnTitle("预览电子合同");
    }
    _presenter?.requestContractTagList('EntryContract');
  }

  @override
  void requestContractList(ContractAllListEntity data) {
    data.list?.forEach((element) {
      if (element.setting?.labelDefaultSignTemplate ?? false) {
        var hashMap = HashMap<String, String>();
        hashMap["template_uuid"] = element.uuid ?? "";
        hashMap["customer_id_card"] = _controller.customerIdCard.value ?? "";

        /// left_date 这个参数只有离职的时候传递 现在没写，
        // hashMap["left_date"] = _controller.contractRangeDate.value ?? "";

        ///入职的时候的选择的参数
        hashMap["start_date"] = _controller.workStartDate.value.replaceAll(".", '-') ?? "";
        hashMap["end_date"] = _controller.workEndDate.value.replaceAll(".", '-') ?? "";
        _presenter?.requestContractCreate(hashMap);
      }
    });
  }

  @override
  void requestContractCreate(ContractCreateEntity contractCreateEntity) {
    var customerId = _controller.customerId.value;
    if (customerId == null || customerId.isEmpty) {
      Toast.show("用户信息异常，无法签署");
      return;
    }

    // 创建参数
    Map<String, dynamic> params = {
      "type": 100, //是入职还是离职
      "url": contractCreateEntity.contractUrl,
      "customer_id": customerId,
      "contract_uuid": contractCreateEntity.contractUuid,
      "hashMap": getHashMap()..putIfAbsent("contract_uuid", () => contractCreateEntity.contractUuid ?? ""),
    };
    if (_nameController.text.toString().isNotEmpty) {
      params["share_title"] = "${_nameController.text.toString()}的${_controller.workTypeName.value}";
    }
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "gotoContractSignWebActivity", "params": params});
  }

  @override
  void requestContractRegister(ContractRegisterEntity data) {
    if (1 == data.code) {
      _controller.customerId.value = data.data ?? "";
      LogUtil.e("注册成功了，id --》 ${_controller.customerId.value}");
    } else {
      Toast.show(data.msg);
    }
  }

  @override
  void dispose() {
    super.dispose();
    addListener?.call();
  }
}
