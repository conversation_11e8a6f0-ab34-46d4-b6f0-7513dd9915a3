import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';
import 'add_staff_contactInfo_page.dart';
import 'add_staff_identityInfo_page.dart';
import 'add_staff_material_attachment_page.dart';
import 'add_staff_position_arrangement_page.dart';
import 'add_staff_salary_Insurance_page.dart';

/// 添加/编辑员工
class AddStaffMainPage extends StatefulWidget {
  String? uuid = "";
  String? userName = "";
  String? applicationNo = "";
  int channel;
  int jumpIndex;
  bool againEntry;

  AddStaffMainPage({
    super.key,
    this.uuid = "",
    this.userName = "",
    this.applicationNo = "",
    this.channel = 0,
    this.jumpIndex = 0,
    this.againEntry = false,
  });

  @override
  _AddStaffMainPageState createState() => _AddStaffMainPageState();
}

class _AddStaffMainPageState extends State<AddStaffMainPage> with BasePageMixin<AddStaffMainPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffMainIView {
  AddStaffMainPresenter? _presenter;
  final AddStaffMainController _controller = AddStaffMainController();

  ///特殊草稿的情况
  EntryConditionEntity? entryConditionEntity;

  var title = '添加成员';

  @override
  void initState() {
    super.initState();
    _controller.channel.value = widget.channel;
    _controller.jumpIndex.value = widget.jumpIndex;
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';
    _controller.againEntry.value = widget.againEntry;

    ///3 - 4 都是编辑档案  之外的都是草稿
    if (_controller.channel.value == 3 || _controller.channel.value == 4) {
      switch (widget.jumpIndex) {
        case 0:
          _controller.currentStep.value = 0;
          title = '编辑身份信息';
          return;
        case 1:
          _controller.currentStep.value = 1;
          title = '编辑联系方式';
          return;
        case 3:
          _controller.currentStep.value = 2;
          title = '编辑任职安排';
          return;
        case 4:
          _controller.currentStep.value = 3;
          title = '编辑薪酬保险';
          return;
        case 5:
          _controller.currentStep.value = 4;
          title = '编辑材料附件';
          return;
      }
    }

    ///如果本地没元数据，那么就请求元数据，并存到本地
    String? json = SpUtil.getString(Constant.META_DATA);
    if (TextUtil.isEmpty(json)) {
      _presenter?.getMetaData();
    }

    ///一进来就获取民族的列表
    String? nationJson = SpUtil.getString(Constant.NATION_DATA);
    if (TextUtil.isEmpty(nationJson)) {
      _presenter?.getMetaDtaNation();
    }

    ///获取城市列表
    String? cityJson = SpUtil.getString(Constant.CITY_META_DATA);
    if (TextUtil.isEmpty(cityJson)) {
      _presenter?.getCityMetaData();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: title,
        centerSubTitle: (_controller.channel.value == 3 || _controller.channel.value == 4) ? widget.userName : '',
      ),
      body: Obx(() => Column(
            children: [
              ///这是步骤器
              Visibility(
                visible: _controller.channel.value != 3 && _controller.channel.value != 4,
                child: Container(
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 10, horizontal: 4),
                  child: defaultHorizontalSteps(),
                ),
              ),
              Expanded(child: _buildStepContent()), // 显示当前步骤的内容
            ],
          )),
    );
  }

  ///步骤器
  Widget defaultHorizontalSteps() {
    return FlutterSteps(
      key: ValueKey(_controller.currentStep.value),
      steps: _controller.basicSteps.value,
      titleFontSize: 12,
      showSubtitle: false,
      subtitleFontSize: 8,
      isStepLineContinuous: false,
      activeColor: Colours.base_primary,
      inactiveColor: Colours.base_primary_bg_page,
      titleActiveColor: Colours.white,
      titleInactiveColor: Colours.base_primary_text_caption,
      leadingSize: 30,
      activeStepLineColor: Colours.base_primary,
      stepLineHeight: 1.2,
    );
  }

  // 根据当前步骤返回对应的内容
  Widget _buildStepContent() {
    return Obx(() {
      // 使用 Obx 来监听 currentStep 变化
      switch (_controller.currentStep.value) {
        case 0:

          ///身份信息
          return AddStaffIdentityInfoPage(
            uuid: _controller.uuid.value,
            applicationNo: _controller.applicationNo.value,
            channel: _controller.channel.value,
            againEntry: _controller.againEntry.value,
            onNext: (uuid, entryCondition) {
              ///如果是编辑，那么就直接关闭掉界面
              if (_controller.channel.value == 3 || _controller.channel.value == 4) {
                BoostNavigator.instance.pop();
                return;
              }
              _controller.uuid.value = uuid;
              entryConditionEntity = entryCondition;
              _controller.updateStep(_controller.currentStep.value + 1);

              ///成功后就把着歌状态值改成false 不然会影响上一步的操作
              _controller.againEntry.value = false;
            },
          );
        case 1:

          ///联系信息
          return AddStaffContactInfoPage(
            uuid: _controller.uuid.value,
            applicationNo: _controller.applicationNo.value,
            channel: _controller.channel.value,
            onPrevious: () {
              _controller.updateStep(_controller.currentStep.value - 1);
            },
            onNext: (uuid) {
              ///如果是编辑，那么就直接关闭掉界面
              if (_controller.channel.value == 3 || _controller.channel.value == 4) {
                BoostNavigator.instance.pop();
                return;
              }
              _controller.uuid.value = uuid;
              _controller.updateStep(_controller.currentStep.value + 1);
            },
            entryCondition: entryConditionEntity,
          );
        case 2:

          ///任职安排
          return AddStaffPositionArrangementPage(
            uuid: _controller.uuid.value,
            applicationNo: _controller.applicationNo.value,
            channel: _controller.channel.value,
            onPrevious: () {
              _controller.updateStep(_controller.currentStep.value - 1);
            },
            onNext: (uuid) {
              ///如果是编辑，那么就直接关闭掉界面
              if (_controller.channel.value == 3 || _controller.channel.value == 4) {
                BoostNavigator.instance.pop();
                return;
              }
              _controller.uuid.value = uuid;
              _controller.updateStep(_controller.currentStep.value + 1);
            },
            entryCondition: entryConditionEntity,
          );
        case 3:

          ///薪酬标准
          return AddStaffSalaryInsurancePage(
            uuid: _controller.uuid.value,
            applicationNo: _controller.applicationNo.value,
            channel: _controller.channel.value,
            onPrevious: () {
              _controller.updateStep(_controller.currentStep.value - 1);
            },
            onNext: (uuid) {
              ///如果是编辑，那么就直接关闭掉界面
              if (_controller.channel.value == 3 || _controller.channel.value == 4) {
                BoostNavigator.instance.pop();
                return;
              }
              _controller.uuid.value = uuid;
              _controller.updateStep(_controller.currentStep.value + 1);
            },
            entryCondition: entryConditionEntity,
          );
        case 4:

          ///材料附件
          return AddStaffMaterialAttachmentPage(
            uuid: _controller.uuid.value,
            applicationNo: _controller.applicationNo.value,
            channel: _controller.channel.value,
            onPrevious: () {
              _controller.updateStep(_controller.currentStep.value - 1);
            },
            onNext: (uuid) {
              /// 3/4 都是编辑真正的员工，直接关闭界面就行
              if (_controller.channel.value == 3 || _controller.channel.value == 4) {
                BoostNavigator.instance.pop(context);
                return;
              }

              ///用户是否开启的签名确定入职
              if (SpUtil.getBool(Constant.IS_ENTRY_SIGN) == false) {
                BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_todo_approve"});
                CommonUtils.finishPage();
                return;
              }

              ///去签字
              BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_sign_hand", 'uuid': uuid});
              CommonUtils.finishPage();
            },
            entryCondition: entryConditionEntity,
          );
        default:
          return Container();
      }
    });
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffMainPresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;
}
