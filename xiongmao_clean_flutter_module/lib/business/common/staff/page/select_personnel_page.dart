import 'dart:collection';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../controller/select_personnel_controller.dart';
import '../item/select_personnel_listview.dart';
import '../iview/select_personnel_iview.dart';
import '../persenter/select_personnel_persenter.dart';

///全局选择人员的界面，只要涉及到选择人员的，都要走这里，这里只支持选择人员
class SelectPersonnelPage extends StatefulWidget {
  int channel;
  String title;
  bool multiple;
  String status;
  String role_id;
  String is_head_office;
  String project_uuid;
  String contract_start_date;
  String contract_end_date;
  String uuids;

  SelectPersonnelPage({
    super.key,
    required this.channel,
    required this.title,
    required this.multiple,
    required this.status,
    required this.role_id,
    required this.is_head_office,
    required this.project_uuid,
    required this.contract_start_date,
    required this.contract_end_date,
    required this.uuids,
  });

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<SelectPersonnelPage> with BasePageMixin<SelectPersonnelPage, PowerPresenter<dynamic>> implements SelectPersonnelIView {
  final SelectPersonnelController _controller = SelectPersonnelController();

  late SelectPersonnelPresenter _presenter;

  @override
  void initState() {
    super.initState();

    ///拿参数
    _controller.channel.value = widget.channel;
    _controller.status.value = widget.status;
    _controller.role_id.value = widget.role_id;
    _controller.is_head_office.value = widget.is_head_office;
    _controller.project_uuid.value = widget.project_uuid;
    _controller.contract_start_date.value = widget.contract_start_date;
    _controller.contract_end_date.value = widget.contract_end_date;
    _controller.uuids.value = widget.uuids;
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _presenter.onRefresh();
  }

  Future<dynamic> _loadMore() async {
    _presenter.loadMore();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: widget.title,
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: Column(
            children: [
              Container(
                child: BrnSearchText(
                  innerPadding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
                  maxHeight: 60,
                  innerColor: Colours.base_primary_bg_page,
                  borderRadius: const BorderRadius.all(Radius.circular(8)),
                  autoFocus: false,
                  textStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_title),
                  hintStyle: const TextStyle(fontSize: 16, color: Colours.base_primary_text_caption),
                  hintText: '请输入内容查找人员',
                  onActionTap: () {},
                  onTextCommit: (text) {},
                  onTextChange: (text) {
                    _controller.keyword.value = text;
                    _onRefresh();
                  },
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.totalNumber.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return SelectPersonnelListView(
                    data: _controller.list[index],
                    onClick: () {
                      // 单选逻辑
                      if (!widget.multiple) {
                        for (var item in _controller.list) {
                          item.isSelected = false; // 清空所有选中状态
                        }
                        _controller.list[index].isSelected = true; // 选中当前项
                        BoostNavigator.instance.pop(_controller.getSelectedItems());
                      } else {
                        // 多选逻辑，切换当前项的选中状态
                        _controller.list[index].isSelected = !_controller.list[index].isSelected;
                      }

                      // 刷新列表
                      _controller.list.refresh();
                    },
                  );
                },
              ))
            ],
          ),
          bottomNavigationBar: Visibility(
            visible: widget.multiple,
            child: Container(
              color: Colors.white,
              height: 54,
              child: Column(
                children: [
                  Gaps.line,
                  // 按钮
                  Padding(
                    padding: const EdgeInsets.only(left: 16, right: 16, top: 7, bottom: 6),
                    child: BrnBigMainButton(
                      themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                      title: '确定',
                      onTap: () {
                        BoostNavigator.instance.pop(_controller.getSelectedItems());
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = SelectPersonnelPresenter(_controller);
    powerPresenter.requestPresenter([_presenter]);
    return powerPresenter;
  }
}
