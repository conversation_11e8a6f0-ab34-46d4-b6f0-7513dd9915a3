import 'dart:collection';
import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:image_pickers/image_pickers.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_auth_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/check_id_number_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_nation_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/permission_util.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/city_data_entity.dart';
import '../bean/entry_condition_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../controller/add_staff_main_controller.dart';
import '../iview/add_company_iview.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../persenter/add_company_persenter.dart';
import '../persenter/add_staff_Identity_Info_persenter.dart';
import '../persenter/add_staff_main_persenter.dart';
import '../widght/brn2_city_row_delegate.dart';
import '../widght/brn3_city_row_delegate.dart';
import '../widght/custom_entry_staff_dialog.dart';
import '../widght/flutter_steps.dart';
import '../widght/steps.dart';

/// 添加身份信息
class AddStaffIdentityInfoPage extends StatefulWidget {
  String? uuid = "";
  String? applicationNo = "";
  int channel;
  bool againEntry;
  final Function(String, EntryConditionEntity) onNext;

  AddStaffIdentityInfoPage({
    Key? key,
    this.uuid = "",
    this.applicationNo = "",
    this.channel = 0,
    this.againEntry = false,
    required this.onNext,
  });

  @override
  _AddStaffIdentityInfoPageState createState() => _AddStaffIdentityInfoPageState();
}

class _AddStaffIdentityInfoPageState extends State<AddStaffIdentityInfoPage> with BasePageMixin<AddStaffIdentityInfoPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin implements AddStaffIdentityInfoIView {
  AddStaffIdentityInfoPresenter? _presenter;
  final AddStaffIdentityInfoController _controller = AddStaffIdentityInfoController();

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';
    _controller.applicationNo.value = widget.applicationNo ?? '';

    ///查看民族
    _controller.nationList.value = CommonUtils.getNationalList();

    ///获取城市
    _controller.cityList.value = CommonUtils.getCityList();

    ///看看是否需要请求详情
    if (!_controller.isDataFetched.value) {
      if (widget.againEntry) {
        ///再次入职，重新拿
        _controller.isLeave.value = false;
        _controller.beforeEntry.value = true;
        _controller.before_entry_and_draft_uuid.value = widget.uuid ?? '';
        _presenter?.getStaffOne(false);
      } else {
        if (!TextUtil.isEmpty(_controller.uuid.value) || !TextUtil.isEmpty(_controller.applicationNo.value)) {
          _presenter?.getStaffOne(widget.channel == 3 ? false : true);
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colours.white,
      body: WillPopScope(
          child: SingleChildScrollView(
            child: Obx(() => Column(
                  children: [
                    Container(
                      color: Colours.base_primary_bg_page,
                      height: 10,
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
                      color: Colors.white,
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CommonUtils.getSimpleText('身份证信息', 16, Colours.base_primary_text_title),
                              CommonUtils.getSimpleText('将为您自动填写身份信息', 16, Colours.base_primary_text_title),
                            ],
                          ),
                          Container(
                            margin: const EdgeInsets.only(top: 10),
                            padding: const EdgeInsets.symmetric(
                              vertical: 10,
                              horizontal: 10,
                            ),
                            color: Colours.base_primary_bg_page,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: InkWell(
                                    child: LoadImage(
                                      (TextUtil.isEmpty(_controller.idZUrl.value) ? 'common/id_card_z_bg' : _controller.idZUrl.value),
                                      height: 100,
                                    ),
                                    onTap: () {
                                      openPhoto(0);
                                    },
                                  ),
                                ),
                                Gaps.hGap10,
                                Expanded(
                                  child: InkWell(
                                    child: LoadImage(
                                      (TextUtil.isEmpty(_controller.idFUrl.value) ? 'common/id_card_f_bg' : _controller.idFUrl.value),
                                      height: 100,
                                    ),
                                    onTap: () {
                                      openPhoto(1);
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),

                    ///开始表单的内容
                    BrnTextInputFormItem(
                      isRequire: true,
                      title: "姓名",
                      isEdit: (widget.channel == 3) ? (httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_MANGER_ID) : true,
                      hint: "请输入",
                      controller: _controller.nameController,
                    ),
                    Gaps.line,
                    BrnTextInputFormItem(
                      isRequire: httpConfig.role_id != HttpConfig.ROLE_SUPER_MANGER_ID,
                      title: "身份证",
                      hint: "请输入",
                      isEdit: (widget.channel == 3) ? (httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_MANGER_ID) : true,
                      inputFormatters: [
                        LengthLimitingTextInputFormatter(18), // 限制输入长度为20个字符
                      ],
                      controller: _controller.idNumberController,
                    ),
                    Gaps.line,
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonUtils.getSimpleText('性别', 15, Colours.base_primary_text_title),
                            flex: 2,
                          ),
                          Expanded(
                              flex: 2,
                              child: SelectTabWidget(
                                key: ValueKey(_controller.sexIndex.value),
                                _controller.sexList.value,
                                multiSelect: false,
                                crossAxisCount: 2,
                                hideMore: false,
                                paddingBottom: 0,
                                paddingTop: 0,
                                tabFontSize: 15,
                                defaultSelectedIndex: _controller.sexIndex.value == -1 ? [] : [_controller.sexIndex.value],
                                lastIsAddOne: false,
                                selectedColor: Colours.base_primary,
                                bgSelectedColor: Colours.base_primary_select,
                                bgUnSelectedColor: Colours.base_primary_un_select,
                                childAspectRatio: 6 / 3,
                                itemClickCallback: (List<int> indexs) {
                                  LogUtil.e("indexs = $indexs");
                                  FocusScope.of(context).unfocus();
                                  _controller.sexIndex.value = (indexs[0]).toInt();
                                },
                              )),
                        ],
                      ),
                    ),

                    Gaps.line,
                    BrnTextSelectFormItem(
                      title: "民族",
                      value: _controller.nation.value,
                      onTap: () {
                        ///先查本地有民族的数据吗 如果还没有，那么就获取在线的
                        if (_controller.nationList.value.isEmpty) {
                          _presenter?.getMetaDtaNation(context);
                        } else {
                          ///进来后获取一下元数据的参数
                          _controller.showNationDialog(context, _controller.nationList.value);
                        }
                      },
                    ),
                    Gaps.line,
                    BrnTextSelectFormItem(
                      title: "出生日期",
                      value: _controller.birthdayDate.value,
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.datetime,
                          dateFormat: 'yyyy年,MMMM月,dd日',
                          initialDateTime: DateUtil.getDateTime(_controller.birthdayDate.value),
                          onConfirm: (dateTime, list) {
                            FocusScope.of(context).unfocus();
                            if (dateTime != null) {
                              final formattedDate = DateUtil.formatDate(dateTime, format: 'yyyy-MM-dd');
                              _controller.updateBirthdayDate(formattedDate);
                            }
                          },
                        );
                      },
                    ),
                    Gaps.line,
                    BrnTextSelectFormItem(
                      title: "户籍城市",
                      value: _controller.city.value,
                      onTap: () {
                        ///选择城市
                        if (_controller.cityList.value.isNotEmpty) {
                          showCityDialog();
                        } else {
                          ///再次获取一下城市数据
                          _presenter?.getCityMetaData();
                        }
                      },
                    ),
                    Gaps.line,
                    BrnTextInputFormItem(
                      title: "户籍地址",
                      hint: "请输入",
                      controller: _controller.homeAddressController,
                    ),
                    Gaps.line,
                    BrnTextSelectFormItem(
                      title: "身份证开始日期",
                      value: _controller.startDate.value,
                      onTap: () {
                        BrnDatePicker.showDatePicker(
                          themeData: BrnPickerConfig(
                            pickerHeight: 300,
                          ),
                          context,
                          pickerTitleConfig: BrnPickerTitleConfig.Default,
                          pickerMode: BrnDateTimePickerMode.datetime,
                          dateFormat: 'yyyy年,MMMM月,dd日',
                          initialDateTime: DateUtil.getDateTime(_controller.startDate.value),
                          onConfirm: (dateTime, list) {
                            FocusScope.of(context).unfocus();
                            if (dateTime != null) {
                              final formattedDate = DateUtil.formatDate(dateTime, format: 'yyyy-MM-dd');
                              _controller.updateStartDate(formattedDate);
                            }
                          },
                        );
                      },
                    ),
                    Gaps.line,
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonUtils.getSimpleText('身份证是否长期', 15, Colours.base_primary_text_title),
                            flex: 2,
                          ),
                          Expanded(
                              flex: 2,
                              child: SelectTabWidget(
                                _controller.idLongList.value,
                                key: ValueKey(_controller.idLongIndex.value),
                                multiSelect: false,
                                crossAxisCount: 2,
                                hideMore: false,
                                paddingBottom: 0,
                                paddingTop: 0,
                                tabFontSize: 15,
                                defaultSelectedIndex: _controller.idLongIndex.value == -1 ? [] : [_controller.idLongIndex.value],
                                lastIsAddOne: false,
                                selectedColor: Colours.base_primary,
                                bgSelectedColor: Colours.base_primary_select,
                                bgUnSelectedColor: Colours.base_primary_un_select,
                                childAspectRatio: 6 / 3,
                                itemClickCallback: (List<int> indexs) {
                                  LogUtil.e("indexs = $indexs");
                                  FocusScope.of(context).unfocus();
                                  _controller.idLongIndex.value = (indexs[0]).toInt();
                                },
                              )),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: _controller.idLongIndex.value == 1,
                      child: Column(
                        children: [
                          Gaps.line,
                          BrnTextSelectFormItem(
                            title: "身份证结束日期",
                            value: _controller.endDate.value,
                            onTap: () {
                              BrnDatePicker.showDatePicker(
                                themeData: BrnPickerConfig(
                                  pickerHeight: 300,
                                ),
                                context,
                                pickerTitleConfig: BrnPickerTitleConfig.Default,
                                pickerMode: BrnDateTimePickerMode.datetime,
                                dateFormat: 'yyyy年,MMMM月,dd日',
                                initialDateTime: DateUtil.getDateTime(_controller.endDate.value),
                                onConfirm: (dateTime, list) {
                                  FocusScope.of(context).unfocus();
                                  if (dateTime != null) {
                                    final formattedDate = DateUtil.formatDate(dateTime, format: 'yyyy-MM-dd');
                                    _controller.updateEndDate(formattedDate);
                                  }
                                },
                              );
                            },
                          )
                        ],
                      ),
                    ),
                    Gaps.line,
                    Padding(
                      padding: const EdgeInsets.only(left: 16, right: 16, bottom: 10, top: 10),
                      child: Row(
                        children: [
                          Expanded(
                            child: CommonUtils.getSimpleText('婚姻状况', 15, Colours.base_primary_text_title),
                            flex: 1,
                          ),
                          Expanded(
                              flex: 3,
                              child: SelectTabWidget(
                                key: ValueKey(_controller.marriageListIndex.value),
                                _controller.marriageList.value,
                                multiSelect: false,
                                crossAxisCount: 3,
                                hideMore: false,
                                paddingBottom: 0,
                                paddingTop: 0,
                                tabFontSize: 15,
                                defaultSelectedIndex: _controller.marriageListIndex.value == -1 ? [] : [_controller.marriageListIndex.value],
                                lastIsAddOne: false,
                                selectedColor: Colours.base_primary,
                                bgSelectedColor: Colours.base_primary_select,
                                bgUnSelectedColor: Colours.base_primary_un_select,
                                childAspectRatio: 6 / 3,
                                itemClickCallback: (List<int> indexs) {
                                  LogUtil.e("indexs = $indexs");
                                  FocusScope.of(context).unfocus();
                                  if (_controller.marriageListIndex.value == (indexs[0]).toInt()) {
                                    _controller.marriageListIndex.value = -1;
                                  } else {
                                    _controller.marriageListIndex.value = (indexs[0]).toInt();
                                  }
                                },
                              )),
                        ],
                      ),
                    ),
                    Container(
                      color: Colours.base_primary_bg_page,
                      height: 100,
                    ),
                  ],
                )),
          ),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        height: 60,
        color: Colors.white,
        child: Column(
          children: [
            Gaps.line,
            InkWell(
              child: Container(
                alignment: Alignment.center,
                width: double.infinity,
                height: 44,
                margin: const EdgeInsets.only(
                  top: 8,
                  left: 10,
                  right: 10,
                ),
                decoration: BoxDecoration(
                  color: Colours.base_primary,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: CommonUtils.getSimpleText(((widget.channel == 3 || widget.channel == 4)) ? '提交' : '下一步', 16, Colours.white, fontWeight: FontWeight.bold),
              ),
              onTap: () {
                if (TextUtil.isEmpty(_controller.nameController.text)) {
                  Toast.show('请输入姓名');
                  return;
                }

                ///如果是超管的话，可以不用输入身份证号 您未录入身份证号，会导致无法给该成员做信用查询、签署电子合同、买保险等，是否确认继续？
                if (httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID && TextUtil.isEmpty(_controller.idNumberController.text)) {
                  showSuperManagerTipDialog();
                  return;
                }

                if (httpConfig.role_id != HttpConfig.ROLE_SUPER_MANGER_ID && TextUtil.isEmpty(_controller.idNumberController.text)) {
                  Toast.show('请输入身份证号');
                  return;
                }

                ///还有一个再入职的情况，如果是再入职的话 检验再入职
                if (widget.againEntry && !_controller.isLeave.value) {
                  ///说明提示过了，不用提示了，直接走创建
                  _presenter?.checkIdCardInfo();
                  return;
                }

                ///如果沿用了以前的登记信息 直接请求新建
                if (_controller.isDraft.value || _controller.beforeEntry.value) {
                  _presenter?.createStaff();
                  return;
                }

                ///说明是新建 channel 1是草稿 0、2 是新建
                if (TextUtil.isEmpty(_controller.uuid.value) && widget.channel == 0 || widget.channel == 2) {
                  _presenter?.checkIdCardInfo();
                } else {
                  _presenter?.checkIdCardInfoAuth();
                }
              },
            )
          ],
        ),
      ),
    );
  }

  ///选择照片
  void openPhoto(int position) {
    showModalBottomSheet(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (BuildContext context) {
          return BrnCommonActionSheet(
            actions: _controller.actions,
            clickCallBack: (
              int index,
              BrnCommonActionSheetItem actionEle,
            ) async {
              Permission permission;
              String tipMsg;

              if (index == 0) {
                permission = Permission.camera;
                tipMsg = '拍摄照片需要用到相机权限';
              } else {
                permission = await CommonUtils.getRequiredPhotosPermission();
                tipMsg = '选择照片需要用到相册权限';
              }

              permissionUtil.requestPermission(permission, tipMsg: tipMsg, requestSuccessFun: () {
                selectImage(position, (index == 0) ? 1 : 0);
              });
            },
          );
        });
  }

  //0是拍照，1是相册选
  Future<void> selectImage(int position, int imageSource) async {
    Media? photo;
    if (imageSource == 0) {
      List<Media> pickerPaths = await ImagePickers.pickerPaths();
      if (pickerPaths.isNotEmpty) {
        photo = pickerPaths[0];
      }
    } else {
      photo = await ImagePickers.openCamera();
    }
    if (photo == null) {
      return;
    }
    print('拿到图片了' + photo.path.toString());
    //拿到图进行上传的操作
    BrnToast.show("识别中，请稍等", context);
    QiNiuUtils(photo.path, statusCallback: (status) {
      print("七牛上传状态---$status");
    }, successCallback: (keyUrl, hashUrl) {
      print("七牛上传成功--$keyUrl");
      switch (position) {
        case 0:
          _controller.idZUrl.value = keyUrl!;
          _presenter?.ocrIdCard();
          break;
        case 1:
          _controller.idFUrl.value = keyUrl!;
          break;
      }
    }, errorCallback: (error) {
      print("七牛上传失败--$error");
      BrnToast.show("上传失败", context);
    }).upload();
  }

  @override
  bool get wantKeepAlive => true; // 保持活跃

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffIdentityInfoPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onCreateSuccess(CreateStaffEntity data) {
    ///创建成功 回调 情况
    widget.onNext(
      data.uuid ?? '',
      EntryConditionEntity(
        beforeEntry: _controller.beforeEntry.value,
        isDraft: _controller.isDraft.value,
        uuid: _controller.before_entry_and_draft_uuid.value,
      ),
    );
  }

  @override
  void onCheckIdNumberAuthSuccess(CheckIdNumberAuthEntity data) {
    ///1一致 2不一致
    if (data.isAuth == 1) {
      ///更新原有的数据
      _presenter?.updateStaffInfo(widget.channel == 3 ? false : true);
    } else {
      if (httpConfig.role_id == HttpConfig.ROLE_HR_ID || httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID || httpConfig.role_id == HttpConfig.ROLE_REGIONAL_MANAGER_ID || httpConfig.role_id == HttpConfig.ROLE_LEADER_ID) {
        Toast.show('员工姓名和身份证不匹配！');
        return;
      }
      DialogManager.setDialogShowing(true);
      showDialog<int>(
        context: context,
        barrierDismissible: false, // 禁止点击外部区域关闭对话框
        builder: (context) {
          return CustomEntryStaffDialog(
            title: '温馨提示',
            content: '员工姓名和身份证不匹配！',
            items: const [
              '忽略，继续录入',
              '取消，不录入',
            ],
            // 外部传入的列表
            coloredItemIndex: 1,
            onItemSelected: (index) {
              DialogManager.setDialogShowing(false);
              print('选中的下标: $index');
              switch (index) {
                case 0:
                  _controller.isNeedAuth.value = false;
                  _controller.before_entry_and_draft_uuid.value = '';

                  ///更新原有的数据
                  _presenter?.updateStaffInfo(widget.channel == 3 ? false : true);
                  break;
              }
            },
          );
        },
      );
    }
  }

  @override
  void onCheckIdNumberSuccess(CheckIdNumberEntity data) {
    ///0正常 1已有草稿档案 2已有离职档案 3姓名身份证号码不一致
    switch (data.status) {
      case 0:
        _presenter?.createStaff();
        break;
      case 1:
        DialogManager.setDialogShowing(true);
        showDialog<int>(
          context: context,
          barrierDismissible: false, // 禁止点击外部区域关闭对话框
          builder: (context) {
            return CustomEntryStaffDialog(
              title: '温馨提示',
              content: '已存在该员工的审批草稿，如果继续会删除原草稿，是否继续？',
              items: const [
                '取消',
                '忽略草稿，继续录入',
                '以草稿为准，继续录入',
              ],
              // 外部传入的列表
              coloredItemIndex: 2,
              onItemSelected: (index) {
                DialogManager.setDialogShowing(false);

                switch (index) {
                  case 1:

                    ///删除之前的草稿
                    _controller.isDraft.value = true;
                    _controller.beforeEntry.value = false;
                    _controller.before_entry_and_draft_uuid.value = '';
                    _presenter?.deleteStaffDraft(data.uuid ?? '');
                    break;
                  case 2:

                    ///先拿到草稿信息，反选内容出来 并删了已经存在的草稿详情
                    _controller.isDraft.value = true;
                    _controller.beforeEntry.value = false;
                    _controller.before_entry_and_draft_uuid.value = data.uuid ?? '';
                    _controller.uuid.value = _controller.before_entry_and_draft_uuid.value;

                    ///请求草稿详情
                    _presenter?.getStaffOne(true);
                    _presenter?.deleteStaffDraft(data.uuid ?? '');
                    break;
                }
              },
            );
          },
        );
        break;
      case 2:
        DialogManager.setDialogShowing(true);
        showDialog<int>(
          context: context,
          barrierDismissible: false, // 禁止点击外部区域关闭对话框
          builder: (context) {
            return CustomEntryStaffDialog(
              title: '温馨提示',
              content: '该员工以前在本司工作过，是否延用以前登记的信息？',
              items: const [
                '取消',
                '否，继续录入',
                '是，延用以前登记的信息',
              ],
              // 外部传入的列表
              coloredItemIndex: 2,
              onItemSelected: (index) {
                DialogManager.setDialogShowing(false);

                switch (index) {
                  case 1:
                    _controller.beforeEntry.value = true;
                    _controller.isNeedAuth.value = true;
                    _presenter?.createStaff();
                    break;
                  case 2:

                    ///如果延用以前的信息，那么就不用校验离职了。直接进
                    _controller.beforeEntry.value = true;
                    _controller.before_entry_and_draft_uuid.value = data.uuid ?? '';
                    _controller.uuid.value = _controller.before_entry_and_draft_uuid.value;

                    ///请求档案详情
                    _presenter?.getStaffOne(false);
                    break;
                }
              },
            );
          },
        );
        break;
      case 3:
        if (httpConfig.role_id == HttpConfig.ROLE_HR_ID || httpConfig.role_id == HttpConfig.ROLE_PROJECT_OWNER_ID || httpConfig.role_id == HttpConfig.ROLE_REGIONAL_MANAGER_ID || httpConfig.role_id == HttpConfig.ROLE_LEADER_ID) {
          Toast.show('员工姓名和身份证不匹配！');
          return;
        }
        DialogManager.setDialogShowing(true);
        showDialog<int>(
          context: context,
          barrierDismissible: false, // 禁止点击外部区域关闭对话框
          builder: (context) {
            return CustomEntryStaffDialog(
              title: '温馨提示',
              content: '员工姓名和身份证不匹配！',
              items: const [
                '忽略，继续录入',
                '取消，不录入',
              ],
              // 外部传入的列表
              coloredItemIndex: 1,
              onItemSelected: (index) {
                DialogManager.setDialogShowing(false);
                print('选中的下标: $index');
                switch (index) {
                  case 0:
                    _controller.before_entry_and_draft_uuid.value = '';
                    _controller.isNeedAuth.value = false;
                    _presenter?.createStaff();
                    break;
                }
              },
            );
          },
        );
        break;
    }
  }

  ///超级管理的新建的dialog
  void showSuperManagerTipDialog() {
    DialogManager.setDialogShowing(true);
    showDialog<int>(
      context: context,
      barrierDismissible: false, // 禁止点击外部区域关闭对话框
      builder: (context) {
        return CustomEntryStaffDialog(
          title: '温馨提示',
          content: '您未录入身份证号，会导致无法给该成员做信用查询、签署电子合同、买保险等，是否确认继续？',
          items: const [
            '忽略，继续录入',
            '取消，不录入',
          ],
          // 外部传入的列表
          coloredItemIndex: 1,
          onItemSelected: (index) {
            DialogManager.setDialogShowing(false);

            switch (index) {
              case 0:
                _controller.isNeedAuth.value = false;

                ///uuid为空就是新建 否则就是更新
                if (TextUtil.isEmpty(_controller.uuid.value)) {
                  _presenter?.createStaff();
                } else {
                  _presenter?.updateStaffInfo(widget.channel == 3 ? false : true);
                }
                break;
            }
          },
        );
      },
    );
  }

  void showCityDialog() {
    FocusScope.of(context).unfocus();

    ///这里支持 省市区 有 Brn2CityRowDelegate 跟 Brn3CityRowDelegate 看代码哈
    BrnMultiDataPicker(
      context: context,
      title: '选择户籍城市',
      delegate: Brn2CityRowDelegate(
        firstSelectedIndex: _controller.firstSelectedIndex,
        secondSelectedIndex: _controller.secondSelectedIndex,
        cityData: _controller.cityList.value,
      ),
      confirmClick: (selectedItemLists) {
        if (selectedItemLists.isNotEmpty) {
          _controller.firstSelectedIndex = selectedItemLists[0];
          _controller.secondSelectedIndex = selectedItemLists[1];
          // 获取选中的省对象
          CityDataList province = _controller.cityList.value[_controller.firstSelectedIndex];
          // 获取选中的城市对象
          CityDataListList city = province.list![_controller.secondSelectedIndex];

          ///赋值給参数
          _controller.city.value = '${province.name}-${city.name}';
          _controller.cityId.value = city.id ?? '';
          // 显示选中的省和市的信息
          print(
            "Selected Province: ${province.name} (${province.id}), Selected City: ${city.name} (${city.id})",
          );
        }
      },
    ).show();
  }

  @override
  void onGetCitySuccess() {
    showCityDialog();
  }
}
