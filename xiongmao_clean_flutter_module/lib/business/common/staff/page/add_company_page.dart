import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/constant.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../company/bean/department_company_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/add_company_controller.dart';
import '../iview/add_company_iview.dart';
import '../persenter/add_company_persenter.dart';

/// 添加/编辑总部人员
class AddCompanyPage extends StatefulWidget {
  String? uuid = ""; ////1是公司，2是常用联系人
  bool ROLE_MANGER = false;
  bool ROLE_HR = false;
  bool ROLE_REGIONAL_MANAGER = false;
  bool ROLE_SUPER_MANGER = false;

  AddCompanyPage({Key? key, this.uuid = "", required this.ROLE_MANGER, required this.ROLE_HR, required this.ROLE_REGIONAL_MANAGER, required this.ROLE_SUPER_MANGER});

  @override
  _MyState createState() => _MyState();
}

//集成分类 然后实现使用
class _MyState extends State<AddCompanyPage> with BasePageMixin<AddCompanyPage, PowerPresenter<dynamic>> implements AddCompanyIView {
  AddCompanyPresenter? _presenter;
  final AddCompanyController _controller = AddCompanyController();
  var tabTitle = "";
  final _nameController = TextEditingController();
  final _mobileController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (TextUtil.isEmpty(widget.uuid)) {
      tabTitle = "添加总部成员";
    } else {
      tabTitle = "编辑总部成员";
      _presenter?.requestCompanyContactDetail(widget.uuid);
    }
  }

  /// 判断是否显示删除按钮
  bool _shouldShowDeleteButton() {
    // 只有在编辑模式下才能删除
    if (TextUtil.isEmpty(widget.uuid)) return false;

    // 当前用户的角色
    final currentRoleId = httpConfig.role_id;

    // 目标员工的角色
    final targetRoleId = _controller.companyContactEntity.value.role_id; // 假设 widget.role_id 表示目标员工的角色

    // 超级管理员不能自己删除自己
    if (currentRoleId == targetRoleId) {
      return false;
    }

    // 超级管理员：可以删除任何总部成员
    if (currentRoleId == HttpConfig.ROLE_SUPER_MANGER_ID) {
      return true;
    }

    // 管理员：只能删除人事和大区经理
    if (currentRoleId == HttpConfig.ROLE_MANGER_ID) {
      return targetRoleId == HttpConfig.ROLE_HR_ID || targetRoleId == HttpConfig.ROLE_REGIONAL_MANAGER_ID || targetRoleId == '0';
    }

    // 其他角色无权限删除
    return false;
  }

  /// 判断是否显示编辑
  bool _shouldShowButton() {
    // 只有在编辑模式下才能
    if (TextUtil.isEmpty(widget.uuid)) return true;

    // 当前用户的角色
    final currentRoleId = httpConfig.role_id;

    // 目标员工的角色
    final targetRoleId = _controller.companyContactEntity.value.role_id; // 假设 widget.role_id 表示目标员工的角色

    // 超级管理员不能自己删除自己
    if (currentRoleId == targetRoleId) {
      return false;
    }

    // 超级管理员：可以删除任何总部成员
    if (currentRoleId == HttpConfig.ROLE_SUPER_MANGER_ID) {
      return true;
    }

    // 管理员：只能删除人事和大区经理
    if (currentRoleId == HttpConfig.ROLE_MANGER_ID) {
      return targetRoleId == HttpConfig.ROLE_HR_ID || targetRoleId == HttpConfig.ROLE_REGIONAL_MANAGER_ID || targetRoleId == '0';
    }

    // 其他角色无权限删除
    return false;
  }

  bool _shouldShowDepartmentButton() {
    // 只有在编辑模式下才能
    if (TextUtil.isEmpty(widget.uuid)) return true;

    // 当前用户的角色
    final currentRoleId = httpConfig.role_id;

    // 目标员工的角色
    final targetRoleId = _controller.companyContactEntity.value.role_id; // 假设 widget.role_id 表示目标员工的角色

    // 超级管理员：可以删除任何总部成员
    if (currentRoleId == HttpConfig.ROLE_SUPER_MANGER_ID) {
      return true;
    }

    // 管理员：只能删除人事和大区经理
    if (currentRoleId == HttpConfig.ROLE_MANGER_ID) {
      return targetRoleId == HttpConfig.ROLE_HR_ID || targetRoleId == HttpConfig.ROLE_REGIONAL_MANAGER_ID || targetRoleId == '0';
    }

    // 其他角色无权限删除
    return false;
  }

  @override
  Widget build(BuildContext context) {
    LogUtil.e("-----刷新-----刷新------");
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: tabTitle,
            actionName: _shouldShowDeleteButton() ? '删除' : '',
            onPressed: () {
              if (_shouldShowDeleteButton()) {
                DialogManager.showConfirmDialog(
                  context: context,
                  title: '提示',
                  cancel: '取消',
                  confirm: '确定',
                  message: '是否删除该员工？',
                  onConfirm: () {
                    _presenter?.delCompanyStaff(widget.uuid ?? '');
                  },
                  onCancel: () {},
                );
              }
            },
            onBack: () {
              BoostNavigator.instance.pop();
            },
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: WillPopScope(
            child: SafeArea(
              child: Column(
                children: [
                  BrnTextInputFormItem(
                    title: "姓名",
                    isRequire: true,
                    hint: "请输入",
                    controller: _nameController,
                    onChanged: (newValue) {
                      _nameController.text = newValue;
                    },
                  ),
                  Gaps.line,
                  BrnTextInputFormItem(
                    title: "手机号",
                    isRequire: true,
                    label: _mobileController.text,
                    maxCharCount: 11,
                    hint: "请输入",
                    inputType: BrnInputType.phone,
                    controller: _mobileController,
                    onChanged: (newValue) {
                      _mobileController.text = newValue;
                    },
                  ),
                  Gaps.line,
                  BrnTextSelectFormItem(
                    isRequire: true,
                    isEdit: _shouldShowButton(),
                    title: "系统操作权限",
                    hint: "请选择",
                    value: _controller.roleName.value,
                    onTap: () {
                      if (widget.uuid != null && widget.uuid != "") {
                        if ("1" == (_controller.companyContactEntity.value.is_super_admin)) {
                          //说明是超级管理员
                          return;
                        }

                        if (widget.ROLE_MANGER && "1" == (_controller.companyContactEntity.value.role_id)) {
                          //管理员不能编辑管理员
                          return;
                        }
                        if (widget.ROLE_HR || widget.ROLE_REGIONAL_MANAGER) {
                          //hr 跟大区经理，都不让点击
                          return;
                        }
                      }
                      MyLog.e("-----roleName----" + _controller.roleName.value);
                      //给原生
                      // BoostChannel.instance.sendEventToNative("native_CommonEvent",
                      // {"method": "goto_add_permission_page","channel":"1","selectText":_controller.companyContactEntity.value.role_name??""});
                      BoostNavigator.instance.push('permissionPage', arguments: {"channel": "1", "selectText": _controller.roleName.value}).then((value) {
                        if (value == null) {
                          return;
                        }
                        var value2 = value as PermissionData;
                        _controller.updatePermissionInfo(value2.id, value2.name);
                      });
                    },
                  ),
                  Gaps.line,
                  BrnTextSelectFormItem(
                    isRequire: true,
                    isEdit: _shouldShowDepartmentButton(),
                    title: "所属部门",
                    hint: "请选择",
                    value: _controller.departmentName.value ?? "",
                    onTap: () {
                      if (widget.uuid != null && widget.uuid != "") {
                        if (widget.ROLE_MANGER && "1" == (_controller.companyContactEntity.value.role_id)) {
                          //管理员不能编辑管理员
                          return;
                        }
                        if (widget.ROLE_HR || widget.ROLE_REGIONAL_MANAGER) {
                          //hr 跟大区经理，都不让点击
                          return;
                        }
                      }
                      BoostNavigator.instance.push('companyManagerStaffBelongPage').then((value) {
                        if (value == null) {
                          return;
                        }
                        var value2 = value as DepartmentCompanyList;
                        _controller.updateDepartmentInfo(value2.departmentName ?? "", value2.uuid ?? "");
                      });
                    },
                  ),
                  Gaps.line,
                ],
              ),
            ),
            onWillPop: () async {
              if (DialogManager.hasOpenDialogs()) {
                DialogManager.dismissAllDialogs(context);
                return false; // Prevent the app from popping the route
              } else {
                return true; // Allow the app to pop the route
              }
            },
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: BrnBigMainButton(
              themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 14),
              title: '保存',
              onTap: () {
                var name = _nameController.text;
                var mobile = _mobileController.text;
                if (TextUtil.isEmpty(name)) {
                  Toast.show("请输入姓名");
                  return;
                }
                if (TextUtil.isEmpty(mobile)) {
                  Toast.show("请输入手机号");
                  return;
                }
                if (TextUtil.isEmpty(_controller.roleId.value)) {
                  Toast.show("请选择权限");
                  return;
                }
                if (TextUtil.isEmpty(_controller.departmentUuid.value)) {
                  Toast.show("请选择所属部门");
                  return;
                }
                var hashMap = HashMap<String, String>();
                hashMap["user_name"] = name;
                hashMap["mobile"] = mobile;
                hashMap["role_id"] = _controller.roleId.value;
                hashMap["department_uuid"] = _controller.departmentUuid.value;
                if (!TextUtil.isEmpty(widget.uuid)) {
                  hashMap["uuid"] = widget.uuid!;
                }
                _presenter?.saveCompanyStaff(hashMap);
              },
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddCompanyPresenter();
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void requestCompanyContactDetail(CompanyContactEntity data) {
    _controller.updateCompanyContactEntity(data);
    _nameController.text = data.user_name ?? "";
    _mobileController.text = data.mobile ?? "";
    setState(() {});
  }

  @override
  void saveCompanyStaff(Object data) {
    Toast.show("成功");
    //通知原生花名册页面刷新列表
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "save_head_office"});
    BoostNavigator.instance.pop();
  }
}
