import 'dart:collection';
import 'dart:io';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:path_provider/path_provider.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/create_staff_entity.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/qiniu/qiniu_utils.dart';
import '../controller/add_staff_sign_hand_controller.dart';
import '../iview/add_staff_sign_hand_iview.dart';
import '../persenter/add_staff_sign_hand_persenter.dart';
import 'dart:ui' as ui;

/// 手写签名区
class AddStaffSignHandPage extends StatefulWidget {
  String? uuid = "";

  AddStaffSignHandPage({
    Key? key,
    this.uuid = "",
  });

  @override
  _AddStaffSignHandPageState createState() => _AddStaffSignHandPageState();
}

class _AddStaffSignHandPageState extends State<AddStaffSignHandPage> with BasePageMixin<AddStaffSignHandPage, PowerPresenter<dynamic>> implements AddStaffSignHandIView {
  AddStaffSignHandPresenter? _presenter;
  final AddStaffSignHandController _controller = AddStaffSignHandController();

  GlobalKey globalKey = GlobalKey();
  GlobalKey gestureDetectorKey = GlobalKey(); // 新增 GestureDetector 的 GlobalKey

  @override
  void initState() {
    super.initState();
    _controller.uuid.value = widget.uuid ?? '';

    /// 强制横屏
    SystemChrome.setPreferredOrientations([DeviceOrientation.landscapeRight]);
  }

  @override
  void dispose() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.landscapeRight,
    ]);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: CommonUtils.getSimpleText('签字确认即代表对填写信息的认可，对提交材料负责', 18, Colours.base_primary_text_title),
        actions: [
          InkWell(
            child: CommonUtils.getSimpleText('清除签名', 16, Colours.base_primary_text_title),
            onTap: () {
              setState(() {
                _controller.points.clear(); // 清空画板
              });
            },
          ),
          Gaps.hGap32,
          InkWell(
            child: CommonUtils.getSimpleText('完成', 16, Colours.base_primary),
            onTap: () async {
              if (_controller.points.isEmpty) {
                Toast.show("请签名后，再尝试提交");
                return;
              }

              ///生成图片上传
              String? tempImagePath = await _saveToImage(); // 获取临时文件路径
              if (tempImagePath != null) {
                // 在这里进行上传操作
                print('Temporary image path: $tempImagePath');
                // 示例上传函数调用
                QiNiuUtils(tempImagePath, statusCallback: (status) {
                  print("七牛上传状态---$status");
                }, successCallback: (keyUrl, hashUrl) {
                  print("七牛上传成功--$keyUrl");

                  ///请求接口更新数据
                  HashMap<String, String> hashMap = HashMap();
                  hashMap["op_type"] = 'approve';
                  hashMap["uuid"] = _controller.uuid.value;
                  hashMap["sign_pic"] = '$keyUrl';
                  _presenter?.updateStaffInfo(true, hashMap);
                }, errorCallback: (error) {
                  print("七牛上传失败--$error");
                  Toast.show('上传失败');
                }).upload();
              } else {
                Toast.show('图片生成异常，请退出重试。');
              }
            },
          ),
          Gaps.hGap20,
        ],
      ),
      body: Container(
        color: Colours.base_primary_bg_page,
        margin: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
        child: RepaintBoundary(
          key: globalKey,
          child: GestureDetector(
            key: gestureDetectorKey, // 设置 GestureDetector 的 Key
            onPanUpdate: (details) {
              setState(() {
                RenderBox renderBox = gestureDetectorKey.currentContext!.findRenderObject() as RenderBox; // 使用 GestureDetector 的 RenderObject
                Offset localPosition = renderBox.globalToLocal(details.globalPosition);
                _controller.points.add(localPosition);
              });
            },
            onPanEnd: (details) {
              _controller.points.add(null); // 添加一个空点，表示一笔结束
            },
            child: CustomPaint(
              size: Size(MediaQuery.of(context).size.width, MediaQuery.of(context).size.height - AppBar().preferredSize.height),
              painter: SignPainter(_controller.points),
            ),
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = AddStaffSignHandPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  void onUpdate(CreateStaffEntity data) {
    ///提交成功 关闭界面
    BoostNavigator.instance.pop(data.uuid);
  }

  Future<String?> _saveToImage() async {
    try {
      RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
      ui.Image image = await boundary.toImage(pixelRatio: 3.0);
      ByteData? byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      if (byteData != null) {
        Uint8List pngBytes = byteData.buffer.asUint8List();
        final directory = await getTemporaryDirectory();
        final imagePath = '${directory.path}/signature_${DateTime.now().millisecondsSinceEpoch}.png';
        File file = File(imagePath);
        await file.writeAsBytes(pngBytes);
        return imagePath; // 返回临时文件路径
      } else {
        Get.snackbar("失败", "无法截取图片");
      }
    } catch (e) {
      print(e.toString());
      Get.snackbar("错误", e.toString());
    }
    return null;
  }
}

class SignPainter extends CustomPainter {
  final List<Offset?> points;

  SignPainter(this.points);

  @override
  void paint(Canvas canvas, Size size) {
    Paint paint = Paint()
      ..color = Colors.black
      ..strokeCap = StrokeCap.round
      ..strokeWidth = 5.0;

    for (int i = 0; i < points.length - 1; i++) {
      if (points[i] != null && points[i + 1] != null) {
        canvas.drawLine(points[i]!, points[i + 1]!, paint);
      }
    }
  }

  @override
  bool shouldRepaint(SignPainter oldDelegate) {
    return oldDelegate.points != points;
  }
}
