import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_all_list_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_main_info_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/contract/bean/contract_tag_one_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';

import '../../../../mvp/mvps.dart';
import '../../contract/bean/contract_main_electronic_entity.dart';
import '../bean/ele_status_entity.dart';

abstract class QuickDepartPageIView extends IMvpView {
  void requestStaffDetail(StaffDetailEntity data) {}

  void requestDepartDetail(StaffDetailEntity data) {}

  void requestMembersDepart(Object data) {}

  void requestContractTagList(ContractTagOneEntity data) {}

  void contractInfo(ContractMainInfoEntity data) {}

  void contractSettingInfo(EleStatusEntity data) {}

  void requestContractList(ContractAllListEntity data) {}

  void requestContractCreate(ContractCreateEntity data) {}

  void requestContractRegister(ContractRegisterEntity data) {}

}
