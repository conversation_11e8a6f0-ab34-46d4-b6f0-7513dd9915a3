import 'package:xiongmao_clean_flutter_module/business/common/project/bean/job_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';

import '../../../../mvp/mvps.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/staff_nation_entity.dart';

abstract class AddStaffPositionArrangementInfoIView extends IMvpView {

  void onUpdate(CreateStaffEntity data){}

  void getCurrentProjectJobs(JobManagerEntity data) {}
}
