import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/add_company_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/add_staff_main_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/add_staff_sign_hand_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/contract_staff_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/permission_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/quick_depart_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/role_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/select_personnel_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/staff_already_resign_main_list_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/staff_one_web_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/page/staff_resign_main_list_page.dart';

import '../../../net/http_config.dart';

/// 添加总部人员
const addCompanyPage = "addCompanyPage";

///添加项目成员
const addStaffPage = "AddStaffPage";

///手写签名
const addStaffSignHandPage = "AddStaffSignHandPage";

/// 权限界面
const permissionPage = "permissionPage";

/// 快速离职
const quickDeparkPage = "quickDeparkPage";

/// 员工合同记录
const contractStaffPage = "contractStaffPage";

/// 全局选择人员的内容
const selectPersonnelPage = "selectPersonnelPage";

///选择要离职的人员
const staffResignMainListPage = "StaffResignMainListPage";

///已经离职的员工
const staffAlreadyResignMainListPage = "StaffAlreadyResignMainListPage";

///员工档案详情
const staffOneWebPage = "StaffOneWebPage";

///角色单选、多选
const rolePage = "RolePage";

/// 根据页面名称定义对应的页面对象，相当于activity的路由
Map<String, FlutterBoostRouteFactory> addCompanyRouterMap = {
  ///总部成员
  addCompanyPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return AddCompanyPage(
            uuid: map["uuid"],
            ROLE_MANGER: map["ROLE_MANGER"],
            ROLE_HR: map["ROLE_HR"],
            ROLE_REGIONAL_MANAGER: map["ROLE_REGIONAL_MANAGER"],
            ROLE_SUPER_MANGER: map['ROLE_SUPER_MANGER'],
          );
        });
  },

  ///项目成员
  addStaffPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return AddStaffMainPage(
            uuid: map["uuid"],
            applicationNo: map["application_no"],
            channel: map["channel"] ?? 0,
            jumpIndex: map["jump_index"] ?? 0,
            userName: map["user_name"] ?? '',
            againEntry: map["again_entry"] ?? false,
          );
        });
  },

  ///手写签名
  addStaffSignHandPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return AddStaffSignHandPage(
            uuid: map["uuid"],
          );
        });
  },

  ///权限界面
  permissionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return PermissionPage(
            channel: map['channel'] ?? '',
            selectText: map['selectText'] ?? '',
          );
        });
  },

  ///快速离职
  quickDeparkPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return QuickDeparkPage(
            uuid: map['uuid'] ?? '',
            application_no: map['application_no'] ?? '',
          );
        });
  },

  ///员工合同记录
  contractStaffPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return ContractStaffPage(
            uuid: map['uuid'] ?? '',
            user_uuid: map['user_uuid'] ?? '',
          );
        });
  },

  /// 选择人员
  selectPersonnelPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          int channel = map['channel'] ?? 0; //用来区分接口
          String title = map['title'] ?? '选择人员';
          bool multiple = map['multiple'] ?? false;
          String status = map['status'] ?? '0'; //状态 0全部 1在职 2离职
          String isHeadOffice = map['is_head_office'] ?? '0'; //是否总部员工0全部 1是 2否
          String roleId = map['role_id'] ?? '';
          String projectUuid = map['project_uuid'] ?? ''; //获取指定项目
          String contractStartDate = map['contract_start_date'] ?? ''; //合同开启时间
          String contractEndDate = map['contract_end_date'] ?? ''; //合同结束时间
          String uuids = map['uuids'] ?? ''; //已选择的数据

          return SelectPersonnelPage(
            channel: channel,
            title: title,
            multiple: multiple,
            status: status,
            role_id: roleId,
            is_head_office: isHeadOffice,
            project_uuid: projectUuid,
            contract_start_date: contractStartDate,
            contract_end_date: contractEndDate,
            uuids: uuids,
          );
        });
  },

  ///选择要离职的人员
  staffResignMainListPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return StaffResignMainListPage();
        });
  },

  ///已经离职的员工
  staffAlreadyResignMainListPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return StaffAlreadyResignMainListPage();
        });
  },

  ///员工档案详情
  staffOneWebPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return StaffOneWebPage(
            uuid: map['uuid'] ?? '',
          );
        });
  },

  ///角色 单选、多选
  rolePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          List<String> choiceUuids = map['choice_uuids'] ?? [];
          bool multiple = false; //默认是多选
          if (map['multiple'] != null) {
            multiple = map['multiple'] as bool;
          }
          return RolePage(
            multiple: multiple,
            choice_uuids: choiceUuids,
          );
        });
  },
};
