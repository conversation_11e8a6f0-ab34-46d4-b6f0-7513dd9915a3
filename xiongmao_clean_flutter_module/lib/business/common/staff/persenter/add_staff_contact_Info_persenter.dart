import 'dart:collection';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../res/constant.dart';
import '../../../../util/log_utils.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../controller/add_staff_contact_Info_controller.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_contact_Info_iview.dart';
import '../iview/add_staff_main_iview.dart';

class AddStaffContactInfoPresenter extends BasePagePresenter<AddStaffContactInfoIView> with WidgetsBindingObserver {
  AddStaffContactInfoController controller;

  AddStaffContactInfoPresenter(this.controller);

  ///获取元数据
  Future<dynamic> getMetaData() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<GetMetaEntity>(Method.get, url: HttpApi.GET_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        SpUtil.putString(Constant.META_DATA, json.encode(data));
        view.onGetMeta(data);
      }
    });
  }

  ///获取已经存在的档案、草稿的详情 注意未完成登记的员工都算是草稿，签字后的员工是档案 isDraft ? 草稿 : 档案
  Future<dynamic> getStaffOne(bool before, bool isDraft, String uuid) {
    HashMap<String, String> hashMap = HashMap();

    ///如果是获取草稿的详情，并且审批编号不为null 那么就优先用这个
    if (before) {
      if (isDraft && !TextUtil.isEmpty(controller.applicationNo.value)) {
        hashMap['application_no'] = controller.applicationNo.value;
      } else {
        if (!TextUtil.isEmpty(uuid)) {
          hashMap['uuid'] = uuid;
        }
      }
    } else {
      if (!TextUtil.isEmpty(uuid)) {
        hashMap['uuid'] = uuid;
      }
    }

    ///根据自身的情况来获取内容 这里的参数去看下接口文档
    hashMap['is_contact'] = '1';
    return requestNetwork<StaffDetailEntity>(Method.get, url: isDraft ? HttpApi.GET_ARCHIVES_DRAFT_DETAIL : HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillData(data);
      }
    });
  }

  ///更新云端数据 isDraft ? 草稿 : 档案
  Future<dynamic> updateStaffInfo(bool isDraft, HashMap<String, String> hashMap) {
    return requestNetwork<CreateStaffEntity>(Method.get, url: isDraft ? HttpApi.UPDATE_ARCHIVES_DRAFT : HttpApi.UPDATE_ARCHIVES, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onUpdate(data);
      }
    });
  }
}
