import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:intl/intl.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../schedule/bean/schedule_export_data_entity.dart';
import '../controller/staff_already_resign_main_list_controller.dart';
import '../iview/staff_resign_main_list_iview.dart';

/// 选择离职员工去离职
class StaffAlreadyResignMainListPresenter extends BasePagePresenter<StaffResignManListIView> with WidgetsBindingObserver {
  StaffAlreadyResignMainListController controller;

  StaffAlreadyResignMainListPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    getStaffListManager();
  }

  void loadMore() {
    _page++;
    getStaffListManager();
  }

  ///获取已经离职的人
  Future<dynamic> getStaffListManager() {
    var params = <String, String>{};
    params["page"] = "$_page";
    if (!TextUtil.isEmpty(controller.projectUuid.value) && controller.projectUuid.value != "0") {
      params["project_uuid"] = controller.projectUuid.value;
    }
    params["status"] = "2"; //状态 0全部 1在职 2离职
    params["is_head_office"] = "0"; ////是否总部员工0全部 1是 2否
    params["keyword"] = controller.keyword.value;
    params['map_code'] = 'gao_de';
    return requestNetwork<ProjectArchivesEntity>(Method.get, url: HttpApi.GET_STAFF_ALL_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取项目的的row
  Future<dynamic> getProjectRowListManager() {
    var params = <String, String>{};
    params["user_status"] = "2"; //状态 0全部 1在职 2离职
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ProjectManagerEntity>(Method.get, url: HttpApi.GET_GENERAL_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initRowMyList(data.list ?? []);

          ///计算
          if (controller.listRow!.isNotEmpty) {
            controller.projectUuid.value = controller.listRow.value[0].uuid ?? "";
            onRefresh();
          }
        } else {
          controller.updateRowMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取下载的审批
  Future<dynamic> requestDownloadResignRecord() {
    var params = <String, String>{};
    params["op_type"] = "user";
    params["status"] = "2";
    params["project_uuid"] = "${controller.projectUuid}";
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<ScheduleExportDataEntity>(Method.get, url: HttpApi.APPROVE_RECORD_EXPORT_DOWNLOAD, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "export_download_url", 'url': data.downloadUrl ?? '', 'fileName': '${httpConfig.company_name}-离职档案-${DateFormat('yyyy-MM-dd-HH-mm').format(DateTime.now())}'});
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }
}
