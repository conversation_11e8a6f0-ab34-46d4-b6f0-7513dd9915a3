import 'dart:collection';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../net/http_config.dart';
import '../../../../res/constant.dart';
import '../../../../util/log_utils.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/city_data_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_main_iview.dart';

class AddStaffIdentityInfoPresenter extends BasePagePresenter<AddStaffIdentityInfoIView> with WidgetsBindingObserver {
  AddStaffIdentityInfoController controller;

  AddStaffIdentityInfoPresenter(this.controller);

  ///身份证识别Ocr
  Future<dynamic> ocrIdCard() {
    HashMap<String, String> hashMap = HashMap();
    hashMap['card_url'] = controller.idZUrl.value;
    return requestNetwork<OcrIdCardEntity>(Method.get, url: HttpApi.OCR_ID_CARD, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.ocrFill(data);
      }
    });
  }

  ///新建员工档案
  Future<dynamic> createStaff() {
    return requestNetwork<CreateStaffEntity>(Method.get, url: HttpApi.CREATE_MEMBERS, queryParameters: getHashMap(), isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onCreateSuccess(data);
      }
    });
  }

  ///身份证二要素校验 产品希望的是每次 只要点击，都要去检查身份证二要素
  Future<dynamic> checkIdCardInfoAuth() {
    HashMap<String, String> hashMap = HashMap();
    hashMap['id_number'] = controller.idNumberController.text;
    hashMap['user_name'] = controller.nameController.text;
    return requestNetwork<CheckIdNumberAuthEntity>(Method.get, url: HttpApi.CHECK_ID_NUMBER_AUTH, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onCheckIdNumberAuthSuccess(data);
      }
    });
  }

  ///草稿档案身份证校验
  Future<dynamic> checkIdCardInfo() {
    HashMap<String, String> hashMap = HashMap();
    hashMap['id_number'] = controller.idNumberController.text;
    hashMap['user_name'] = controller.nameController.text;
    hashMap['is_leave'] = controller.isLeave.value ? '1' : '0'; //是否校验离职档案 1是0否
    return requestNetwork<CheckIdNumberEntity>(Method.get, url: HttpApi.CHECK_ID_NUMBER, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onCheckIdNumberSuccess(data);
      }
    });
  }

  ///获取已经存在的档案、草稿的详情 注意未完成登记的员工都算是草稿，签字后的员工是档案 isDraft ? 草稿 : 档案
  Future<dynamic> getStaffOne(bool isDraft) {
    HashMap<String, String> hashMap = HashMap();

    if (!TextUtil.isEmpty(controller.uuid.value)) {
      hashMap['uuid'] = controller.uuid.value;
    }
    if (!TextUtil.isEmpty(controller.applicationNo.value)) {
      hashMap['application_no'] = controller.applicationNo.value;
    }

    ///根据自身的情况来获取内容 这里的参数去看下接口文档
    hashMap['is_identity'] = '1';
    return requestNetwork<StaffDetailEntity>(Method.get, url: isDraft ? HttpApi.GET_ARCHIVES_DRAFT_DETAIL : HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.isDataFetched.value = true;
        controller.fillData(data);
      }
    });
  }

  ///更新云端数据 isDraft ? 草稿 : 档案
  Future<dynamic> updateStaffInfo(bool isDraft) {
    HashMap<String, String> hashMap = getHashMap();
    hashMap['op_type'] = 'identity';
    hashMap['uuid'] = controller.uuid.value;
    hashMap['is_identity'] = '1';

    ///如果是编辑的情况下才需要传递
    if (!isDraft) {
      ///其他角色就传递
      if(httpConfig.role_id != HttpConfig.ROLE_SUPER_MANGER_ID){
        hashMap['is_need_auth'] = '2'; //是否忽略身份证验证
      }
    }
    return requestNetwork<CreateStaffEntity>(Method.get, url: isDraft ? HttpApi.UPDATE_ARCHIVES_DRAFT : HttpApi.UPDATE_ARCHIVES, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onCreateSuccess(data);
      }
    });
  }

  ///删除草稿详情
  Future<dynamic> deleteStaffDraft(String uuid) {
    HashMap<String, String> hashMap = HashMap();
    hashMap['uuid'] = uuid;
    return requestNetwork<Object>(Method.get, url: HttpApi.DEL_ARCHIVES_DRAFT, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      controller.isNeedAuth.value = true;
      createStaff();
    });
  }

  HashMap<String, String> getHashMap() {
    HashMap<String, String> hashMap = HashMap();
    hashMap['user_name'] = controller.nameController.text;
    if (!TextUtil.isEmpty(controller.idZUrl.value)) {
      hashMap['id_card_front'] = controller.idZUrl.value;
    }
    if (!TextUtil.isEmpty(controller.idFUrl.value)) {
      hashMap['id_card_back'] = controller.idFUrl.value;
    }
    hashMap['sex'] = '${controller.sexIndex.value + 1}';
    hashMap['id_number'] = controller.idNumberController.text;
    hashMap['birthday'] = controller.birthdayDate.value;
    hashMap['city_id'] = controller.cityId.value;
    hashMap['address'] = controller.homeAddressController.text;
    hashMap['marriage_status'] = '${controller.marriageListIndex.value + 1}';
    hashMap['nation_id'] = controller.getNationId();
    hashMap['id_start_date'] = controller.startDate.value;
    hashMap['id_end_date'] = controller.endDate.value;
    hashMap['is_id_long'] = '${(controller.idLongIndex.value == -1) ? 0 : controller.idLongIndex.value + 1}'; //是否长期有效
    hashMap['is_need_auth'] = controller.isNeedAuth.value ? '1' : '2'; //是否忽略身份证验证
    return hashMap;
  }

  ///获取名族的元数据
  Future<dynamic> getMetaDtaNation(BuildContext context) {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<StaffNationEntity>(Method.get, url: HttpApi.GET_META_DATA_NATION, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null && data.list!.isNotEmpty) {
        SpUtil.putString(Constant.NATION_DATA, json.encode(data));
        controller.nationList.value = data.list!;
        controller.showNationDialog(context, controller.nationList.value);
      }
    });
  }

  ///获取城市的元数据
  Future<dynamic> getCityMetaData() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<CityDataEntity>(Method.get, url: HttpApi.GET_CITY_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        SpUtil.putString(Constant.CITY_META_DATA, json.encode(data));
        controller.cityList.value = data.list!;
        view.onGetCitySuccess();
      }
    });
  }
}
