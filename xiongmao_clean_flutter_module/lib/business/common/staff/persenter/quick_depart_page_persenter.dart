import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_create_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/contract_register_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/user_approve_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/quick_depart_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../contract/bean/contract_all_list_entity.dart';
import '../../contract/bean/contract_main_electronic_entity.dart';
import '../../contract/bean/contract_main_info_entity.dart';
import '../../contract/bean/contract_tag_one_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/ele_status_entity.dart';
import '../bean/staff_detail_entity.dart';

class QuickDepartPagePresenter extends BasePagePresenter<QuickDepartPageIView> with WidgetsBindingObserver {
  Future<dynamic> requestStaffDetail(HashMap<String, String> hashMap) {
    return requestNetwork<StaffDetailEntity>(Method.get, url: HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestStaffDetail(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestDepartDetail(HashMap<String, String> hashMap) {
    return requestNetwork<StaffDetailEntity>(Method.get, url: HttpApi.GET_DEPART_GET_ONE, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestDepartDetail(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestMembersDepart(HashMap<String, String> hashMap) {
    return requestNetwork<Object>(Method.get, url: HttpApi.DEPOART_MEMBERS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestMembersDepart(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestContractTagList(String code) {
    var hashMap = HashMap<String, String>();
    hashMap["code"] = code;
    hashMap["n_sub_label_list"] = "true";
    return requestNetwork<ContractTagOneEntity>(Method.get, url: HttpApi.CREATE_CONTRACT_TAG_ONE_LIST, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestContractTagList(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestContractInfo(String customerType, String customerIdCard) {
    var hashMap = HashMap<String, String>();
    hashMap["customer_type"] = customerType; //帐户类型：1-员工 2-企业
    hashMap["customer_id_card"] = customerIdCard; //身份证号，帐户类型为员工时必填
    return requestNetwork<ContractMainInfoEntity>(Method.get, url: HttpApi.GET_CONRRACT_ONE, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.contractInfo(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///改逻辑，从新接口来获取企业配置 入离职接口是否开启
  Future<dynamic> getContractMainInfo(String uuid) {
    var params = <String, String>{};
    params['uuid'] = uuid;
    params['label_code'] = "SeparationContract";
    return requestNetwork<EleStatusEntity>(Method.get, url: HttpApi.GET_ELECTRONIC_CONTRACT_PAGE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.contractSettingInfo(data);
      }
    }, onError: (_, __) {});
  }

  Future<dynamic> requestContractList(String main_lable_code, String? sub_label_code) {
    var hashMap = HashMap<String, String>();
    hashMap["main_label_code"] = main_lable_code;
    hashMap["sub_label_code"] = sub_label_code ?? "";
    hashMap["n_setting"] = 'true';
    return requestNetwork<ContractAllListEntity>(Method.get, url: HttpApi.GET_CONTRACT_LIST, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestContractList(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestContractCreate(HashMap<String, String> hashMap) {
    return requestNetwork<ContractCreateEntity>(Method.get, url: HttpApi.CREATE_CONTRACT, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestContractCreate(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> requestContractRegister(String customer_type, String enforce, String customer_id_card) {
    var hashMap = HashMap<String, String>();
    hashMap["customer_type"] = customer_type; ////帐户类型：1-员工 2-企业
    hashMap["enforce"] = enforce; //强制注册：1-是 2-否（针对已经注册成功的账号需强制注册） 默认不强注册 参数为 2
    hashMap["customer_id_card"] = customer_id_card;
    return requestNetwork<ContractRegisterEntity>(Method.get, url: HttpApi.CREATE_CONTRACT_REGISTER, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestContractRegister(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
