import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/permission_entity.dart';
import '../controller/select_personnel_controller.dart';
import '../iview/permiss_iview.dart';
import '../iview/select_personnel_iview.dart';

class SelectPersonnelPresenter extends BasePagePresenter<SelectPersonnelIView> with WidgetsBindingObserver {
  SelectPersonnelController controller;

  SelectPersonnelPresenter(this.controller);

  int _page = 1;

  void onRefresh() {
    _page = 1;
    requestArchives();
  }

  void loadMore() {
    _page++;
    requestArchives();
  }

  Future<dynamic> requestArchives() {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["status"] = controller.status.value; ////状态 0全部 1在职 2离职
    params["is_head_office"] = controller.is_head_office.value; //是否总部员工0全部 1是 2否
    if (!TextUtil.isEmpty(controller.role_id.value)) {
      params["role_id"] = controller.role_id.value; ////按角色筛选
    }
    if (!TextUtil.isEmpty(controller.contract_start_date.value)) {
      params["contract_start_date"] = controller.contract_start_date.value; ////合约开始
    }
    if (!TextUtil.isEmpty(controller.contract_end_date.value)) {
      params["contract_end_date"] = controller.contract_end_date.value; ////合约结束
    }
    if (!TextUtil.isEmpty(controller.project_uuid.value)) {
      params["project_uuid"] = controller.project_uuid.value; ////指定项目
    }
    if (!TextUtil.isEmpty(controller.keyword.value)) {
      params["keyword"] = controller.keyword.value; ////关键字
    }

    ///根据不同的渠道请求不同的接口
    String url = HttpApi.GET_ARCHIVES_ALL_LIST;
    if (controller.channel.value == 1) {
      url = HttpApi.GET_CAN_OVERTIME_HOLIDAY_LIST;
      if (!TextUtil.isEmpty(controller.contract_start_date.value)) {
        params["start_date"] = controller.contract_start_date.value; ////合约开始
      }
      if (!TextUtil.isEmpty(controller.contract_end_date.value)) {
        params["end_date"] = controller.contract_end_date.value; ////合约结束
      }
    }

    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';

    return requestNetwork<ProjectArchivesEntity>(Method.get, url: url, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (_page == 1) {
          controller.initMyList(data.total ?? 0, data.list ?? []);
        } else {
          controller.updateMyList(data.list ?? []);
        }
      } else {
        /// 加载失败
        if (_page > 1) {
          _page--;
        }
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
