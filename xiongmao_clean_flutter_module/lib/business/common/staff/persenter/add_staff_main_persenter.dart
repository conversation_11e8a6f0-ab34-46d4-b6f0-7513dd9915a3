import 'dart:collection';
import 'dart:convert';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../res/constant.dart';
import '../../../../util/log_utils.dart';
import '../../workpost/bean/work_rules_config_entity.dart';
import '../bean/city_data_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../iview/add_staff_main_iview.dart';

class AddStaffMainPresenter extends BasePagePresenter<AddStaffMainIView> with WidgetsBindingObserver {
  ///获取元数据
  Future<dynamic> getMetaData() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<GetMetaEntity>(Method.get, url: HttpApi.GET_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        SpUtil.putString(Constant.META_DATA, json.encode(data));
      }
    });
  }

  ///获取城市的元数据
  Future<dynamic> getCityMetaData() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<CityDataEntity>(Method.get, url: HttpApi.GET_CITY_META_DATA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        SpUtil.putString(Constant.CITY_META_DATA, json.encode(data));
      }
    });
  }

  ///获取企业配置
  Future<dynamic> getWorkRulesInfo() {
    var params = <String, String>{};
    return requestNetwork<WorkRulesConfigEntity>(Method.get, url: HttpApi.WORK_RULES_INFO, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        ///是否不允许补入今日之前的入职信息 1是 2否
        if ('1' != data.isEntryBeforeToday) {
          SpUtil.putBool(Constant.IS_ENTRY_BEFORE_TODAY, true);
        } else {
          SpUtil.putBool(Constant.IS_ENTRY_BEFORE_TODAY, false);
        }

        ///办理入职时是否需要入职的员工签字确认 1是 2否
        if ('1' == data.isEntrySign) {
          SpUtil.putBool(Constant.IS_ENTRY_SIGN, true);
        } else {
          SpUtil.putBool(Constant.IS_ENTRY_SIGN, false);
        }

        ///总部成员管理模式 1极简模式 2完整模式
        if ("1" == data.headOfficeMode) {
          SpUtil.putBool(Constant.HEAD_OFFICE_MODE, false);
        } else {
          SpUtil.putBool(Constant.HEAD_OFFICE_MODE, true);
        }
      }
    }, onError: (_, __) {});
  }

  ///获取名族的元数据
  Future<dynamic> getMetaDtaNation() {
    HashMap<String, String> hashMap = HashMap();
    return requestNetwork<StaffNationEntity>(Method.get, url: HttpApi.GET_META_DATA_NATION, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null && data.list!.isNotEmpty) {
        SpUtil.putString(Constant.NATION_DATA, json.encode(data));
      }
    });
  }
}
