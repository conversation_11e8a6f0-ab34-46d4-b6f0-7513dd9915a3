import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../../approve/bean/approve_project_entity.dart';
import '../../project/bean/job_manager_entity.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../controller/add_staff_contact_Info_controller.dart';
import '../controller/add_staff_position_arrangement_Info_controller.dart';
import '../controller/add_staff_salary_Info_controller.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_contact_Info_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_position_arrangement_Info_iview.dart';
import '../iview/add_staff_salary_Info_iview.dart';

class AddStaffPositionArrangementInfoPresenter extends BasePagePresenter<AddStaffPositionArrangementInfoIView> with WidgetsBindingObserver {
  AddStaffPositionArrangementInfoController controller;

  AddStaffPositionArrangementInfoPresenter(this.controller);

  ///获取已经存在的档案、草稿的详情 注意未完成登记的员工都算是草稿，签字后的员工是档案 isDraft ? 草稿 : 档案
  Future<dynamic> getStaffOne(bool before, bool isDraft, String uuid) {
    HashMap<String, String> hashMap = HashMap();

    if (before) {
      if (isDraft && !TextUtil.isEmpty(controller.applicationNo.value)) {
        hashMap['application_no'] = controller.applicationNo.value;
      } else {
        if (!TextUtil.isEmpty(uuid)) {
          hashMap['uuid'] = uuid;
        }
      }
    } else {
      if (!TextUtil.isEmpty(uuid)) {
        hashMap['uuid'] = uuid;
      }
    }

    ///根据自身的情况来获取内容 这里的参数去看下接口文档
    hashMap['is_work_info'] = '1';
    return requestNetwork<StaffDetailEntity>(Method.get, url: isDraft ? HttpApi.GET_ARCHIVES_DRAFT_DETAIL : HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        ///再set数据之前，先获取当前用户下是否有相关权限
        if (data.workInfo != null && data.workInfo!.isHeadOffice == '1') {
          ///如果是总部成员的话，直接返回参数去回填
          controller.fillData(data);
        } else {
          getProjectManagerList(data);
        }
        // controller.fillData(data);
      }
    });
  }

  /// 获取项目列表
  Future<dynamic> getProjectManagerList(StaffDetailEntity? oneData) {
    var params = <String, String>{};

    ///是否需要总部项目 1是2否 (这里不需要获取总部，所以截断总部)
    params['is_head_office'] = '2';
    return requestNetwork<ApproveProjectEntity>(Method.get, url: HttpApi.GET_PROJECT_MANGER_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null && data.list != null) {
        if (oneData != null && oneData.workInfo != null) {
          ///判断oneData.project_uuid 在data.list中是否有，如果就中断循环，执行controller.fillData(data);
          bool exists = data.list!.any((project) => project.uuid == oneData.workInfo!.projectUuid);

          ///说明找到了，那么就赋值
          if (exists) {
            controller.fillData(oneData);
          } else {
            controller.projectUuid.value = data.list?[0].uuid ?? '';
            controller.projectName.value = data.list?[0].projectShortName ?? '';
            getCurrentProjectJobs();
            getCurrentProjectAttendanceRules();
          }
        } else {
          ///重新点击了项目，重新获取
          controller.projectUuid.value = data.list?[0].uuid ?? '';
          controller.projectName.value = data.list?[0].projectShortName ?? '';
          getCurrentProjectJobs();
          getCurrentProjectAttendanceRules();
        }
      }
    }, onError: (_, __) {});
  }

  ///获取当前项目下的所有岗位
  Future<dynamic> getCurrentProjectJobs() {
    var params = <String, String>{};
    params["size"] = "100";
    params["page"] = "1";
    params["project_uuid"] = controller.projectUuid.value;
    return requestNetwork<JobManagerEntity>(Method.get, url: HttpApi.GET_JOB_ALL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.getCurrentProjectJobs(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///获取当前项目下的考勤规则
  Future<dynamic> getCurrentProjectAttendanceRules() {
    var params = <String, String>{};
    params["page"] = "1";
    if (!TextUtil.isEmpty(controller.projectUuid.value)) {
      params["project_uuid"] = controller.projectUuid.value;
    }
    params['map_code'] = 'gao_de';
    return requestNetwork<AttendanceManagerEntity>(Method.get, url: HttpApi.GET_ATTENDANCE_MANAGER_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillCurrentProjectAttendanceRules(data);
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///获取总部的项目的信息
  Future<dynamic> getHeadOfficeProject() {
    var params = <String, String>{};
    return requestNetwork<ProjectManagerList>(Method.get, url: HttpApi.GET_HEAD_OFFICE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillHeadOfficeProject(data);
      }
    }, onError: (_, __) {
      MyLog.e("========客户管理-获取列表-加载失败========");
    });
  }

  ///更新云端数据 isDraft ? 草稿 : 档案
  Future<dynamic> updateStaffInfo(bool isDraft, HashMap<String, String> hashMap) {
    return requestNetwork<CreateStaffEntity>(Method.get, url: isDraft ? HttpApi.UPDATE_ARCHIVES_DRAFT : HttpApi.UPDATE_ARCHIVES, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onUpdate(data);
      }
    });
  }
}
