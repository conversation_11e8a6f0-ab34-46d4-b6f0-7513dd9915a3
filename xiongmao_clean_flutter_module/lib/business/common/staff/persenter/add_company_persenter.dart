import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/company_contact_entity.dart';

class AddCompanyPresenter extends BasePagePresenter<AddCompanyIView> with WidgetsBindingObserver {
  Future<dynamic> requestCompanyContactDetail(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid ?? "";
    return requestNetwork<CompanyContactEntity>(Method.get, url: HttpApi.GET_COMPANY_CONTACTA_DETAIL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestCompanyContactDetail(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  Future<dynamic> saveCompanyStaff(HashMap<String, String> hashMap) {
    return requestNetwork<Object>(Method.get, url: HttpApi.SAVE_COMPANY_CONTACTA, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.saveCompanyStaff(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除总部成员
  Future<dynamic> delCompanyStaff(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid ?? "";
    return requestNetwork<Object>(Method.get, url: HttpApi.DELETE_COMPANY_STAFF, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.saveCompanyStaff(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
