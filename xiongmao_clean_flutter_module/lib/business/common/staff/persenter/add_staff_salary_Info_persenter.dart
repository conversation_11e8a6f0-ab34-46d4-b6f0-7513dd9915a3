import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/salary_config_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../controller/add_staff_contact_Info_controller.dart';
import '../controller/add_staff_salary_Info_controller.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_contact_Info_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_salary_Info_iview.dart';

class AddStaffSalaryInfoPresenter extends BasePagePresenter<AddStaffSalaryInfoIView> with WidgetsBindingObserver {
  AddStaffSalaryInfoController controller;

  AddStaffSalaryInfoPresenter(this.controller);

  ///获取已经存在的档案、草稿的详情 注意未完成登记的员工都算是草稿，签字后的员工是档案 isDraft ? 草稿 : 档案
  Future<dynamic> getStaffOne(bool before, bool isDraft, String uuid) {
    HashMap<String, String> hashMap = HashMap();

    if (before) {
      if (isDraft && !TextUtil.isEmpty(controller.applicationNo.value)) {
        hashMap['application_no'] = controller.applicationNo.value;
      } else {
        if (!TextUtil.isEmpty(uuid)) {
          hashMap['uuid'] = uuid;
        }
      }
    } else {
      if (!TextUtil.isEmpty(uuid)) {
        hashMap['uuid'] = uuid;
      }
    }

    ///根据自身的情况来获取内容 这里的参数去看下接口文档
    hashMap['is_work_info'] = '1';
    hashMap['is_salary_insurance'] = '1';
    return requestNetwork<StaffDetailEntity>(Method.get, url: isDraft ? HttpApi.GET_ARCHIVES_DRAFT_DETAIL : HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillData(data);
      }
    });
  }

  ///更新云端数据 isDraft ? 草稿 : 档案
  Future<dynamic> updateStaffInfo(bool isDraft, HashMap<String, dynamic> hashMap) {
    return requestNetwork<CreateStaffEntity>(Method.post, url: isDraft ? HttpApi.UPDATE_ARCHIVES_DRAFT : HttpApi.UPDATE_ARCHIVES, params: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onUpdate(data);
      }
    });
  }

  ///获取薪酬的配置
  Future<dynamic> getSalaryInfoConfig(String uuid) {
    HashMap<String, String> hashMap = HashMap();
    hashMap['uuid'] = uuid;
    return requestNetwork<SalaryConfigEntity>(Method.get, url: HttpApi.USER_ENTRY_ONE, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillSalaryInfoConfig(data);
      }
    });
  }
}
