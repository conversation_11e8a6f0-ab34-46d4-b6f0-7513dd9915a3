import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/company_contact_entity.dart';
import '../bean/permission_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../controller/staff_one_controller.dart';
import '../iview/permiss_iview.dart';
import '../iview/staff_one_iview.dart';

class StaffOnePresenter extends BasePagePresenter<StaffOneIView> with WidgetsBindingObserver {
  StaffOneController controller;

  StaffOnePresenter(this.controller);


  ///获取详情
  Future<dynamic> getStaffOne() {
    HashMap<String, String> hashMap = HashMap();
    hashMap['uuid'] = controller.uuid.value;
    ///根据自身的情况来获取内容 这里的参数去看下接口文档
    hashMap['is_identity'] = '1';
    hashMap['is_work_info'] = '1';
    return requestNetwork<StaffDetailEntity>(Method.get, url: HttpApi.GET_STAFF_DETAILS, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.fillData(data);
      }
    });
  }



  ///删除员工档案
  Future<dynamic> delCompanyStaff(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid ?? "";
    return requestNetwork<Object>(Method.get, url: HttpApi.DELETE_COMPANY_STAFF, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.updateStatus();
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
