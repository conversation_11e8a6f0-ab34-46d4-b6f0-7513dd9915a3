import 'dart:collection';

import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/check_id_number_auth_entity.dart';
import '../bean/check_id_number_entity.dart';
import '../bean/company_contact_entity.dart';
import '../bean/create_staff_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../controller/add_staff_Identity_Info_controller.dart';
import '../controller/add_staff_contact_Info_controller.dart';
import '../controller/add_staff_salary_Info_controller.dart';
import '../controller/add_staff_sign_hand_controller.dart';
import '../iview/add_staff_IdentityInfo_iview.dart';
import '../iview/add_staff_contact_Info_iview.dart';
import '../iview/add_staff_main_iview.dart';
import '../iview/add_staff_salary_Info_iview.dart';
import '../iview/add_staff_sign_hand_iview.dart';

class AddStaffSignHandPresenter extends BasePagePresenter<AddStaffSignHandIView> with WidgetsBindingObserver {
  AddStaffSignHandController controller;

  AddStaffSignHandPresenter(this.controller);


  ///更新云端数据 isDraft ? 草稿 : 档案
  Future<dynamic> updateStaffInfo(bool isDraft, HashMap<String, String> hashMap) {
    return requestNetwork<CreateStaffEntity>(Method.get, url: isDraft ? HttpApi.UPDATE_ARCHIVES_DRAFT : HttpApi.UPDATE_ARCHIVES, queryParameters: hashMap, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.onUpdate(data);
      }
    });
  }
}
