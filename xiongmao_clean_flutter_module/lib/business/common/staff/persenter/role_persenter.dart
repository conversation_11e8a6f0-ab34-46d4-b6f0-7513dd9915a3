import 'dart:collection';

import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/add_company_iview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/iview/role_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/company_contact_entity.dart';
import '../bean/permission_entity.dart';
import '../iview/permiss_iview.dart';

class RolePresenter extends BasePagePresenter<RoleIView> with WidgetsBindingObserver {
  Future<dynamic> requestPermission() {
    var params = <String, String>{};
    params["type"] = "0"; ////类型0全部 1总部员工角色 2项目成员角色
    return requestNetwork<PermissionEntity>(Method.get, url: HttpApi.GET_PERMISSIONS, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        view.requestPermissionData(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
