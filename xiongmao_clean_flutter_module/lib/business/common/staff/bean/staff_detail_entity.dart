import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/salary_config_entity.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/staff_detail_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/staff_detail_entity.g.dart';

@JsonSerializable()
class StaffDetailEntity {
	String? uuid;
	@JSONField(name: "user_name")
	String? userName;
	String? avatar;
	String? salary;
	@JSONField(name: "role_name")
	String? roleName;
	String? sex;
	@J<PERSON>NField(name: "sex_name")
	String? sexName;
	String? address;
	String? age;
	@JSONField(name: "status")
	String? status;
	@JSONField(name: "nation_name")
	String? nationName;
	@JSONField(name: "province_name")
	String? provinceName;
	@JSONField(name: "city_name")
	String? cityName;
	@JSONField(name: "work_age")
	String? workAge;
	@JSONField(name: "application_no")
	String? applicationNo;
	@JSONField(name: "is_has_id_card")
	String? isHasIdCard;
	@JSONField(name: "is_has_bank_card")
	String? isHasBankCard;
	@JSONField(name: "is_has_contract")
	String? isHasContract;
	@JSONField(name: "is_has_healthy_card")
	String? isHasHealthyCard;
	@JSONField(name: "is_has_credit_wind")
	String? isHasCreditWind;
	@JSONField(name: "search_credit_time")
	String? searchCreditTime;
	@JSONField(name: "create_time")
	String? createTime;
	@JSONField(name: "sign_url")
	String? signUrl;
	@JSONField(name: "superiors_user_uuid")
	String? superiorsUserUuid;
	@JSONField(name: "superiors_user_name")
	String? superiorsUserName;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "project_cat_name")
	String? projectCatName;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "left_remark")
	String? leftRemark;
	@JSONField(name: "insurance_type")
	String? insuranceType;
	@JSONField(name: "insurance_product_name")
	String? insuranceProductName;
	StaffDetailIdentity? identity;
	StaffDetailBank? bank;
	StaffDetailContact? contact;
	@JSONField(name: "work_info")
	StaffDetailWorkInfo? workInfo;
	@JSONField(name: "healthy_pic")
	StaffDetailHealthyPic? healthyPic;
	@JSONField(name: "no_crime_pic_list")
	List<StaffDetailIdentityPicList>? noCrimePicList;
	@JSONField(name: "entry_registration_pic_list")
	List<StaffDetailIdentityPicList>? entryRegistrationPicList;
	@JSONField(name: "credit_list")
	List<StaffDetailCreditList>? creditList;
	StaffDetailContract? contract;
	@JSONField(name: "left_contract")
	StaffDetailLeftContract? leftContract;
	@JSONField(name: "salary_insurance")
	StaffDetailSalaryInsurance? salaryInsurance;

	StaffDetailEntity();

	factory StaffDetailEntity.fromJson(Map<String, dynamic> json) => $StaffDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailIdentity {
	@JSONField(name: "user_name")
	String? userName;
	@JSONField(name: "id_number")
	String? idNumber;
	String? birthday;
	@JSONField(name: "province_id")
	String? provinceId;
	@JSONField(name: "province_name")
	String? provinceName;
	@JSONField(name: "city_id")
	String? cityId;
	@JSONField(name: "city_name")
	String? cityName;
	String? address;
	@JSONField(name: "marriage_status")
	String? marriageStatus;
	@JSONField(name: "marriage_status_name")
	String? marriageStatusName;
	@JSONField(name: "nation_id")
	String? nationId;
	@JSONField(name: "nation_name")
	String? nationName;
	@JSONField(name: "is_id_long")
	String? isIdLong;
	@JSONField(name: "id_start_date")
	String? idStartDate;
	@JSONField(name: "id_end_date")
	String? idEndDate;
	@JSONField(name: "pic_list")
	List<StaffDetailIdentityPicList>? picList;

	StaffDetailIdentity();

	factory StaffDetailIdentity.fromJson(Map<String, dynamic> json) => $StaffDetailIdentityFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailIdentityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailIdentityPicList {
	@JSONField(name: "source_url")
	String? sourceUrl;
	@JSONField(name: "is_front")
	String? isFront;

	StaffDetailIdentityPicList();

	factory StaffDetailIdentityPicList.fromJson(Map<String, dynamic> json) => $StaffDetailIdentityPicListFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailIdentityPicListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailBank {
	@JSONField(name: "bank_no")
	String? bankNo;
	@JSONField(name: "bank_code")
	String? bankCode;
	String? bank;
	@JSONField(name: "bank_name")
	String? bankName;
	@JSONField(name: "pic_list")
	List<StaffDetailIdentityPicList>? picList;

	StaffDetailBank();

	factory StaffDetailBank.fromJson(Map<String, dynamic> json) => $StaffDetailBankFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailBankToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailContact {
	String? mobile;
	@JSONField(name: "exigency_user_name")
	String? exigencyUserName;
	@JSONField(name: "exigency_user_mobile")
	String? exigencyUserMobile;
	@JSONField(name: "exigency_user_relation")
	String? exigencyUserRelation;
	@JSONField(name: "exigency_user_relation_name")
	String? exigencyUserRelationName;

	StaffDetailContact();

	factory StaffDetailContact.fromJson(Map<String, dynamic> json) => $StaffDetailContactFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailContactToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailWorkInfo {
	@JSONField(name: "work_start_time")
	String? workStartTime;
	@JSONField(name: "work_end_time")
	String? workEndTime;
	@JSONField(name: "role_id")
	String? roleId;
	@JSONField(name: "contract_type")
	String? contractType;
	@JSONField(name: "contract_type_name")
	String? contractTypeName;
	@JSONField(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "role_name")
	String? roleName;
	@JSONField(name: "group_uuid")
	String? groupUuid;
	@JSONField(name: "group_name")
	String? groupName;
	@JSONField(name: "job_uuid")
	String? jobUuid;
	@JSONField(name: "job_name")
	String? jobName;
	@JSONField(name: "is_head_office")
	String? isHeadOffice;
	@JSONField(name: "department_uuid")
	String? departmentUuid;
	@JSONField(name: "department_name")
	String? departmentName;
	@JSONField(name: "contract_company_uuid")
	String? contractCompanyUuid;
	@JSONField(name: "contract_company_name")
	String? contractCompanyName;
	@JSONField(name: "pic_list")
	List<StaffDetailIdentityPicList>? picList;

	StaffDetailWorkInfo();

	factory StaffDetailWorkInfo.fromJson(Map<String, dynamic> json) => $StaffDetailWorkInfoFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailWorkInfoToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailHealthyPic {
	@JSONField(name: "pic_list")
	List<StaffDetailHealthyPicPicList>? picList;

	StaffDetailHealthyPic();

	factory StaffDetailHealthyPic.fromJson(Map<String, dynamic> json) => $StaffDetailHealthyPicFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailHealthyPicToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailHealthyPicPicList {
	@JSONField(name: "source_url")
	String? sourceUrl;
	@JSONField(name: "is_front")
	String? isFront;

	StaffDetailHealthyPicPicList();

	factory StaffDetailHealthyPicPicList.fromJson(Map<String, dynamic> json) => $StaffDetailHealthyPicPicListFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailHealthyPicPicListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailCreditList {
	String? item;
	@JSONField(name: "credit_type")
	int? creditType;
	int? status;
	String? title;
	String? message;
	String? explain;

	StaffDetailCreditList();

	factory StaffDetailCreditList.fromJson(Map<String, dynamic> json) => $StaffDetailCreditListFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailCreditListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailContract {
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "contract_type")
	String? contractType;
	@JSONField(name: "contract_sign_type")
	String? contractSignType;
	@JSONField(name: "pic_list")
	List<StaffDetailIdentityPicList>? picList;

	StaffDetailContract();

	factory StaffDetailContract.fromJson(Map<String, dynamic> json) => $StaffDetailContractFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailContractToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailLeftContract {
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "contract_type")
	String? contractType;
	@JSONField(name: "contract_type_name")
	String? contractTypeName;
	@JSONField(name: "contract_sign_type")
	String? contractSignType;
	@JSONField(name: "contract_sign_type_name")
	String? contractSignTypeName;
	@JSONField(name: "left_reason_name")
	String? leftReasonName;
	@JSONField(name: "left_reason_remark")
	String? leftReasonRemark;
	@JSONField(name: "pic_list")
	List<String>? picList;
	@JSONField(name: "sign_status_name")
	String? signStatusName;
	@JSONField(name: "ele_contract_url")
	String? eleContractUrl;

	StaffDetailLeftContract();

	factory StaffDetailLeftContract.fromJson(Map<String, dynamic> json) => $StaffDetailLeftContractFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailLeftContractToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffDetailSalaryInsurance {
	@JSONField(name: "insurance_type")
	String? insuranceType;
	@JSONField(name: "insurance_product_name")
	String? insuranceProductName;
	String? salary;
	@JSONField(name: "salary_level")
	String? salaryLevel;
	@JSONField(name: "salary_item_list")
	List<SalaryConfigItemListSalaryConfigItemList>? itemList;

	StaffDetailSalaryInsurance();

	factory StaffDetailSalaryInsurance.fromJson(Map<String, dynamic> json) => $StaffDetailSalaryInsuranceFromJson(json);

	Map<String, dynamic> toJson() => $StaffDetailSalaryInsuranceToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}