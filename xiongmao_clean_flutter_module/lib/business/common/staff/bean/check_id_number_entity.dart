import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/check_id_number_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/check_id_number_entity.g.dart';

@JsonSerializable()
class CheckIdNumberEntity {
	int? status;
	String? uuid;
	@JSONField(name: "data_type")
	int? dataType;

	CheckIdNumberEntity();

	factory CheckIdNumberEntity.fromJson(Map<String, dynamic> json) => $CheckIdNumberEntityFromJson(json);

	Map<String, dynamic> toJson() => $CheckIdNumberEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}