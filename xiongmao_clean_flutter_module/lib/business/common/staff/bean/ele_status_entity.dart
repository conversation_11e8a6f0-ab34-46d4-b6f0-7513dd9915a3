import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/ele_status_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/ele_status_entity.g.dart';

@JsonSerializable()
class EleStatusEntity {
	int? status;
	String? tips;

	EleStatusEntity();

	factory EleStatusEntity.fromJson(Map<String, dynamic> json) => $EleStatusEntityFromJson(json);

	Map<String, dynamic> toJson() => $EleStatusEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}