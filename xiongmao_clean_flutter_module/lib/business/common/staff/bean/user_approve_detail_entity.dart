import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/user_approve_detail_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/user_approve_detail_entity.g.dart';

@JsonSerializable()
class UserApproveDetailEntity {
	@JSONField(name: "application_title")
	String? applicationTitle;
	@JSONField(name: "application_status")
	String? applicationStatus;
	@JSONField(name: "application_status_name")
	String? applicationStatusName;
	@JSONField(name: "summary_list")
	List<UserApproveDetailSummaryList>? summaryList;
	@JSONField(name: "application_list")
	List<dynamic>? applicationList;
	@JSONField(name: "application_type")
	String? applicationType;
	@JSONField(name: "field_list")
	List<UserApproveDetailFieldList>? fieldList;
	@JSONField(name: "create_time")
	String? createTime;
	@J<PERSON>NField(name: "material_sku")
	UserApproveDetailMaterialSku? materialSku;
	@JSONField(name: "sp_node_list")
	List<UserApproveDetailSpNodeList>? spNodeList;

	UserApproveDetailEntity();

	factory UserApproveDetailEntity.fromJson(Map<String, dynamic> json) => $UserApproveDetailEntityFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class UserApproveDetailSummaryList {
	String? item;
	String? content;

	UserApproveDetailSummaryList();

	factory UserApproveDetailSummaryList.fromJson(Map<String, dynamic> json) => $UserApproveDetailSummaryListFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailSummaryListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class UserApproveDetailFieldList {
	@JSONField(name: "field_form_name")
	String? fieldFormName;
	@JSONField(name: "field_name")
	String? fieldName;
	@JSONField(name: "field_type")
	String? fieldType;
	@JSONField(name: "field_value")
	List<String>? fieldValue;
	@JSONField(name: "table_list")
	List<dynamic>? tableList;
	@JSONField(name: "attachment_list")
	List<dynamic>? attachmentList;

	UserApproveDetailFieldList();

	factory UserApproveDetailFieldList.fromJson(Map<String, dynamic> json) => $UserApproveDetailFieldListFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailFieldListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class UserApproveDetailMaterialSku {
	int? total;
	List<UserApproveDetailMaterialSkuList>? list;

	UserApproveDetailMaterialSku();

	factory UserApproveDetailMaterialSku.fromJson(Map<String, dynamic> json) => $UserApproveDetailMaterialSkuFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailMaterialSkuToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class UserApproveDetailMaterialSkuList {
	String? number;
	@JSONField(name: "product_name")
	String? productName;
	@JSONField(name: "product_number")
	String? productNumber;
	String? specification;
	@JSONField(name: "cover_url")
	String? coverUrl;
	String? num;
	@JSONField(name: "unit_name")
	String? unitName;

	UserApproveDetailMaterialSkuList();

	factory UserApproveDetailMaterialSkuList.fromJson(Map<String, dynamic> json) => $UserApproveDetailMaterialSkuListFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailMaterialSkuListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class UserApproveDetailSpNodeList {
	String? uuid;
	@JSONField(name: "node_name")
	String? nodeName;
	@JSONField(name: "sub_node_name")
	String? subNodeName;
	@JSONField(name: "node_status")
	int? nodeStatus;
	@JSONField(name: "node_status_name")
	String? nodeStatusName;
	@JSONField(name: "user_list")
	List<dynamic>? userList;

	UserApproveDetailSpNodeList();

	factory UserApproveDetailSpNodeList.fromJson(Map<String, dynamic> json) => $UserApproveDetailSpNodeListFromJson(json);

	Map<String, dynamic> toJson() => $UserApproveDetailSpNodeListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}