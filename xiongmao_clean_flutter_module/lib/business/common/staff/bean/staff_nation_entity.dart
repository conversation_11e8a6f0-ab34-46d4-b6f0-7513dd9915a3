import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/staff_nation_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/staff_nation_entity.g.dart';

@JsonSerializable()
class StaffNationEntity {
	List<StaffNationList>? list;

	StaffNationEntity();

	factory StaffNationEntity.fromJson(Map<String, dynamic> json) => $StaffNationEntityFromJson(json);

	Map<String, dynamic> toJson() => $StaffNationEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class StaffNationList {
	String? id;
	String? name;

	StaffNationList();

	factory StaffNationList.fromJson(Map<String, dynamic> json) => $StaffNationListFromJson(json);

	Map<String, dynamic> toJson() => $StaffNationListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}