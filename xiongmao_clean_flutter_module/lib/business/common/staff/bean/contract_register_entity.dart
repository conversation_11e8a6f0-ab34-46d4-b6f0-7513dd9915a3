import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_register_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_register_entity.g.dart';

@JsonSerializable()
class ContractRegisterEntity {
	int? code;
	String? data;
	String? msg;

	ContractRegisterEntity();

	factory ContractRegisterEntity.fromJson(Map<String, dynamic> json) => $ContractRegisterEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractRegisterEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}