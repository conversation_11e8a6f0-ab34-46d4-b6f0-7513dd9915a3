import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_staff_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_staff_one_entity.g.dart';

@JsonSerializable()
class ContractStaffOneEntity {
	String? uuid;
	@JSONField(name: "start_time")
	String? startTime;
	@JSONField(name: "end_time")
	String? endTime;
	@JSONField(name: "sub_label_code")
	String? subLabelCode;
	@JSONField(name: "contract_type")
	String? contractType;
	@J<PERSON>NField(name: "left_reason")
	String? leftReason;
	@J<PERSON><PERSON>ield(name: "left_reason_name")
	String? leftReasonName;
	String? remark;
	@J<PERSON>NField(name: "contract_pic_list")
	List<String>? contractPicList;

	ContractStaffOneEntity();

	factory ContractStaffOneEntity.fromJson(Map<String, dynamic> json) => $ContractStaffOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractStaffOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
