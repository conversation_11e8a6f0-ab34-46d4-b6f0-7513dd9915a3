import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/city_data_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/city_data_entity.g.dart';

@JsonSerializable()
class CityDataEntity {
	List<CityDataList>? list;

	CityDataEntity();

	factory CityDataEntity.fromJson(Map<String, dynamic> json) => $CityDataEntityFromJson(json);

	Map<String, dynamic> toJson() => $CityDataEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CityDataList {
	String? id;
	String? name;
	String? pid;
	List<CityDataListList>? list;

	CityDataList();

	factory CityDataList.fromJson(Map<String, dynamic> json) => $CityDataListFromJson(json);

	Map<String, dynamic> toJson() => $CityDataListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CityDataListList {
	String? id;
	String? name;
	String? pid;
	List<CityDataListListList>? list;

	CityDataListList();

	factory CityDataListList.fromJson(Map<String, dynamic> json) => $CityDataListListFromJson(json);

	Map<String, dynamic> toJson() => $CityDataListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CityDataListListList {
	String? id;
	String? name;
	String? pid;

	CityDataListListList();

	factory CityDataListListList.fromJson(Map<String, dynamic> json) => $CityDataListListListFromJson(json);

	Map<String, dynamic> toJson() => $CityDataListListListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}