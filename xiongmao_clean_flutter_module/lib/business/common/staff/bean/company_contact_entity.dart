import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';
import 'dart:convert';

import '../../../../generated/json/company_contact_entity.g.dart';
export 'package:xiongmao_clean_flutter_module/generated/json/holiday_data_entity.g.dart';

@JsonSerializable()
class CompanyContactEntity {
	String? uuid;
	String? user_name;
	String? mobile;
	String? role_id;
	String? role_name;
	String? is_super_admin;
	String? department_uuid;
	String? department_name;

	CompanyContactEntity();

	factory CompanyContactEntity.fromJson(Map<String, dynamic> json) => $CompanyContactEntityFromJson(json);

	Map<String, dynamic> toJson() => $CompanyContactEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}
