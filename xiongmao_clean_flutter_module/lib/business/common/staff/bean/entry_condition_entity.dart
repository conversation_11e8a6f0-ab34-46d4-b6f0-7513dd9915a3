class EntryConditionEntity {
  bool beforeEntry;
  bool isDraft;
  String uuid;

  EntryConditionEntity({required this.beforeEntry, required this.isDraft, required this.uuid});

  factory EntryConditionEntity.fromJson(Map<String, dynamic> json) {
    return EntryConditionEntity(
      beforeEntry: json['before_entry'] as bool,
      isDraft: json['is_draft'] as bool,
      uuid: json['uuid'] as String,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'before_entry': beforeEntry,
      'is_draft': isDraft,
      'uuid': uuid,
    };
  }
}