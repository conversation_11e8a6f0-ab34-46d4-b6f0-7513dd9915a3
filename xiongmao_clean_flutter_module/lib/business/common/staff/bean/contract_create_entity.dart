import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/contract_create_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/contract_create_entity.g.dart';

@JsonSerializable()
class ContractCreateEntity {
	@JSONField(name: "contract_uuid")
	String? contractUuid;
	@JSONField(name: "contract_url")
	String? contractUrl;

	ContractCreateEntity();

	factory ContractCreateEntity.fromJson(Map<String, dynamic> json) => $ContractCreateEntityFromJson(json);

	Map<String, dynamic> toJson() => $ContractCreateEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}