import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/salary_config_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/salary_config_entity.g.dart';

@JsonSerializable()
class SalaryConfigEntity {
	String? uuid;
	@JSONField(name: "template_name")
	String? templateName;
	@JSONField(name: "level_total")
	String? levelTotal;
	@JSONField(name: "item_list")
	List<List<SalaryConfigItemListSalaryConfigItemList>>? itemList;

	SalaryConfigEntity();

	factory SalaryConfigEntity.fromJson(Map<String, dynamic> json) => $SalaryConfigEntityFromJson(json);

	Map<String, dynamic> toJson() => $SalaryConfigEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class SalaryConfigItemListSalaryConfigItemList {
	String? uuid;
	@JSONField(name: "item_name")
	String? itemName;
	@JSONField(name: "data_type")
	String? dataType;
	@JSONField(name: "data_type_name")
	String? dataTypeName;
	@JSONField(name: "is_edit")
	String? isEdit;
	@JSONField(name: "is_edit_name")
	String? isEditName;
	@JSONField(name: "is_adjustment_stat")
	String? isAdjustmentStat;
	@JSONField(name: "data_type_format")
	String? dataTypeFormat;
	@JSONField(name: "item_value")
	String? itemValue;

	SalaryConfigItemListSalaryConfigItemList();

	factory SalaryConfigItemListSalaryConfigItemList.fromJson(Map<String, dynamic> json) => $SalaryConfigItemListSalaryConfigItemListFromJson(json);

	Map<String, dynamic> toJson() => $SalaryConfigItemListSalaryConfigItemListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}