import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/check_id_number_auth_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/check_id_number_auth_entity.g.dart';

@JsonSerializable()
class CheckIdNumberAuthEntity {
	@JSONField(name: "is_auth")
	int? isAuth;

	CheckIdNumberAuthEntity();

	factory CheckIdNumberAuthEntity.fromJson(Map<String, dynamic> json) => $CheckIdNumberAuthEntityFromJson(json);

	Map<String, dynamic> toJson() => $CheckIdNumberAuthEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}