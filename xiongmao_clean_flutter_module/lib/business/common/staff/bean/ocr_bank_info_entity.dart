import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/ocr_bank_info_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/ocr_bank_info_entity.g.dart';

@JsonSerializable()
class OcrBankInfoEntity {
	@JSONField(name: "image_status")
	int? imageStatus;
	@JSONField(name: "image_status_name")
	String? imageStatusName;
	@JSONField(name: "bank_card_number")
	String? bankCardNumber;
	@JSONField(name: "valid_date")
	String? validDate;
	@JSONField(name: "bank_name")
	String? bankName;
	@JSONField(name: "bank_code")
	String? bankCode;

	OcrBankInfoEntity();

	factory OcrBankInfoEntity.fromJson(Map<String, dynamic> json) => $OcrBankInfoEntityFromJson(json);

	Map<String, dynamic> toJson() => $OcrBankInfoEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}