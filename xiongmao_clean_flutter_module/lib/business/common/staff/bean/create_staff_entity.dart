import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/create_staff_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/create_staff_entity.g.dart';

@JsonSerializable()
class CreateStaffEntity {
	String? uuid;
	@JSONField(name: "application_no")
	String? applicationNo;

	CreateStaffEntity();

	factory CreateStaffEntity.fromJson(Map<String, dynamic> json) => $CreateStaffEntityFromJson(json);

	Map<String, dynamic> toJson() => $CreateStaffEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}