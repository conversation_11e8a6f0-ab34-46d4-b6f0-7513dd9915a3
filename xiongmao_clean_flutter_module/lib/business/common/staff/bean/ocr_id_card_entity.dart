import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/ocr_id_card_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/ocr_id_card_entity.g.dart';

@JsonSerializable()
class OcrIdCardEntity {
	@JSO<PERSON>ield(name: "image_status")
	int? imageStatus;
	@JSO<PERSON>ield(name: "image_status_name")
	String? imageStatusName;
	int? direction;
	String? address;
	String? birthday;
	String? name;
	@JSONField(name: "id_number")
	String? idNumber;
	String? hometown;
	@JSONField(name: "hometown_city")
	String? hometownCity;
	String? sex;
	String? nation;
	@JSONField(name: "chinese_zodiac")
	String? chineseZodiac;
	String? zodiac;

	OcrIdCardEntity();

	factory OcrIdCardEntity.fromJson(Map<String, dynamic> json) => $OcrIdCardEntityFromJson(json);

	Map<String, dynamic> toJson() => $OcrIdCardEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}