import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:image_pickers/image_pickers.dart';

import '../../../../util/qiniu/qiniu_utils.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/city_data_entity.dart';
import '../bean/ocr_id_card_entity.dart';
import '../bean/staff_detail_entity.dart';
import '../bean/staff_nation_entity.dart';
import '../widght/steps.dart';

class AddStaffIdentityInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;

  ///城市的列表
  var cityList = <CityDataList>[].obs;
  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;

  var isDataFetched = false.obs;

  ///是否校验离职档案 1是0否
  var isLeave = true.obs;

  ///以草稿为准，继续编辑
  var isDraft = false.obs;

  ///以前是否入职过
  var beforeEntry = false.obs;

  ///身份证是否验证
  var isNeedAuth = true.obs;

  ///有个共同的ID 要返回给上层使用
  var before_entry_and_draft_uuid = ''.obs;

  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  ///性别的列表
  var sexList = [
    BaseChooseString("男"),
    BaseChooseString("女"),
  ].obs;

  ///性别的位置
  var sexIndex = (-1).obs;

  ///身份证是否长期
  var idLongList = [
    BaseChooseString("是"),
    BaseChooseString("否"),
  ].obs;

  ///身份证的位置
  var idLongIndex = (-1).obs;

  ///婚姻状态
  var marriageList = [
    BaseChooseString("已婚"),
    BaseChooseString("离异"),
    BaseChooseString("未婚"),
  ].obs;

  ///获取民族的列表
  var nationList = <StaffNationList>[].obs;

  ///婚姻的位置
  var marriageListIndex = (-1).obs;

  ///身份证正面
  var idZUrl = ''.obs;

  ///身份证反面
  var idFUrl = ''.obs;

  ///姓名
  var nameController = TextEditingController();

  ///身份证
  var idNumberController = TextEditingController();

  ///户籍地址
  var homeAddressController = TextEditingController();

  ///出生日期
  var birthdayDate = ''.obs;

  ///户籍城市
  var city = ''.obs;
  var cityId = ''.obs;

  ///民族
  var nation = ''.obs;
  var nationIndex = (-1).obs;

  ///户籍城市
  ///身份证开始日期
  var startDate = "".obs;

  ///身份证结束日期
  var endDate = "".obs;

  void updateBirthdayDate(String data) {
    birthdayDate.value = data;
  }

  void updateStartDate(String data) {
    startDate.value = data;
  }

  void updateEndDate(String data) {
    endDate.value = data;
  }

  ///展示民族的dialog
  void showNationDialog(BuildContext context, List<StaffNationList> nationList) {
    ///民族的列表
    List<String> names = nationList
        .map((e) => e.name)
        .where((name) => name != null) // 过滤掉 null 值
        .cast<String>() // 转换为 List<String>
        .toList();

    SingleColumnDataPickerView.showSingleColumnDataPicker(context, '请选择民族', names, nationIndex.value, (index, selectedText) {
      print('当前选择的下标：$index');
      print('当前选择的文本内容：$selectedText');
      nation.value = "$selectedText";
      nationIndex.value = (index + 1);
    });
  }

  ///识别身份证的内容填充
  void ocrFill(OcrIdCardEntity ocrData) {
    ///状态 0-正常状态，其它为非正常状态
    if (0 == ocrData.imageStatus) {
      nameController.text = ocrData.name ?? '';
      idNumberController.text = ocrData.idNumber ?? '';

      ///是男是女
      if ('女' == ocrData.sex) {
        sexIndex.value = 1;
      } else if ('男' == ocrData.sex) {
        sexIndex.value = 0;
      }

      ///民族
      nation.value = ocrData.nation ?? '';
      nationIndex.value = nationList.indexWhere((e) => e.name == nation.value);

      ///生日
      birthdayDate.value = ocrData.birthday ?? '';

      ///户籍地址
      homeAddressController.text = ocrData.address ?? '';

      ///户籍城市
      city.value = '${ocrData.hometown ?? ''}-${ocrData.hometownCity ?? ''}';
    }
  }

  void fillData(StaffDetailEntity staffDetailEntity) {
    uuid.value = staffDetailEntity.uuid ?? '';
    if ('2' == staffDetailEntity.sex) {
      sexIndex.value = 1;
    } else if ('1' == staffDetailEntity.sex) {
      sexIndex.value = 0;
    }
    StaffDetailIdentity? identity = staffDetailEntity.identity;
    if (identity != null) {
      ///身份证照片
      if (identity.picList != null && identity.picList!.isNotEmpty) {
        for (int i = 0; i < identity.picList!.length; i++) {
          String? isFront = identity.picList![i].isFront;
          String? sourceUrl = identity.picList![i].sourceUrl;

          if ("1" == (isFront)) {
            // 处理正面图片
            if (!TextUtil.isEmpty(sourceUrl)) {
              idZUrl.value = sourceUrl!;
            }
          } else {
            // 处理背面图片
            if (!TextUtil.isEmpty(sourceUrl)) {
              idFUrl.value = sourceUrl!;
            }
          }
        }
      }

      updateBirthdayDate(identity.birthday ?? '');

      ///其他的信息
      nameController.text = identity.userName ?? '';
      idNumberController.text = identity.idNumber ?? '';

      ///民族
      nation.value = identity.nationName ?? '';

      ///城市
      cityId.value = identity.cityId ?? '';
      if (!TextUtil.isEmpty(identity.provinceName) && !TextUtil.isEmpty(identity.cityName)) {
        city.value = '${identity.provinceName ?? ''}-${identity.cityName ?? ''}';
      }

      ///户籍地址
      homeAddressController.text = identity.address ?? '';

      ///身份证开始
      startDate.value = identity.idStartDate ?? '';
      endDate.value = identity.idEndDate ?? '';

      ///是否长期
      if ('1' == identity.isIdLong) {
        idLongIndex.value = 0;
      } else {
        idLongIndex.value = 1;
      }

      ///婚姻状态
      if ('1' == identity.marriageStatus) {
        marriageListIndex.value = 0;
      } else if ('2' == identity.marriageStatus) {
        marriageListIndex.value = 1;
      } else if ('3' == identity.marriageStatus) {
        marriageListIndex.value = 2;
      }
    }
  }

  ///获取民族的id
  String getNationId() {
    if (nationList.isNotEmpty) {
      var matchingNation = nationList.firstWhere(
        (e) => e.name == nation.value,
        orElse: () => StaffNationList(), // 返回一个 id 为 '' 的对象
      );
      return matchingNation.id ?? ''; // 直接返回 id
    }
    return '';
  }
}
