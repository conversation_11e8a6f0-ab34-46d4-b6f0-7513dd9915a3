import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';

import '../../approve/bean/base_choose_string.dart';
import '../../contract/bean/contract_main_electronic_entity.dart';
import '../../contract/bean/contract_main_info_entity.dart';
import '../../contract/bean/contract_tag_one_entity.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/ele_status_entity.dart';
import '../bean/staff_detail_entity.dart';

class ContractStaffController extends GetxController {
  var contractRangeDate = "".obs;

  var staffDetailEntity = StaffDetailEntity().obs;

  var customerIdCard = "".obs;
  var customerId = "".obs;

  var isEntryContract = false.obs;

  var contractSettingInfo = EleStatusEntity().obs;

  var reason = 0.obs;

  var imagesList = <BaseMediaEntity>[].obs;

  void updateImagesList(List<BaseMediaEntity> data) {
    imagesList.value = data;
  }

  void updateIsEntryContract(bool data) {
    isEntryContract.value = data;

    ///如果开启了电子签，默认选中电子合同
    if (data) {
      updateSignType('电子合同');
    }
  }

  void updateStaffDetailEntity(StaffDetailEntity data) {
    staffDetailEntity.value = data;
  }

  var signType = "纸质合同".obs; //默认电子合同
  void updateSignType(String data) {
    signType.value = data;
  }

  void updateRangeDate(String data) {
    contractRangeDate.value = data;
  }

  var bottomBtnTitle = "提交".obs;

  void updateBottomBtnTitle(String s) {
    bottomBtnTitle.value = s;
  }

  ///记录合同子类型的code
  var contractSubEntity = ContractTagOneEntity().obs;

  ///合约的日期范围
  DateTime startDateTime = DateTime.now();
  DateTime endDateTime = DateTime.now();

  ///购买开始日期
  var workStartDate = "".obs;

  ///购买结束日期
  var workEndDate = "".obs;

  ///开票类型
  var workTypeList = [
    BaseChooseString("退休返聘合同"),
    BaseChooseString("劳务合同"),
    BaseChooseString("劳动合同"),
    BaseChooseString("兼职合同"),
  ].obs;

  var workTypeIndex = 0.obs;
  var workTypeName = '退休返聘合同'.obs;

  ///获取子标签code
  String getSubLabelCode() {
    // 检查 contractTagEntity 和 subLabelList 是否为空
    if (contractSubEntity.value.subLabelList != null && contractSubEntity.value.subLabelList!.isNotEmpty) {
      // 获取选中的子标签
      final selectedSubLabel = contractSubEntity.value.subLabelList![workTypeIndex.value];
      // 返回选中子标签的 code
      return selectedSubLabel.code ?? '';
    }

    // 如果条件不满足，返回空字符串
    return "";
  }

  /// 获取子标签的下标
  int getSubLabelIndexByCode(String code) {
    // 检查 contractSubEntity 和 subLabelList 是否为空
    if (contractSubEntity.value.subLabelList != null && contractSubEntity.value.subLabelList!.isNotEmpty) {
      // 遍历 subLabelList 查找匹配的 code
      for (int i = 0; i < contractSubEntity.value.subLabelList!.length; i++) {
        final subLabel = contractSubEntity.value.subLabelList![i];
        if (subLabel.code == code) {
          return i; // 找到匹配的 code，返回当前下标
        }
      }
    }

    // 如果未找到匹配的 code，返回默认值 0
    return 0;
  }

  // 解析日期字符串为 DateTime
  DateTime? parseWorkStartTime(String? workStartTimeStr) {
    if (workStartTimeStr == null || workStartTimeStr.isEmpty) {
      return null; // 如果字符串为空或 null，则返回 null
    }
    try {
      // 尝试解析字符串为 DateTime
      return DateTime.parse(workStartTimeStr);
    } catch (e) {
      print("无法解析日期字符串: $workStartTimeStr, 错误: $e");
      return null; // 如果解析失败，返回 null
    }
  }
}
