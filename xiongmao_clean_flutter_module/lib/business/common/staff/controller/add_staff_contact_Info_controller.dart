import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/staff_detail_entity.dart';

class AddStaffContactInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;

  ///关系位置
  var contactIndex = (-1).obs;

  ///关系列表
  var contactList = <BaseChooseString>[].obs;

  ///关系的原始数据
  var relationList = <GetMetaExigencyUserRelationList>[].obs;

  ///手机号
  var phoneController = TextEditingController();

  ///联系人姓名
  var contactNameController = TextEditingController();

  ///联系人手机号
  var contactPhoneController = TextEditingController();

  ///填充信息
  void fillData(StaffDetailEntity staffDetailEntity) {
    StaffDetailContact? contact = staffDetailEntity.contact;
    if (contact != null) {
      phoneController.text = contact.mobile ?? '';
      contactNameController.text = contact.exigencyUserName ?? '';
      contactPhoneController.text = contact.exigencyUserMobile ?? '';
      contactIndex.value = contactList.indexWhere((element) => element.name == contact.exigencyUserRelationName);
    }
  }

  String get relationId {
    if (contactIndex.value == -1) {
      return '0';
    }
    return relationList[contactIndex.value].id ?? '0';
  }
}
