import 'package:flustars/flustars.dart';
import 'package:get/get.dart';

import '../widght/steps.dart';

class AddStaffMainController extends GetxController {


  var channel = 0.obs;
  var jumpIndex = 0.obs;
  var uuid = ''.obs;
  var applicationNo = ''.obs;
  var againEntry = false.obs;

  ///步骤器
  var basicSteps = [
    Steps(
      title: '身份信息',
      isActive: true,
    ),
    Steps(
      title: '联系信息',
    ),
    Steps(
      title: '任职安排',
    ),
    Steps(
      title: '薪酬保险',
    ),
    Steps(
      title: '材料附件',
    ),
  ].obs;

  ///步骤器的下表
  var currentStep = 0.obs;

  void updateStep(int step) {
    if (step >= 0 && step < basicSteps.length) {
      // 更新当前步骤
      currentStep.value = step;

      // 更新步骤的 isActive 状态
      for (int i = 0; i < basicSteps.length; i++) {
        basicSteps[i].isActive = (i <= step); // 当前步骤及之前的都是 true
      }
    }
    print(basicSteps); // 打印步骤的 isActive 状态
  }
}
