import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import '../../../../util/common_utils.dart';

class RoleController extends GetxController {
  /// 用来记录反选的 UUIDs
  var invertUuids = <String>[].obs;

  var list = <PermissionData>[].obs;

  void updatePermissionData(List<PermissionData> data) {
    // 对比 data 和 invertUuids，如果 uuid 在 invertUuids 中，则改变 isSelected 的值
    for (var newData in data) {
      if (invertUuids.contains(newData.id)) {
        newData.isSelected = !newData.isSelected; // 反转选择状态
      }
    }

    list.value = List.from(data); // 更新 permissionEntity
    list.refresh();
  }

  getSelectedItems() {
    return list.where((item) => item.isSelected).toList();
  }
}
