import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import '../../../../util/common_utils.dart';

class PermissionController extends GetxController {
  var permissionEntity = <PermissionData>[].obs;

  void updatePermissionData(List<PermissionData> data) {
    permissionEntity.value = data;
    PermissionData noPermissionData = PermissionData();
    noPermissionData.id = '0';
    noPermissionData.name = '无操作权限';
    noPermissionData.role_desc = '该用户仅存在于花名册中，不可登录APP进行操作';
    permissionEntity.insert(0, noPermissionData);
    permissionEntity.refresh();
  }
}
