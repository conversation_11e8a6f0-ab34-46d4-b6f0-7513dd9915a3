import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/ocr_bank_info_entity.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/staff_detail_entity.dart';

class AddStaffMaterialAttachmentInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;

  ///是否提交了
  var commit = false.obs;

  List<BrnCommonActionSheetItem> actions = [
    BrnCommonActionSheetItem(
      '拍照',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    ),
    BrnCommonActionSheetItem(
      '相册',
      actionStyle: BrnCommonActionSheetItemStyle.normal,
    )
  ];

  var bankPic = ''.obs;

  var headImages = <BaseMediaEntity>[].obs;
  var healthImages = <BaseMediaEntity>[].obs;
  var noCrimeImages = <BaseMediaEntity>[].obs;

  ///入职登记表
  var entryImages = <BaseMediaEntity>[].obs;

  ///银行卡的列表
  var bankOriginalList = <GetMetaBankList>[].obs;
  var bankList = <String>[].obs;

  ///银行卡选中的下标
  var bankIndex = (-1).obs;

  ///银行卡号
  var bankNoController = TextEditingController();

  ///支行银行名
  var banNameController = TextEditingController();

  ///银行名称
  var bankName = ''.obs;
  var bankCode = ''.obs;

  ///填充信息
  void fillData(StaffDetailEntity staffDetailEntity) {
    StaffDetailBank? bank = staffDetailEntity.bank;
    if (bank != null) {
      if (bank.picList != null && bank.picList!.isNotEmpty) {
        bankPic.value = bank.picList![0].sourceUrl ?? '';
      }
      bankNoController.text = bank.bankNo ?? '';
      banNameController.text = bank.bankName ?? '';
      bankName.value = bank.bank ?? '';
      bankCode.value = bank.bankCode ?? '';
    }

    ///头像
    if (!TextUtil.isEmpty(staffDetailEntity.avatar)) {
      headImages.add(BaseMediaEntity(media_type: '1', media_url: staffDetailEntity.avatar));
    }

    ///健康证明
    if (staffDetailEntity.healthyPic != null && staffDetailEntity.healthyPic!.picList != null && staffDetailEntity.healthyPic!.picList!.isNotEmpty) {
      for (StaffDetailHealthyPicPicList item in staffDetailEntity.healthyPic!.picList!) {
        healthImages.add(BaseMediaEntity(media_type: '1', media_url: item.sourceUrl));
      }
    }

    ///无犯罪证明
    if (staffDetailEntity.noCrimePicList != null && staffDetailEntity.noCrimePicList!.isNotEmpty) {
      for (StaffDetailIdentityPicList item in staffDetailEntity.noCrimePicList!) {
        noCrimeImages.add(BaseMediaEntity(media_type: '1', media_url: item.sourceUrl));
      }
    }

    ///入职登记
    if (staffDetailEntity.entryRegistrationPicList != null && staffDetailEntity.entryRegistrationPicList!.isNotEmpty) {
      for (StaffDetailIdentityPicList item in staffDetailEntity.entryRegistrationPicList!) {
        entryImages.add(BaseMediaEntity(media_type: '1', media_url: item.sourceUrl));
      }
    }
  }

  ///ocr识别的信息
  void ocrFillBankData(OcrBankInfoEntity data) {
    if (1 == data.imageStatus || 2 == data.imageStatus) {
      bankNoController.text = data.bankCardNumber ?? '';
      bankName.value = data.bankName ?? '';
      bankCode.value = data.bankCode ?? '';
    }
  }
}
