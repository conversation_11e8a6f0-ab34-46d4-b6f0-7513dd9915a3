import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../project/bean/project_manager_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';

/// 选择离职员工去离职
class StaffResignMainListController extends GetxController {
  ///记录当前的选择哪个uuid
  var projectUuid = "".obs;

  ///输入关键字搜素
  var keyword = "".obs;

  ///自定义的筛选
  var searchProjectName = "搜项目".obs;

  ///考勤规则列表
  var list = <ProjectArchivesList>[].obs;

  var totalNumber = 0.obs;

  void initMyList(int total, List<ProjectArchivesList> value) {
    totalNumber.value = total;
    list.value = value.toList();
  }

  void updateMyList(List<ProjectArchivesList> value) {
    list.value.addAll(value);
    list.value = list.value.toList();
  }

  ///这里是row
  var listRow = <ProjectManagerList>[].obs;
  var originalListRow = <ProjectManagerList>[].obs; // 存储原始数据

  void initRowMyList(List<ProjectManagerList> value) {
    listRow.value = value.toList();
    originalListRow.value = listRow.value;
  }

  void updateRowMyList(List<ProjectManagerList> value) {
    listRow.value.addAll(value);
    listRow.value = listRow.value.toList();
    originalListRow.value = listRow.value.toList();
  }

  ///进行筛选
  void filterProject() {
    if (searchProjectName.value.isEmpty || searchProjectName.value == '搜项目') {
      // 如果 searchProjectName 为空或默认值，恢复为原始数据
      listRow.value = originalListRow.toList();
    } else {
      // 否则，进行筛选
      listRow.value = originalListRow.where((project) {
        bool matchesFirstName = !TextUtil.isEmpty(project.projectShortName) && project.projectShortName!.contains(searchProjectName.value);
        bool matchesSecondName = !TextUtil.isEmpty(project.projectName) && project.projectName!.contains(searchProjectName.value);
        return matchesFirstName || matchesSecondName;
      }).toList();
    }
    if (listRow.isNotEmpty) {
      projectUuid.value = listRow.value[0].uuid!;
    }
  }
}
