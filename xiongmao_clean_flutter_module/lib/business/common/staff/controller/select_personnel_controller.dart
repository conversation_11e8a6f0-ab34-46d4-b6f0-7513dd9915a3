import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import '../../../../util/common_utils.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';

class SelectPersonnelController extends GetxController {
  var list = <ProjectArchivesList>[].obs;

  var totalNumber = "0".obs;

  void initMyList(int total, List<ProjectArchivesList> value) {
    totalNumber.value = total.toString();
    list.value = value.toList();

    // 根据 uuids 更新 isSelected 状态
    _updateSelectedStatus(list.value);

    list.refresh(); // 刷新 Obx 绑定
  }

  void updateMyList(List<ProjectArchivesList> value) {
    list.addAll(value);

    // 根据 uuids 更新 isSelected 状态
    _updateSelectedStatus(list.value);

    list.refresh(); // 刷新 Obx 绑定
  }

  /// 筛选 isSelected 为 true 的数据
  List<ProjectArchivesList> getSelectedItems() {
    return list.value.where((item) => item.isSelected == true).toList();
  }

  /// 更新列表中的 isSelected 状态
  void _updateSelectedStatus(List<ProjectArchivesList> items) {
    // 将 uuids 转换为一个 Set，方便快速查找
    Set<String> selectedUuids = uuids.value.split(',').toSet();

    for (var item in items) {
      if (selectedUuids.contains(item.uuid)) {
        item.isSelected = true; // 匹配到的项目设置为 true
      } else {
        item.isSelected = false; // 其他项目保持为 false
      }
    }
  }

  /// 列表上的参数
  var channel = 0.obs; //用来区分不同的接口处理
  var status = '0'.obs; // 状态 0全部 1在职 2离职
  var role_id = ''.obs; // 1管理员 2项目负责人 3人事 4领班 5保洁 6大区经理
  var is_head_office = '0'.obs; // 是否总部员工 0全部 1是 2否
  var project_uuid = ''.obs; // 指定项目下的
  var contract_start_date = ''.obs; // 合约开始 -- 合约开始日期范围 上周 lastWeek 本周 thisWeek 本月 thisMonth 下月 nextMonth 自定义 日期格式 2023-08-21#2024-08-21
  var contract_end_date = ''.obs; // 合约结束
  var keyword = ''.obs; // 关键字搜索
  var uuids = ''.obs; // 已经选择的数据 是这样的 123123,12312,55433,22222
}
