import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/permission_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/staff_detail_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import '../../../../util/common_utils.dart';

class StaffOneController extends GetxController {
  ///用户的uuid
  var uuid = ''.obs;

  ///身份
  var idNumber = ''.obs;

  ///用户姓名
  var userName = ''.obs;

  ///用户所在的项目
  var projectName = ''.obs;

  ///是否显示删除按钮
  var showDelBut = false.obs;

  ///默认员工在职
  var status = '1'.obs;

  void fillData(StaffDetailEntity data) {

    status.value = data.status ?? '1';

    if (data.identity != null) {
      idNumber.value = data.identity!.idNumber ?? '';
      userName.value = data.identity!.userName ?? '';
      projectName.value = data.workInfo!.projectName ?? '';
    }

    if (data.workInfo != null) {
      // 检查当前用户是否是超管或管理员
      bool isSuperOrManager = httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_MANGER_ID;

      // 如果当前用户是超管或管理员，并且工作信息的角色 ID 是相同或者为 "-1"
      if (isSuperOrManager && (httpConfig.role_id == data.workInfo!.roleId || data.workInfo!.roleId == "-1")) {
        showDelBut.value = false; // 不显示删除按钮
      } else {
        showDelBut.value = true; // 显示删除按钮
      }
    }
  }
}
