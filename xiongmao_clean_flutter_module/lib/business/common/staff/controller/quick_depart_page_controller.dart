import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';

import '../../contract/bean/contract_main_electronic_entity.dart';
import '../../contract/bean/contract_main_info_entity.dart';
import '../../quality_service/bean/base_media_entity.dart';
import '../bean/ele_status_entity.dart';
import '../bean/staff_detail_entity.dart';

class QuickDepartPageController extends GetxController {
  var departDate = "".obs;
  var departType = "自愿离职".obs;

  var staffDetailEntity = StaffDetailEntity().obs;

  var customerIdCard = "".obs;
  var customerId = "".obs;

  var isSeparationContract = false.obs;

  var contractPageInfo = EleStatusEntity().obs;

  var reason = 1.obs;

  var imagesList = <BaseMediaEntity>[].obs;

  void updateImagesList(List<BaseMediaEntity> data) {
    imagesList.value = data;
  }

  void updateIsSeparationContract(bool data) {
    isSeparationContract.value = data;
    if (data) {
      updateSignType('电子合同');
    }
  }

  void updateStaffDetailEntity(StaffDetailEntity data) {
    staffDetailEntity.value = data;
  }

  var signType = "纸质合同".obs; //默认电子合同
  void updateSignType(String data) {
    signType.value = data;
  }

  void updateDepartDate(String data) {
    departDate.value = data;
  }

  void updateDepartType(String data) {
    departType.value = data;
  }

  var bottomBtnTitle = "提交".obs;

  void updateBottomBtnTitle(String s) {
    bottomBtnTitle.value = s;
  }
}
