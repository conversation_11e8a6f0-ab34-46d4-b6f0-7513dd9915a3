import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/salary_config_entity.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../bean/staff_detail_entity.dart';

class AddStaffSalaryInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;

  ///关系位置
  var insureIndex = 0.obs;

  ///参保方案
  var insureDesc = '未匹配到参保方案，请向上级反馈'.obs;

  ///关系列表
  var insureList = [
    BaseChooseString('商业保险'),
    BaseChooseString('社保'),
    BaseChooseString('不参保'),
  ].obs;


  ///合同公司
  var companyName = ''.obs;
  var companyUuid = ''.obs;


  ///薪酬结构
  var salaryOptions = <BaseChooseString>[].obs;
  var salaryOptionsIndex = 0.obs;

  ///基础薪资
  var salaryController = TextEditingController();

  ///填充信息
  void fillData(StaffDetailEntity staffDetailEntity) {
    StaffDetailSalaryInsurance? contact = staffDetailEntity.salaryInsurance;
    if (contact != null) {
      salaryController.text = contact.salary ?? '0';
      if (!TextUtil.isEmpty(contact.insuranceProductName)) {
        insureDesc.value = contact.insuranceProductName!;
      } else {
        insureDesc.value = '未匹配到参保方案，请向上级反馈';
      }

      if ('1' == contact.insuranceType) {
        insureIndex.value = 2;
      } else if ('2' == contact.insuranceType) {
        insureIndex.value = 1;
      } else if ('3' == contact.insuranceType) {
        insureIndex.value = 0;
      }

      ///注意，这里要比对salary的中的itemList 对比id 替换value
      if (contact.itemList!.isNotEmpty) {
        salaryConfig.value = contact.itemList!;
      }

      ///设置当前的档位
      if (!TextUtil.isEmpty(contact.salaryLevel)) {
        salaryOptionsIndex.value = int.parse(contact.salaryLevel!) - 1;
      }
    }
  }

  ///配置
  var salaryConfig = <SalaryConfigItemListSalaryConfigItemList>[].obs; // 或者其他默认值
  ///配置模版的uuid
  var salaryConfigUuid = ''.obs;

  ///原始数据
  var salary = SalaryConfigEntity().obs;

  ///设置薪水的配置 这里是三位数组 level_total是档位数、itemlist里面有[[....]1档,[...]2档,[...]3档]
  void fillSalaryInfoConfig(SalaryConfigEntity data) {
    salaryOptions.clear();
    salaryConfig.clear();

    salary.value = data;

    ///获取uuid
    salaryConfigUuid.value = data.uuid ?? '';

    ///循环data 中的level_total
    if (!TextUtil.isEmpty(data.levelTotal)) {
      int total = int.parse(data.levelTotal!);
      for (int i = 0; i < total; i++) {
        salaryOptions.add(BaseChooseString("${i + 1}档"));
      }

      ///拿到itemlist中的[] 默认拿第一个
      salaryOptionsIndex.value = 0;
      if (data.itemList!.isNotEmpty) {
        salaryConfig.value = data.itemList![0];
      }
    }
  }

  ///更新档位的时候，然后获取下标-1  从item中取数据
  updateSalaryOptions() {
    int index = salaryOptionsIndex.value;
    if (index != -1 && salary.value.itemList != null) {
      salaryConfig.value = salary.value.itemList![index]; // 更新薪水配置
    }
    print("切换的值 ${salaryConfig.toString()}");
    salaryConfig.refresh();
  }

  ///循环着歌itemList 然后组装成一个新的数组返回出去，uuid 跟itemValue 是一组数据 类似于 [{"uuid":"234567","value":"123456"},{"uuid":"234567","value":"123456"}]
  convertData() {
    return salaryConfig.value.map((e) {
      return {"uuid": e.uuid, "value": e.itemValue};
    }).toList();
  }
}
