import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/project/bean/project_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../generated/get_meta_entity.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../project/bean/job_manager_entity.dart';
import '../../roster/bean/attendance_manager_entity.dart';
import '../bean/staff_detail_entity.dart';

class AddStaffPositionArrangementInfoController extends GetxController {
  var uuid = ''.obs;
  var applicationNo = ''.obs;
  var channel = 0.obs;


  ///是否允许IS_ENTRY_BEFORE_TODAY
  var isEntryBeforeToday = false.obs;

  ///关系位置
  var workIndex = 1.obs;

  ///总部项目的信息
  var headOfficeManger = ProjectManagerList().obs;

  ///任职安排
  var workList = [
    BaseChooseString('总部'),
    BaseChooseString('项目'),
  ].obs;

  ///入职日期
  var entryDate = ''.obs;

  ///项目
  var projectName = ''.obs;
  var projectUuid = ''.obs;

  ///岗位
  var jobName = ''.obs;
  var jobUuid = ''.obs;
  var jobIndex = 0.obs;

  var jobManagerList = <JobManagerList>[];

  ///考勤规则
  var attendanceRuleName = ''.obs;
  var attendanceRuleUuid = ''.obs;

  ///角色
  var roleName = ''.obs;
  var roleUuid = ''.obs;

  ///部门
  var departmentName = ''.obs;
  var departmentUuid = ''.obs;

  ///合同公司
  var companyName = ''.obs;
  var companyUuid = ''.obs;

  ///基础薪资
  var jobDescController = TextEditingController();

  ///填充信息
  void fillData(StaffDetailEntity staffDetailEntity) {
    StaffDetailWorkInfo? workInfo = staffDetailEntity.workInfo;

    ///填充信息
    if (workInfo != null) {
      ///判断当前是入职到总部还是项目  是否总部 0未知 1是 2否
      if ('1' == workInfo.isHeadOffice) {
        workIndex.value = 0;
      } else if ('2' == workInfo.isHeadOffice) {
        workIndex.value = 1;
      }

      ///这个争议太大，直接取消，让用户自己选择
      if (channel.value == 3 || channel.value == 4) {
        //如果是档案的话，那么就直接显示出来
        entryDate.value = workInfo.workStartTime ?? '';
      }

      projectUuid.value = workInfo.projectUuid ?? '';
      if (!TextUtil.isEmpty(workInfo.projectShortName)) {
        projectName.value = workInfo.projectShortName ?? '';
      } else {
        projectName.value = workInfo.projectName ?? '';
      }

      jobName.value = workInfo.jobName ?? '';
      jobUuid.value = workInfo.jobUuid ?? '';

      jobDescController.text = workInfo.jobName ?? '';

      departmentName.value = workInfo.departmentName ?? '';
      departmentUuid.value = workInfo.departmentUuid ?? '';

      attendanceRuleName.value = workInfo.groupName ?? '';
      attendanceRuleUuid.value = workInfo.groupUuid ?? '';

      roleName.value = workInfo.roleName ?? '';
      roleUuid.value = workInfo.roleId ?? '';

      companyName.value = workInfo.contractCompanyName ?? '';
      companyUuid.value = workInfo.contractCompanyUuid ?? '';
    }
  }

  ///获取到的考勤列表
  void fillCurrentProjectAttendanceRules(AttendanceManagerEntity data) {
    if (data.list != null && data.list!.isNotEmpty) {
      attendanceRuleName.value = data.list![0].groupName ?? '';
      attendanceRuleUuid.value = data.list![0].uuid ?? '';
    }
  }

  ///清空掉衣镜选择的内容
  void cleanSelect() {
    projectName.value = '';
    projectUuid.value = '';

    jobIndex.value = 0;
    jobName.value = '';
    jobUuid.value = '';

    attendanceRuleName.value = '';
    attendanceRuleUuid.value = '';

    departmentName.value = '';
    departmentUuid.value = '';

    roleName.value = '';
    roleUuid.value = '';
  }

  ///拿到总部成员信息
  void fillHeadOfficeProject(ProjectManagerList data) {
    headOfficeManger.value = data;
  }
}
