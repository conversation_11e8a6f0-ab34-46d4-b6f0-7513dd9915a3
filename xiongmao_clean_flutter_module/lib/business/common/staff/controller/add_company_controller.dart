import 'package:flustars/flustars.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/staff/bean/company_contact_entity.dart';
class AddCompanyController extends GetxController {
  var companyContactEntity = CompanyContactEntity().obs;

  var departmentName = "".obs;
  var departmentUuid = "".obs;
  var roleId = "".obs;
  var roleName = "".obs;

  void updateCompanyContactEntity(CompanyContactEntity data) {
    companyContactEntity.value = data;
    departmentName.value =data.department_name??"";
    departmentUuid.value =data.department_uuid??"";
    roleId.value = data.role_id??"";
    roleName.value = data.role_name??"";
  }

  void updateDepartmentInfo(String? value,String departmentUuidValue) {
    departmentName.value = value??"";
    departmentUuid.value = departmentUuidValue??"";
  }
  void updatePermissionInfo(String? id,String? name) {
    roleId.value = id??'';
    roleName.value = name??'';
  }
}
