import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../approve/bean/approve_detail_entity.dart';
import '../../../../generated/role_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../approve/bean/base_choose_string.dart';
import '../../project/bean/project_manager_entity.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../../workpost/iview/work_post_save_iview.dart';
import '../bean/base_media_entity.dart';
import '../bean/clean_area_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../controller/work_add_plan_controller.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_persenter.dart';

/**
 * 添加工单
 */
class WorkAddTaskPage extends StatefulWidget {
  String? uuid = "";
  String? type = "";
  String? work_url = "";
  String? work_type = "";
  String? isChooseProject = "1";

  WorkAddTaskPage({
    Key? key,
    this.uuid = "",
    this.type = "",
    this.work_url,
    this.work_type,
    this.isChooseProject = "1",
  }) : super(key: key);

  @override
  _WorkAddTaskPageState createState() => _WorkAddTaskPageState();
}

class _WorkAddTaskPageState extends State<WorkAddTaskPage> with BasePageMixin<WorkAddTaskPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkAddTaskPage> implements WorkAddPlanIView {
  WorkAddPlanPresenter? _presenter;

  final WorkAddPlanController _controller = WorkAddPlanController();

  List<BrnMultiSelectBottomPickerItem> monthList = []; //月的存储
  List<BrnMultiSelectBottomPickerItem> endDayList = []; //结束日期的存储
  List<BrnMultiSelectBottomPickerItem> quarterList = []; //季度的存储

  String? title = "添加工单";

  @override
  void initState() {
    super.initState();

    ///如果是编辑，请求接口
    if (!TextUtil.isEmpty(widget.uuid)) {
      title = "编辑工单";
      _presenter?.requestCleanPlanTaskOne(widget.uuid);
    }

    _controller.projectUuid.value = httpConfig.project_uuid;
    _controller.projectName.value = httpConfig.project_name;

    ///赋值给列表
    if (!TextUtil.isEmpty(widget.work_type) && !TextUtil.isEmpty(widget.work_url)) {
      BaseMediaEntity entity = BaseMediaEntity();
      entity.media_type = widget.work_type;
      entity.media_url = widget.work_url;

      _controller.media_list.add(entity);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '$title',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Container(
          margin: EdgeInsets.only(top: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Visibility(
                visible: (widget.isChooseProject == "1"),
                child: BrnTextInputFormItem(
                  isRequire: true,
                  isEdit: false,
                  controller: TextEditingController()..text = _controller.projectName.value,
                  title: "所属项目",
                ),
              ),

              Gaps.line,
              Visibility(
                  visible: (widget.isChooseProject == "2"),
                  child: Obx(() => BrnTextSelectFormItem(
                        title: "所属项目",
                        isRequire: true,
                        value: _controller.projectName.value,
                        onTap: () {
                          BoostNavigator.instance.push('ProjectManagerPage', arguments: {
                            'choose_uuids': _controller.projectUuid.value.split(","),
                            'isSelected': true,
                          }).then((value) {
                            if (value is ProjectManagerList) {
                              _controller.projectUuid.value = value.uuid ?? '';
                              _controller.projectName.value = value.projectShortName ?? '';

                              _controller.clean_area_name.value = "";
                              _controller.clean_area_uuid.value = "";
                            }
                          });
                        },
                      ))),
              Gaps.line,
              Obx(
                () => BrnRadioInputFormItem(
                  title: "来源",
                  isRequire: true,
                  options: const ['内部巡检', '客户投诉'],
                  value: _controller.task_source.value,
                  onChanged: (oldValue, newValue) {
                    _controller.task_source.value = newValue!;
                  },
                ),
              ),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    title: "清洁区域",
                    isRequire: true,
                    value: _controller.clean_area_name.value,
                    onTap: () {
                      BoostNavigator.instance.push("regionPage", arguments: {"choose": true, 'project_uuid': _controller.projectUuid.value}).then((value) {
                        if (value is CleanAreaList) {
                          CleanAreaList area = value;
                          _controller.clean_area_name.value = area.areaName ?? "";
                          _controller.clean_area_uuid.value = area.uuid ?? "";
                        }
                      });
                    },
                  )),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    title: "截止日期",
                    isRequire: true,
                    value: _controller.endDate.value,
                    onTap: () {
                      BrnDatePicker.showDatePicker(
                        themeData: BrnPickerConfig(
                          pickerHeight: 300,
                        ),
                        context,
                        pickerTitleConfig: BrnPickerTitleConfig.Default,
                        pickerMode: BrnDateTimePickerMode.date,
                        dateFormat: 'yyyy年,MMMM月,dd日',
                        onConfirm: (dateTime, list) {
                          _controller.endDate.value = DateUtil.formatDate(dateTime, format: 'yyyy/MM/dd');
                        },
                      );
                    },
                  )),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    title: "执行人",
                    isRequire: true,
                    value: _controller.executor.value,
                    onTap: () {
                      //因为是单选，直接把Uuid 传递过去
                      String is_head_office = "2";
                      if (httpConfig.role_id == HttpConfig.ROLE_SUPER_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_MANGER_ID || httpConfig.role_id == HttpConfig.ROLE_HR_ID || httpConfig.role_id == HttpConfig.ROLE_REGIONAL_MANAGER_ID) {
                        is_head_office = "0";
                      }
                      BoostNavigator.instance.push('selectPersonnelPage', arguments: {
                        'title': '选择执行人',
                        'is_head_office': '$is_head_office',
                        'status': '1',
                        'uuids': _controller.executor_uuid.value,
                      }).then((value) {
                        if (value != null) {
                          List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                          if (data.isNotEmpty) {
                            _controller.executor.value = data[0].userName ?? "";
                            _controller.executor_uuid.value = data[0].uuid ?? "";
                          }
                        }
                      });
                    },
                  )),
              Gaps.line,
              BrnBaseTitle(
                title: "工作内容",
                isRequire: true,
                isEdit: false,
              ),
              BrnInputText(
                maxHeight: 200,
                minHeight: 50,
                autoFocus: false,
                minLines: 1,
                maxLength: 150,
                bgColor: Colors.white,
                textEditingController: _controller.inputRemarkController,
                textInputAction: TextInputAction.newline,
                maxHintLines: 20,
                hint: '请输入',
                padding: EdgeInsets.fromLTRB(20, 10, 20, 14),
                onTextChange: (text) {},
                onSubmit: (text) {},
              ),

              ///九宫格来显示图片
              Obx(() => CustomImageGridView(
                  imageUrls: _controller.media_list.value,
                  maxImageCount: 4,
                  onImageUrlsChanged: (List<BaseMediaEntity> updatedUrls) {
                    _controller.media_list.value = updatedUrls;
                    print('这里是回调在外面的的大小--->${updatedUrls.length}');
                  })),
              Gaps.vGap24
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '提交',
          onTap: () {
            commit();
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  updateStatus() {
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "create_work_order"});
    BrnToast.show("操作成功", context);
    Future.delayed(Duration(seconds: 1), () {
      BoostNavigator.instance.pop();
    });
  }

  ///添加或者编辑提交的内容
  void commit() {
    if (TextUtil.isEmpty(_controller.task_source.value)) {
      BrnToast.show('请选择来源', context);
      return;
    }
    if (TextUtil.isEmpty(_controller.clean_area_uuid.value)) {
      BrnToast.show('请选择清洁区域', context);
      return;
    }
    if (TextUtil.isEmpty(_controller.endDate.value)) {
      BrnToast.show('请选择截止日期', context);
      return;
    }
    if (TextUtil.isEmpty(_controller.executor_uuid.value)) {
      BrnToast.show('请选择执行人', context);
      return;
    }
    var params = <String, String>{};
    params["end_date"] = _controller.endDate.value; //截止日期
    params["execute_user_uuid"] = _controller.executor_uuid.value; //执行人uuid
    params["project_uuid"] = _controller.projectUuid.value; //项目UUID 添加时必传
    params["clean_area_uuid"] = _controller.clean_area_uuid.value; //清洁区域UUID
    if (!TextUtil.isEmpty(_controller.inputRemarkController.text)) {
      params["work_content"] = _controller.inputRemarkController.text; //计划内容
    }
    print("图片的内容->${_controller.media_list}");
    if (_controller.media_list.isNotEmpty) {
      params["media_list"] = jsonEncode(_controller.media_list); //图片 json
    }
    if (!TextUtil.isEmpty(widget.uuid)) {
      params["uuid"] = "${widget.uuid}";
    }

    //来源 1内部巡检 2客户投诉
    if (!TextUtil.isEmpty(_controller.task_source.value)) {
      params["source_type"] = (_controller.task_source.value == '内部巡检') ? '1' : '2';
    }
    _presenter?.requestWorkOrderSave(params);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  deleteStatus() {
  }
}
