import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/bean/base_media_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/item/order_list_item_listview.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../net/http_config.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../controller/work_add_plan_controller.dart';
import '../controller/work_task_controller.dart';
import '../iview/work_add_plan_iview.dart';
import '../iview/work_task_iview.dart';
import '../persenter/work_add_plan_persenter.dart';
import '../persenter/work_task_persenter.dart';

/// 工单详情-工作计划
class WorkPlanTaskOnePage extends StatefulWidget {
  String? uuid = "";
  String? task_type = "";
  bool openCamera;

  WorkPlanTaskOnePage({Key? key, this.uuid = "", required this.task_type, required this.openCamera}) : super(key: key);

  @override
  _WorkPlanTaskOnePageState createState() => _WorkPlanTaskOnePageState();
}

//集成分类 然后实现使用
class _WorkPlanTaskOnePageState extends State<WorkPlanTaskOnePage> with BasePageMixin<WorkPlanTaskOnePage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkPlanTaskOnePage> implements WorkTaskIView {
  final WorkTaskController _controller = WorkTaskController();

  WorkTaskPresenter? _presenter;

  VoidCallback? refreshListener;

  @override
  void initState() {
    super.initState();
    requestOne();
    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      print('收到消息了，开始刷新');
      requestOne();
    });
    print('进来工单详情了，是否要打开相机 - ${widget.openCamera}');

    ///延迟3秒去发送消息给原生
    if (widget.openCamera) {
      Future.delayed(const Duration(seconds: 1), () {
        BoostChannel.instance.sendEventToNative("native_CommonEvent", {
          "method": "goto_mark_camera",
          'uuid': widget.uuid ?? '',
          'project_uuid': _controller.projectUuid.value,
        });
      });
    }
  }

  Future<void> requestOne() async {
    try {
      if (TextUtil.isEmpty(widget.uuid)) {
        return;
      }
      await _presenter?.requestCleanPlanTaskOne(widget.uuid);
    } catch (e) {
      // Handle error here
      print('Error fetching data: $e');
    }
  }

  @override
  void dispose() {
    super.dispose();
    refreshListener?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: '详情',
            actionName: ('20' == widget.task_type && '1' == _controller.isSelf.value) ? '编辑' : '',
            onPressed: () {
              if ('20' == widget.task_type && '1' == _controller.isSelf.value) {
                BoostNavigator.instance.push("workAddTaskPage", arguments: {
                  "uuid": widget.uuid,
                }).then((value) => _presenter?.requestCleanPlanTaskOne(widget.uuid));
              }
            },
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: RefreshIndicator(
            onRefresh: requestOne,
            child: ListView(
              children: [
                Container(
                  width: double.infinity,
                  color: Colors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Visibility(
                        child: CommonUtils.getSimpleText(_controller.cleanAreaName.value, 18, Colours.base_primary, fontWeight: FontWeight.bold),
                        visible: (!TextUtil.isEmpty(_controller.cleanAreaName.value)),
                      ),
                      Gaps.vGap4,
                      CommonUtils.getSimpleText(_controller.cleanContent.value, 16, Colours.base_primary_text_title, fontWeight: FontWeight.bold),

                      ///工作的照片
                      CustomImageGridView(
                        imageUrls: _controller.workImages.value,
                        maxImageCount: 9,
                        showAddButton: false,
                        showDelButton: false,
                        includeEdge: false,
                      ),
                      Row(
                        children: [
                          CommonUtils.getSimpleText('所属项目', 16, Colours.base_primary_text_caption),
                          Expanded(child: CommonUtils.getSimpleText(_controller.projectName.value, 16, Colours.base_primary_text_title, textAlign: TextAlign.right, overflow: TextOverflow.ellipsis)),
                        ],
                      ),
                      Gaps.vGap6,
                      Row(
                        children: [
                          CommonUtils.getSimpleText('来源', 16, Colours.base_primary_text_caption),
                          Expanded(child: CommonUtils.getSimpleText(_controller.taskSource.value, 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                        ],
                      ),
                      Gaps.vGap6,
                      Row(
                        children: [
                          CommonUtils.getSimpleText('截止日期', 16, Colours.base_primary_text_caption),
                          Expanded(child: CommonUtils.getSimpleText(_controller.endDate.value, 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                        ],
                      ),
                      Gaps.vGap6,
                      Row(
                        children: [
                          CommonUtils.getSimpleText('执行人', 16, Colours.base_primary_text_caption),
                          Expanded(child: CommonUtils.getSimpleText(_controller.executorName.value, 16, Colours.base_primary_text_title, textAlign: TextAlign.right)),
                        ],
                      ),
                    ],
                  ),
                ),
                Gaps.vGap10,
                Container(
                  width: double.infinity,
                  color: Colors.white,
                  padding: EdgeInsets.symmetric(vertical: 16, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      CommonUtils.getSimpleText('处理结果', 18, Colours.base_primary_text_title),
                      Gaps.vGap4,
                      CustomImageGridView(
                        imageUrls: _controller.workFinishImages.value,
                        maxImageCount: 9,
                        showAddButton: true,
                        showDelButton: true,
                        addButtonGotoNative: true,
                        includeEdge: false,
                        onAddImageChanged: () {
                          BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                            "method": "goto_mark_camera",
                            'uuid': widget.uuid ?? '',
                            'project_uuid': _controller.projectUuid.value,
                          });
                        },
                        onDelCustomChanged: (index) {
                          _presenter?.delCleanPlanOnePic(widget.uuid ?? '', _controller.originalWorkFinishImages[index].uuid ?? '');
                        },
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
            child: Row(
              children: [
                Expanded(
                    child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: Border.all(color: Colours.base_primary),
                    borderRadius: BorderRadius.circular(5.0),
                  ),
                  child: InkWell(
                    onTap: () {
                      BoostNavigator.instance.push("jigsawPuzzlePage", arguments: {
                        "task_uuid": widget.uuid,
                        'reporter_name': httpConfig.user_name,
                      }).then((value) => _presenter?.requestCleanPlanTaskOne(widget.uuid));
                    },
                    child: CommonUtils.getSimpleText('拼图汇报', 16, Colours.base_primary, textAlign: TextAlign.center, fontWeight: FontWeight.bold),
                  ),
                )),
                Gaps.hGap10,
                Expanded(
                  flex: 2,
                  child: InkWell(
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Container(
                        height: 40,
                        color: Colours.base_primary,
                        alignment: Alignment.center,
                        child: CommonUtils.getSimpleText(
                          (_controller.taskStatus.value == '2' || _controller.taskStatus.value == '3') ? '处理完成' : '已于${_controller.finishDate.value}处理完毕',
                          16,
                          Colours.white,
                          textAlign: TextAlign.center,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    onTap: () {
                      if ('1' != _controller.taskStatus.value) {
                        _presenter?.finishTask(widget.uuid ?? '');
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkTaskPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => true;

  @override
  updateStatus() {}
}
