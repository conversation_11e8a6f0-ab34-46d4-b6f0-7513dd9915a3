import 'dart:convert';
import 'dart:ffi';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../generated/role_detail_entity.dart';
import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/custom_sing_column_picker.dart';
import '../../../../widgets/select_tab/select_tab_widget.dart';
import '../../risk_monitoring/bean/project_archives_entity.dart';
import '../bean/base_media_entity.dart';
import '../bean/clean_area_entity.dart';
import '../controller/work_add_plan_controller.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 添加工作计划
class WorkAddPlanPage extends StatefulWidget {
  String? uuid = "";
  String? type = "1";
  String? project_name;
  String? project_uuid;

  WorkAddPlanPage({Key? key, this.uuid = "", this.type = "", this.project_name, this.project_uuid}) : super(key: key);

  @override
  _WorkAddPlanPageState createState() => _WorkAddPlanPageState();
}

class _WorkAddPlanPageState extends State<WorkAddPlanPage> with BasePageMixin<WorkAddPlanPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkAddPlanPage> implements WorkAddPlanIView {
  WorkAddPlanPresenter? _presenter;

  final WorkAddPlanController _controller = WorkAddPlanController();

  List<BrnMultiSelectBottomPickerItem> monthList = []; //月的存储
  List<String> endDayList = []; //结束日期的存储
  List<BrnMultiSelectBottomPickerItem> quarterList = []; //季度的存储

  String? title = "添加清洁计划";

  @override
  void initState() {
    super.initState();

    _controller.projectName.value = widget.project_name ?? httpConfig.project_name;
    _controller.projectUuid.value = widget.project_uuid ?? httpConfig.project_uuid;

    switch (widget.type) {
      case "1":
        if (!TextUtil.isEmpty(widget.uuid)) {
          title = "编辑清洁计划";
        } else {
          title = "添加清洁计划";
        }
        break;
      case "2":
        if (!TextUtil.isEmpty(widget.uuid)) {
          title = "编辑巡检计划";
        } else {
          title = "添加巡检计划";
        }
        break;
      case "3":
        if (!TextUtil.isEmpty(widget.uuid)) {
          title = "编辑培训计划";
        } else {
          title = "添加培训计划";
        }
        break;
    }

    ///循环每个月增加数据
    for (int i = 1; i <= 28; i++) {
      if (i == 28) {
        monthList.add(BrnMultiSelectBottomPickerItem(i.toString(), "每月最后1天"));
      } else {
        monthList.add(BrnMultiSelectBottomPickerItem(i.toString(), "$i号"));
      }
    }

    ///循环每个季度增加数据
    for (int i = 1; i <= 90; i++) {
      if (i == 90) {
        quarterList.add(BrnMultiSelectBottomPickerItem(i.toString(), "最后1天"));
      } else {
        quarterList.add(BrnMultiSelectBottomPickerItem(i.toString(), "第$i天"));
      }
    }

    ///循环加入截止日期的数据
    for (int i = 1; i <= 7; i++) {
      endDayList.add("开始后的第$i天结束");
    }

    ///如果是编辑，请求接口
    if (!TextUtil.isEmpty(widget.uuid)) {
      _presenter?.requestCleanPlanOne(widget.uuid, true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: '$title',
        onBack: () {
          BoostNavigator.instance.pop();
        },
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: SingleChildScrollView(
        child: Container(
          margin: EdgeInsets.only(top: 10),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              BrnTextInputFormItem(
                controller: TextEditingController()..text = (!TextUtil.isEmpty(widget.project_name)) ? widget.project_name ?? '' : httpConfig.project_name,
                title: "所属项目",
                isEdit: false,
              ),
              Gaps.line,
              Obx(() => Visibility(
                    visible: widget.type == '1',
                    child: BrnTextSelectFormItem(
                      title: "清洁区域",
                      value: _controller.clean_area_name.value,
                      onTap: () {
                        BoostNavigator.instance.push("regionPage", arguments: {"choose": true, "project_uuid": widget.project_uuid}).then((value) {
                          if (value is CleanAreaList) {
                            CleanAreaList area = value;
                            _controller.clean_area_name.value = area.areaName ?? "";
                            _controller.clean_area_uuid.value = area.uuid ?? "";
                          }
                        });
                      },
                    ),
                  )),
              Gaps.line,
              Obx(() => BrnRadioInputFormItem(
                    title: "循环",
                    isRequire: true,
                    options: const ["每周", "每月", "每季度"],
                    value: _controller.currentDateText.value,
                    //默认锁定第一个
                    onChanged: (oldValue, newValue) {
                      Map<String, int> dateMap = {
                        '每周': 0,
                        '每月': 1,
                      };
                      _controller.week_invert.value = [];
                      _controller.startDate.value = "";
                      _controller.currentDateText.value = newValue!;
                      _controller.currentDate.value = dateMap[newValue] ?? 2;
                    },
                  )),
              Gaps.line,

              ///每周的开始日期

              Obx(() => Visibility(
                  visible: _controller.currentDateText.value == '每周',
                  child: Container(
                    color: Colors.white,
                    padding: EdgeInsets.only(top: 10, bottom: 10, right: 10),
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(padding: const EdgeInsets.only(top: 10, left: 10), child: CommonUtils.getSimpleText("*", 16, Colours.red)),
                        Padding(padding: const EdgeInsets.only(top: 10, left: 2, right: 4), child: CommonUtils.getSimpleText("单次任务开始", 16, Colours.base_primary_text_title)),
                        Expanded(
                            child: Container(
                          margin: const EdgeInsets.only(left: 0, right: 0),
                          child: SelectTabWidget(
                            _controller.weekList.value,
                            multiSelect: true,
                            crossAxisCount: 4,
                            hideMore: false,
                            paddingBottom: 0,
                            paddingTop: 0,
                            tabFontSize: 13,
                            defaultSelectedIndex: (_controller.currentDateText.value == '每周') ? _controller.week_invert : [],
                            lastIsAddOne: false,
                            selectedColor: Colours.base_primary,
                            bgSelectedColor: Colours.base_primary_select,
                            bgUnSelectedColor: Colours.base_primary_un_select,
                            childAspectRatio: 6 / 4,
                            itemClickCallback: (List<int> indexs) {
                              LogUtil.e("indexs = $indexs");
                              _controller.startDate.value = indexs.map((element) => element + 1).toList().join(",");
                            },
                          ),
                        )),
                      ],
                    ),
                  ))),

              Obx(() => Visibility(
                  visible: _controller.currentDate.value != 0,
                  child: BrnTextSelectFormItem(
                    valueMaxLines: 3,
                    titleMaxLines: 3,
                    isRequire: true,
                    title: "单次任务开始",
                    value: _controller.startDate.value,
                    onTap: () {
                      BrnMultiSelectListPicker.show(
                        context,
                        items: (_controller.currentDate.value == 1) ? monthList : quarterList,
                        pickerTitleConfig: const BrnPickerTitleConfig(titleContent: "单次任务开始"),
                        onItemClick: (_, index) {
                          if (((_controller.currentDate.value == 1) ? monthList : quarterList).where((element) => element.isChecked).length > 7) {
                            BrnToast.show("最多可以选七个", context);
                            ((_controller.currentDate.value == 1) ? monthList : quarterList)[index].isChecked = false;
                            setState(() {});
                          }
                        },
                        onSubmit: (List<BrnMultiSelectBottomPickerItem> data) {
                          var str = "";
                          data.forEach((item) {
                            str = str + item.content + ",";
                          });
                          Navigator.of(context).pop();
                          if (str.isNotEmpty) {
                            _controller.startDate.value = str.substring(0, str.length - 1);
                          } else {
                            _controller.startDate.value = '';
                          }
                        },
                      );
                    },
                  ))),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    title: "单次任务结束",
                    isRequire: true,
                    value: _controller.endDate.value,
                    onTap: () {
                      SingleColumnDataPickerView.showSingleColumnDataPicker(context, '单次任务结束', endDayList, -1, (index, selectedText) {
                        print('当前选择的下标：$index');
                        print('当前选择的文本内容：$selectedText');
                        _controller.endDate.value = "$selectedText";
                        _controller.endDateIndex.value = "${index + 1}";
                      });
                    },
                  )),
              Gaps.line,
              Obx(() => BrnTextSelectFormItem(
                    title: "执行人",
                    isRequire: true,
                    value: _controller.executor.value,
                    onTap: () {
                      BoostNavigator.instance.push('selectPersonnelPage', arguments: {'title': '选择执行人', 'status': '1', 'uuids': _controller.executor_uuid.value}).then((value) {
                        if (value != null) {
                          List<ProjectArchivesList> data = value as List<ProjectArchivesList>;
                          if (data.isNotEmpty) {
                            _controller.executor.value = data[0].userName ?? "";
                            _controller.executor_uuid.value = data[0].uuid ?? "";
                          }
                        }
                      });
                    },
                  )),
              Gaps.line,
              BrnBaseTitle(
                title: "工作内容",
                isRequire: true,
                isEdit: false,
                themeData: BrnFormItemConfig(formPadding: EdgeInsets.only(top: 10, bottom: 0)),
              ),
              BrnInputText(
                maxHeight: 200,
                minHeight: 50,
                autoFocus: false,
                minLines: 1,
                bgColor: Colors.white,
                textEditingController: _controller.inputRemarkController,
                textInputAction: TextInputAction.newline,
                maxHintLines: 20,
                hint: '请输入',
                padding: EdgeInsets.fromLTRB(20, 10, 20, 14),
                onTextChange: (text) {},
                onSubmit: (text) {},
              ),

              ///九宫格来显示图片
              Obx(() => CustomImageGridView(
                  imageUrls: _controller.media_list.value,
                  maxImageCount: 3,
                  onImageUrlsChanged: (List<BaseMediaEntity> updatedUrls) {
                    _controller.media_list.value = updatedUrls;
                    print('这里是回调在外面的的大小--->${updatedUrls.length}');
                  })),
              Gaps.vGap24,
            ],
          ),
        ),
      ),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
        child: BrnBigMainButton(
          themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
          title: '提交',
          onTap: () {
            commit();
          },
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  updateStatus() {
    BrnToast.show("操作成功", context);
    BoostNavigator.instance.pop();
  }

  ///添加或者编辑提交的内容
  void commit() {
    if (TextUtil.isEmpty(_controller.startDate.value)) {
      BrnToast.show('请选择单次任务开始', context);
      return;
    }
    if (TextUtil.isEmpty(_controller.endDate.value)) {
      BrnToast.show('请选择单次任务结束', context);
      return;
    }
    if (TextUtil.isEmpty(_controller.executor_uuid.value)) {
      BrnToast.show('请选择执行人', context);
      return;
    }
    print("单次任务开始" + _controller.startDate.value.replaceAll("第", "").replaceAll("天", "").replaceAll("号", ""));
    var params = <String, String>{};
    params["plan_type"] = "${widget.type}"; //计划类型 1清洁计划 2巡检计划 3培训计划
    params["loop_type"] = "${_controller.currentDate.value.toInt() + 1}"; //循环频率 1每周 2每月 3每季度
    params["start_date"] = _controller.startDate.value.replaceAll("第", "").replaceAll("天", "").replaceAll("号", ""); //开始日期
    params["end_date"] = _controller.endDateIndex.value; //单次任务结束
    params["execute_user_uuid"] = _controller.executor_uuid.value; //执行人uuid
    params["project_uuid"] = widget.project_uuid ?? ''; //项目UUID 添加时必传
    params["clean_area_uuid"] = _controller.clean_area_uuid.value; //清洁区域UUID
    if (!TextUtil.isEmpty(_controller.inputRemarkController.text)) {
      params["plan_content"] = _controller.inputRemarkController.text; //计划内容
    }
    print("情况 ${_controller.media_list.value}");
    if (_controller.media_list.length > 0) {
      params["media_list"] = jsonEncode(_controller.media_list); //图片 json
    }
    if (!TextUtil.isEmpty(widget.uuid)) {
      params["uuid"] = "${widget.uuid}";
    }
    _presenter?.requestCleanPlanSave(params);
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  deleteStatus() {}
}
