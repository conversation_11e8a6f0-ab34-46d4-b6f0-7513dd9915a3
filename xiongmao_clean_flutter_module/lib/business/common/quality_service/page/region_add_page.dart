import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/presenter/work_post_add_edit_persenter.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../res/colors.dart';
import '../../../../util/brn_config_utils.dart';
import '../../workpost/iview/work_post_save_iview.dart';
import '../controller/work_add_plan_area_controller.dart';
import '../controller/work_add_plan_controller.dart';
import '../iview/work_add_plan_area_iview.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_area_persenter.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 添加清洁区域
class RegionAddPage extends StatefulWidget {
  String? uuid = "";
  String? project_uuid = "";
  String? work_name = "";

  RegionAddPage({Key? key, this.uuid = "", this.project_uuid, this.work_name = ""}) : super(key: key);

  @override
  _RegionAddPageState createState() => _RegionAddPageState();
}

class _RegionAddPageState extends State<RegionAddPage> with BasePageMixin<RegionAddPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RegionAddPage> implements WorkAddPlanAreaIView {
  WorkAddPlanAreaPresenter? _presenter;

  final _editNameControl = TextEditingController();

  final WorkAddPlanAreaController _controller = WorkAddPlanAreaController();

  String title = "添加清洁区域";

  @override
  void initState() {
    super.initState();
    if (!TextUtil.isEmpty(widget.work_name)) {
      _editNameControl.text = widget.work_name!;
    }
    if (!TextUtil.isEmpty(widget.uuid)) {
      title = "编辑清洁区域";
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        centerTitle: title,
      ),
      backgroundColor: Colours.base_primary_bg_page,
      body: Container(
        margin: EdgeInsets.only(top: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            BrnTextInputFormItem(
              title: "区域名称",
              isRequire: true,
              autofocus: true,
              maxCharCount: 15,
              hint: "请输入",
              controller: _editNameControl,
            ),
            Padding(
              padding: EdgeInsets.all(10),
              child: Text(
                "如：男卫生间、女卫生间、老板办公室等",
                style: TextStyle(
                  fontSize: 11,
                  color: Colors.grey,
                ),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 16, right: 16),
              child: BrnBigMainButton(
                themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
                title: '确定',
                onTap: () {
                  _presenter?.requestCleanAreaSave(_editNameControl.text.toString(), widget.uuid, widget.project_uuid);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanAreaPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  updateStatus() {
    //不管新增还是新建都主动刷新上一层界面
    BrnToast.show("操作成功", context);
    BoostNavigator.instance.pop();
  }
}
