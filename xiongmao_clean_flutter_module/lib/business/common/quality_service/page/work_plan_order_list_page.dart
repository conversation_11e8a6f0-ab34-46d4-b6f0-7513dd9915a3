import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/item/order_list_item_listview.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/custom_report_selector.dart';
import '../../../../widgets/custom_selected_arrow_view.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../workpost/item/work_post_listview.dart';
import '../../workpost/iview/work_post_iview.dart';
import '../controller/work_add_plan_controller.dart';
import '../controller/work_add_plan_order_list_controller.dart';
import '../item/region_item_listview.dart';
import '../item/work_plan_item_listview.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_order_persenter.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 工单列表
class WorkPlanOrderListPage extends StatefulWidget {
  WorkPlanOrderListPage({Key? key}) : super(key: key);

  @override
  _WorkPlanOrderListPageState createState() => _WorkPlanOrderListPageState();
}

//集成分类 然后实现使用
class _WorkPlanOrderListPageState extends State<WorkPlanOrderListPage> with BasePageMixin<WorkPlanOrderListPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkPlanOrderListPage>, SingleTickerProviderStateMixin implements WorkAddPlanIView {
  WorkAddPlanOrderPresenter? _presenter;

  final WorkAddPlanOrderController _controller = WorkAddPlanOrderController();

  String title = "工作进展";

  VoidCallback? addProjectListener;
  VoidCallback? refreshListener;

  final ScrollController _scrollController = ScrollController();

  DateTime currentMonth = DateTime.now();
  DateTime currentDay = DateTime.now();

  late final TabController _tabController = TabController(length: 2, vsync: this, initialIndex: currentIndex);

  int currentIndex = 0;

  @override
  void initState() {
    super.initState();

    _controller.projectName.value = httpConfig.project_name;
    _controller.projectUuid.value = httpConfig.project_uuid;

    // 格式化年份和月份
    _controller.searchDay.value = getFormattedDate(currentDay);
    _controller.searchMonth.value = getFormattedDate(currentMonth);

    _onRefresh();

    refreshListener ??= BoostChannel.instance.addEventListener("refresh", (key, arguments) async {
      _onRefresh();
    });

    ///监听从原生选择项目的内容
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      setState(() {
        _controller.projectName.value = project_name;
        _controller.projectUuid.value = project_uuid;
        _onRefresh();
      });
    });

    //滑动的监听
    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        _loadMore();
      }
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh(_controller.projectUuid.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore(_controller.projectUuid.value);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: title,
            centerSubTitle: _controller.projectName.value,
            onBack: () {
              BoostNavigator.instance.pop();
            },
            actionWidget: Visibility(
              visible: (httpConfig.role_id != HttpConfig.ROLE_PROJECT_OWNER_ID && httpConfig.role_id != HttpConfig.ROLE_LEADER_ID), //领班、项目负责人 不可以看到
              child: Padding(
                padding: const EdgeInsets.only(right: 16),
                child: Row(
                  children: [
                    InkWell(
                      onTap: () {
                        ///通过原生来选择项目
                        BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                          "method": "goto_show_project_dialog",
                          "isChangeAppProject": CommonUtils.checkRoleHeadOffice(),
                          'isNeedAll': CommonUtils.checkRoleHeadOffice(),
                          'isHeadOffice': CommonUtils.checkRoleHeadOffice(),
                          'project_uuid': _controller.projectUuid.value,
                        });
                      },
                      child: const Padding(
                        padding: EdgeInsets.only(left: 10),
                        child: LoadAssetImage(
                          "icon_change",
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: Column(
            children: [
              Container(
                padding: const EdgeInsets.only(top: 10, bottom: 10, left: 10, right: 10),
                color: Colors.white,
                child: Row(
                  children: [
                    Expanded(
                        child: CustomReportSelector(
                      defSelectedIndex: 1,
                      options: const ['日报', '月报'],
                      onSelectedIndexChanged: (index) {
                        setState(() {
                          _controller.reportType = index;
                          if (index == 0) {
                            _controller.searchDay.value = getFormattedDate(currentDay);
                          } else {
                            _controller.searchMonth.value = getFormattedDate(currentMonth);
                          }
                          print('切换  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
                        });
                        _onRefresh();
                      },
                    )),
                    Gaps.hGap10,
                    Expanded(
                        flex: 2,
                        child: CustomSelectedArrowView(
                          dateText: (_controller.reportType == 0) ? _controller.searchDay.value : _controller.searchMonth.value,
                          onDateTextPressed: () {
                            BrnDatePicker.showDatePicker(
                              themeData: BrnPickerConfig(
                                pickerHeight: 300,
                              ),
                              context,
                              initialDateTime: (_controller.reportType == 0) ? currentDay : currentMonth,
                              pickerMode: BrnDateTimePickerMode.date,
                              dateFormat: (_controller.reportType == 1) ? 'yyyy年,MMMM月' : 'yyyy年,MMMM月,dd日',
                              onConfirm: (dateTime, list) {
                                if (_controller.reportType == 0) {
                                  _controller.searchDay.value = DateUtil.formatDate(dateTime, format: (_controller.reportType == 1) ? 'yyyy-MM' : 'yyyy-MM-dd');
                                  currentDay = dateTime;
                                } else {
                                  _controller.searchMonth.value = DateUtil.formatDate(dateTime, format: (_controller.reportType == 1) ? 'yyyy-MM' : 'yyyy-MM-dd');
                                  currentMonth = dateTime;
                                }
                                _onRefresh();
                              },
                            );
                          },
                          onNextDayPressed: () {
                            _nextMonth();
                          },
                          onPreviousDayPressed: () {
                            _previousMonth();
                          },
                        )),
                  ],
                ),
              ),
              Container(
                color: Colors.white,
                width: double.infinity,
                alignment: Alignment.center,
                child: TabBar(
                  indicatorColor: Colours.base_primary,
                  isScrollable: false,
                  controller: _tabController,
                  tabs: [
                    Tab(
                      text: '未完成 ${_controller.unFinishTotal.value}',
                    ),
                    Tab(
                      text: '已完成 ${_controller.finishTotal.value}',
                    ),
                  ],
                  labelColor: Colours.base_primary,
                  unselectedLabelColor: Colours.base_primary_text_title.withOpacity(0.6),
                  labelStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: Colours.base_primary,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.normal,
                  ),
                  onTap: (index) {
                    if (0 == index) {
                      _controller.status.value = '5';
                    } else {
                      _controller.status.value = '1';
                    }
                    _onRefresh();
                  },
                ),
              ),
              Expanded(
                  child: MyRefreshListView(
                itemCount: _controller.listTask.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                hasMore: int.parse(_controller.listTotal.value) > _controller.listTask.length,
                itemBuilder: (_, index) {
                  return OrderListItem(
                      data: _controller.listTask[index],
                      onClick: () {
                        BoostNavigator.instance.push('WorkPlanTaskOnePage', arguments: {
                          'uuid': "${_controller.listTask[index].uuid}",
                        });
                      });
                },
              )),
            ],
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanOrderPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  ///上一天
  void _previousMonth() {
    if (_controller.reportType == 0) {
      currentDay = DateTime(currentDay.year, currentDay.month, currentDay.day - 1);
    } else {
      currentMonth = DateTime(currentMonth.year, currentMonth.month - 1);
    }
    _updateSearchValues();
    print('切换上  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
    _onRefresh();
  }

  ///下一天
  void _nextMonth() {
    if (_controller.reportType == 0) {
      currentDay = DateTime(currentDay.year, currentDay.month, currentDay.day + 1);
    } else {
      currentMonth = DateTime(currentMonth.year, currentMonth.month + 1);
    }
    _updateSearchValues();
    print('切换下  ${_controller.searchDay.value} - ${_controller.searchMonth.value}');
    _onRefresh();
  }

  void _updateSearchValues() {
    _controller.searchDay.value = getFormattedDate(currentDay);
    _controller.searchMonth.value = getFormattedDate(currentMonth);
  }

  String getFormattedDate(DateTime date) {
    if (_controller.reportType == 0) {
      return "${date.year.toString()}-${(date.month.toString().padLeft(2, '0'))}-${(date.day.toString().padLeft(2, '0'))}";
    } else {
      return "${date.year.toString()}-${(date.month.toString().padLeft(2, '0'))}";
    }
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
    refreshListener?.call();
  }

  @override
  updateStatus() {
    _onRefresh();
  }

  @override
  deleteStatus() {}
}
