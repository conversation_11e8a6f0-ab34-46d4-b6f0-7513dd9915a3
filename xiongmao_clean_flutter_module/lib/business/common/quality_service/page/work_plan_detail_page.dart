import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/item/order_list_item_listview.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../net/http_config.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../util/toast_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../controller/work_add_plan_controller.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 工作计划-详情
class WorkPlanDetailPage extends StatefulWidget {
  String? uuid = "";
  String? type = "";
  String? project_name;
  String? project_uuid;

  WorkPlanDetailPage({Key? key, this.uuid = "", this.type = "", this.project_name, this.project_uuid}) : super(key: key);

  @override
  _WorkPlanDetailPageState createState() => _WorkPlanDetailPageState();
}

//集成分类 然后实现使用
class _WorkPlanDetailPageState extends State<WorkPlanDetailPage> with BasePageMixin<WorkPlanDetailPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkPlanDetailPage> implements WorkAddPlanIView {
  final WorkAddPlanController _controller = WorkAddPlanController();

  WorkAddPlanPresenter? _presenter;

  ScrollController _scrollController = ScrollController();

  int _page = 1;

  @override
  void initState() {
    super.initState();
    _controller.projectName.value = widget.project_name ?? httpConfig.project_name;
    _controller.projectUuid.value = widget.project_uuid ?? httpConfig.project_uuid;

    _scrollController.addListener(() {
      if (_scrollController.position.pixels == _scrollController.position.maxScrollExtent) {
        _loadMore();
      }
    });
    _onRefresh();
  }

  Future<dynamic> _onRefresh() async {
    _page = 1;
    _presenter?.requestCleanPlanOne(widget.uuid, false);
    _presenter?.requestCleanPlanTaskList(widget.uuid, "$_page");
  }

  Future<dynamic> _loadMore() async {
    _page++;
    _presenter?.requestCleanPlanTaskList(widget.uuid, "$_page");
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          centerTitle: '计划详情',
          onBack: () {
            BoostNavigator.instance.pop();
          }),
      backgroundColor: Colours.base_primary_bg_page,
      body: WillPopScope(
          child: Obx(() => RefreshIndicator(
                onRefresh: _onRefresh,
                child: ListView(
                  controller: _scrollController,
                  children: [
                    Container(
                      color: Colours.white,
                      padding: EdgeInsets.only(left: 16, right: 16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Gaps.vGap10,

                          ///这里是清理的项目
                          CommonUtils.getSimpleText(_controller.planOneEntity.value.cleanAreaName ?? "-", 18, Colours.base_primary_blue, fontWeight: FontWeight.bold),

                          ///清理的情况
                          CommonUtils.getSimpleText('${_controller.planOneEntity.value.planContent}', 16, Colours.base_primary_text_title),

                          Gaps.vGap12,

                          ///这里是图片的占位
                          CustomImageGridView(
                            maxImageCount: 4,
                            imageUrls: _controller.planTaskImgs,
                            showAddButton: false,
                            showDelButton: false,
                            includeEdge: false,
                          ),

                          Gaps.vGap16,

                          ///文字信息
                          Row(
                            children: [
                              CommonUtils.getSimpleText('所属项目', 14, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText('${_controller.planOneEntity.value.projectName}', 14, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                              )
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            children: [
                              CommonUtils.getSimpleText('状态', 14, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText('${_controller.planOneEntity.value.planStatusName}', 14, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                              )
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              CommonUtils.getSimpleText("单次任务开始", 13, Colours.base_primary_text_caption),
                              Gaps.hGap20,
                              Expanded(child: CommonUtils.getSimpleText('${_controller.planOneEntity.value.startDateName}', 14, Colours.base_primary_text_caption, textAlign: TextAlign.right)),
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            children: [
                              CommonUtils.getSimpleText('单次任务结束', 14, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText('${_controller.planOneEntity.value.endDateName}', 14, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                              )
                            ],
                          ),
                          Gaps.vGap4,
                          Row(
                            children: [
                              CommonUtils.getSimpleText('执行人', 14, Colours.base_primary_text_caption),
                              Expanded(
                                child: CommonUtils.getSimpleText('${_controller.planOneEntity.value.executeUserName}', 14, Colours.base_primary_text_caption, textAlign: TextAlign.right),
                              )
                            ],
                          ),
                          Gaps.vGap10,
                        ],
                      ),
                    ),

                    ///完成情况
                    Visibility(
                      visible: _controller.listTask.isNotEmpty,
                      child: Padding(
                        padding: const EdgeInsets.only(left: 10, top: 10, bottom: 10),
                        child: CommonUtils.getSimpleText('完成情况', 16, Colours.base_primary_text_title),
                      ),
                    ),

                    ///任务子详情
                    ListView.builder(
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      itemCount: _controller.listTask.length,
                      itemBuilder: (context, position) {
                        return OrderListItem(
                            data: _controller.listTask[position],
                            onClick: () {
                              BoostNavigator.instance.push('WorkPlanTaskOnePage', arguments: {
                                'uuid': "${_controller.listTask[position].uuid}",
                              });
                            });
                      },
                    ),
                  ],
                ),
              )),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Container(
        color: Colors.white,
        padding: const EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
        child: Row(
          children: [
            ///删除计划
            Expanded(
                child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: InkWell(
                onTap: () {
                  DialogManager.showConfirmDialog(
                    context: context,
                    title: '提示',
                    cancel: '取消',
                    confirm: '确定',
                    message: '是否确认删除？',
                    onConfirm: () {
                      _presenter?.requestDelCleanPlan(_controller.planOneEntity.value.uuid ?? "");
                    },
                    onCancel: () {},
                  );
                },
                child: CommonUtils.getSimpleText('删除', 14, Colours.base_primary, textAlign: TextAlign.center),
              ),
            )),

            Gaps.hGap10,

            /// 停用计划
            Expanded(
                child: Container(
              height: 40,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                border: Border.all(color: Colours.base_primary),
                borderRadius: BorderRadius.circular(5.0),
              ),
              child: InkWell(
                onTap: () {
                  _presenter?.requestDelCleanStatus(_controller.planOneEntity.value.uuid ?? "", (_controller.planOneEntity.value.planStatus == '1' ? "2" : "1"));
                },
                child: Obx(() => CommonUtils.getSimpleText(((_controller.planOneEntity.value.planStatus == '1') ? "停用" : "启动"), 14, Colours.base_primary)),
              ),
            )),

            Gaps.hGap10,

            ///编辑计划
            Expanded(
              child: ClipRRect(
                borderRadius: BorderRadius.circular(5), // 设置圆角半径为20
                child: Container(
                  height: 40,
                  color: Colours.base_primary,
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      BoostNavigator.instance.push(
                        "workAddPlanPage",
                        arguments: {"uuid": _controller.planOneEntity.value.uuid, "type": widget.type, "project_name": widget.project_name, "project_uuid": widget.project_uuid},
                      ).then((value) => {_onRefresh()});
                    },
                    child: CommonUtils.getSimpleText('编辑', 14, Colours.white, textAlign: TextAlign.center),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  updateStatus() {
    Toast.show("操作成功");
    _onRefresh();
  }

  @override
  deleteStatus() {
    Toast.show("操作成功");
    Future.delayed(const Duration(seconds: 1), () {
      BoostNavigator.instance.pop();
    });
  }
}
