import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/dialog_manager.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../widgets/custom_image_grid_view.dart';
import '../../../../widgets/my_app_bar.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../workpost/item/work_post_listview.dart';
import '../../workpost/iview/work_post_iview.dart';
import '../controller/work_add_plan_controller.dart';
import '../item/region_item_listview.dart';
import '../item/work_plan_item_listview.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 工作计划-列表  工作计划、巡检计划、培训计划 目前大致用的都是一样的界面
class WorkPlanPage extends StatefulWidget {
  String? type = "1";
  String? project_name;
  String? project_uuid;

  WorkPlanPage({Key? key, this.type = "1", this.project_name, this.project_uuid}) : super(key: key);

  @override
  _WorkPlanPageState createState() => _WorkPlanPageState();
}

//集成分类 然后实现使用
class _WorkPlanPageState extends State<WorkPlanPage> with BasePageMixin<WorkPlanPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<WorkPlanPage> implements WorkAddPlanIView {
  WorkAddPlanPresenter? _presenter;

  final WorkAddPlanController _controller = WorkAddPlanController();

  String title = "清洁计划";

  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();
    _controller.projectName.value = widget.project_name ?? httpConfig.project_name;
    _controller.projectUuid.value = widget.project_uuid ?? httpConfig.project_uuid;
    updateTitle();
    _onRefresh();

    ///监听从原生选择项目的内容
    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      setState(() {
        _controller.projectName.value = project_name;
        _controller.projectUuid.value = project_uuid;
        updateTitle();
        _controller.list.value = [];
        _onRefresh();
      });
    });
  }

  Future<dynamic> _onRefresh() async {
    _presenter!.onRefresh(widget.type, _controller.projectUuid.value);
  }

  Future<dynamic> _loadMore() async {
    _presenter!.loadMore(widget.type, _controller.projectUuid.value);
  }

  @override
  Widget build(BuildContext context) {
    return Obx(() => Scaffold(
          appBar: MyAppBar(
            centerTitle: title,
            centerSubTitle: _controller.projectName.value,
            mainRightImg: 'icon_change',
            onPressed: () {
              BoostChannel.instance.sendEventToNative("native_CommonEvent", {"method": "goto_show_project_dialog"});
            },
          ),
          backgroundColor: Colours.base_primary_bg_page,
          body: WillPopScope(
              child: MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: const EdgeInsets.all(10.0),
                hasMore: int.parse(_controller.listTotal.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return WorkPlanListItem(
                    data: _controller.list[index],
                    type: widget.type,
                    onDelete: () {
                      DialogManager.showConfirmDialog(
                          context: context,
                          title: title,
                          cancel: '取消',
                          confirm: '删除',
                          message: '是否确认删除？',
                          onConfirm: () {
                            _presenter?.requestDelCleanPlan(_controller.list[index].uuid ?? "");
                          },
                          onCancel: () {});
                    },
                    onEdit: () {
                      BoostNavigator.instance.push(
                        "workAddPlanPage",
                        arguments: {
                          "uuid": _controller.list[index].uuid,
                          "type": widget.type,
                          'project_uuid': _controller.projectUuid.value,
                          'project_name': _controller.projectName.value,
                        },
                      ).then((value) => {_onRefresh()});
                    },
                    onDetail: () {
                      BoostNavigator.instance.push(
                        "workPlanDetailPage",
                        arguments: {
                          "uuid": _controller.list[index].uuid,
                          "type": widget.type,
                          'project_uuid': _controller.projectUuid.value,
                          'project_name': _controller.projectName.value,
                        },
                      ).then((value) => {_onRefresh()});
                    },
                    onStatus: () {
                      _presenter?.requestDelCleanStatus(_controller.list[index].uuid ?? "", (_controller.list[index].planStatus == '1' ? "2" : "1"));
                    },
                  );
                },
              ),
              onWillPop: () async {
                if (DialogManager.hasOpenDialogs()) {
                  DialogManager.dismissAllDialogs(context);
                  return false; // Prevent the app from popping the route
                } else {
                  return true; // Allow the app to pop the route
                }
              }),
          // body: CustomImageGridView(),
          bottomNavigationBar: Container(
            color: Colors.white,
            padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
            child: BrnBigMainButton(
              themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
              title: '添加计划',
              onTap: () {
                BoostNavigator.instance.push("workAddPlanPage", arguments: {
                  "type": widget.type,
                  'project_uuid': _controller.projectUuid.value,
                  'project_name': _controller.projectName.value,
                }).then((value) => _onRefresh());
              },
            ),
          ),
        ));
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    addProjectListener?.call();
  }

  @override
  updateStatus() {
    _onRefresh();
  }

  void updateTitle() {
    switch (widget.type) {
      case "1":
        title = "清洁计划";
        break;
      case "2":
        title = "巡检计划";
        break;
      case "3":
        title = "培训计划";
        break;
    }
  }

  @override
  deleteStatus() {
  }
}
