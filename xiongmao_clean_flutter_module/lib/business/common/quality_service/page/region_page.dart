import 'package:bruno/bruno.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:get/get.dart';
import 'package:get/get_core/src/get_main.dart';
import 'package:get/get_navigation/get_navigation.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/bean/work_post_manager_entity.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/controller/work_post_manager_page_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/workpost/page/work_add_post_page.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/log_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/toast_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/my_app_bar.dart';

import '../../../../mvp/base_page.dart';
import '../../../../mvp/power_presenter.dart';
import '../../../../util/brn_config_utils.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/dialog_manager.dart';
import '../../../../widgets/load_image.dart';
import '../../../../widgets/my_refresh_list.dart';
import '../../workpost/item/work_post_listview.dart';
import '../../workpost/iview/work_post_iview.dart';
import '../controller/work_add_plan_area_controller.dart';
import '../controller/work_add_plan_controller.dart';
import '../item/region_item_listview.dart';
import '../item/work_plan_item_listview.dart';
import '../iview/work_add_plan_area_iview.dart';
import '../iview/work_add_plan_iview.dart';
import '../persenter/work_add_plan_area_persenter.dart';
import '../persenter/work_add_plan_persenter.dart';

/// 清洁区域划分
class RegionPage extends StatefulWidget {
  bool choose;
  String? project_uuid;
  String? project_name;

  RegionPage({Key? key, required this.choose, this.project_uuid = "", this.project_name}) : super(key: key);

  @override
  _RegionPageState createState() => _RegionPageState();
}

//集成分类 然后实现使用
class _RegionPageState extends State<RegionPage> with BasePageMixin<RegionPage, PowerPresenter<dynamic>>, AutomaticKeepAliveClientMixin<RegionPage> implements WorkAddPlanAreaIView {
  List<WorkPostManagerList> datas = [];

  final WorkAddPlanAreaController _controller = WorkAddPlanAreaController();
  WorkAddPlanAreaPresenter? _presenter;
  int _page = 1;

  VoidCallback? addProjectListener;

  @override
  void initState() {
    super.initState();
    _controller.projectName.value = widget.project_name ?? httpConfig.project_name;
    _controller.projectUuid.value = widget.project_uuid ?? httpConfig.project_uuid;

    _onRefresh();

    addProjectListener ??= BoostChannel.instance.addEventListener("SelectProject", (key, arguments) async {
      var project_name = arguments["project_name"];
      var project_uuid = arguments["project_uuid"];
      print('这里是接收到的值${project_name}');
      setState(() {
        _controller.projectUuid.value = project_uuid;
        _controller.projectName.value = project_name;
        _onRefresh();
      });
    });
  }

  Future<dynamic> _onRefresh() async {
    _page = 1;
    _presenter!.requestCleanArea(_page, _controller.projectUuid.value);
  }

  Future<dynamic> _loadMore() async {
    _page++;
    _presenter!.requestCleanArea(_page, _controller.projectUuid.value);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: (widget.choose != true)
          ? MyAppBar(
              centerTitle: '清洁区域',
              centerSubTitle: _controller.projectName.value,
              actionWidget: Row(
                children: [
                  InkWell(
                    onTap: () {
                      BoostChannel.instance.sendEventToNative("native_CommonEvent", {
                        "method": "goto_show_project_dialog",
                        "isChangeAppProject": CommonUtils.checkRoleHeadOffice(),
                        'isNeedAll': false,
                        'isHeadOffice': false,
                        'project_uuid': _controller.projectUuid.value,
                      });
                    },
                    child: const Padding(
                      padding: EdgeInsets.only(left: 10, right: 10),
                      child: LoadAssetImage(
                        "icon_change",
                        width: 20,
                        height: 20,
                      ),
                    ),
                  ),
                ],
              ),
            )
          : MyAppBar(
              centerTitle: '清洁区域',
              centerSubTitle: httpConfig.project_name,
              actionName: '添加',
              onPressed: () {
                BoostNavigator.instance.push("regionAddPage").then((value) => _onRefresh());
              },
            ),
      backgroundColor: Colours.base_primary_bg_page,
      body: WillPopScope(
          child: Obx(() => MyRefreshListView(
                itemCount: _controller.list.length,
                onRefresh: _onRefresh,
                loadMore: _loadMore,
                padding: const EdgeInsets.all(10.0),
                hasMore: int.parse(_controller.listTotal.value) > _controller.list.length,
                itemBuilder: (_, index) {
                  return RegionListItem(
                    data: _controller.list[index],
                    onDelete: () {
                      DialogManager.showConfirmDialog(
                        context: context,
                        title: '提示',
                        cancel: '取消',
                        confirm: '确定',
                        message: '是否确认删除？',
                        onConfirm: () {
                          _presenter?.requestDelCleanArea(_controller.list[index].uuid ?? "", widget.project_uuid ?? httpConfig.project_uuid);
                        },
                        onCancel: () {},
                      );
                    },
                    onEdit: () {
                      if (widget.choose) {
                        //如果是选中状态，那么就回调上个界面
                        BoostNavigator.instance.pop(_controller.list[index]);
                        return;
                      }
                      BoostNavigator.instance.push(
                        "regionAddPage",
                        arguments: {"uuid": _controller.list[index].uuid, "work_name": _controller.list[index].areaName, 'project_uuid': widget.project_uuid},
                      ).then((value) => {_onRefresh()});
                    },
                    choose: widget.choose,
                    choose_uuid: widget.project_uuid ?? "",
                  );
                },
              )),
          onWillPop: () async {
            if (DialogManager.hasOpenDialogs()) {
              DialogManager.dismissAllDialogs(context);
              return false; // Prevent the app from popping the route
            } else {
              return true; // Allow the app to pop the route
            }
          }),
      bottomNavigationBar: Visibility(
        visible: !widget.choose,
        child: Container(
          color: Colors.white,
          padding: const EdgeInsets.only(left: 14, right: 14, top: 10, bottom: 10),
          child: BrnBigMainButton(
            themeData: BrnButtonConfig(bigButtonHeight: 40, bigButtonFontSize: 16, bigButtonRadius: 4),
            title: '添加清洁区域',
            onTap: () {
              BoostNavigator.instance.push("regionAddPage", arguments: {'project_uuid': _controller.projectUuid.value}).then((value) => _onRefresh());
            },
          ),
        ),
      ),
    );
  }

  @override
  PowerPresenter createPresenter() {
    final PowerPresenter<dynamic> powerPresenter = PowerPresenter<dynamic>(this);
    _presenter = WorkAddPlanAreaPresenter(_controller);
    powerPresenter.requestPresenter([_presenter!]);
    return powerPresenter;
  }

  @override
  bool get wantKeepAlive => false;

  @override
  updateStatus() {}

  @override
  void dispose() {
    super.dispose();
    addProjectListener?.call();
  }
}
