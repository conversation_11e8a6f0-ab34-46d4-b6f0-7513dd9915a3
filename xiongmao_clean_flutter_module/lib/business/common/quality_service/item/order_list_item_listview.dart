import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/custom_image_grid_view.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/base_media_entity.dart';
import '../bean/clean_area_entity.dart';
import '../bean/clean_plan_task_entity.dart';

class OrderListItem extends StatelessWidget {
  final Function onClick;
  CleanPlanTaskList data;

  OrderListItem({
    super.key,
    required this.data,
    required this.onClick,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(10), // 设置圆角半径为10
          color: Colors.white, // 设置Container的背景颜色
        ),
        margin: EdgeInsets.only(left: 16, right: 16, top: 10),
        padding: EdgeInsets.only(left: 10, right: 10, top: 10, bottom: 10),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Row(
              children: [
                Expanded(child: CommonUtils.getSimpleText("${data.workContent}", 16, Colours.base_primary_text_title, maxLines: 1, fontWeight: FontWeight.bold)),
                Image.asset(
                  'assets/images/icon_base_arrow.png',
                  height: 14,
                  width: 14,
                )
              ],
            ),
            Gaps.vGap6,
            Visibility(visible: (data.mediaList!.isNotEmpty), child: CustomImageGridView(imageUrls: convertList(data.mediaList ?? []), maxImageCount: 4, onImageUrlsChanged: (List<BaseMediaEntity> updatedUrls) {}, includeEdge: false, showAddButton: false, showDelButton: false)),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Wrap(
                  children: [
                    CommonUtils.getSimpleText("${data.projectShortName} ${data.endDate}截止${(!TextUtil.isEmpty(data.finishedTime) ? "\n已于${data.finishedTime}处理" : "")}", 13, Colours.base_primary_text_caption, overflow: TextOverflow.ellipsis),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.only(
                    left: 10,
                    right: 10,
                    top: 4,
                    bottom: 4,
                  ),
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    color: Colours.base_primary,
                    border: Border.all(color: Colours.base_primary),
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  child: InkWell(
                    onTap: () {
                      BoostNavigator.instance.push('WorkPlanTaskOnePage', arguments: {
                        'uuid': "${data.uuid}",
                        'open_camera': true,
                      });
                    },
                    child: CommonUtils.getSimpleText('追加照片', 14, Colours.white, textAlign: TextAlign.center),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
      onTap: () {
        onClick();
      },
    );
  }

  List<BaseMediaEntity> convertList(List<CleanPlanTaskListMediaList> mediaList) {
    List<BaseMediaEntity> planTaskImgs = [];
    for (int i = 0; i < mediaList.length; i++) {
      BaseMediaEntity entity = BaseMediaEntity();
      entity.media_type = mediaList[i].mediaType;
      entity.media_url = mediaList[i].mediaUrl;
      planTaskImgs.add(entity);
    }
    return planTaskImgs;
  }
}
