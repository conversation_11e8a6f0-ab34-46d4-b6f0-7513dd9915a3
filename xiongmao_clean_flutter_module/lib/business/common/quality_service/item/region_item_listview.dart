import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/gaps.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/clean_area_entity.dart';

class RegionListItem extends StatelessWidget {
  final Function onDelete;
  final Function onEdit;
  final bool choose;
  final String choose_uuid;
  CleanAreaList data;

  RegionListItem({required this.data, required this.onDelete, required this.onEdit, required this.choose, required this.choose_uuid});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: () {
        onEdit();
      },
      child: Container(
        margin: EdgeInsets.only(bottom: 10),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(10),
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withOpacity(0.0),
              spreadRadius: 2,
              blurRadius: 5,
              offset: Offset(0, 2),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.only(top: 10, bottom: 10, left: 16, right: 16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonUtils.getSimpleText("${data.areaName}", 16, Colours.base_primary_text_title),
                  GestureDetector(
                    onTap: () {
                      if (!choose) {
                        onDelete();
                      } else {
                        onEdit();
                      }
                    },
                    child: (!choose)
                        ? Image.asset(
                            'assets/images/base/icon_base_del.png',
                            width: 22,
                            height: 22,
                          )
                        : Image.asset(
                            (choose_uuid == data.uuid) ? 'assets/images/icon_check.png' : 'assets/images/icon_uncheck.png',
                            width: 22,
                            height: 22,
                          ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
