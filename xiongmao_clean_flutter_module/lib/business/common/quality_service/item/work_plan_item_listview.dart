import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../res/colors.dart';
import '../../../../res/gaps.dart';
import '../../workpost/bean/work_post_manager_entity.dart';
import '../bean/clean_plan_all_entity.dart';

class WorkPlanListItem extends StatelessWidget {
  final Function onDelete;
  final Function onEdit;
  final Function onDetail;
  final Function onStatus;
  CleanPlanAllList? data;
  String? type = "1";

  WorkPlanListItem({required this.data, required this.type, required this.onDelete, required this.onEdit, required this.onStatus, required this.onDetail});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.0),
            spreadRadius: 2,
            blurRadius: 5,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.only(top: 16, bottom: 6, left: 16, right: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CommonUtils.getSimpleText("${data?.planContent}", 16, Colours.black, fontWeight: FontWeight.bold, maxLines: 1),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap10,
            Visibility(
              visible: type == "1",
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  CommonUtils.getSimpleText("清洁区域", 13, Colours.black),
                  CommonUtils.getSimpleText("${data?.cleanAreaName}", 13, Colours.text_gray),
                ],
              ),
            ),
            Gaps.vGap4,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("单次任务开始", 13, Colours.black),
                Gaps.hGap20,
                Expanded(child: CommonUtils.getSimpleText("${data?.startDate}", 13, Colours.text_gray, textAlign: TextAlign.right)),
              ],
            ),
            Gaps.vGap4,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("单次任务结束", 13, Colours.black),
                CommonUtils.getSimpleText("${data?.endDate}", 13, Colours.text_gray),
              ],
            ),
            Gaps.vGap4,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("执行人", 13, Colours.black),
                CommonUtils.getSimpleText("${data?.executeUserName}", 13, Colours.text_gray),
              ],
            ),
            Gaps.vGap4,
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                CommonUtils.getSimpleText("状态", 13, Colours.black),
                CommonUtils.getSimpleText("${data?.planStatusName}", 13, Colours.text_gray),
              ],
            ),
            Gaps.vGap10,
            Gaps.line,
            Gaps.vGap4,
            Row(
              children: [
                Expanded(
                    child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      onDelete();
                    },
                    child: CommonUtils.getSimpleText(
                      '删除',
                      16,
                      Colours.red,
                    ),
                  ),
                )),
                Gaps.vLine,
                // Gaps.hGap20,
                Expanded(
                    child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      onStatus();
                    },
                    child: CommonUtils.getSimpleText((data?.planStatus == "1" ? "停用" : "启动"), 16, Colours.base_primary),
                  ),
                )),
                // Gaps.hGap20,
                Gaps.vLine,
                Expanded(
                    child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      onEdit();
                    },
                    child: CommonUtils.getSimpleText('编辑', 16, Colours.base_primary),
                  ),
                )),
                Gaps.vLine,
                Expanded(
                    child: Container(
                  height: 40,
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () {
                      onDetail();
                    },
                    child: CommonUtils.getSimpleText('详情', 16, Colours.base_primary),
                  ),
                )),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
