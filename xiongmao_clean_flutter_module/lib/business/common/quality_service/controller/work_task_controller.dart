import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/base_media_entity.dart';
import '../bean/clean_plan_all_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../bean/clean_plan_task_entity.dart';
import '../bean/plan_task_one_new_entity.dart';

class WorkTaskController extends GetxController {
  var cleanAreaName = "-".obs; //清洁区域
  var cleanContent = "-".obs; //工作内容
  var projectUuid = '-'.obs; //所属项目
  var projectName = '-'.obs; //所属项目
  var taskSource = '-'.obs; //来源
  var endDate = '-'.obs; //截止日期
  var finishDate = ''.obs; //完成日期
  var executorName = '-'.obs; //执行人

  var uuidTask = ''.obs; //当前的uuid
  var taskStatus = ''.obs; //状态
  ///是否自己
  var isSelf = ''.obs; //是否自己

  ///工作照片
  var workImages = <BaseMediaEntity>[].obs;

  ///完成处理照片
  var workFinishImages = <BaseMediaEntity>[].obs;
  var originalWorkFinishImages = <PlanTaskOneNewDealMediaList>[].obs;

  ///这里是工单的详情
  void updateCleanPlanTaskOneEntityEntity(PlanTaskOneNewEntity data) {
    uuidTask.value = data.uuid ?? "";

    ///状态 1已完成 2待处理 3已超时
    taskStatus.value = data.taskStatus ?? "";

    cleanAreaName.value = data.areaName ?? "-";
    cleanContent.value = data.workContent ?? "-";
    projectUuid.value = data.projectUuid ?? "";
    projectName.value = data.projectShortName ?? "-";
    executorName.value = data.executeUserName ?? "-";
    endDate.value = data.endDate ?? "-";
    finishDate.value = data.finishedTime ?? '';
    isSelf.value = data.isSelf ?? "-";

    if ('1' == data.sourceType) {
      taskSource.value = '内部巡检';
    } else if ('2' == data.sourceType) {
      taskSource.value = '客户投诉';
    }

    originalWorkFinishImages.value.clear();
    workImages.value.clear();
    workFinishImages.value.clear();

    originalWorkFinishImages.value = data.dealMediaList ?? [];

    ///工作图片
    for (PlanTaskOneNewMediaList item in data.mediaList ?? []) {
      workImages.value.add(BaseMediaEntity(media_url: item.mediaUrl, media_type: item.mediaType));
    }

    ///处理结果的图片
    for (PlanTaskOneNewDealMediaList item in originalWorkFinishImages) {
      workFinishImages.value.add(BaseMediaEntity(media_url: item.mediaUrl, media_type: item.mediaType));
    }
    workImages.refresh();
    workFinishImages.refresh();
  }
}
