import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/base_media_entity.dart';
import '../bean/clean_plan_all_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../bean/clean_plan_task_entity.dart';
import '../bean/plan_task_one_new_entity.dart';

class WorkAddPlanController extends GetxController {


  final TextEditingController inputRemarkController = TextEditingController();

  //默认的数据
  var weekList = [
    BaseChooseString("周一"),
    BaseChooseString("周二"),
    BaseChooseString("周三"),
    BaseChooseString("周四"),
    BaseChooseString("周五"),
    BaseChooseString("周六"),
    BaseChooseString("周日"),
  ].obs;

  //默认的数据
  var task_source_list = [
    BaseChooseString("内部巡检"),
    BaseChooseString("客户投诉"),
  ].obs;

  //项目的uuid 项目的名字
  var projectName = "".obs;
  var projectUuid = "".obs;

  ///计划的列表
  var list = <CleanPlanAllList>[].obs;

  void initMyList(List<CleanPlanAllList> value) {
    list.value = value.toList();
  }

  ///计划的总数
  var listTotal = "0".obs;

  void initTotal(int value) {
    listTotal.value = value.toString();
  }

  ///计划的详情
  var planOneEntity = CleanPlanOneEntity().obs;

  var currentDate = 0.obs; //循环的下表
  var currentDateText = '每周'.obs; //循环的周期
  var startDate = ''.obs; //多选的开始时间
  var endDate = ''.obs; //截止时间
  var endDateIndex = ''.obs; //截止时间下表
  var executor = ''.obs; //执行人
  var executor_uuid = ''.obs; //执行人的 uuid
  var clean_area_name = "".obs; //清洁区域
  var clean_area_uuid = ''.obs; //清洁区域的 uuid
  var week_invert = <int>[].obs; //这里是每周的情况下显示的内容
  var media_list = <BaseMediaEntity>[].obs; //图片的 uuid

  var task_source = '内部巡检'.obs; //工单来源

  ///工作任务详情
  var planTaskImgs = <BaseMediaEntity>[].obs;

  ///计划详情
  void CleanPlanOneEntityEntity(CleanPlanOneEntity data) {
    planOneEntity.value = data;
    planTaskImgs.clear();
    for (int i = 0; i < data.mediaList!.length; i++) {
      BaseMediaEntity entity = BaseMediaEntity();
      entity.media_type = data.mediaList![i].mediaType;
      entity.media_url = data.mediaList![i].mediaUrl;
      planTaskImgs.add(entity);
    }
  }

  void updateCleanPlanOneEntityEntity(CleanPlanOneEntity data) {
    planOneEntity.value = data;
    clean_area_name.value = data.cleanAreaName ?? "";
    clean_area_uuid.value = data.cleanAreaUuid ?? "";
    startDate.value = data.startDate ?? "";
    endDate.value = "开始后的第${data.endDate}天结束" ?? "";
    endDateIndex.value = "${data.endDate}" ?? "";
    executor.value = data.executeUserName ?? "";
    executor_uuid.value = data.executeUserUuid ?? "";
    inputRemarkController.text = data.planContent ?? "";
    //把图片的内容给到具体的
    if (data.mediaList!.isNotEmpty) {
      for (int i = 0; i < (data.mediaList!.length ?? 0); i++) {
        BaseMediaEntity entity = BaseMediaEntity();
        entity.media_type = data.mediaList![i].mediaType;
        entity.media_url = data.mediaList![i].mediaUrl;
        media_list.add(entity);
      }
    }

    //循环计划
    switch (data.loopType) {
      case "1":
        currentDate.value = 0;
        currentDateText.value = "每周";
        print("我是开始日期，周周 - ${data.startDate!.split(",").map((str) => (int.parse(str) - 1)).toList()}");
        week_invert.value = data.startDate!.split(",").map((str) => (int.parse(str) - 1)).toList();
        break;
      case "2":
        currentDate.value = 1;
        currentDateText.value = "每月";
        break;
      case "3":
        currentDate.value = 2;
        currentDateText.value = "每季度";
        break;
    }
  }

  ///这里是工单的详情
  void updateCleanPlanTaskOneEntityEntity(PlanTaskOneNewEntity data) {
    clean_area_name.value = data.areaName ?? "";
    clean_area_uuid.value = data.areaUuid ?? "";
    startDate.value = data.startDate ?? "";
    endDate.value = data.endDate ?? "";
    endDateIndex.value = "${data.endDate}" ?? "";
    executor.value = data.executeUserName ?? "";
    executor_uuid.value = data.executeUserUuid ?? "";
    inputRemarkController.text = data.workContent ?? "";
    //把图片的内容给到具体的
    if (data.mediaList!.isNotEmpty) {
      for (int i = 0; i < (data.mediaList!.length ?? 0); i++) {
        BaseMediaEntity entity = BaseMediaEntity();
        entity.media_type = data.mediaList![i].mediaType;
        entity.media_url = data.mediaList![i].mediaUrl;
        media_list.add(entity);
      }
    }

    if (!TextUtil.isEmpty(data.sourceType)) {
      if ('1' == data.sourceType) {
        task_source.value = '内部巡检';
      } else {
        task_source.value = '客户投诉';
      }
    }
  }

  ///计划任务详情
  var planTaskEntity = CleanPlanTaskEntity().obs;

  void PlanOneTaskEntityEntity(CleanPlanTaskEntity data) {
    planTaskEntity.value = data;
  }

  var listTask = <CleanPlanTaskList>[].obs;

  void initTaskList(List<CleanPlanTaskList> value) {
    listTask.value = value.toList();
  }

  void updateTaskList(List<CleanPlanTaskList> value) {
    listTask.value.addAll(value);
    listTask.value = listTask.value.toList();
  }
}
