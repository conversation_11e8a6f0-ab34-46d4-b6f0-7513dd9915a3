import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get/get_state_manager/src/rx_flutter/rx_getx_widget.dart';
import 'package:get/get_state_manager/src/simple/get_controllers.dart';
import 'package:provider/provider.dart';
import 'package:sp_util/sp_util.dart';

import '../../approve/bean/base_choose_string.dart';
import '../bean/clean_area_entity.dart';

class WorkAddPlanAreaController extends GetxController {

  //项目的uuid 项目的名字
  var projectName = "".obs;
  var projectUuid = "".obs;

  ///清洁区域的列表
  var list = <CleanAreaList>[].obs;

  void initMyList(List<CleanAreaList> value) {
    list.value = value.toList();
  }

  ///清洁区域的列表
  var listTotal = "0".obs;

  void initTotal(int value) {
    listTotal.value = value.toString();
  }

}
