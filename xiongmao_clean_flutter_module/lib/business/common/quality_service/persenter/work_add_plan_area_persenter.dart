import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/controller/work_add_plan_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/clean_area_entity.dart';
import '../controller/work_add_plan_area_controller.dart';
import '../iview/work_add_plan_area_iview.dart';

/**
 * 清洁区域
 */
class WorkAddPlanAreaPresenter extends BasePagePresenter<WorkAddPlanAreaIView> with WidgetsBindingObserver {
  WorkAddPlanAreaController controller;

  WorkAddPlanAreaPresenter(this.controller);

  ///请求清洁区域的内容
  Future<dynamic> requestCleanArea(int page, String project_uuid) {
    var params = <String, String>{};
    params["page"] = "$page";
    params["size"] = "20";
    params["project_uuid"] = "$project_uuid";
    return requestNetwork<CleanAreaEntity>(Method.get, url: HttpApi.GET_CLEAN_AREA_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.initMyList(data.list!);
        controller.initTotal(data.total!);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///增加-修改清洁区域
  Future<dynamic> requestCleanAreaSave(String area_name, String? uuid, String? project_uuid) {
    var params = <String, String>{};
    params["area_name"] = "$area_name";
    if (!TextUtil.isEmpty(uuid)) {
      params["uuid"] = "$uuid";
    }
    if (!TextUtil.isEmpty(project_uuid)) {
      params["project_uuid"] = "$project_uuid";
    } else {
      params["project_uuid"] = httpConfig.project_uuid;
    }
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_AREA_SAVE, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      view.updateStatus();
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///删除清洁区域
  Future<dynamic> requestDelCleanArea(String uuid, String project_uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    params["project_uuid"] = project_uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_AREA_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      requestCleanArea(1, project_uuid); //重新请求列表刷新
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
