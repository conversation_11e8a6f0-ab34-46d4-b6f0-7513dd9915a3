import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/controller/work_add_plan_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/clean_area_entity.dart';
import '../bean/clean_plan_all_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../bean/clean_plan_task_entity.dart';
import '../bean/plan_task_one_new_entity.dart';
import '../controller/work_add_plan_order_list_controller.dart';
import '../controller/work_task_controller.dart';
import '../iview/work_task_iview.dart';

/// 工单
class WorkTaskPresenter extends BasePagePresenter<WorkTaskIView> with WidgetsBindingObserver {
  WorkTaskController controller;

  WorkTaskPresenter(this.controller);

  ///获取计划详情 - 编辑的时候获取
  Future<dynamic> requestCleanPlanTaskOne(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<PlanTaskOneNewEntity>(Method.post, url: HttpApi.CONTRCT_ONE_TASK, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.updateCleanPlanTaskOneEntityEntity(data);
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///任务结果图删除
  Future<dynamic> delCleanPlanOnePic(String uuid, String media_uuid) {
    var params = <String, String>{};
    params["uuid"] = media_uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_PLAN_PIC_DEL, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      requestCleanPlanTaskOne(
        uuid,
      );
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }

  ///完成任务
  Future<dynamic> finishTask(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    params['map_code'] = 'gao_de';
    return requestNetwork<Object>(Method.post, url: HttpApi.FINISH_TASK, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      requestCleanPlanTaskOne(
        uuid,
      );
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
