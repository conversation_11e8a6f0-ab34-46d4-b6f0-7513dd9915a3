import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/controller/work_add_plan_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/log_utils.dart';
import '../bean/clean_area_entity.dart';
import '../bean/clean_plan_all_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../bean/clean_plan_task_entity.dart';
import '../controller/work_add_plan_order_list_controller.dart';

/// 工作计划-清单列表
class WorkAddPlanOrderPresenter extends BasePagePresenter<WorkAddPlanIView> with WidgetsBindingObserver {
  WorkAddPlanOrderController controller;

  WorkAddPlanOrderPresenter(this.controller);

  int _page = 1;

  void onRefresh(String? projectUuid) {
    _page = 1;
    requestCleanPlanTaskList(projectUuid);
  }

  void loadMore(String? projectUuid) {
    _page++;
    requestCleanPlanTaskList(projectUuid);
  }

  ///获取计划任务列表
  Future<dynamic> requestCleanPlanTaskList(String? planUuid) {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["is_self"] = "2";
    params["is_finished_total"] = "1";
    params["is_unfinished_total"] = "1";
    params["status"] = controller.status.value;
    if (controller.reportType == 1) {
      params["start_month"] = controller.searchMonth.value.replaceAll("年", '-').replaceAll("月", '');
    } else {
      params["start_date"] = controller.searchDay.value.replaceAll("年", '-').replaceAll("月", '').replaceAll("天", "");
    }
    params["project_uuid"] = '$planUuid';

    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<CleanPlanTaskEntity>(Method.post, url: HttpApi.GET_CLEAN_PLAN_TASK_LIST, queryParameters: params, isShow: true, isClose: true, onSuccess: (data) {
      if (data != null) {
        controller.unFinishTotal.value = '${data.unFinishTotal ?? '0'}';
        controller.finishTotal.value = '${data.finishTotal ?? '0'}';
        if ("$_page" == '1') {
          controller.initTaskList(data.list!);
        } else {
          controller.updateTaskList(data.list!);
        }
        controller.listTotal.value = "${data.total ?? '0'}";
      }
    }, onError: (code, msg) {
      MyLog.e("请求失败 状态码：$code ；msg ：$msg");
    });
  }
}
