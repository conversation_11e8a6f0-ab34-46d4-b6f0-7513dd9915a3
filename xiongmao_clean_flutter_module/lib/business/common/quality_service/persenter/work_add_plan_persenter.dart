import 'dart:collection';

import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/controller/work_add_plan_controller.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/iview/work_add_plan_iview.dart';
import 'package:xiongmao_clean_flutter_module/mvp/base_page_presenter.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';

import '../../../../net/dio_utils.dart';
import '../../../../net/http_api.dart';
import '../../../../util/common_utils.dart';
import '../../../../util/log_utils.dart';
import '../bean/clean_area_entity.dart';
import '../bean/clean_plan_all_entity.dart';
import '../bean/clean_plan_one_entity.dart';
import '../bean/clean_plan_task_entity.dart';
import '../bean/plan_task_one_new_entity.dart';

/**
 * 清洁区域
 */
class WorkAddPlanPresenter extends BasePagePresenter<WorkAddPlanIView> with WidgetsBindingObserver {
  WorkAddPlanController controller;

  WorkAddPlanPresenter(this.controller);

  int _page = 1;
  String? types = "";

  void onRefresh(String? type, String? project_uuid) {
    _page = 1;
    types = type;
    requestListAll(project_uuid);
  }

  void loadMore(String? type, String? project_uuid) {
    _page++;
    types = type;
    requestListAll(project_uuid);
  }

  ///请求清洁计划的列表
  Future<dynamic> requestListAll(String? project_uuid) {
    var params = <String, String>{};
    params["page"] = "$_page";
    params["size"] = "20";
    params["plan_type"] = "$types";
    params["project_uuid"] = "$project_uuid";
    return requestNetwork<CleanPlanAllEntity>(Method.get, url: HttpApi.GET_CLEAN_PLAN_LIST_ALL,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          if (data != null) {
            controller.initMyList(data.list!);
            controller.initTotal(data.total!);
          }
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///增加-修改 清洁计划
  Future<dynamic> requestCleanPlanSave(Map<String, String> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_PLAN_SAVE,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          view.updateStatus();
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///删除清洁计划
  Future<dynamic> requestDelCleanPlan(String uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_PLAN_DEL,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          view.deleteStatus();
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///启动/暂停清洁计划
  Future<dynamic> requestDelCleanStatus(String uuid, String status) {
    var params = <String, String>{};
    params["uuid"] = uuid;
    params["status"] = status;
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_PLAN_UPDATE_STATUS,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          view.updateStatus();
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///获取计划详情 - 编辑的时候获取
  Future<dynamic> requestCleanPlanOne(String? uuid, bool? isEdit) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    params["project_uuid"] = controller.projectUuid.value;
    return requestNetwork<CleanPlanOneEntity>(Method.post, url: HttpApi.GET_CLEAN_PLAN_ONE,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          if (data != null) {
            if (isEdit == true) {
              controller.updateCleanPlanOneEntityEntity(data);
            } else {
              controller.CleanPlanOneEntityEntity(data);
            }
          }
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///获取计划详情 - 编辑的时候获取
  Future<dynamic> requestCleanPlanTaskOne(String? uuid) {
    var params = <String, String>{};
    params["uuid"] = "$uuid";
    return requestNetwork<PlanTaskOneNewEntity>(Method.post, url: HttpApi.CONTRCT_ONE_TASK,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          if (data != null) {
            controller.updateCleanPlanTaskOneEntityEntity(data);
          }
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///获取计划任务详情
  Future<dynamic> requestCleanPlanTaskList(String? plan_uuid, String page) {
    var params = <String, String>{};
    params["page"] = "$page";
    params["plan_uuid"] = "$plan_uuid";
    params["project_uuid"] = controller.projectUuid.value;
    ///新增参数  是否需要总部项目 1是2否   (大区经理、人事、管理员、超管）只有这些可以搜
    params['is_head_office_project'] = CommonUtils.checkRoleHeadOffice() ? '1' : '2';
    return requestNetwork<CleanPlanTaskEntity>(Method.post, url: HttpApi.GET_CLEAN_PLAN_TASK_LIST,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          if (data != null) {
            if (page == '1') {
              controller.initTaskList(data.list!);
            } else {
              controller.updateTaskList(data.list!);
            }
            controller.PlanOneTaskEntityEntity(data);
          }
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///工作计划结果图删除
  Future<dynamic> delCleanPlanOnePic(String uuid, String media_uuid) {
    var params = <String, String>{};
    params["uuid"] = uuid; //任务 uuid
    params["media_uuid"] = media_uuid; //任务 uuid
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_CLEAN_PLAN_PIC_DEL,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          view.updateStatus();
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }

  ///工单的添加编辑
  Future<dynamic> requestWorkOrderSave(Map<String, String> params) {
    return requestNetwork<Object>(Method.post, url: HttpApi.GET_WORK_ORDER_SAVE,
        queryParameters: params,
        isShow: true,
        isClose: true,
        onSuccess: (data) {
          view.updateStatus();
        },
        onError: (code, msg) {
          MyLog.e("请求失败 状态码：$code ；msg ：$msg");
        });
  }
}
