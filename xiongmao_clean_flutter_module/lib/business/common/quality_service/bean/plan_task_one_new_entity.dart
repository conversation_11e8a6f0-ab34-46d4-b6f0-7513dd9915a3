import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/plan_task_one_new_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/plan_task_one_new_entity.g.dart';

@JsonSerializable()
class PlanTaskOneNewEntity {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@J<PERSON><PERSON>ield(name: "project_uuid")
	String? projectUuid;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "area_name")
	String? areaName;
	@JSONField(name: "work_content")
	String? workContent;
	@JSONField(name: "start_date")
	String? startDate;
	@JSONField(name: "end_date")
	String? endDate;
	@J<PERSON><PERSON>ield(name: "execute_user_uuid")
	String? executeUserUuid;
	@J<PERSON>NField(name: "execute_user_name")
	String? executeUserName;
	@JSONField(name: "task_type")
	String? taskType;
	@JSO<PERSON>ield(name: "source_type")
	String? sourceType;
	@J<PERSON><PERSON>ield(name: "finished_time")
	String? finishedTime;
	@JSONField(name: "task_status")
	String? taskStatus;
	@JSONField(name: "task_status_name")
	String? taskStatusName;
	@JSONField(name: "is_self")
	String? isSelf;
	@JSONField(name: "area_uuid")
	String? areaUuid;
	@JSONField(name: "media_list")
	List<PlanTaskOneNewMediaList>? mediaList;
	@JSONField(name: "deal_media_list")
	List<PlanTaskOneNewDealMediaList>? dealMediaList;

	PlanTaskOneNewEntity();

	factory PlanTaskOneNewEntity.fromJson(Map<String, dynamic> json) => $PlanTaskOneNewEntityFromJson(json);

	Map<String, dynamic> toJson() => $PlanTaskOneNewEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PlanTaskOneNewMediaList {
	String? uuid;
	@JSONField(name: "media_type")
	String? mediaType;
	@JSONField(name: "media_url")
	String? mediaUrl;
	@JSONField(name: "origin_media_url")
	String? originMediaUrl;
	@JSONField(name: "video_cover_url")
	String? videoCoverUrl;
	@JSONField(name: "pic_thumb")
	String? picThumb;

	PlanTaskOneNewMediaList();

	factory PlanTaskOneNewMediaList.fromJson(Map<String, dynamic> json) => $PlanTaskOneNewMediaListFromJson(json);

	Map<String, dynamic> toJson() => $PlanTaskOneNewMediaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class PlanTaskOneNewDealMediaList {
	String? uuid;
	@JSONField(name: "media_type")
	String? mediaType;
	@JSONField(name: "media_url")
	String? mediaUrl;
	@JSONField(name: "origin_media_url")
	String? originMediaUrl;
	@JSONField(name: "video_cover_url")
	String? videoCoverUrl;
	@JSONField(name: "pic_thumb")
	String? picThumb;

	PlanTaskOneNewDealMediaList();

	factory PlanTaskOneNewDealMediaList.fromJson(Map<String, dynamic> json) => $PlanTaskOneNewDealMediaListFromJson(json);

	Map<String, dynamic> toJson() => $PlanTaskOneNewDealMediaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}