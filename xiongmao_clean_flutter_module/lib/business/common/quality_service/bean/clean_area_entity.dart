import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/clean_area_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/clean_area_entity.g.dart';

@JsonSerializable()
class CleanAreaEntity {
	int? page;
	int? size;
	int? total;
	List<CleanAreaList>? list;

	CleanAreaEntity();

	factory CleanAreaEntity.fromJson(Map<String, dynamic> json) => $CleanAreaEntityFromJson(json);

	Map<String, dynamic> toJson() => $CleanAreaEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanAreaList {
	String? uuid;
	@JSONField(name: "area_name")
	String? areaName;

	CleanAreaList();

	factory CleanAreaList.fromJson(Map<String, dynamic> json) => $CleanAreaListFromJson(json);

	Map<String, dynamic> toJson() => $CleanAreaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}