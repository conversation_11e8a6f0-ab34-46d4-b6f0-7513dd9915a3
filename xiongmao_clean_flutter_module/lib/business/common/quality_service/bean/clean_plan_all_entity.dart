import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_all_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_all_entity.g.dart';

@JsonSerializable()
class CleanPlanAllEntity {
	int? page;
	int? size;
	int? total;
	List<CleanPlanAllList>? list;

	CleanPlanAllEntity();

	factory CleanPlanAllEntity.fromJson(Map<String, dynamic> json) => $CleanPlanAllEntityFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanAllEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanPlanAllList {
	String? uuid;
	@JSONField(name: "plan_content")
	String? planContent;
	@JSONField(name: "clean_area_name")
	String? cleanAreaName;
	@JSONField(name: "start_date")
	String? startDate;
	@JSONField(name: "end_date")
	String? endDate;
	@JSONField(name: "execute_user_name")
	String? executeUserName;
	@JSONField(name: "plan_status_name")
	String? planStatusName;
	@JSONField(name: "plan_status")
	String? planStatus;

	CleanPlanAllList();

	factory CleanPlanAllList.fromJson(Map<String, dynamic> json) => $CleanPlanAllListFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanAllListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}