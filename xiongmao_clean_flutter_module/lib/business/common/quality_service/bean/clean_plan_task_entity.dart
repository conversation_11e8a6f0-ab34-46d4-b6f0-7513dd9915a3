import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_task_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_task_entity.g.dart';

@JsonSerializable()
class CleanPlanTaskEntity {
	int? page;
	int? size;
	int? total;
	@JSONField(name: "over_time_total")
	int? overTimeTotal;
	@JSONField(name: "finished_total")
	int? finishTotal;
	@JSONField(name: "unfinished_total")
	int? unFinishTotal;
	@JSONField(name: "wait_total")
	int? waitTotal;
	@JSONField(name: "my_total")
	int? myTotal;
	List<CleanPlanTaskList>? list;

	CleanPlanTaskEntity();

	factory CleanPlanTaskEntity.fromJson(Map<String, dynamic> json) => $CleanPlanTaskEntityFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanTaskEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanPlanTaskList {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "work_content")
	String? workContent;
	@JSONField(name: "end_date")
	String? endDate;
	@JSONField(name: "finished_time")
	String? finishedTime;
	String? status;
	@JSONField(name: "status_name")
	String? statusName;
	@JSONField(name: "media_list")
	List<CleanPlanTaskListMediaList>? mediaList;
	@JSONField(name: "deal_media_list")
	List<CleanPlanTaskListDealMediaList>? dealMediaList;

	CleanPlanTaskList();

	factory CleanPlanTaskList.fromJson(Map<String, dynamic> json) => $CleanPlanTaskListFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanTaskListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanPlanTaskListMediaList {
	@JSONField(name: "media_type")
	String? mediaType;
	@JSONField(name: "media_url")
	String? mediaUrl;

	CleanPlanTaskListMediaList();

	factory CleanPlanTaskListMediaList.fromJson(Map<String, dynamic> json) => $CleanPlanTaskListMediaListFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanTaskListMediaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanPlanTaskListDealMediaList {
	String? uuid;
	@JSONField(name: "media_type")
	String? mediaType;
	@JSONField(name: "media_url")
	String? mediaUrl;

	CleanPlanTaskListDealMediaList();

	factory CleanPlanTaskListDealMediaList.fromJson(Map<String, dynamic> json) => $CleanPlanTaskListDealMediaListFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanTaskListDealMediaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}