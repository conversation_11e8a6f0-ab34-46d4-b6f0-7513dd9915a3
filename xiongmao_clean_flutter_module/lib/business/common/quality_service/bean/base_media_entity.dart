import '../../../../widgets/select_tab/select_tab_data.dart';

class BaseMediaEntity {
  String? media_type;
  String? media_url;

  BaseMediaEntity({this.media_type, this.media_url});


  @override
  String toString() {
    return '{media_type: $media_type, media_url: $media_url}';
  }


  Map<String, dynamic> toJson() {
    return {
      'media_type': media_type,
      'media_url': media_url,
    };
  }
}
