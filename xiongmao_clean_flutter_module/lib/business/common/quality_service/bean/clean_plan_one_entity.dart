import 'package:xiongmao_clean_flutter_module/generated/json/base/json_field.dart';
import 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_one_entity.g.dart';
import 'dart:convert';
export 'package:xiongmao_clean_flutter_module/generated/json/clean_plan_one_entity.g.dart';

@JsonSerializable()
class CleanPlanOneEntity {
	String? uuid;
	@JSONField(name: "project_name")
	String? projectName;
	@JSONField(name: "project_short_name")
	String? projectShortName;
	@JSONField(name: "plan_content")
	String? planContent;
	@JSONField(name: "clean_area_uuid")
	String? cleanAreaUuid;
	@JSONField(name: "clean_area_name")
	String? cleanAreaName;
	@JSONField(name: "start_date")
	String? startDate;
	@JSONField(name: "end_date")
	String? endDate;
	@JSONField(name: "start_date_name")
	String? startDateName;
	@J<PERSON>NField(name: "end_date_name")
	String? endDateName;
	@J<PERSON><PERSON>ield(name: "execute_user_uuid")
	String? executeUserUuid;
	@JSO<PERSON>ield(name: "execute_user_name")
	String? executeUserName;
	@JSONField(name: "plan_status_name")
	String? planStatusName;
	@JSONField(name: "plan_status")
	String? planStatus;
	@JSONField(name: "loop_type")
	String? loopType;
	@JSONField(name: "media_list")
	List<CleanPlanOneMediaList>? mediaList;

	CleanPlanOneEntity();

	factory CleanPlanOneEntity.fromJson(Map<String, dynamic> json) => $CleanPlanOneEntityFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanOneEntityToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}

@JsonSerializable()
class CleanPlanOneMediaList {
	@JSONField(name: "media_type")
	String? mediaType;
	@JSONField(name: "media_url")
	String? mediaUrl;

	CleanPlanOneMediaList();

	factory CleanPlanOneMediaList.fromJson(Map<String, dynamic> json) => $CleanPlanOneMediaListFromJson(json);

	Map<String, dynamic> toJson() => $CleanPlanOneMediaListToJson(this);

	@override
	String toString() {
		return jsonEncode(this);
	}
}