import 'package:flutter/material.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/region_add_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/region_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_add_plan_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_add_task_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_plan_detail_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_plan_order_list_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_plan_page.dart';
import 'package:xiongmao_clean_flutter_module/business/common/quality_service/page/work_plan_task_one_page.dart';

import '../../../net/http_config.dart';

/// 清洁区域设置
const regionPage = "regionPage";

/// 增加清洁区域
const regionAddPage = "regionAddPage";

///清洁计划
const workPlanPage = "workPlanPage";

/// 增加/编辑清洁计划
const workAddPlanPage = "workAddPlanPage";

/// 计划详情
const workPlanDetailPage = "workPlanDetailPage";

/// 工单详情
const workTaskOnePage = "WorkPlanTaskOnePage";

/// 增加/编辑工单计划
const workAddTaskPage = "workAddTaskPage";

/// 工单列表
const workPlanOrderListPage = "workPlanOrderListPage";

/// 清洁区域设置
Map<String, FlutterBoostRouteFactory> regionRouterMap = {
  regionPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          bool? choose = map['choose'];
          return RegionPage(
            project_uuid: project_uuid,
            project_name: project_name,
            choose: choose ?? false,
          );
        });
  },

  /// 清洁区域设置
  regionAddPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map["uuid"];
          String? project_uuid = map["project_uuid"];
          String? work_name = map["work_name"];
          return RegionAddPage(
            uuid: uuid,
            project_uuid: project_uuid,
            work_name: work_name,
          );
        });
  },

  /// 工作计划
  workPlanPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? type = map['type'];
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          return WorkPlanPage(type: type, project_uuid: project_uuid, project_name: project_name);
        });
  },

  /// 工作计划
  workAddPlanPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? type = map['type'];
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          return WorkAddPlanPage(
            uuid: uuid,
            type: type,
            project_uuid: project_uuid,
            project_name: project_name,
          );
        });
  },

  /// 清洁计划详情
  workPlanDetailPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? type = map['type'];
          String? project_uuid = map['project_uuid'];
          String? project_name = map['project_name'];
          return WorkPlanDetailPage(
            uuid: uuid,
            type: type,
            project_uuid: project_uuid,
            project_name: project_name,
          );
        });
  },

  ///工单详情
  workTaskOnePage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'] ?? '';
          // String? uuid = '63fb0ca42758fc04a2410cf979115b17';
          String? task_type = map['task_type'];
          bool open_camera = map['open_camera'] ?? false;
          return WorkPlanTaskOnePage(
            uuid: uuid,
            task_type: task_type,
            openCamera: open_camera,
          );
        });
  },

  /// 增加工单计划
  workAddTaskPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          String? uuid = map['uuid'];
          String? type = map['type'];
          String? work_url = map['work_url'];
          String? work_type = map['work_type'];
          String? choose = map['choose'];

          return WorkAddTaskPage(
            uuid: uuid,
            type: type,
            work_url: work_url,
            work_type: work_type,
            isChooseProject: choose,
          );
        });
  },

  /// 工单列表
  workPlanOrderListPage: (settings, uniqueId) {
    return MaterialPageRoute(
        settings: settings,
        builder: (_) {
          Map<String, dynamic> map = settings.arguments != null ? settings.arguments as Map<String, dynamic> : {};
          httpConfig.initHttpParams(map);
          return WorkPlanOrderListPage();
        });
  },
};
