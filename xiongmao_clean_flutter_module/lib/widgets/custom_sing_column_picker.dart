import 'package:bruno/bruno.dart';
import 'package:flutter/material.dart';

import '../res/colors.dart';

typedef ItemTitleBuilder = String Function(int index);

class SingleColumnDataPickerView {
  static void showSingleColumnDataPicker(BuildContext context, String title, List<String> _list, int? invertPosition, Function(int, String) onConfirm) {
    int current = 0;

    BrnMultiDataPicker(
      context: context,
      themeData: BrnPickerConfig(
        pickerHeight: 350,
      ),
      title: title,
      textColor: Colors.black,
      textSelectedColor: Colors.black,
      delegate: SingleColumnDataPickerDelegate(_list, selectedRowIndex: invertPosition ?? 0),
      confirmClick: (list) {
        onConfirm(list[current], _list[list[current]]);
      },
    ).show();
  }
}

class SingleColumnDataPickerDelegate extends BrnMultiDataPickerDelegate {
  List<String> dataList;
  int selectedRowIndex;

  SingleColumnDataPickerDelegate(this.dataList, {this.selectedRowIndex = 0});

  @override
  int numberOfComponent() {
    return 1;
  }

  @override
  int numberOfRowsInComponent(int component) {
    return dataList.length;
  }

  @override
  String titleForRowInComponent(int component, int index) {
    return dataList[index];
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    selectedRowIndex = row;
  }

  @override
  int initSelectedRowForComponent(int component) {
    return selectedRowIndex;
  }
}
