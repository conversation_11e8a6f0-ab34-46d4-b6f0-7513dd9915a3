import 'package:flutter/material.dart';

import '../res/colors.dart';

/// 默认字号18，白字蓝底，高度48
class MyButton extends StatelessWidget {

  MyButton({Key? key,
    this.text = '',
    this.fontSize,
    this.textColor = Colours.white,
    this.disabledTextColor,
    this.backgroundColor = Colours.base_primary,
    this.disabledBackgroundColor,
    this.minHeight = 48.0,
    this.minWidth = double.infinity,
    this.padding = const EdgeInsets.symmetric(horizontal: 16.0),
    this.radius = 2.0,
    this.side = BorderSide.none,
    this.onPressed,
    this.fontWeight,
  }) : super(key: key);

  final String text;
  double? fontSize = 18;
  final Color? textColor;
  final Color? disabledTextColor;
  final Color? backgroundColor;
  final Color? disabledBackgroundColor;
  final double? minHeight;
  final double? minWidth;
  final VoidCallback? onPressed;
  final EdgeInsetsGeometry padding;
  final double radius;
  final BorderSide side;
  final FontWeight? fontWeight;

  @override
  Widget build(BuildContext context) {
    // var isDark = context.isDark;
    return TextButton(
      onPressed: onPressed,
      style: ButtonStyle(
        // 文字颜色
        // foregroundColor: MaterialStateProperty.resolveWith((states) {
        //     if (states.contains(MaterialState.disabled)) {
        //       return disabledTextColor ?? Colors.white;
        //     }
        //     return textColor ?? Colors.white;
        //     // return textColor ?? (isDark ? Colours.dark_button_text : Colors.white);
        //   },
        // ),
        // // 背景颜色
        // backgroundColor: MaterialStateProperty.resolveWith((states) {
        //   if (states.contains(MaterialState.disabled)) {
        //     return disabledBackgroundColor ?? Colours.base_primary_disable;
        //   }
        //   return backgroundColor ?? Colours.base_primary;
        // }),

        // 文字颜色
        foregroundColor: MaterialStateProperty.resolveWith((states) {
            return textColor;
          },
        ),
        // 背景颜色
        backgroundColor: MaterialStateProperty.resolveWith((states) {
          return backgroundColor;
        }),
        // 水波纹
        overlayColor: MaterialStateProperty.resolveWith((states) {
          return (textColor ?? Colors.white).withOpacity(0.12);
        }),
        // 按钮最小大小
        minimumSize: (minWidth == null || minHeight == null) ? null : MaterialStateProperty.all<Size>(Size(minWidth!, minHeight!)),
        padding: MaterialStateProperty.all<EdgeInsetsGeometry>(padding),
        shape: MaterialStateProperty.all<OutlinedBorder>(
          RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(radius),
          ),
        ),
        side: MaterialStateProperty.all<BorderSide>(side),
      ),
      child: Text(text,textAlign: TextAlign.center, style: TextStyle(fontSize: fontSize,fontWeight: fontWeight),)
    );
  }
}
