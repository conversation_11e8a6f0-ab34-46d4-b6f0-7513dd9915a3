import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

class CustomSelectedArrowView extends StatelessWidget {
  final String dateText;
  final VoidCallback onPreviousDayPressed;
  final VoidCallback onNextDayPressed;
  final VoidCallback onDateTextPressed;
  final bool showLeftButton; // 控制左边按钮是否显示
  final bool showRightButton; // 控制右边按钮是否显示
  final Color backgroundColor; // 整体 View 的背景色
  final Color textColor; // 文字的颜色

  CustomSelectedArrowView({
    required this.dateText,
    required this.onPreviousDayPressed,
    required this.onNextDayPressed,
    required this.onDateTextPressed,
    this.showLeftButton = true, // 默认显示左边按钮
    this.showRightButton = true, // 默认显示右边按钮
    this.backgroundColor = Colours.base_primary_bg_page, // 默认背景色为灰色
    this.textColor = Colours.base_primary_text_title, // 默认文字颜色
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 37,
      decoration: BoxDecoration(
        color: backgroundColor, // 使用传入的背景色
        borderRadius: BorderRadius.circular(4),
      ),
      padding: EdgeInsets.symmetric(horizontal: 10),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // 左边按钮
          if (showLeftButton)
            InkWell(
              onTap: onPreviousDayPressed,
              child: const Padding(
                padding: EdgeInsets.only(top: 8, bottom: 8),
                child: LoadAssetImage(
                  'base/icon_circle_left_arrow',
                  height: 20,
                  width: 20,
                ),
              ),
            )
          else
            const SizedBox(width: 20), // 如果不显示左边按钮，留出占位空间

          // 中间日期文本
          Expanded(
            child: Center(
              child: InkWell(
                onTap: onDateTextPressed,
                child: CommonUtils.getSimpleText(
                  dateText,
                  14,
                  textColor, // 使用传入的文字颜色
                ),
              ),
            ),
          ),

          // 右边按钮
          if (showRightButton)
            InkWell(
              onTap: onNextDayPressed,
              child: const Padding(
                padding: EdgeInsets.only(top: 8, bottom: 8),
                child: LoadAssetImage(
                  'base/icon_circle_right_arrow',
                  height: 20,
                  width: 20,
                ),
              ),
            )
          else
            const SizedBox(width: 20), // 如果不显示右边按钮，留出占位空间
        ],
      ),
    );
  }
}
