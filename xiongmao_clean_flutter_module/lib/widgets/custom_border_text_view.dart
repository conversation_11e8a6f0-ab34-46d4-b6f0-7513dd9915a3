import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';

class CustomBorderText extends StatelessWidget {
  final String text;
  final Color borderColor;
  final Color textColor;
  final double borderRadius;
  final double paddingValue;
  final double fontSize;
  final Function()? onTapCallback;

  CustomBorderText({
    required this.text,
    this.borderColor = Colours.base_primary,
    this.textColor = Colours.base_primary,
    this.borderRadius = 8.0,
    this.paddingValue = 2.0,
    this.fontSize = 14.0,
    this.onTapCallback,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTapCallback,
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(color: borderColor),
          borderRadius: BorderRadius.circular(borderRadius),
        ),
        child: Padding(
          padding: EdgeInsets.all(paddingValue),
          child: Text(
            text,
            style: TextStyle(fontSize: fontSize, color: textColor),
          ),
        ),
      ),
    );
  }
}
