import 'package:bruno/bruno.dart';

class Brn7RowCustomDelegate implements BrnMultiDataPickerDelegate {
  List<String> days1;
  List<String> hours1;
  List<String> minutes1;
  List<String> seconds;
  List<String> days2;
  List<String> hours2;
  List<String> minutes2;

  int firstSelectedIndex1 = 0;
  int secondSelectedIndex1 = 0;
  int thirdSelectedIndex1 = 0;
  int firstSelectedIndex2 = 0;
  int secondSelectedIndex2 = 0;
  int thirdSelectedIndex2 = 0;

  Brn7RowCustomDelegate({
    required this.days1,
    required this.hours1,
    required this.minutes1,
    required this.seconds,
    required this.days2,
    required this.hours2,
    required this.minutes2,
    this.firstSelectedIndex1 = 0,
    this.secondSelectedIndex1 = 0,
    this.thirdSelectedIndex1 = 0,
    this.firstSelectedIndex2 = 0,
    this.secondSelectedIndex2 = 0,
    this.thirdSelectedIndex2 = 0,
  });

  @override
  int numberOfComponent() {
    return 7; // 返回组件数量
  }

  @override
  int numberOfRowsInComponent(int component) {
    switch (component) {
      case 0:
        return days1.length;
      case 1:
        return hours1.length;
      case 2:
        return minutes1.length;
      case 3:
        return seconds.length;
      case 4:
        return days2.length;
      case 5:
        return hours2.length;
      case 6:
        return minutes2.length;
      default:
        return 0;
    }
  }

  @override
  String titleForRowInComponent(int component, int index) {
    switch (component) {
      case 0:
        return days1[index];
      case 1:
        return hours1[index];
      case 2:
        return minutes1[index];
      case 3:
        return seconds[index];
      case 4:
        return days2[index];
      case 5:
        return hours2[index];
      case 6:
        return minutes2[index];
      default:
        return '';
    }
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    switch (component) {
      case 0:
        firstSelectedIndex1 = row;
        break;
      case 1:
        secondSelectedIndex1 = row;
        break;
      case 2:
        thirdSelectedIndex1 = row;
        break;
      case 4:
        firstSelectedIndex2 = row;
        break;
      case 5:
        secondSelectedIndex2 = row;
        break;
      case 6:
        thirdSelectedIndex2 = row;
        break;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    switch (component) {
      case 0:
        return firstSelectedIndex1;
      case 1:
        return secondSelectedIndex1;
      case 2:
        return thirdSelectedIndex1;
      case 4:
        return firstSelectedIndex2;
      case 5:
        return secondSelectedIndex2;
      case 6:
        return thirdSelectedIndex2;
      default:
        return 0;
    }
  }
}
