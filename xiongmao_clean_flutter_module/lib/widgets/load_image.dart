import 'dart:io';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';

import '../res/constant.dart';
import '../util/log_utils.dart';
import 'file_image_ex.dart';
import 'image_utils.dart';

/// 图片加载（支持本地与网络图片）
class LoadImage extends StatelessWidget {
  LoadImage(
    this.image, {
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.format = ImageFormat.png,
    this.holderImg = '',
    this.cacheWidth,
    this.cacheHeight,
    this.radius = 0,
  });

  late String image;
  final double? width;
  final double? height;
  final BoxFit fit;
  final ImageFormat format;
  final String holderImg;
  final int? cacheWidth;
  final int? cacheHeight;
  final double? radius;

  @override
  Widget build(BuildContext context) {
    // MyLog.e("msg-image-替换链接前缀--"+image);
    // if(image.startsWith("http://intranet")){
    //   // 替换链接前缀
    //   Uri originalUri = Uri.parse(image);
    //   var host = originalUri.host;
    //   var substring = image.substring(image.indexOf(host)+host.length);
    //   image = "https://"+Constant.myDomain+substring;
    // }

    if (image.isEmpty || image.startsWith('http')) {
      var init = "init_loading_pic";
      if (holderImg.isNotEmpty) {
        init = holderImg;
      }
      // ImageCache().clear();
      // final Widget holder = LoadAssetImage(holderImg, height: height, width: width, fit: fit);
      final Widget placeholder = LoadAssetImage(init, height: height, width: width, fit: fit);
      final Widget errorWidget = LoadAssetImage(init, height: height, width: width, fit: fit);
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius!), // 设置圆角半径
        child: CachedNetworkImage(
          // imageUrl: "https://eeapi.bluestarm.com/viewrise/employeasy/eng/compapp/v2/photo?type=incomeProof2&accessToken=Ko2tsKG4s0l_Hk/PmwSXNF9otWXFYShrbKQWJnGGgaE=",
          imageUrl: image,
          httpHeaders: {"accessToken": (SpUtil.getString(Constant.accessToken) ?? "")},
          placeholder: (_, __) => placeholder,
          errorWidget: (_, __, dynamic error) => errorWidget,
          width: width,
          height: height,
          // cacheKey: Uuid().v4(),
          fit: fit,
          errorListener: (value) {
            MyLog.e("msg----value---" + value.toString());
          },
          // memCacheWidth: cacheWidth,
          // memCacheHeight: cacheHeight,
        ),
      );

      // return Image.network(image,width: width,height: height,);
    } else if (image.startsWith('/storage') || image.startsWith('/var') || image.startsWith('/data')) {
      //路径是/storage/emulated/0/Android/data/com.viewrise.hk/cache/luban_disk_cache/CMP_20230326102501655.jpg
      return Image(
        image: FileImageEx(File(image)),
        width: width,
        height: height,
      );
    } else {
      return LoadAssetImage(
        image,
        height: height,
        width: width,
        fit: fit,
        format: format,
        radius: radius!,
        cacheWidth: cacheWidth,
        cacheHeight: cacheHeight,
      );
    }
  }
}

/// 加载本地资源图片
class LoadAssetImage extends StatelessWidget {
  const LoadAssetImage(this.image, {this.width, this.height, this.cacheWidth, this.cacheHeight, this.radius = 0, this.fit, this.format = ImageFormat.png, this.color});

  final String image;
  final double? width;
  final double? height;
  final int? cacheWidth;
  final int? cacheHeight;
  final double radius;
  final BoxFit? fit;
  final ImageFormat format;
  final Color? color;

  @override
  Widget build(BuildContext context) {
    if (image.isNotEmpty) {
      return ClipRRect(
        borderRadius: BorderRadius.circular(radius), // 设置圆角半径
        child: Image.asset(
          ImageUtils.getImgPath(image, format: format),
          height: height,
          width: width,
          cacheWidth: cacheWidth,
          cacheHeight: cacheHeight,
          fit: fit,
          color: color,

          /// 忽略图片语义
          excludeFromSemantics: true,
        ),
      );
    } else {
      return Container();
    }
  }
}
