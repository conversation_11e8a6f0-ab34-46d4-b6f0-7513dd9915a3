import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/util/theme_utils.dart';
import '../res/colors.dart';
import '../res/constant.dart';
import '../res/dimens.dart';
import '../res/gaps.dart';
import '../util/log_utils.dart';
import 'load_image.dart';

/// 自定义AppBar 带路由的
class MyAppRouteBar extends StatelessWidget implements PreferredSizeWidget {
  static const String BACK = 'back';
  static const String CLOSE = 'close';

  MyAppRouteBar({
    this.backgroundColor = Colours.white,
    this.mainRightImg = '',
    this.subRightImg = '',
    this.leftWidgt,
    this.actionName = '',
    this.actionWidget,
    this.backImg = 'icon_base_back',
    this.backImgColor,
    this.onPressed,
    this.subRightOnPressed,
    this.onBack,
    required this.nameList,
    required this.tabController,
    this.backType = CLOSE,
    this.isHaveLeftBtn = true,
    required this.onSelect,
  });

  final List<String> nameList;
  final TabController tabController;
  final ValueChanged<int> onSelect;

  final Color? backgroundColor;
  final String mainRightImg;
  final String subRightImg;
  String backImg;
  final Color? backImgColor;
  final String actionName;
  final VoidCallback? onPressed;
  final VoidCallback? subRightOnPressed;
  final VoidCallback? onBack;
  final String backType;
  final bool isHaveLeftBtn;
  final Widget? actionWidget;
  final Widget? leftWidgt;

  @override
  Widget build(BuildContext context) {
    final Color bgColor = backgroundColor ?? Theme.of(context).backgroundColor;

    final SystemUiOverlayStyle overlayStyle = ThemeData.estimateBrightnessForColor(bgColor) == Brightness.dark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;

    final Widget imgAction = mainRightImg.isNotEmpty
        ? Positioned(
            right: 0,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              child: Container(
                padding: const EdgeInsets.all(20),
                child: LoadAssetImage(
                  mainRightImg,
                  width: 25,
                  height: 25,
                ),
              ),
              onTap: () {
                MyLog.e("msg---1111---" + ("icon_person_center" == mainRightImg).toString());
                "icon_person_center" == mainRightImg ? gotoPersonCenter() : onPressed?.call();
              },
            ),
          )
        : Gaps.empty;

    final Widget subImgAction = subRightImg.isNotEmpty
        ? Positioned(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              child: Container(
                padding: const EdgeInsets.all(20),
                child: LoadAssetImage(
                  subRightImg,
                  width: 24,
                  height: 24,
                ),
              ),
              onTap: subRightOnPressed,
            ),
            right: 40,
          )
        : Gaps.empty;

    final Widget action = (actionWidget != null || actionName.isNotEmpty)
        ? Positioned(
            right: 0.0,
            child: GestureDetector(
              child: actionWidget ??
                  Container(
                      padding: const EdgeInsets.only(left: 12, right: 12, top: 20, bottom: 20),
                      child: Text(
                        actionName,
                        style: TextStyle(fontSize: 14, color: Colours.base_primary_text_title),
                      )),
              onTap: onPressed,
            ),
          )
        : Gaps.empty;

    final Widget back = isHaveLeftBtn
        ? (leftWidgt == null
            ? IconButton(
                padding: EdgeInsets.all(15),
                onPressed: () async {
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (onBack != null) {
                    onBack!.call();
                    return;
                  }
                  final isBack = await Navigator.maybePop(context);
                  if (!isBack) {
                    await SystemNavigator.pop();
                  }
                },
                icon: LoadAssetImage(
                  backImg,
                  width: 25,
                  height: 25,
                ),
              )
            : leftWidgt!)
        : Gaps.empty;

    /// 中间的部分
    final Widget titleWidget = Container(
      width: double.infinity,
      alignment: Alignment.center,
      child: TabBar(
        padding: EdgeInsets.only(left: 30, right: 30),
        indicatorColor: Colours.base_primary,
        isScrollable: true,
        controller: tabController,
        tabs: nameList.map((name) => Tab(text: name)).toList(),
        labelColor: Colours.base_primary,
        unselectedLabelColor: Colours.base_primary_text_title.withOpacity(0.6),
        labelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: Colours.base_primary,
        ),
        unselectedLabelStyle: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.normal,
        ),
        onTap: (index) {
          onSelect(index);
        },
      ),
    );

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: overlayStyle,
      child: Material(
        color: bgColor,
        child: SafeArea(
          child: Stack(
            alignment: Alignment.centerLeft,
            children: <Widget>[
              titleWidget,
              back,
              subImgAction,
              Visibility(visible: (actionName.isNotEmpty || actionWidget != null) && mainRightImg.isEmpty, child: action),
              Visibility(visible: mainRightImg.isNotEmpty, child: imgAction),
              // 使用 Positioned 将 Gaps.line 放置在视图的最下方
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Gaps.line,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);

  void gotoPersonCenter() {
    MyLog.e("msg---2222---");
  }
}
