import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:sp_util/sp_util.dart';
import 'package:xiongmao_clean_flutter_module/util/theme_utils.dart';
import '../res/colors.dart';
import '../res/constant.dart';
import '../res/dimens.dart';
import '../res/gaps.dart';
import '../util/log_utils.dart';
import 'load_image.dart';
import 'my_button.dart';

/// 自定义AppBar
class MyAppBar extends StatelessWidget implements PreferredSizeWidget {
  static const String BACK = 'back';
  static const String CLOSE = 'close';

  MyAppBar({this.backgroundColor = Colours.white, this.centerTitleColor = Colours.base_primary_text_title, this.centerSubTitleColor = Colours.base_primary_text_caption, this.mainRightImg = '', this.subRightImg = '', this.leftWidgt, this.centerTitle = '', this.centerSubTitle = '', this.actionName = '', this.actionWidget, this.backImg = 'icon_base_back', this.backImgColor, this.onPressed, this.subRightOnPressed, this.onBack, this.backType = CLOSE, this.isHaveLeftBtn = true});

  final Color centerTitleColor;
  final Color centerSubTitleColor;
  final Color? backgroundColor;
  final String mainRightImg;
  final String subRightImg;
  final String centerTitle;
  final String? centerSubTitle;
  String backImg;
  final Color? backImgColor;
  final String actionName;
  final VoidCallback? onPressed;
  final VoidCallback? subRightOnPressed;
  final VoidCallback? onBack;
  final String backType;
  final bool isHaveLeftBtn;
  final Widget? actionWidget;
  final Widget? leftWidgt;

  @override
  Widget build(BuildContext context) {
    // if (isHaveLeftBtn && backType == CLOSE) {
    //   backImg = 'base_close';
    // }

    final Color bgColor = backgroundColor ?? context.backgroundColor;

    final SystemUiOverlayStyle overlayStyle = ThemeData.estimateBrightnessForColor(bgColor) == Brightness.dark ? SystemUiOverlayStyle.light : SystemUiOverlayStyle.dark;

    final Widget imgAction = mainRightImg.isNotEmpty
        ? Positioned(
            right: 0,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              child: Container(
                padding: const EdgeInsets.all(20),
                child: LoadAssetImage(
                  mainRightImg,
                  width: 25,
                  height: 25,
                ),
              ),
              onTap: () {
                MyLog.e("msg---1111---" + ("icon_person_center" == mainRightImg).toString());
                "icon_person_center" == mainRightImg ? gotoPersonCenter() : onPressed?.call();
              },
            ),
          )
        : Gaps.empty;

    final Widget subImgAction = subRightImg.isNotEmpty
        ? Positioned(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              child: Container(
                padding: const EdgeInsets.all(20),
                child: LoadAssetImage(
                  subRightImg,
                  width: 24,
                  height: 24,
                ),
              ),
              onTap: subRightOnPressed,
            ),
            right: 40,
          )
        : Gaps.empty;

    final Widget action = (actionWidget != null || actionName.isNotEmpty)
        ? Positioned(
            right: 0.0,
            child: GestureDetector(
              child: actionWidget ??
                  Container(
                      padding: const EdgeInsets.only(left: 12, right: 12, top: 20, bottom: 20),
                      child: Text(
                        actionName,
                        style: TextStyle(fontSize: 14, color: Colours.base_primary_text_title),
                      )),
              onTap: onPressed,
            ),
          )
        : Gaps.empty;

    final Widget back = isHaveLeftBtn
        ? (leftWidgt == null
            ? IconButton(
                padding: EdgeInsets.all(15),
                onPressed: () async {
                  FocusManager.instance.primaryFocus?.unfocus();
                  if (onBack != null) {
                    onBack!.call();
                    return;
                  }
                  final isBack = await Navigator.maybePop(context);
                  if (!isBack) {
                    await SystemNavigator.pop();
                  }
                },
                // padding: const EdgeInsets.all(12.0),
                icon: LoadAssetImage(
                  backImg,
                  width: 25,
                  height: 25,
                ),
              )
            : leftWidgt!)
        : Gaps.empty;

    final Widget titleWidget = Semantics(
      namesRoute: true,
      header: true,
      child: Container(
        alignment: Alignment.center,
        width: double.infinity,
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              centerTitle,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                color: centerTitleColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
                height: 1.0, // 设置行高为1.0来调整行间距
              ),
            ),
            if (!TextUtil.isEmpty(centerSubTitle))
              Text(
                centerSubTitle!,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  color: centerSubTitleColor,
                  fontSize: 12,
                ),
              ),
          ],
        ),
      ),
    );

    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: overlayStyle,
      child: Material(
        color: bgColor,
        child: SafeArea(
          child: Stack(
            alignment: Alignment.centerLeft,
            children: <Widget>[
              titleWidget,
              back,
              subImgAction,
              Visibility(child: action, visible: (actionName.isNotEmpty || actionWidget != null) && mainRightImg.isEmpty),
              Visibility(child: imgAction, visible: mainRightImg.isNotEmpty),
              // 使用 Positioned 将 Gaps.line 放置在视图的最下方
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: Gaps.line,
              ),
            ],
          ),
        ),
      ),
    );
  }

  @override
  Size get preferredSize => const Size.fromHeight(48.0);

  gotoPersonCenter() {
    MyLog.e("msg---2222---");
  }
}
