import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

class CustomReportSelector extends StatefulWidget {
  int? defSelectedIndex = 0;
  final List<String> options;
  final ValueChanged<int> onSelectedIndexChanged;

  CustomReportSelector({
    this.defSelectedIndex,
    required this.options,
    required this.onSelectedIndexChanged,
  }) {
    assert(options.length <= 3, 'Options list should contain a maximum of 3 items.');
  }

  @override
  _CustomReportSelectorState createState() => _CustomReportSelectorState();
}

class _CustomReportSelectorState extends State<CustomReportSelector> {
  int selectedIndex = 0;

  @override
  void initState() {
    super.initState();
    selectedIndex = widget.defSelectedIndex ?? 0;
  }

  void handleOptionTap(int index) {
    setState(() {
      selectedIndex = index;
    });
    widget.onSelectedIndexChanged(index);
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(widget.options.length, (index) {
        String option = widget.options[index];

        return Expanded(
          child: GestureDetector(
            onTap: () => handleOptionTap(index),
            child: Stack(
              alignment: Alignment.bottomRight, // 对齐到右下角
              children: [
                Container(
                  padding: EdgeInsets.symmetric(vertical: 8, horizontal: 10),
                  decoration: BoxDecoration(
                    color: selectedIndex == index ? Colours.base_primary_select : Colours.base_primary_bg_page,
                    borderRadius: BorderRadius.all(Radius.circular(4)),
                  ),
                  alignment: Alignment.center,
                  child: Text(
                    option,
                    style: TextStyle(
                      color: selectedIndex == index ? Colours.base_primary : Colours.base_primary_text_title,
                      fontSize: 14,
                    ),
                  ),
                ),
                if (selectedIndex == index)
                  const Padding(
                    padding: EdgeInsets.all(0.0), // 图标与边界的间距
                    child: LoadAssetImage(
                      'base/icon_base_tab_select',
                      height: 16,
                      width: 16,
                    ),
                  ),
              ],
            ),
          ),
        );
      }).expand((widget) => [widget, const SizedBox(width: 10)]).toList()
        ..removeLast(), // 移除最后一个多余的间隔
    );
  }
}
