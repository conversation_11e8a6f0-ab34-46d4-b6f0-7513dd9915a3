import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

import '../res/colors.dart';

class CustomSearchView extends StatefulWidget {
  final ValueChanged<String> onTextChanged;
  final String hint; // 新增的hint参数
  final String initialText; // 新增的initialText参数

  CustomSearchView({
    required this.onTextChanged,
    this.hint = "请输入内容", // 设置默认提示文本
    this.initialText = "", // 设置默认初始文本为空
  });

  @override
  _CustomSearchViewState createState() => _CustomSearchViewState();
}

class _CustomSearchViewState extends State<CustomSearchView> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText) // 设置初始文本
      ..addListener(() {
        widget.onTextChanged(_controller.text);
      });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 36.0,
      decoration: BoxDecoration(
        color: Colours.base_primary_bg_page,
        borderRadius: BorderRadius.circular(4.0),
      ),
      child: Row(
        children: <Widget>[
          const Padding(
            padding: EdgeInsets.only(left: 8.0),
            child: LoadAssetImage(
              'icon_search',
              height: 18,
              width: 18,
            ), // 搜索图标
          ),
          Gaps.hGap4,
          Expanded(
            child: TextField(
              controller: _controller,
              decoration: InputDecoration(
                hintText: widget.hint, // 使用外部传入的hint文本
                hintStyle: const TextStyle(fontSize: 14.0),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(vertical: 10.9), // 垂直居中调整
              ),
              style: const TextStyle(fontSize: 14.0),
              onChanged: widget.onTextChanged,
            ),
          ),
        ],
      ),
    );
  }
}
