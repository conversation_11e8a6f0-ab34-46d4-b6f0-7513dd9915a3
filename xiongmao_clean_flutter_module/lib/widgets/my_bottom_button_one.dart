import 'package:flutter/material.dart';

import '../res/colors.dart';
import '../res/gaps.dart';
import 'my_button.dart';

class MyBottomButtonOne extends StatelessWidget {
  final Color? backgroundColor;
  final double? height;
  final double? horizontalPadding;
  final double? verticalPadding;
  final VoidCallback onPressed;
  final String title;
  final bool isNeedBg;
  final bool isNeedDivideLine;
  final bool isDisabled; // 新增禁止点击的状态

  MyBottomButtonOne({
    required this.title,
    Key? key,
    this.isNeedBg = true,
    this.height = 50,
    this.horizontalPadding = 16.0,
    this.verticalPadding = 5.0,
    this.backgroundColor = Colours.base_primary,
    required this.onPressed,
    this.isNeedDivideLine = true,
    this.isDisabled = false, // 默认不禁用按钮
  });

  @override
  Widget build(BuildContext context) {
    Widget line = isNeedDivideLine ? Gaps.line : Gaps.empty;
    return Column(
      children: [
        line,
        Container(
          height: height,
          padding: EdgeInsets.symmetric(horizontal: horizontalPadding ?? 0.0, vertical: verticalPadding ?? 0.0),
          width: double.infinity,
          color: (isNeedBg ? Colours.white : Colours.transparent),
          child: MyButt<PERSON>(
            onPressed: isDisabled ? null : onPressed,
            // 如果禁用状态，设置onPressed为null
            text: title,
            fontSize: 14,
            textColor: Colours.white,
            radius: 4,
            backgroundColor: isDisabled ? Colors.grey : backgroundColor,
          ),
        ),
      ],
    );
  }
}
