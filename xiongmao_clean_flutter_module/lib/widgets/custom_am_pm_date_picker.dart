import 'package:flutter/material.dart';
import 'package:bruno/bruno.dart';

typedef ItemTitleBuilder = String Function(int index);

class AMPMDatePickerView {
  static void showAMPMColumnDataPicker(BuildContext context, String title, String initialDate, String initialAMPM, Function(String, int, int, int) onConfirm) {
    DateTime now = DateTime.now();
    DateTime initialDateTime = DateTime.tryParse(initialDate) ?? now;
    int currentYearIndex = initialDateTime.year - 2024;
    int currentMonthIndex = initialDateTime.month - 1;
    int currentDayIndex = initialDateTime.day - 1;
    int currentAMPMIndex = (initialAMPM == '下午') ? 1 : 0;

    BrnMultiDataPicker(
      context: context,
      themeData: BrnPickerConfig(pickerHeight: 350),
      title: title,
      delegate: AMPMColumnDataPickerDelegate(currentAMPMIndex, currentYearIndex, currentMonthIndex, currentDayIndex, onConfirm),
      confirmClick: (selectedIndexs) {
        if (selectedIndexs.length == 4) {
          int year = 2024 + selectedIndexs[0] as int;
          int month = selectedIndexs[1].toInt() + 1;
          int day = selectedIndexs[2].toInt() + 1;
          String ampm = AMPMColumnDataPickerDelegate.dataList[3][selectedIndexs[3].toInt()];
          onConfirm(ampm, year, month, day);
        }
      },
    ).show();
  }
}

class AMPMColumnDataPickerDelegate extends BrnMultiDataPickerDelegate {
  static final List<List<String>> dataList = [
    List.generate(7, (index) => '${index + 2024}年'), // 数据1 (年份)
    List.generate(12, (index) => '${index + 1}月'), // 数据2 (月份)
    [], // 数据3 (天数会在运行时动态生成)
    ['上午', '下午'], // 数据4 (AM/PM)
  ];

  final int _initialYearIndex;
  final int _initialMonthIndex;
  final int _initialDayIndex;
  final int _initialAMPMIndex;
  final Function(String, int, int, int) _onConfirm;

  int _selectedYearIndex;
  int _selectedMonthIndex;
  int _selectedDayIndex;
  int _selectedAMPMIndex;

  AMPMColumnDataPickerDelegate(this._initialAMPMIndex, this._initialYearIndex, this._initialMonthIndex, this._initialDayIndex, this._onConfirm)
      : _selectedYearIndex = _initialYearIndex,
        _selectedMonthIndex = _initialMonthIndex,
        _selectedDayIndex = _initialDayIndex,
        _selectedAMPMIndex = _initialAMPMIndex;

  @override
  int numberOfComponent() {
    return 4;
  }

  @override
  int numberOfRowsInComponent(int component) {
    switch (component) {
      case 0:
        return dataList[0].length;
      case 1:
        return dataList[1].length;
      case 2:
        int year = 2024 + _selectedYearIndex;
        int month = _selectedMonthIndex + 1;
        return _getDaysInMonth(year, month);
      case 3:
        return dataList[3].length;
      default:
        return 0;
    }
  }

  @override
  String titleForRowInComponent(int component, int index) {
    switch (component) {
      case 0:
        return dataList[0][index];
      case 1:
        return dataList[1][index];
      case 2:
        return '${index + 1}日';
      case 3:
        return dataList[3][index];
      default:
        return '';
    }
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    switch (component) {
      case 0:
        _selectedYearIndex = row;
        // Ensure the day index is within the valid range for the new year and month
        int daysInMonth = _getDaysInMonth(2024 + _selectedYearIndex, _selectedMonthIndex + 1);
        if (_selectedDayIndex >= daysInMonth) {
          _selectedDayIndex = daysInMonth - 1;
        }
        break;
      case 1:
        _selectedMonthIndex = row;
        // Ensure the day index is within the valid range for the new year and month
        int daysInMonth = _getDaysInMonth(2024 + _selectedYearIndex, _selectedMonthIndex + 1);
        if (_selectedDayIndex >= daysInMonth) {
          _selectedDayIndex = daysInMonth - 1;
        }
        break;
      case 2:
        _selectedDayIndex = row;
        break;
      case 3:
        _selectedAMPMIndex = row;
        break;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    switch (component) {
      case 0:
        return _selectedYearIndex;
      case 1:
        return _selectedMonthIndex;
      case 2:
        return _selectedDayIndex;
      case 3:
        return _selectedAMPMIndex;
      default:
        return 0;
    }
  }

  int _getDaysInMonth(int year, int month) {
    switch (month) {
      case 1:
      case 3:
      case 5:
      case 7:
      case 8:
      case 10:
      case 12:
        return 31;
      case 4:
      case 6:
      case 9:
      case 11:
        return 30;
      case 2:
        if ((year % 4 == 0 && year % 100 != 0) || (year % 400 == 0)) {
          return 29; // Leap year
        } else {
          return 28;
        }
      default:
        return 31;
    }
  }

  @override
  void confirmClick(List<num> selectedIndexs) {
    if (selectedIndexs.length == 4) {
      int year = 2024 + selectedIndexs[0].toInt();
      int month = selectedIndexs[1].toInt() + 1;
      int day = selectedIndexs[2].toInt() + 1;
      String ampm = dataList[3][selectedIndexs[3].toInt()];
      _onConfirm(ampm, year, month, day);
    }
  }
}
