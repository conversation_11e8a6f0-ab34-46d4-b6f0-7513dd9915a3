import 'package:flutter/material.dart';

import '../res/colors.dart';
import '../util/common_utils.dart';

///自定义实体类
class GridTagNumItem {
  final String title;
  final Color backgroundColor;
  final String num;

  GridTagNumItem({
    required this.title,
    required this.backgroundColor,
    required this.num,
  });
}

typedef OnItemSelectedCallback = void Function(int index);

/// 自定义tagNumber
class CustomTagNumberView extends StatefulWidget {
  final List<GridTagNumItem> items;
  final int crossAxisCount;
  final OnItemSelectedCallback onItemSelected;

  CustomTagNumberView({
    Key? key,
    required this.items,
    this.crossAxisCount = 5,
    required this.onItemSelected,
  }) : super(key: key);

  @override
  _CustomTagNumberViewState createState() => _CustomTagNumberViewState();
}

class _CustomTagNumberViewState extends State<CustomTagNumberView> {
  int selectedIndex = 0; // 默认选中第一个

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      padding: const EdgeInsets.only(bottom: 16, left: 10, right: 10),
      child: GridView.builder(
        padding: const EdgeInsets.only(top: 10),
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: widget.crossAxisCount,
          crossAxisSpacing: 10,
          mainAxisSpacing: 6,
          childAspectRatio: 1.6, // 调整高度
        ),
        itemCount: widget.items.length,
        itemBuilder: (context, index) {
          final item = widget.items[index];
          bool isSelected = selectedIndex == index;

          return GestureDetector(
            onTap: () {
              setState(() {
                selectedIndex = index;
              });
              widget.onItemSelected(index);
            },
            child: Stack(
              children: [
                Container(
                  margin: const EdgeInsets.only(
                    top: 10,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected ? Colours.base_primary_select : Colours.base_primary_un_select,
                    borderRadius: BorderRadius.circular(50),
                    border: Border.all(
                      color: isSelected ? Colours.base_primary : Colours.transparent,
                      width: isSelected ? 1 : 0,
                    ),
                  ),
                  child: Align(
                    alignment: Alignment.center,
                    child: CommonUtils.getSimpleText(
                      item.title,
                      12,
                      isSelected ? Colours.base_primary : Colours.base_primary_text_title,
                    ),
                  ),
                ),
                Positioned(
                  right: 0,
                  child: ClipOval(
                    child: Container(
                      width: 24,
                      height: 24,
                      color: item.backgroundColor,
                      alignment: Alignment.center,
                      child: CommonUtils.getSimpleText(
                        item.num,
                        12,
                        Colours.white,
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}
