import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/widgets/load_image.dart';

class CustomAvatarView extends StatelessWidget {
  final String? name;
  final String? avatarUrl;
  final double textSize;
  final double size;

  CustomAvatarView({required this.name, this.avatarUrl, this.textSize = 16, this.size = 46});

  @override
  Widget build(BuildContext context) {
    String? displayName = (!TextUtil.isEmpty(name)
        ? name!.length > 2
            ? name?.substring(name!.length - 2)
            : name
        : name);

    return Container(
      width: size,
      height: size,
      decoration: BoxDecoration(
        color: Colours.base_primary,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          if (!TextUtil.isEmpty(avatarUrl))
            ClipRRect(
              borderRadius: BorderRadius.circular(8.0), // 设置圆角半径为20
              child: LoadImage(
                avatarUrl!,
                width: size,
                height: size,
              ),
            ),
          if (avatarUrl == null || avatarUrl!.isEmpty)
            Text(
              displayName ?? "",
              style: TextStyle(color: Colors.white, fontSize: textSize),
              textAlign: TextAlign.center,
            ),
        ],
      ),
    );
  }
}
