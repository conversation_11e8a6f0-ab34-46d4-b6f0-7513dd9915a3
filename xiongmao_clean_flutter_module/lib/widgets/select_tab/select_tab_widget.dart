import 'package:common_utils/common_utils.dart';
import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/widgets/select_tab/select_tab_data.dart';

typedef TabItemClickCallback = void Function(List<int> indexs);

/// _multiSelect 是否支持多选
/// _lastIsAddOne 和 _addFunction 配套使用，最有一个item 是否是添加按钮， _lastIsAddOne == false 时 _addFunction 不生效
/// _hideMore 和 _showLines 使用，全部展示或者展示可折叠
/// _crossAxisCount，每行展示个数
/// _tabFontSize 文字大小
/// _itemClickCallback item 选择变化后的回调函数
class SelectTabWidget<T extends SelectTabData> extends StatefulWidget {
  final List<T> _data;
  List<int>? _selectedIndex;
  bool _multiSelect = false;
  int _showLines = -1;
  bool _hideMore = false;
  bool _lastIsAddOne = false;
  int _crossAxisCount = 4;
  double _tabFontSize = 12;
  double _childAspectRatio = 10 / 5;
  double _paddingTop = 10;
  double _paddingBottom = 10;
  double _radius = 4; //每个item的圆角
  Color? _selectedColor;
  Color? bgSelectedColor;
  Color? bgUnSelectedColor;

  TabItemClickCallback? _itemClickCallback;
  Function? _addFunction;
  Function? postFrameFunc;

  SelectTabWidget(this._data, {Key? key, bool multiSelect = false, int showLines = -1, double paddingTop = 10, double paddingBottom = 10, bool? hideMore, int? crossAxisCount, double? tabFontSize, double? childAspectRatio, double? radius, List<int>? defaultSelectedIndex, bool? lastIsAddOne, Color selectedColor = Colours.base_primary, TabItemClickCallback? itemClickCallback, this.bgSelectedColor = Colours.base_primary_select, this.bgUnSelectedColor = Colours.base_primary_select, Function? addFunction, this.postFrameFunc}) : super(key: key) {
    _multiSelect = multiSelect;
    _selectedColor = selectedColor;
    _showLines = showLines;
    _paddingTop = paddingTop;
    _paddingBottom = paddingBottom;
    _hideMore = hideMore ?? false;
    _lastIsAddOne = lastIsAddOne ?? false;
    _crossAxisCount = crossAxisCount ?? 4;
    _tabFontSize = tabFontSize ?? 12;
    _selectedIndex = defaultSelectedIndex ?? [];
    _itemClickCallback = itemClickCallback;
    _addFunction = addFunction;
    _childAspectRatio = childAspectRatio ?? (10 / 5);
    _radius = radius ?? 4;
  }

  @override
  State<SelectTabWidget> createState() => _SelectTabState();
}

class _SelectTabState extends State<SelectTabWidget> {
  int count = 0;
  int hideCount = 0;
  List<int> selectedIndex = [];

  @override
  void initState() {
    super.initState();
    selectedIndex = widget._selectedIndex!;
    if (widget._hideMore) {
      int lines = (widget._data.length / widget._crossAxisCount + 0.5).toInt();
      if (lines <= widget._showLines) {
        count = widget._data.length;
      } else {
        count = widget._showLines * widget._crossAxisCount;
      }
    } else {
      count = widget._data.length;
    }
    hideCount = count;

    WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      widget.postFrameFunc?.call();
    });
  }

  @override
  void didUpdateWidget(covariant SelectTabWidget<SelectTabData> oldWidget) {
    // TODO: implement didUpdateWidget
    super.didUpdateWidget(oldWidget);
    setState(() {
      count = widget._data.length;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        ConstrainedBox(
          constraints: const BoxConstraints.tightFor(),
          child: GridView.builder(
            physics: const NeverScrollableScrollPhysics(),
            shrinkWrap: true,
            padding: EdgeInsets.only(left: 0, right: 0, top: widget._paddingTop, bottom: widget._paddingBottom),
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(childAspectRatio: widget._childAspectRatio, mainAxisSpacing: 0, crossAxisSpacing: 0, crossAxisCount: widget._crossAxisCount),
            itemCount: count,
            itemBuilder: (context, index) {
              return _getItemWidget(widget._data[index].getTabText(), widget._data[index].getTabMark(), index, widget._tabFontSize, (widget._lastIsAddOne && index == widget._data.length - 1), widget._itemClickCallback, widget._addFunction);
            },
          ),
        ),
        InkWell(
          child: Container(
            height: widget._hideMore ? 40 : 0,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  (widget._hideMore && count == hideCount) ? "展开" : "收起",
                  style: TextStyle(fontSize: widget._tabFontSize, color: Colours.base_primary),
                ),
                const SizedBox(
                  width: 10,
                ),
                // Image.asset(
                //     (widget._hideMore && count == hideCount)
                //         ? ResImage.iconArrowDown
                //         : ResImage.iconArrowUp,
                //     width: 15),
              ],
            ),
            alignment: Alignment.center,
          ),
          onTap: () {
            if (count == widget._data.length) {
              count = hideCount;
            } else {
              count = widget._data.length;
            }
            setState(() {
              count;
            });
          },
        )
      ],
    );
  }

  Widget _getItemWidget(String? text, String? mark, int index, double? tabFontSize, bool isAdd, TabItemClickCallback? itemClickCallback, Function? addFunction) {
    return text == null
        ? Container()
        : Container(
            padding: const EdgeInsets.all(5),
            child: GestureDetector(
              child: Container(
                height: 45,
                alignment: Alignment.center,
                child: Container(
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    border: selectedIndex.contains(index) ? Border.all(width: 1, color: Colours.base_primary) : null,
                    color: selectedIndex.contains(index) ? widget.bgSelectedColor : widget.bgUnSelectedColor,
                    borderRadius: BorderRadius.circular(widget._radius),
                  ),
                  child: Text(
                    text,
                    style: TextStyle(
                      decoration: TextDecoration.none,
                      fontSize: tabFontSize,
                      color: selectedIndex.contains(index) ? widget._selectedColor : Colours.base_primary_text_title,
                    ),
                    softWrap: false,
                    overflow: TextOverflow.fade,
                  ),
                ),
              ),
              onTap: () {
                if (!isAdd) {
                  if (widget._multiSelect) {
                    bool isSelected = selectedIndex.contains(index);
                    if (isSelected) {
                      selectedIndex.remove(index);
                    } else {
                      selectedIndex.add(index);
                    }
                  } else {
                    if (selectedIndex.length > 1) {
                      selectedIndex.clear();
                      selectedIndex.add(index);
                    } else if (selectedIndex.isEmpty) {
                      selectedIndex.add(index);
                    } else {
                      selectedIndex[0] = index;
                    }
                  }
                  setState(() {
                    selectedIndex;
                  });

                  if (itemClickCallback != null) {
                    itemClickCallback.call(selectedIndex);
                  }
                } else {
                  if (addFunction != null) {
                    addFunction.call();
                  }
                }
              },
            ),
          );
  }
}
