import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/util/theme_utils.dart';
import '../res/gaps.dart';
import 'load_image.dart';

/// design/9暂无状态页面/index.html#artboard3
class StateLayout extends StatelessWidget {
  const StateLayout({
    required this.type,
    this.hintText,
    this.onClickCallBack,
    this.onClickEmptyWidgetCallBack,
    this.emptyWidget,
    this.isNeedScroll,
  });

  final Widget? emptyWidget;
  final VoidCallback? onClickCallBack;
  final VoidCallback? onClickEmptyWidgetCallBack;
  final StateType type;
  final String? hintText;
  final bool? isNeedScroll;

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colours.base_primary_bg_page,
      child: InkWell(
        onTap: onClickCallBack,
        child: (isNeedScroll ?? false)
            ? SingleChildScrollView(
                child: buildColumn(context),
              )
            : buildColumn(context),
      ),
    );
  }

  Column buildColumn(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        if (type == StateType.loading)
          const CupertinoActivityIndicator(radius: 16.0)
        else
          Opacity(
            opacity: context.isDark ? 0.5 : 1,
            child: LoadAssetImage(
              // 'state/${type.img}',
              type.img,
              width: 100,
            ),
          ),
        const SizedBox(
          width: double.infinity,
          height: 16,
        ),
        Text(
          hintText ?? type.hintText,
          textAlign: TextAlign.center,
          style: Theme.of(context).textTheme.subtitle2?.copyWith(fontSize: 14, color: Colors.black54, fontWeight: FontWeight.normal),
        ),
        emptyWidget == null
            ? Text(
                "",
                textAlign: TextAlign.center,
                style: Theme.of(context).textTheme.subtitle2?.copyWith(fontSize: 14),
              )
            : InkWell(
                child: emptyWidget,
                onTap: () {
                  onClickEmptyWidgetCallBack?.call();
                },
              ),
        Gaps.vGap50,
      ],
    );
  }
}

enum StateType {
  /// 无网络
  network,

  /// 加载中
  loading,

  /// 空
  empty
}

extension StateTypeExtension on StateType {
  String get img => <String>['state/network', '', 'state/icon_empty'][index];

  String get hintText => <String>["网络异常", '', "暂无数据"][index];
}
