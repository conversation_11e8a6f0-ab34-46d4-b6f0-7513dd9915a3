import 'package:flutter/cupertino.dart';
import 'package:get/get.dart';

/// 输入控制器的管理器
class TextEditingControllerManager {
  final Map<String, TextEditingController> _controllers = {};

  TextEditingController getController(String key, RxString observable) {
    if (!_controllers.containsKey(key)) {
      final controller = TextEditingController(text: observable.value);
      ever(observable, (newValue) {
        if (_controllers.containsKey(key)) {
          _controllers[key]!.text = newValue;
        }
      });
      _controllers[key] = controller;
    }
    return _controllers[key]!;
  }

  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    _controllers.clear();
  }
}
