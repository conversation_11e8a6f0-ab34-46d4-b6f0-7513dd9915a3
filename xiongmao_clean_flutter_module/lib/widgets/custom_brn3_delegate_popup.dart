import 'package:bruno/bruno.dart';


class Brn3RowCustomDelegate implements BrnMultiDataPickerDelegate {

  List<String> days;
  List<String> hours;
  List<String> minutes;

  int firstSelectedIndex = 0;
  int secondSelectedIndex = 0;
  int thirdSelectedIndex = 0;

  Brn3RowCustomDelegate({
    required this.days,
    required this.hours,
    required this.minutes,
    this.firstSelectedIndex = 0,
    this.secondSelectedIndex = 0,
    this.thirdSelectedIndex = 0,
  });

  @override
  int numberOfComponent() {
    return 3;
  }

  @override
  int numberOfRowsInComponent(int component) {
    switch (component) {
      case 0:
        return days.length;
      case 1:
        return hours.length;
      case 2:
        return minutes.length;
      default:
        return 0;
    }
  }

  @override
  String titleForRowInComponent(int component, int index) {
    switch (component) {
      case 0:
        return days[index];
      case 1:
        return hours[index];
      case 2:
        return minutes[index];
      default:
        return '';
    }
  }

  @override
  double? rowHeightForComponent(int component) {
    return null;
  }

  @override
  void selectRowInComponent(int component, int row) {
    switch (component) {
      case 0:
        firstSelectedIndex = row;
        break;
      case 1:
        secondSelectedIndex = row;
        break;
      case 2:
        thirdSelectedIndex = row;
        break;
    }
  }

  @override
  int initSelectedRowForComponent(int component) {
    switch (component) {
      case 0:
        return firstSelectedIndex;
      case 1:
        return secondSelectedIndex;
      case 2:
        return thirdSelectedIndex;
      default:
        return 0;
    }
  }

  List<int> get selectedIndex => [firstSelectedIndex, secondSelectedIndex, thirdSelectedIndex];

  List<String> get selectedValues => [
    days[firstSelectedIndex],
    hours[secondSelectedIndex],
    minutes[thirdSelectedIndex],
  ];
}