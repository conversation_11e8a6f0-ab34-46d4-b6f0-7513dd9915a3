import 'package:flutter/material.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/res/gaps.dart';
import 'package:xiongmao_clean_flutter_module/util/common_utils.dart';

class CustomInputDialog {
  final BuildContext context;
  final String title;
  final String hintText;
  final String inputText;
  final Function(String) onConfirmed;
  final TextEditingController controller = TextEditingController();

  CustomInputDialog({required this.context, required this.title, required this.onConfirmed, this.hintText = '请输入正文内容', this.inputText = ''});

  void show() {
    controller.text = inputText;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return Dialog(
          child: Container(
            color: Colors.white,
            padding: const EdgeInsets.all(0.0),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Gaps.vGap20,
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 20),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                  ),
                  child: TextField(
                    controller: controller,
                    autofocus: true,
                    decoration: InputDecoration(
                      enabledBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey, // 未聚焦状态下的边框颜色
                          width: 1.0, // 边框宽度
                        ),
                      ),
                      focusedBorder: const OutlineInputBorder(
                        borderSide: BorderSide(
                          color: Colors.grey, // 聚焦状态下的边框颜色
                          width: 1.0, // 边框宽度
                        ),
                      ),
                      hintText: hintText,
                      contentPadding: const EdgeInsets.symmetric(horizontal: 10.0),
                    ),
                  ),
                ),
                const SizedBox(height: 20),
                Gaps.line,
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                        child: CommonUtils.getSimpleText('取消', 16, Colours.base_primary_text_title),
                      ),
                    ),
                    Gaps.vLine,
                    Expanded(
                      child: TextButton(
                        onPressed: () {
                          final input = controller.text;
                          onConfirmed(input);
                          Navigator.of(context).pop();
                        },
                        child: CommonUtils.getSimpleText('确定', 16, Colours.base_primary),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}
