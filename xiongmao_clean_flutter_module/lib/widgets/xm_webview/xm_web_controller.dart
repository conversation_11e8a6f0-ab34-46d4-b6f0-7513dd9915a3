
import 'package:webview_flutter/webview_flutter.dart';

class XmWebController {

  WebViewController realController;

  XmWebController(this.realController);

  Future<void> loadRequest(Uri uri) {
    return realController.loadRequest(uri);
  }

  Future<String?> currentUrl() {
    return realController.currentUrl();
  }

  /// Checks whether there's a back history item.
  Future<bool> canGoBack() {
    return realController.canGoBack();
  }

  /// Checks whether there's a forward history item.
  Future<bool> canGoForward() {
    return realController.canGoForward();
  }

  /// Goes back in the history of this WebView.
  ///
  /// If there is no back history item this is a no-op.
  Future<void> goBack() {
    return realController.goBack();
  }

  /// Goes forward in the history of this WebView.
  ///
  /// If there is no forward history item this is a no-op.
  Future<void> goForward() {
    return realController.goForward();
  }

  /// Reloads the current URL.
  Future<void> reload() {
    return realController.reload();
  }
}