

/// A decision on how to handle a navigation request.
enum XmNavigationDecision {
  /// Prevent the navigation from taking place.
  prevent,

  /// Allow the navigation to take place.
  navigate,
}


/// Defines the parameters of the pending navigation callback.
class XmNavigationRequest {
  /// Creates a [NavigationRequest].
  const XmNavigationRequest({
    required this.url,
    required this.isMainFrame,
  });

  /// The URL of the pending navigation request.
  final String url;

  /// Indicates whether the request was made in the web site's main frame or a subframe.
  final bool isMainFrame;
}