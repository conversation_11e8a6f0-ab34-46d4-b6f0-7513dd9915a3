import 'package:bruno/bruno.dart';
import 'package:flustars/flustars.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_boost/flutter_boost.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_smart_dialog/flutter_smart_dialog.dart';
import 'package:get/get_navigation/src/root/get_material_app.dart';
import 'package:get/get_navigation/src/routes/transitions_type.dart';
import 'package:xiongmao_clean_flutter_module/net/http_config.dart';
import 'package:xiongmao_clean_flutter_module/res/colors.dart';
import 'package:xiongmao_clean_flutter_module/router/xm_router.dart';
import 'package:xiongmao_clean_flutter_module/util/brn_config_utils.dart';
import 'package:xiongmao_clean_flutter_module/util/device_utils.dart';

import 'net/http_config.dart';

void main() async {
  // 初始化 Flutter Boost 的路由
  CustomFlutterBoostBinding();

  await SpUtil.getInstance(); // 初始化 SpUtil
  await Device.initDeviceInfo(); // 初始化设备信息

  initRouters(); // 初始化路由

  // 框架全局配置
  BrnInitializer.register(allThemeConfig: BrnConfigUtils.defaultAllConfig);

  SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
    statusBarColor: Colors.white,
    statusBarBrightness: Brightness.light,
  ));

  // 捕获 Flutter 错误
  FlutterError.onError = (FlutterErrorDetails details) {
    // 处理错误，例如显示错误页面 发消息到原生 原生写数据
    BoostChannel.instance.sendEventToNative("native_CommonEvent", {
      "method": "Flutter_Error",
      "error_msg": details.exceptionAsString(),
    });

    ///如果是正是环境，就不显示了
    if (httpConfig.serverType != SERVER_TYPE_RELEASE) {
      // runApp(MyErrorApp(details));
    }
  };

  // 直接Run
  runApp(ScreenUtilInit(
    designSize: const Size(375, 667),
    builder: (context, child) {
      return GetMaterialApp(
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
          BrnLocalizationDelegate(), // 添加 BrnLocalizationDelegate
        ],
        supportedLocales: const [
          Locale('en', ''),
          Locale('zh', 'CN'),
        ],
        defaultTransition: Transition.noTransition,
        debugShowCheckedModeBanner: false,
        title: '熊猫清洁云',
        theme: ThemeData(
          primarySwatch: Colors.blue,
          colorScheme: ColorScheme.fromSwatch().copyWith(
            background: Colours.base_primary_bg_page,
          ),
        ),
        home: const MyApp(),
        builder: FlutterSmartDialog.init(),
      );
    },
  ));
}

/// 创建一个自定义的 Binding
class CustomFlutterBoostBinding extends WidgetsFlutterBinding with BoostFlutterBinding {}

class MyApp extends StatefulWidget {
  const MyApp();

  @override
  State<StatefulWidget> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  @override
  void initState() {
    super.initState();
    print('flutter initState was called');
  }

  @override
  Widget build(BuildContext context) {
    return FlutterBoostApp(routeFactory, appBuilder: appBuilder);
  }
}

class MyErrorApp extends StatelessWidget {
  final FlutterErrorDetails details;

  MyErrorApp(this.details);

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      home: Scaffold(
        appBar: AppBar(title: Text("发生错误")),
        body: Center(
          child: Text(
            '发生了一个错误:\n${details.exceptionAsString()}',
            textAlign: TextAlign.center,
          ),
        ),
      ),
    );
  }
}
