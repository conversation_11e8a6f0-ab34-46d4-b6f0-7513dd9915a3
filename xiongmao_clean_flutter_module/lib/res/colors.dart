import 'package:flutter/material.dart';

class Colours {
  static const Color bg_color = Color(0xfff1f1f1);
  static const Color dark_bg_color = Color(0xFF18191A);

  static const Color material_bg = Color(0xFFFFFFFF);
  static const Color dark_material_bg = Color(0xFF303233);

  static const Color text = Color(0xFF333333);
  static const Color dark_text = Color(0xFFB8B8B8);

  static const Color text_gray = Color(0xFF999999);
  static const Color dark_text_gray = Color(0xFF666666);

  static const Color text_gray_c = Color(0xFFcccccc);
  static const Color dark_button_text = Color(0xFFF2F2F2);

  static const Color bg_gray = Color(0xFFF6F6F6);
  static const Color dark_bg_gray = Color(0xFF1F1F1F);

  static const Color line = Color(0xFFEEEEEE);
  static const Color dark_line = Color(0xFF3A3C3D);

  static const Color red = Color(0xFFFF4759);
  static const Color dark_red = Color(0xFFE03E4E);
  static const Color pink = Color(0xFFFE6D9A);

  static const Color text_disabled = Color(0xFFD4E2FA);

  static const Color button_disabled = Color(0xFF96BBFA);

  static const Color unselected_item_color = Color(0xffbfbfbf);
  static const Color dark_unselected_item_color = Color(0xFF4D4D4D);

  static const Color bg_gray_ = Color(0xFFFAFAFA);
  static const Color dark_bg_gray_ = Color(0xFF242526);

  static const Color gradient_blue = Color(0xFF5793FA);
  static const Color shadow_blue = Color(0x805793FA);
  static const Color shadow_blue1 = Color(0x99CCDFFC);
  static const Color shadow_blue2 = Color(0x994C8DEA);
  static const Color text_black1 = Color(0x99A4A9BB);
  static const Color light_bule = Color(0x20CCE4FA);
  static const Color orange = Color(0xFFFF8547);

  static const Color drawer_bg = Color(0xFFF0F0F0);
  static const Color drawer_bg2 = Color(0xFFF2F3F6);
  static const Color drawer_bg3 = Color(0xFFE1F3EE);

  static const Color yiji = Color(0xFFFF4040);
  static const Color erji = Color(0xFFFF801A);
  static const Color sanji = Color(0xFFFFB847);

  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color transparent = Color(0x00000000);
  static const Color half_transparent = Color(0x55000000);
  static const Color half_transparent_33 = Color(0x33000000);
  static const Color half_transparent_44 = Color(0x10000000);

  static const Color trans_base_primary = Color(0x559F1600);
  static const Color base_primary_purple = Color(0xFF800080);
  static const Color base_primary_brown = Color(0xFFF4A460);
  static const Color base_primary = Color(0xFF00B882);
  static const Color base_primary_blue = Color(0xFF1890FF);
  static const Color base_primary_green = Color(0xFF00B882);
  static const Color base_primary_warning = Color(0xFFFF4040);
  static const Color base_primary_select = Color(0xFFE6F8F3);
  static const Color base_primary_select1 = Color(0xFFCCF0E6);
  static const Color base_primary_select2 = Color(0xFFF5F6F9);

  static const Color base_primary_warning_trans = Color(0x11FF4040);
  static const Color base_primary_green_trans = Color(0x1109BE89);
  static const Color base_primary_trans = Color(0x2209BE89);
  static const Color base_primary_jv = Color(0xFFFE6B03);
  static const Color base_primary_press = Color(0xFF008E64);
  static const Color base_primary_disable = Color(0x601890FF);
  static const Color base_primary_gray_disable = Color(0xFFD7D8DB);
  static const Color base_primary_un_select = Color(0xFFF7F8FA);
  static const Color base_primary_notice_bg = Color(0xFFFFF7D4);
  static const Color base_primary_warning_bg = Color(0xFFFFF1F0);
  static const Color base_primary_share_bg = Color(0xFFF7F8FA);

  static const Color base_primary_text_title = Color(0xFF1A1A1A);
  static const Color base_primary_text_body = Color(0xFF5E5E66);
  static const Color base_primary_text_caption = Color(0xFF8D8E99);
  static const Color base_primary_text_hint = Color(0xFFB0B1B8);
  static const Color base_primary_line_b = Color(0xFFE3E3E6);
  static const Color base_primary_bg_page = Color(0xFFF7F7F7);
  static const Color table_title_color = Color(0xFFE7F3FF);
  static const Color tag_pink_color = Color(0xFFFE7096);
}
