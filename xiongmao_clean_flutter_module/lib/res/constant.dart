import 'package:flutter/foundation.dart';

const SEARCH_MODE = 0;
const CHOOSE_MODE = 1;
const LIST_MODE = 2;

class Constant {
  /// App运行在Release环境时，inProduction为true；当App运行在Debug和Profile环境时，inProduction为false
  static bool inProduction = false;
  static const String dashBoardData = "dashBoardData";
  static const String companyCode = "eeplus";
  static const String fatch = "fatch";
  static const String notificationReaded = "notificationReaded";
  static const String keyEnableProxy = "keyEnableProxy";
  static const String buyContractAmount = "buy_contract_amount";

  static bool isDriverTest = false;
  static bool isUnitTest = false;

  static String textEmployeeMobile = "80881111";
  static String textWorkerMobile = "80882222";

  static String pageSize = "20";

  static const String result = 'result';
  static const String data = 'data';
  static const String message = 'message';
  static const String msg = 'msg';
  static const String code = 'code';
  static const String ts = 'ts';
  static const String rid = 'rid';
  static const String p = 'p';

  static const String showGuide = 'showGuide';
  static const String phone = 'phone';
  static const String areaCode = 'areaCode';
  static const String refreshToken = 'refreshToken';
  static const String filterWorkerParams = 'filterWorkerParams';
  static const String filterWorkerTitleList = 'filterWorkerTitleList';

  static const String theme = 'AppTheme';
  static const String locale = 'viewRiselocale';
  static const String userInfo = 'UserInfo';
  static const String accessToken = "accessToken";
  static const String loginData = "loginData";
  static const String isEmployer = "isEmployer";
  static const String employerMobile = "employerMobile";
  static const String userId = "userId";
  static const String loginAccount = "loginMobile";
  static const String loginName = "loginName";
  static const String splashImage = "splashImage";
  static const String system = "system";
  static const String jsSpaceName = "WebViewJavascriptBridge";
  static const String jsSpaceCallback = "CallBack";
  static const String versionName = "versionName";
  static const String appleId = '**********'; // If this value is null, its packagename will be considered
  static const String playStoreId = 'com.example.app'; // If this value is null, its packagename will be considered
  static const String country = 'br'; //
  static const String options = "matadata";
  static const String deviceName = "device_name";
  static const String hkCitys = "hk_citys";
  static const String homeData = "homeData";
  static const String homeInitEntity = "homeInitEntity";
  static const String agencyAll = "agencyAll";

  static const int loggerLineLength = 120;
  static const int loggerErrorMethodCount = 8;
  static const int loggerMethodCount = 2;

  static const String settingData = "SettingData";
  static const String getAppVersionData = "getAppVersionData";

  static const String netEnvironment = "netEnvironment";
  static const String sdkAppID = "sdkAppID";
  static const String userSig = "userSig";
  static const String isForeground = "isForeground";

  ///元数据
  static const String META_DATA = "META_DATA";
  ///城市元数据
  static const String CITY_META_DATA = "CITY_META_DATA";

  ///民族的列表
  static const String NATION_DATA = "NATION_DATA";

  ///是否不允许补入今日之前的入职信息 1是 2否
  static const String IS_ENTRY_BEFORE_TODAY = "IS_ENTRY_BEFORE_TODAY";

  /// 办理入职时是否需要入职的员工签字确认 1是 2否
  static const String IS_ENTRY_SIGN = "IS_ENTRY_SIGN";

  ///是否开启完整模式
  static const String HEAD_OFFICE_MODE = "HEAD_OFFICE_MODE";
}
