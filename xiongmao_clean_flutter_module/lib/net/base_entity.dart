// import '../generated/json/base/json_convert_content.dart';
import '../generated/json/base/json_convert_content.dart';
import '../res/constant.dart';
import '../util/log_utils.dart';

class BaseEntity<T> {
  BaseEntity(this.code, this.message, this.data);

  BaseEntity.fromJson(Map<String, dynamic> json) {
    if (json[Constant.code] is int) {
      code = json[Constant.code];
    } else {
      code = int.parse(json[Constant.code]);
    }
    if (json[Constant.message] != null) {
      message = json[Constant.message] as String;
    } else if (json[Constant.msg] != null) {
      message = json[Constant.msg] as String;
    }
    if (json.containsKey(Constant.data) && json[Constant.data] != null) {
      data = _generateOBJ<T>(json[Constant.data] as Object?);
      // if(json[Constant.data][Constant.data] is T) {
      //   data = json[Constant.data][Constant.data];
      // } else {
      //   data = _generateOBJ<T>(json[Constant.data][Constant.data] as Object?);
      // }
    }
  }

  int? code;
  String? message;
  String? msg;
  T? data;

  T? _generateOBJ<O>(Object? json) {
    if (json == null) {
      return null;
    }
    MyLog.e("base 解析的工具类，解析对象是--> " + T.toString());
    if (T.toString() == 'String') {
      return json.toString() as T;
    } else if (T.toString() == 'Map<dynamic, dynamic>' || T.toString() == "bool") {
      return json as T;
    } else {
      /// List类型数据由fromJsonAsT判断处理
      return JsonConvert.fromJsonAsT<T>(json);
      return null;
    }
  }
}
