import 'dart:io';

import 'package:dio/adapter.dart';
import 'package:dio/dio.dart';
import 'package:flustars/flustars.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:sp_util/sp_util.dart';
import '../res/constant.dart';
import 'intercept.dart';

HttpConfig httpConfig = HttpConfig._();

const String SERVER_TYPE_RELEASE = "release";
const String SERVER_TYPE_DEV = "dev";
const String SERVER_TYPE_TEST = "test";

class HttpConfig {
  //默认正式环境
  static const String BASE_URL_BLUESTAR = "https://pccappapi.jiazhengye.cn";

  static const String BASE_URL_PCCM = "https://pccm.jiazhengye.cn";
  static const String BASE_URL_M = "https://m2.jiazhengye.cn";
  static const String BASE_URL_M2 = "https://m2.jiazhengye.cn";

  //测试环境
  static const String TEST_BASE_URL = "https://pccappapi-test.jiazhengye.cn";
  static const String TEST_BASE_URL_M = "https://pccm-test.jiazhengye.cn";
  static const String TEST_BASE_URL_M2 = "https://m2-test.jiazhengye.cn";

  //开发环境
  static const String DEV_BASE_URL = "https://pccappapi-dev.jiazhengye.cn";
  static const String DEV_BASE_URL_M = "https://pccm-dev.jiazhengye.cn";
  static const String DEV_BASE_URL_M2 = "https://m2-dev.jiazhengye.cn";

  static const int _TIME_OUT_DURATION = 15000;

  var _xmjztoken = "67f4e3a910ae0de65ec3a8aa71a5112b";
  var _xmjzversion = "1.8.0.8";
  var _project_uuid = "a3e0c3d95c96785a6b80f0a435c8a468"; //a3e0c3d95c96785a6b80f0a435c8a468 这是群英科技园的
  var _project_name = "群英科技园"; // 给个项目的名称
  var _xmjzdevice = "vivo v2057A"; //手机机型 xmjzdevice
  var _company_uuid = "054ca48ddf00b22b89d1b7e55dcacd3f"; //企业的uuid
  var _company_name = "熊猫清洁云公司"; //企业的uuid
  var role_id = "-1"; //角色的ID
  var _user_uuid = ""; //当前用户的uuid
  var _user_name = "演示账号"; //当前用户的名字

  static const ROLE_SUPER_MANGER_ID = "-1"; //超级管理员
  static const ROLE_MANGER_ID = "1"; //管理员
  static const ROLE_PROJECT_OWNER_ID = "2"; //项目负责人
  static const ROLE_HR_ID = "3"; //人事
  static const ROLE_LEADER_ID = "4"; //领班
  static const ROLE_CLEAN_ID = "5"; //保洁
  static const ROLE_REGIONAL_MANAGER_ID = "6"; //大区经理

  ///设置代理
  var proxy = ''; //默认空的 如果有就走代理

  String get token => _xmjztoken;

  String get version => _xmjzversion;

  String get device => _xmjzdevice;

  String get company_uuid => _company_uuid;

  String get company_name => _company_name;

  String get project_uuid => _project_uuid;

  String get project_name => _project_name;

  String get user_uuid => _user_uuid;

  String get user_name => _user_name;

  set serverType(value) {
    _serverType = value;
  }

  /// 打包切换环境修改此参数即可，无需频繁修改服务器地址
  // var _serverType = SpUtil.getString(Constant.netEnvironment, defValue: SERVER_TYPE_RELEASE) ?? SERVER_TYPE_RELEASE;
  var _serverType = SpUtil.getString(Constant.netEnvironment, defValue: SERVER_TYPE_DEV) ?? SERVER_TYPE_DEV;

  String get serverType => _serverType;

  String get getServerType => _getWebDomain();

  String get getServerTypeM2 => _getWebDomainM2();

  Dio? _dio;
  Dio? _mDio;
  Dio? _m2Dio;

  HttpConfig._();

  final _options = BaseOptions(
    baseUrl: DEV_BASE_URL,
    headers: {
      "xmjzplatform": "Android",
    },
    connectTimeout: _TIME_OUT_DURATION,
    receiveTimeout: _TIME_OUT_DURATION,
    sendTimeout: _TIME_OUT_DURATION,
  );

  final _mOptions = BaseOptions(
    baseUrl: DEV_BASE_URL_M,
    contentType: "application/x-www-form-urlencoded",
    headers: {
      "xmjzplatform": "Android",
    },
    connectTimeout: _TIME_OUT_DURATION,
    receiveTimeout: _TIME_OUT_DURATION,
    sendTimeout: _TIME_OUT_DURATION,
  );

  final _m2Options = BaseOptions(
    baseUrl: DEV_BASE_URL_M2,
    contentType: "application/x-www-form-urlencoded",
    headers: {
      "xmjzplatform": "Android",
    },
    connectTimeout: _TIME_OUT_DURATION,
    receiveTimeout: _TIME_OUT_DURATION,
    sendTimeout: _TIME_OUT_DURATION,
  );

  ///*************************************************************************
  ///设置请求参数及环境参数
  void initHttpParams(Map<String, dynamic>? params) {
    print("跳转接受的参数 $params");
    Constant.inProduction = false;
    if (params == null || params.isEmpty || params["serverType"] == null) {
      return;
    }
    //这里赋值一下当前用户的user_uuid
    if (params["current_user_uuid"] != null) {
      _user_uuid = params["current_user_uuid"];
    }
    //这里赋值一下当前用户的user_name
    if (params["current_user_name"] != null) {
      _user_name = params["current_user_name"];
    }
    //这里赋值一下角色的ID
    if (params["role_id"] != null) {
      role_id = params["role_id"];
    }
    //这里赋值一下企业的 uuid
    if (params["company_uuid"] != null) {
      _company_uuid = params["company_uuid"];
    }

    //这里赋值一下企业的 名称
    if (params["company_name"] != null) {
      _company_name = params["company_name"];
    }
    //这里赋值一下项目的 uuid
    if (params["current_project_uuid"] != null) {
      _project_uuid = params["current_project_uuid"];
    }

    //这里赋值一下项目的名称
    if (params["current_project_name"] != null) {
      _project_name = params["current_project_name"];
    }

    //这里赋值一下token
    if (params["xmjztoken"] != null) {
      _xmjztoken = params["xmjztoken"];
    }

    //这里赋值一下version
    if (params["xmjzversion"] != null) {
      _xmjzversion = params["xmjzversion"];
    }

    //这里赋值一下机型
    if (params["xmjzdevice"] != null) {
      _xmjzdevice = params["xmjzdevice"];
    }

    if (params["is_entry_sign"] != null) {
      SpUtil.putBool(Constant.IS_ENTRY_SIGN, params["is_entry_sign"]);
    }
    if (params["is_entry_before_today"] != null) {
      SpUtil.putBool(Constant.IS_ENTRY_BEFORE_TODAY, params["is_entry_before_today"]);
    }
    if (params["head_office_mode"] != null) {
      SpUtil.putBool(Constant.HEAD_OFFICE_MODE, params["head_office_mode"]);
    }

    //判断是什么环境
    if (_serverType != params["serverType"]!) {
      _serverType = params["serverType"]!;
    }

    if (_serverType == SERVER_TYPE_RELEASE) {
      Constant.inProduction = true;
    } else {
      Constant.inProduction = false;
    }

    ///这里获取一下本地的proxy
    _loadStoredValue();
  }

  Future<void> _loadStoredValue() async {
    SharedPreferences prefs = await SharedPreferences.getInstance();
    proxy = prefs.getString('flutter_proxy') ?? '';
    print('进来获取proxy了 --- $proxy');
  }

  void setDioProxy(Dio proxyDio) {
    print('setDioProxy 来赛值了1 ---');
    if (_serverType != SERVER_TYPE_RELEASE && !TextUtil.isEmpty(proxy)) {
      print('setDioProxy 来赛值了2 ---');
      (proxyDio.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
        client.badCertificateCallback = (X509Certificate cert, String host, int port) => true; // 忽略SSL证书验证
        print('setDioProxy 来赛值了3 ---');

        client.findProxy = (uri) {
          return "PROXY $proxy"; // 你的代理地址和端口
        };
      };
    }
  }

  ///*************************************************************************
  /// 创建dio实例，并根据不同环境进行配置
  Dio get dio => getDio();

  Dio getDio({String contentType = "application/x-www-form-urlencoded"}) {
    if (_dio == null) {
      _dio = Dio(_options);
      _addIntercepter(_dio!);
    }
    // 根据环境设置代理
    setDioProxy(_dio!);
    _dio!.options.contentType = contentType;

    if (_serverType == SERVER_TYPE_RELEASE) {
      _dio!.options.baseUrl = BASE_URL_BLUESTAR;
    } else if (_serverType == SERVER_TYPE_TEST) {
      _dio!.options.baseUrl = TEST_BASE_URL;
    } else {
      _dio!.options.baseUrl = DEV_BASE_URL;
    }

    return _dio!;
  }

  Dio get mDio => _getMdio();

  Dio _getMdio() {
    if (_mDio == null) {
      _mDio = Dio(_mOptions);
      _addIntercepter(_mDio!);
    }
    // 根据环境设置代理
    setDioProxy(_mDio!);
    if (_serverType == SERVER_TYPE_RELEASE) {
      _mDio!.options.baseUrl = BASE_URL_M;
    } else if (_serverType == SERVER_TYPE_TEST) {
      _mDio!.options.baseUrl = TEST_BASE_URL_M;
    } else {
      _mDio!.options.baseUrl = DEV_BASE_URL_M;
    }
    return _mDio!;
  }

  Dio get m2Dio => _getM2dio();

  Dio _getM2dio() {
    if (_m2Dio == null) {
      _m2Dio = Dio(_m2Options);
      _addIntercepter(_m2Dio!);
    }
    // 根据环境设置代理
    setDioProxy(_m2Dio!);
    if (_serverType == SERVER_TYPE_RELEASE) {
      _m2Dio!.options.baseUrl = BASE_URL_M2;
    } else if (_serverType == SERVER_TYPE_TEST) {
      _m2Dio!.options.baseUrl = TEST_BASE_URL_M2;
    } else {
      _m2Dio!.options.baseUrl = DEV_BASE_URL_M2;
    }
    return _m2Dio!;
  }

  ///获取不同环境的 PCCM 站域名
  String _getWebDomain() {
    if (httpConfig.isReleaseServer()) {
      return BASE_URL_PCCM;
    } else if (httpConfig.isDevServer()) {
      return DEV_BASE_URL_M;
    } else {
      return TEST_BASE_URL_M;
    }
  }

  ///获取不同环境的 M2 站域名
  String _getWebDomainM2() {
    if (httpConfig.isReleaseServer()) {
      return BASE_URL_M;
    } else if (httpConfig.isDevServer()) {
      return DEV_BASE_URL_M2;
    } else {
      return TEST_BASE_URL_M2;
    }
  }

  ///*************************************************************************
  /// 请求拦截器，为请求添加统一参数
  _addIntercepter(Dio dio) {
    /// 签名拦截器
    dio.interceptors.add(AuthInterceptor());

    dio.interceptors.add(SignInterceptor());

    /// 打印Log(生产模式去除)
    // if (!Constant.inProduction) {
    dio.interceptors.add(LoggingInterceptor());
    // }
    /// 适配数据(根据自己的数据结构，可自行选择添加)
    dio.interceptors.add(AdapterInterceptor());
  }

  /// 判断服务器环境类型
  bool isReleaseServer() {
    return _serverType == SERVER_TYPE_RELEASE;
  }

  bool isTestServer() {
    return _serverType == SERVER_TYPE_TEST;
  }

  bool isDevServer() {
    return _serverType == SERVER_TYPE_DEV;
  }
}
