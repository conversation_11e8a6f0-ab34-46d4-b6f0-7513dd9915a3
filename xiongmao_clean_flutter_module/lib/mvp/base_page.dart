import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../util/loading_util.dart';
import '../util/log_utils.dart';
import 'base_presenter.dart';
import 'mvps.dart';

mixin BasePageMixin<T extends StatefulWidget, P extends BasePresenter>
    on State<T> implements IMvpView {
  P? presenter;

  P createPresenter();

  @override
  BuildContext getContext() {
    return context;
  }

  @override
  void closeProgress() {
    Loading.dismiss();
  }

  @override
  void showProgress() {
    Loading.show(msg: '加载中');
  }

  @override
  void showMsgProgress(String msg) {
    Loading.show(msg: msg);
  }

  @override
  void didChangeDependencies() {
    presenter?.didChangeDependencies();
    MyLog.d('$T ==> didChangeDependencies');
    super.didChangeDependencies();
  }

  @override
  void dispose() {
    presenter?.dispose();
    MyLog.d('$T ==> dispose');
    super.dispose();
  }

  @override
  void deactivate() {
    presenter?.deactivate();
    MyLog.d('$T ==> deactivate');
    super.deactivate();
  }

  @override
  void didUpdateWidget(T oldWidget) {
    presenter?.didUpdateWidgets<T>(oldWidget);
    MyLog.d('$T ==> didUpdateWidgets');
    super.didUpdateWidget(oldWidget);
  }

  @override
  void initState() {
    MyLog.d('$T ==> initState');
    presenter = createPresenter();
    presenter?.view = this;
    presenter?.initState();
    super.initState();
  }
}
