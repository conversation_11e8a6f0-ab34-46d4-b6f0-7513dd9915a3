/*!

pica
https://github.com/nodeca/pica

*/
!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{("undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this).pica=t()}}((function(){return function t(e,A,i){function r(a,o){if(!A[a]){if(!e[a]){var s="function"==typeof require&&require;if(!o&&s)return s(a,!0);if(n)return n(a,!0);var h=new Error("Cannot find module '"+a+"'");throw h.code="MODULE_NOT_FOUND",h}var u=A[a]={exports:{}};e[a][0].call(u.exports,(function(t){return r(e[a][1][t]||t)}),u,u.exports,t,e,A,i)}return A[a].exports}for(var n="function"==typeof require&&require,a=0;a<i.length;a++)r(i[a]);return r}({1:[function(t,e,A){"use strict";var i=t("multimath"),r=t("./mm_unsharp_mask"),n=t("./mm_resize");function a(t){var e=t||[],A={js:e.indexOf("js")>=0,wasm:e.indexOf("wasm")>=0};i.call(this,A),this.features={js:A.js,wasm:A.wasm&&this.has_wasm()},this.use(r),this.use(n)}a.prototype=Object.create(i.prototype),a.prototype.constructor=a,a.prototype.resizeAndUnsharp=function(t,e){var A=this.resize(t,e);return t.unsharpAmount&&this.unsharp_mask(A,t.toWidth,t.toHeight,t.unsharpAmount,t.unsharpRadius,t.unsharpThreshold),A},e.exports=a},{"./mm_resize":4,"./mm_unsharp_mask":9,multimath:19}],2:[function(t,e,A){"use strict";function i(t){return t<0?0:t>255?255:t}function r(t){return t>=0?t:0}e.exports={convolveHor:function(t,e,A,i,n,a){var o,s,h,u,c,f,g,l,I,d,B,m=0,Q=0;for(I=0;I<i;I++){for(c=0,d=0;d<n;d++){for(f=a[c++],g=a[c++],l=m+4*f|0,o=s=h=u=0;g>0;g--)u=u+(B=a[c++])*t[l+3]|0,h=h+B*t[l+2]|0,s=s+B*t[l+1]|0,o=o+B*t[l]|0,l=l+4|0;e[Q+3]=r(u>>7),e[Q+2]=r(h>>7),e[Q+1]=r(s>>7),e[Q]=r(o>>7),Q=Q+4*i|0}Q=4*(I+1)|0,m=(I+1)*A*4|0}},convolveVert:function(t,e,A,r,n,a){var o,s,h,u,c,f,g,l,I,d,B,m=0,Q=0;for(I=0;I<r;I++){for(c=0,d=0;d<n;d++){for(f=a[c++],g=a[c++],l=m+4*f|0,o=s=h=u=0;g>0;g--)u=u+(B=a[c++])*t[l+3]|0,h=h+B*t[l+2]|0,s=s+B*t[l+1]|0,o=o+B*t[l]|0,l=l+4|0;o>>=7,s>>=7,h>>=7,u>>=7,e[Q+3]=i(u+8192>>14),e[Q+2]=i(h+8192>>14),e[Q+1]=i(s+8192>>14),e[Q]=i(o+8192>>14),Q=Q+4*r|0}Q=4*(I+1)|0,m=(I+1)*A*4|0}},convolveHorWithPre:function(t,e,A,i,n,a){var o,s,h,u,c,f,g,l,I,d,B,m,Q=0,p=0;for(d=0;d<i;d++){for(f=0,B=0;B<n;B++){for(g=a[f++],l=a[f++],I=Q+4*g|0,o=s=h=u=0;l>0;l--)u=u+(m=a[f++])*(c=t[I+3])|0,h=h+m*t[I+2]*c|0,s=s+m*t[I+1]*c|0,o=o+m*t[I]*c|0,I=I+4|0;h=h/255|0,s=s/255|0,o=o/255|0,e[p+3]=r(u>>7),e[p+2]=r(h>>7),e[p+1]=r(s>>7),e[p]=r(o>>7),p=p+4*i|0}p=4*(d+1)|0,Q=(d+1)*A*4|0}},convolveVertWithPre:function(t,e,A,r,n,a){var o,s,h,u,c,f,g,l,I,d,B,m=0,Q=0;for(I=0;I<r;I++){for(c=0,d=0;d<n;d++){for(f=a[c++],g=a[c++],l=m+4*f|0,o=s=h=u=0;g>0;g--)u=u+(B=a[c++])*t[l+3]|0,h=h+B*t[l+2]|0,s=s+B*t[l+1]|0,o=o+B*t[l]|0,l=l+4|0;o>>=7,s>>=7,h>>=7,(u=i((u>>=7)+8192>>14))>0&&(o=255*o/u|0,s=255*s/u|0,h=255*h/u|0),e[Q+3]=u,e[Q+2]=i(h+8192>>14),e[Q+1]=i(s+8192>>14),e[Q]=i(o+8192>>14),Q=Q+4*r|0}Q=4*(I+1)|0,m=(I+1)*A*4|0}}}},{}],3:[function(t,e,A){"use strict";e.exports="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"},{}],4:[function(t,e,A){"use strict";e.exports={name:"resize",fn:t("./resize"),wasm_fn:t("./resize_wasm"),wasm_src:t("./convolve_wasm_base64")}},{"./convolve_wasm_base64":3,"./resize":5,"./resize_wasm":8}],5:[function(t,e,A){"use strict";var i=t("./resize_filter_gen"),r=t("./convolve"),n=r.convolveHor,a=r.convolveVert,o=r.convolveHorWithPre,s=r.convolveVertWithPre;e.exports=function(t){var e=t.src,A=t.width,r=t.height,h=t.toWidth,u=t.toHeight,c=t.scaleX||t.toWidth/t.width,f=t.scaleY||t.toHeight/t.height,g=t.offsetX||0,l=t.offsetY||0,I=t.dest||new Uint8Array(h*u*4),d=void 0===t.filter?"mks2013":t.filter,B=i(d,A,h,c,g),m=i(d,r,u,f,l),Q=new Uint16Array(h*r*4);return!function(t,e,A){for(var i=3,r=e*A*4|0;i<r;){if(255!==t[i])return!0;i=i+4|0}return!1}(e,A,r)?(n(e,Q,A,r,h,B),a(Q,I,r,h,u,m),function(t,e,A){for(var i=3,r=e*A*4|0;i<r;)t[i]=255,i=i+4|0}(I,h,u)):(o(e,Q,A,r,h,B),s(Q,I,r,h,u,m)),I}},{"./convolve":2,"./resize_filter_gen":6}],6:[function(t,e,A){"use strict";var i=t("./resize_filter_info");function r(t){return Math.round(16383*t)}e.exports=function(t,e,A,n,a){var o,s,h,u,c,f,g,l,I,d,B,m,Q,p,E,w,C,_=i.filter[t].fn,b=1/n,y=Math.min(1,n),v=i.filter[t].win/y,D=Math.floor(2*(v+1)),M=new Int16Array((D+2)*A),x=0,k=!M.subarray||!M.set;for(o=0;o<A;o++){for(s=(o+.5)*b+a,h=Math.max(0,Math.floor(s-v)),c=(u=Math.min(e-1,Math.ceil(s+v)))-h+1,f=new Float32Array(c),g=new Int16Array(c),l=0,I=h,d=0;I<=u;I++,d++)l+=B=_((I+.5-s)*y),f[d]=B;for(m=0,d=0;d<f.length;d++)m+=Q=f[d]/l,g[d]=r(Q);for(g[A>>1]+=r(1-m),p=0;p<g.length&&0===g[p];)p++;if(p<g.length){for(E=g.length-1;E>0&&0===g[E];)E--;if(w=h+p,C=E-p+1,M[x++]=w,M[x++]=C,k)for(d=p;d<=E;d++)M[x++]=g[d];else M.set(g.subarray(p,E+1),x),x+=C}else M[x++]=0,M[x++]=0}return M}},{"./resize_filter_info":7}],7:[function(t,e,A){"use strict";var i={box:{win:.5,fn:function(t){return t<0&&(t=-t),t<.5?1:0}},hamming:{win:1,fn:function(t){if(t<0&&(t=-t),t>=1)return 0;if(t<1.1920929e-7)return 1;var e=t*Math.PI;return Math.sin(e)/e*(.54+.46*Math.cos(e/1))}},lanczos2:{win:2,fn:function(t){if(t<0&&(t=-t),t>=2)return 0;if(t<1.1920929e-7)return 1;var e=t*Math.PI;return Math.sin(e)/e*Math.sin(e/2)/(e/2)}},lanczos3:{win:3,fn:function(t){if(t<0&&(t=-t),t>=3)return 0;if(t<1.1920929e-7)return 1;var e=t*Math.PI;return Math.sin(e)/e*Math.sin(e/3)/(e/3)}},mks2013:{win:2.5,fn:function(t){return t<0&&(t=-t),t>=2.5?0:t>=1.5?-.125*(t-2.5)*(t-2.5):t>=.5?.25*(4*t*t-11*t+7):1.0625-1.75*t*t}}};e.exports={filter:i,f2q:{box:0,hamming:1,lanczos2:2,lanczos3:3},q2f:["box","hamming","lanczos2","lanczos3"]}},{}],8:[function(t,e,A){"use strict";var i=t("./resize_filter_gen");var r=!0;try{r=1===new Uint32Array(new Uint8Array([1,0,0,0]).buffer)[0]}catch(t){}function n(t,e,A){if(r)e.set(function(t){return new Uint8Array(t.buffer,0,t.byteLength)}(t),A);else for(var i=A,n=0;n<t.length;n++){var a=t[n];e[i++]=255&a,e[i++]=a>>8&255}}e.exports=function(t){var e=t.src,A=t.width,r=t.height,a=t.toWidth,o=t.toHeight,s=t.scaleX||t.toWidth/t.width,h=t.scaleY||t.toHeight/t.height,u=t.offsetX||0,c=t.offsetY||0,f=t.dest||new Uint8Array(a*o*4),g=void 0===t.filter?"mks2013":t.filter,l=i(g,A,a,s,u),I=i(g,r,o,h,c),d=Math.max(e.byteLength,f.byteLength),B=this.__align(0+d),m=r*a*4*2,Q=this.__align(B+m),p=this.__align(Q+l.byteLength),E=p+I.byteLength,w=this.__instance("resize",E),C=new Uint8Array(this.__memory.buffer),_=new Uint32Array(this.__memory.buffer),b=new Uint32Array(e.buffer);_.set(b),n(l,C,Q),n(I,C,p);var y=w.exports.convolveHV||w.exports._convolveHV;return!function(t,e,A){for(var i=3,r=e*A*4|0;i<r;){if(255!==t[i])return!0;i=i+4|0}return!1}(e,A,r)?(y(Q,p,B,A,r,a,o,0),function(t,e,A){for(var i=3,r=e*A*4|0;i<r;)t[i]=255,i=i+4|0}(f,a,o)):y(Q,p,B,A,r,a,o,1),new Uint32Array(f.buffer).set(new Uint32Array(this.__memory.buffer,0,o*a)),f}},{"./resize_filter_gen":6}],9:[function(t,e,A){"use strict";e.exports={name:"unsharp_mask",fn:t("./unsharp_mask"),wasm_fn:t("./unsharp_mask_wasm"),wasm_src:t("./unsharp_mask_wasm_base64")}},{"./unsharp_mask":10,"./unsharp_mask_wasm":11,"./unsharp_mask_wasm_base64":12}],10:[function(t,e,A){"use strict";var i=t("glur/mono16");e.exports=function(t,e,A,r,n,a){var o,s,h,u,c;if(!(0===r||n<.5)){n>2&&(n=2);var f=function(t,e,A){for(var i,r,n,a,o=e*A,s=new Uint16Array(o),h=0;h<o;h++)i=t[4*h],r=t[4*h+1],n=t[4*h+2],a=i>=r&&i>=n?i:r>=n&&r>=i?r:n,s[h]=a<<8;return s}(t,e,A),g=new Uint16Array(f);i(g,e,A,n);for(var l=r/100*4096+.5|0,I=a<<8,d=e*A,B=0;B<d;B++)u=(o=f[B])-g[B],Math.abs(u)>=I&&(h=((s=(s=(s=o+(l*u+2048>>12))>65280?65280:s)<0?0:s)<<12)/(o=0!==o?o:1)|0,t[c=4*B]=t[c]*h+2048>>12,t[c+1]=t[c+1]*h+2048>>12,t[c+2]=t[c+2]*h+2048>>12)}}},{"glur/mono16":18}],11:[function(t,e,A){"use strict";e.exports=function(t,e,A,i,r,n){if(!(0===i||r<.5)){r>2&&(r=2);var a=e*A,o=4*a,s=2*a,h=2*a,u=4*Math.max(e,A),c=o,f=c+s,g=f+h,l=g+h,I=l+u,d=this.__instance("unsharp_mask",o+s+2*h+u+32,{exp:Math.exp}),B=new Uint32Array(t.buffer);new Uint32Array(this.__memory.buffer).set(B);var m=d.exports.hsv_v16||d.exports._hsv_v16;m(0,c,e,A),(m=d.exports.blurMono16||d.exports._blurMono16)(c,f,g,l,I,e,A,r),(m=d.exports.unsharp||d.exports._unsharp)(0,0,c,f,e,A,i,n),B.set(new Uint32Array(this.__memory.buffer,0,a))}}},{}],12:[function(t,e,A){"use strict";e.exports="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"},{}],13:[function(t,e,A){"use strict";function i(t,e){this.create=t,this.available=[],this.acquired={},this.lastId=1,this.timeoutId=0,this.idle=e||2e3}i.prototype.acquire=function(){var t,e=this;return 0!==this.available.length?t=this.available.pop():((t=this.create()).id=this.lastId++,t.release=function(){return e.release(t)}),this.acquired[t.id]=t,t},i.prototype.release=function(t){var e=this;delete this.acquired[t.id],t.lastUsed=Date.now(),this.available.push(t),0===this.timeoutId&&(this.timeoutId=setTimeout((function(){return e.gc()}),100))},i.prototype.gc=function(){var t=this,e=Date.now();this.available=this.available.filter((function(A){return!(e-A.lastUsed>t.idle)||(A.destroy(),!1)})),0!==this.available.length?this.timeoutId=setTimeout((function(){return t.gc()}),100):this.timeoutId=0},e.exports=i},{}],14:[function(t,e,A){"use strict";e.exports=function(t,e,A,i,r,n){var a=A/t,o=i/e,s=(2*n+2+1)/r;if(s>.5)return[[A,i]];var h=Math.ceil(Math.log(Math.min(a,o))/Math.log(s));if(h<=1)return[[A,i]];for(var u=[],c=0;c<h;c++){var f=Math.round(Math.pow(Math.pow(t,h-c-1)*Math.pow(A,c+1),1/h)),g=Math.round(Math.pow(Math.pow(e,h-c-1)*Math.pow(i,c+1),1/h));u.push([f,g])}return u}},{}],15:[function(t,e,A){"use strict";var i=1e-5;function r(t){var e=Math.round(t);return Math.abs(t-e)<i?e:Math.floor(t)}function n(t){var e=Math.round(t);return Math.abs(t-e)<i?e:Math.ceil(t)}e.exports=function(t){var e,A,i,a,o,s,h=t.toWidth/t.width,u=t.toHeight/t.height,c=r(t.srcTileSize*h)-2*t.destTileBorder,f=r(t.srcTileSize*u)-2*t.destTileBorder;if(c<1||f<1)throw new Error("Internal error in pica: target tile width/height is too small.");var g,l=[];for(a=0;a<t.toHeight;a+=f)for(i=0;i<t.toWidth;i+=c)(e=i-t.destTileBorder)<0&&(e=0),e+(o=i+c+t.destTileBorder-e)>=t.toWidth&&(o=t.toWidth-e),(A=a-t.destTileBorder)<0&&(A=0),A+(s=a+f+t.destTileBorder-A)>=t.toHeight&&(s=t.toHeight-A),g={toX:e,toY:A,toWidth:o,toHeight:s,toInnerX:i,toInnerY:a,toInnerWidth:c,toInnerHeight:f,offsetX:e/h-r(e/h),offsetY:A/u-r(A/u),scaleX:h,scaleY:u,x:r(e/h),y:r(A/u),width:n(o/h),height:n(s/u)},l.push(g);return l}},{}],16:[function(t,e,A){"use strict";function i(t){return Object.prototype.toString.call(t)}e.exports.isCanvas=function(t){var e=i(t);return"[object HTMLCanvasElement]"===e||"[object OffscreenCanvas]"===e||"[object Canvas]"===e},e.exports.isImage=function(t){return"[object HTMLImageElement]"===i(t)},e.exports.isImageBitmap=function(t){return"[object ImageBitmap]"===i(t)},e.exports.limiter=function(t){var e=0,A=[];function i(){e<t&&A.length&&(e++,A.shift()())}return function(t){return new Promise((function(r,n){A.push((function(){t().then((function(t){r(t),e--,i()}),(function(t){n(t),e--,i()}))})),i()}))}},e.exports.cib_quality_name=function(t){switch(t){case 0:return"pixelated";case 1:return"low";case 2:return"medium"}return"high"},e.exports.cib_support=function(t){return Promise.resolve().then((function(){if("undefined"==typeof createImageBitmap)return!1;var e=t(100,100);return createImageBitmap(e,0,0,100,100,{resizeWidth:10,resizeHeight:10,resizeQuality:"high"}).then((function(t){var A=10===t.width;return t.close(),e=null,A}))})).catch((function(){return!1}))},e.exports.worker_offscreen_canvas_support=function(){return new Promise((function(t,e){if("undefined"!=typeof OffscreenCanvas){var A=btoa("(".concat(function(t){"undefined"!=typeof createImageBitmap?Promise.resolve().then((function(){var t=new OffscreenCanvas(10,10);return t.getContext("2d").rect(0,0,1,1),createImageBitmap(t,0,0,1,1)})).then((function(){return t.postMessage(!0)}),(function(){return t.postMessage(!1)})):t.postMessage(!1)}.toString(),")(self);")),i=new Worker("data:text/javascript;base64,".concat(A));i.onmessage=function(e){return t(e.data)},i.onerror=e}else t(!1)})).then((function(t){return t}),(function(){return!1}))},e.exports.can_use_canvas=function(t){var e=!1;try{var A=t(2,1).getContext("2d"),i=A.createImageData(2,1);i.data[0]=12,i.data[1]=23,i.data[2]=34,i.data[3]=255,i.data[4]=45,i.data[5]=56,i.data[6]=67,i.data[7]=255,A.putImageData(i,0,0),i=null,12===(i=A.getImageData(0,0,2,1)).data[0]&&23===i.data[1]&&34===i.data[2]&&255===i.data[3]&&45===i.data[4]&&56===i.data[5]&&67===i.data[6]&&255===i.data[7]&&(e=!0)}catch(t){}return e},e.exports.cib_can_use_region=function(){return new Promise((function(t){if("undefined"!=typeof Image&&"undefined"!=typeof createImageBitmap){var e=new Image;e.src="data:image/jpeg;base64,/9j/4QBiRXhpZgAATU0AKgAAAAgABQESAAMAAAABAAYAAAEaAAUAAAABAAAASgEbAAUAAAABAAAAUgEoAAMAAAABAAIAAAITAAMAAAABAAEAAAAAAAAAAABIAAAAAQAAAEgAAAAB/9sAQwAEAwMEAwMEBAMEBQQEBQYKBwYGBgYNCQoICg8NEBAPDQ8OERMYFBESFxIODxUcFRcZGRsbGxAUHR8dGh8YGhsa/9sAQwEEBQUGBQYMBwcMGhEPERoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoaGhoa/8IAEQgAAQACAwERAAIRAQMRAf/EABQAAQAAAAAAAAAAAAAAAAAAAAf/xAAUAQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIQAxAAAAF/P//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAQUCf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Bf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Bf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEABj8Cf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8hf//aAAwDAQACAAMAAAAQH//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQMBAT8Qf//EABQRAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQIBAT8Qf//EABQQAQAAAAAAAAAAAAAAAAAAAAD/2gAIAQEAAT8Qf//Z",e.onload=function(){createImageBitmap(e,0,0,e.width,e.height).then((function(A){A.width===e.width&&A.height===e.height?t(!0):t(!1)}),(function(){return t(!1)}))},e.onerror=function(){return t(!1)}}else t(!1)}))}},{}],17:[function(t,e,A){"use strict";e.exports=function(){var e,A=t("./mathlib");onmessage=function(t){var i=t.data.opts;if(!i.src&&i.srcBitmap){var r=new OffscreenCanvas(i.width,i.height),n=r.getContext("2d");n.drawImage(i.srcBitmap,0,0),i.src=n.getImageData(0,0,i.width,i.height).data,r.width=r.height=0,r=null,i.srcBitmap.close(),i.srcBitmap=null}e||(e=new A(t.data.features));var a=e.resizeAndUnsharp(i);postMessage({data:a},[a.buffer])}}},{"./mathlib":1}],18:[function(t,e,A){var i,r,n,a,o,s;function h(t,e,A,i,r,n){var a,o,s,h,u,c,f,g,l,I,d,B,m,Q;for(l=0;l<n;l++){for(f=l,g=0,h=u=(a=t[c=l*r])*i[6],d=i[0],B=i[1],m=i[4],Q=i[5],I=0;I<r;I++)s=(o=t[c])*d+a*B+h*m+u*Q,u=h,h=s,a=o,A[g]=h,g++,c++;for(g--,f+=n*(r-1),h=u=(a=t[--c])*i[7],o=a,d=i[2],B=i[3],I=r-1;I>=0;I--)s=o*d+a*B+h*m+u*Q,u=h,h=s,a=o,o=t[c],e[f]=A[g]+h,c--,g--,f-=n}}e.exports=function(t,e,A,u){if(u){var c=new Uint16Array(t.length),f=new Float32Array(Math.max(e,A)),g=function(t){t<.5&&(t=.5);var e=Math.exp(.527076)/t,A=Math.exp(-e),h=Math.exp(-2*e),u=(1-A)*(1-A)/(1+2*e*A-h);return i=u,r=u*(e-1)*A,n=u*(e+1)*A,a=-u*h,o=2*A,s=-h,new Float32Array([i,r,n,a,o,s,(i+r)/(1-o-s),(n+a)/(1-o-s)])}(u);h(t,c,f,g,e,A),h(c,t,f,g,A,e)}}},{}],19:[function(t,e,A){"use strict";var i=t("object-assign"),r=t("./lib/base64decode"),n=t("./lib/wa_detect"),a={js:!0,wasm:!0};function o(t){if(!(this instanceof o))return new o(t);var e=i({},a,t||{});if(this.options=e,this.__cache={},this.__init_promise=null,this.__modules=e.modules||{},this.__memory=null,this.__wasm={},this.__isLE=1===new Uint32Array(new Uint8Array([1,0,0,0]).buffer)[0],!this.options.js&&!this.options.wasm)throw new Error('mathlib: at least "js" or "wasm" should be enabled')}o.prototype.has_wasm=n,o.prototype.use=function(t){return this.__modules[t.name]=t,this.options.wasm&&this.has_wasm()&&t.wasm_fn?this[t.name]=t.wasm_fn:this[t.name]=t.fn,this},o.prototype.init=function(){if(this.__init_promise)return this.__init_promise;if(!this.options.js&&this.options.wasm&&!this.has_wasm())return Promise.reject(new Error('mathlib: only "wasm" was enabled, but it\'s not supported'));var t=this;return this.__init_promise=Promise.all(Object.keys(t.__modules).map((function(e){var A=t.__modules[e];return t.options.wasm&&t.has_wasm()&&A.wasm_fn?t.__wasm[e]?null:WebAssembly.compile(t.__base64decode(A.wasm_src)).then((function(A){t.__wasm[e]=A})):null}))).then((function(){return t})),this.__init_promise},o.prototype.__base64decode=r,o.prototype.__reallocate=function(t){if(!this.__memory)return this.__memory=new WebAssembly.Memory({initial:Math.ceil(t/65536)}),this.__memory;var e=this.__memory.buffer.byteLength;return e<t&&this.__memory.grow(Math.ceil((t-e)/65536)),this.__memory},o.prototype.__instance=function(t,e,A){if(e&&this.__reallocate(e),!this.__wasm[t]){var r=this.__modules[t];this.__wasm[t]=new WebAssembly.Module(this.__base64decode(r.wasm_src))}if(!this.__cache[t]){var n={memoryBase:0,memory:this.__memory,tableBase:0,table:new WebAssembly.Table({initial:0,element:"anyfunc"})};this.__cache[t]=new WebAssembly.Instance(this.__wasm[t],{env:i(n,A||{})})}return this.__cache[t]},o.prototype.__align=function(t,e){var A=t%(e=e||8);return t+(A?e-A:0)},e.exports=o},{"./lib/base64decode":20,"./lib/wa_detect":21,"object-assign":22}],20:[function(t,e,A){"use strict";e.exports=function(t){for(var e=t.replace(/[\r\n=]/g,""),A=e.length,i=new Uint8Array(3*A>>2),r=0,n=0,a=0;a<A;a++)a%4==0&&a&&(i[n++]=r>>16&255,i[n++]=r>>8&255,i[n++]=255&r),r=r<<6|"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/".indexOf(e.charAt(a));var o=A%4*6;return 0===o?(i[n++]=r>>16&255,i[n++]=r>>8&255,i[n++]=255&r):18===o?(i[n++]=r>>10&255,i[n++]=r>>2&255):12===o&&(i[n++]=r>>4&255),i}},{}],21:[function(t,e,A){"use strict";var i;e.exports=function(){if(void 0!==i)return i;if(i=!1,"undefined"==typeof WebAssembly)return i;try{var t=new Uint8Array([0,97,115,109,1,0,0,0,1,6,1,96,1,127,1,127,3,2,1,0,5,3,1,0,1,7,8,1,4,116,101,115,116,0,0,10,16,1,14,0,32,0,65,1,54,2,0,32,0,40,2,0,11]),e=new WebAssembly.Module(t);return 0!==new WebAssembly.Instance(e,{}).exports.test(4)&&(i=!0),i}catch(t){}return i}},{}],22:[function(t,e,A){
/*
object-assign
(c) Sindre Sorhus
@license MIT
*/
"use strict";var i=Object.getOwnPropertySymbols,r=Object.prototype.hasOwnProperty,n=Object.prototype.propertyIsEnumerable;function a(t){if(null==t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}e.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},A=0;A<10;A++)e["_"+String.fromCharCode(A)]=A;if("**********"!==Object.getOwnPropertyNames(e).map((function(t){return e[t]})).join(""))return!1;var i={};return"abcdefghijklmnopqrst".split("").forEach((function(t){i[t]=t})),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},i)).join("")}catch(t){return!1}}()?Object.assign:function(t,e){for(var A,o,s=a(t),h=1;h<arguments.length;h++){for(var u in A=Object(arguments[h]))r.call(A,u)&&(s[u]=A[u]);if(i){o=i(A);for(var c=0;c<o.length;c++)n.call(A,o[c])&&(s[o[c]]=A[o[c]])}}return s}},{}],23:[function(t,e,A){var i=arguments[3],r=arguments[4],n=arguments[5],a=JSON.stringify;e.exports=function(t,e){for(var A,o=Object.keys(n),s=0,h=o.length;s<h;s++){var u=o[s],c=n[u].exports;if(c===t||c&&c.default===t){A=u;break}}if(!A){A=Math.floor(Math.pow(16,8)*Math.random()).toString(16);var f={};for(s=0,h=o.length;s<h;s++){f[u=o[s]]=u}r[A]=["function(require,module,exports){"+t+"(self); }",f]}var g=Math.floor(Math.pow(16,8)*Math.random()).toString(16),l={};l[A]=A,r[g]=["function(require,module,exports){var f = require("+a(A)+");(f.default ? f.default : f)(self);}",l];var I={};!function t(e){for(var A in I[e]=!0,r[e][1]){var i=r[e][1][A];I[i]||t(i)}}(g);var d="("+i+")({"+Object.keys(I).map((function(t){return a(t)+":["+r[t][0]+","+a(r[t][1])+"]"})).join(",")+"},{},["+a(g)+"])",B=window.URL||window.webkitURL||window.mozURL||window.msURL,m=new Blob([d],{type:"text/javascript"});if(e&&e.bare)return m;var Q=B.createObjectURL(m),p=new Worker(Q);return p.objectURL=Q,p}},{}],"/index.js":[function(t,e,A){"use strict";function i(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var A=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null==A)return;var i,r,n=[],a=!0,o=!1;try{for(A=A.call(t);!(a=(i=A.next()).done)&&(n.push(i.value),!e||n.length!==e);a=!0);}catch(t){o=!0,r=t}finally{try{a||null==A.return||A.return()}finally{if(o)throw r}}return n}(t,e)||function(t,e){if(!t)return;if("string"==typeof t)return r(t,e);var A=Object.prototype.toString.call(t).slice(8,-1);"Object"===A&&t.constructor&&(A=t.constructor.name);if("Map"===A||"Set"===A)return Array.from(t);if("Arguments"===A||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(A))return r(t,e)}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function r(t,e){(null==e||e>t.length)&&(e=t.length);for(var A=0,i=new Array(e);A<e;A++)i[A]=t[A];return i}var n=t("object-assign"),a=t("webworkify"),o=t("./lib/mathlib"),s=t("./lib/pool"),h=t("./lib/utils"),u=t("./lib/worker"),c=t("./lib/stepper"),f=t("./lib/tiler"),g=t("./lib/mm_resize/resize_filter_info"),l={},I=!1;try{"undefined"!=typeof navigator&&navigator.userAgent&&(I=navigator.userAgent.indexOf("Safari")>=0)}catch(t){}var d=1;"undefined"!=typeof navigator&&(d=Math.min(navigator.hardwareConcurrency||1,4));var B={tile:1024,concurrency:d,features:["js","wasm","ww"],idle:2e3,createCanvas:function(t,e){var A=document.createElement("canvas");return A.width=t,A.height=e,A}},m={filter:"mks2013",unsharpAmount:0,unsharpRadius:0,unsharpThreshold:0},Q=!1,p=!1,E=!1,w=!1,C=!1;function _(){return{value:a(u),destroy:function(){if(this.value.terminate(),"undefined"!=typeof window){var t=window.URL||window.webkitURL||window.mozURL||window.msURL;t&&t.revokeObjectURL&&this.value.objectURL&&t.revokeObjectURL(this.value.objectURL)}}}}function b(t){if(!(this instanceof b))return new b(t);this.options=n({},B,t||{});var e="lk_".concat(this.options.concurrency);this.__limit=l[e]||h.limiter(this.options.concurrency),l[e]||(l[e]=this.__limit),this.features={js:!1,wasm:!1,cib:!1,ww:!1},this.__workersPool=null,this.__requested_features=[],this.__mathlib=null}b.prototype.init=function(){var e=this;if(this.__initPromise)return this.__initPromise;if("undefined"!=typeof ImageData&&"undefined"!=typeof Uint8ClampedArray)try{new ImageData(new Uint8ClampedArray(400),10,10),Q=!0}catch(t){}"undefined"!=typeof ImageBitmap&&(ImageBitmap.prototype&&ImageBitmap.prototype.close?p=!0:this.debug("ImageBitmap does not support .close(), disabled"));var A=this.options.features.slice();if(A.indexOf("all")>=0&&(A=["cib","wasm","js","ww"]),this.__requested_features=A,this.__mathlib=new o(A),A.indexOf("ww")>=0&&"undefined"!=typeof window&&"Worker"in window)try{t("webworkify")((function(){})).terminate(),this.features.ww=!0;var i="wp_".concat(JSON.stringify(this.options));l[i]?this.__workersPool=l[i]:(this.__workersPool=new s(_,this.options.idle),l[i]=this.__workersPool)}catch(t){}var r,a,u=this.__mathlib.init().then((function(t){n(e.features,t.features)}));r=p?h.cib_support(this.options.createCanvas).then((function(t){e.features.cib&&A.indexOf("cib")<0?e.debug("createImageBitmap() resize supported, but disabled by config"):A.indexOf("cib")>=0&&(e.features.cib=t)})):Promise.resolve(!1),E=h.can_use_canvas(this.options.createCanvas),a=(a=p&&Q&&-1!==A.indexOf("ww")?h.worker_offscreen_canvas_support():Promise.resolve(!1)).then((function(t){w=t}));var c=h.cib_can_use_region().then((function(t){C=t}));return this.__initPromise=Promise.all([u,r,a,c]).then((function(){return e})),this.__initPromise},b.prototype.__invokeResize=function(t,e){var A=this;return e.__mathCache=e.__mathCache||{},Promise.resolve().then((function(){return A.features.ww?new Promise((function(i,r){var n=A.__workersPool.acquire();e.cancelToken&&e.cancelToken.catch((function(t){return r(t)})),n.value.onmessage=function(t){n.release(),t.data.err?r(t.data.err):i(t.data)};var a=[];t.src&&a.push(t.src.buffer),t.srcBitmap&&a.push(t.srcBitmap),n.value.postMessage({opts:t,features:A.__requested_features,preload:{wasm_nodule:A.__mathlib.__}},a)})):{data:A.__mathlib.resizeAndUnsharp(t,e.__mathCache)}}))},b.prototype.__extractTileData=function(t,e,A,i,r){if(this.features.ww&&w&&(h.isCanvas(e)||C))return this.debug("Create tile for OffscreenCanvas"),createImageBitmap(i.srcImageBitmap||e,t.x,t.y,t.width,t.height).then((function(t){return r.srcBitmap=t,r}));if(h.isCanvas(e))return i.srcCtx||(i.srcCtx=e.getContext("2d")),this.debug("Get tile pixel data"),r.src=i.srcCtx.getImageData(t.x,t.y,t.width,t.height).data,r;this.debug("Draw tile imageBitmap/image to temporary canvas");var n=this.options.createCanvas(t.width,t.height),a=n.getContext("2d");return a.globalCompositeOperation="copy",a.drawImage(i.srcImageBitmap||e,t.x,t.y,t.width,t.height,0,0,t.width,t.height),this.debug("Get tile pixel data"),r.src=a.getImageData(0,0,t.width,t.height).data,n.width=n.height=0,r},b.prototype.__landTileData=function(t,e,A){var i;if(this.debug("Convert raw rgba tile result to ImageData"),e.bitmap)return A.toCtx.drawImage(e.bitmap,t.toX,t.toY),null;if(Q)i=new ImageData(new Uint8ClampedArray(e.data),t.toWidth,t.toHeight);else if((i=A.toCtx.createImageData(t.toWidth,t.toHeight)).data.set)i.data.set(e.data);else for(var r=i.data.length-1;r>=0;r--)i.data[r]=e.data[r];return this.debug("Draw tile"),I?A.toCtx.putImageData(i,t.toX,t.toY,t.toInnerX-t.toX,t.toInnerY-t.toY,t.toInnerWidth+1e-5,t.toInnerHeight+1e-5):A.toCtx.putImageData(i,t.toX,t.toY,t.toInnerX-t.toX,t.toInnerY-t.toY,t.toInnerWidth,t.toInnerHeight),null},b.prototype.__tileAndResize=function(t,e,A){var i=this,r={srcCtx:null,srcImageBitmap:null,isImageBitmapReused:!1,toCtx:null};return Promise.resolve().then((function(){if(r.toCtx=e.getContext("2d"),h.isCanvas(t))return null;if(h.isImageBitmap(t))return r.srcImageBitmap=t,r.isImageBitmapReused=!0,null;if(h.isImage(t))return p?(i.debug("Decode image via createImageBitmap"),createImageBitmap(t).then((function(t){r.srcImageBitmap=t})).catch((function(t){return null}))):null;throw new Error('Pica: ".from" should be Image, Canvas or ImageBitmap')})).then((function(){if(A.canceled)return A.cancelToken;i.debug("Calculate tiles");var n=f({width:A.width,height:A.height,srcTileSize:i.options.tile,toWidth:A.toWidth,toHeight:A.toHeight,destTileBorder:A.__destTileBorder}).map((function(e){return function(e){return i.__limit((function(){if(A.canceled)return A.cancelToken;var n={width:e.width,height:e.height,toWidth:e.toWidth,toHeight:e.toHeight,scaleX:e.scaleX,scaleY:e.scaleY,offsetX:e.offsetX,offsetY:e.offsetY,filter:A.filter,unsharpAmount:A.unsharpAmount,unsharpRadius:A.unsharpRadius,unsharpThreshold:A.unsharpThreshold};return i.debug("Invoke resize math"),Promise.resolve(n).then((function(n){return i.__extractTileData(e,t,A,r,n)})).then((function(t){return i.debug("Invoke resize math"),i.__invokeResize(t,A)})).then((function(t){return A.canceled?A.cancelToken:(r.srcImageData=null,i.__landTileData(e,t,r))}))}))}(e)}));function a(t){t.srcImageBitmap&&(t.isImageBitmapReused||t.srcImageBitmap.close(),t.srcImageBitmap=null)}return i.debug("Process tiles"),Promise.all(n).then((function(){return i.debug("Finished!"),a(r),e}),(function(t){throw a(r),t}))}))},b.prototype.__processStages=function(t,e,A,r){var a=this;if(r.canceled)return r.cancelToken;var o,s,h=i(t.shift(),2),u=h[0],c=h[1],f=0===t.length;return o=f||g.q2f.indexOf(r.filter)<0?r.filter:"box"===r.filter?"box":"hamming",r=n({},r,{toWidth:u,toHeight:c,filter:o}),f||(s=this.options.createCanvas(u,c)),this.__tileAndResize(e,f?A:s,r).then((function(){return f?A:(r.width=u,r.height=c,a.__processStages(t,s,A,r))})).then((function(t){return s&&(s.width=s.height=0),t}))},b.prototype.__resizeViaCreateImageBitmap=function(t,e,A){var i=this,r=e.getContext("2d");return this.debug("Resize via createImageBitmap()"),createImageBitmap(t,{resizeWidth:A.toWidth,resizeHeight:A.toHeight,resizeQuality:h.cib_quality_name(g.f2q[A.filter])}).then((function(t){if(A.canceled)return A.cancelToken;if(!A.unsharpAmount)return r.drawImage(t,0,0),t.close(),r=null,i.debug("Finished!"),e;i.debug("Unsharp result");var n=i.options.createCanvas(A.toWidth,A.toHeight),a=n.getContext("2d");a.drawImage(t,0,0),t.close();var o=a.getImageData(0,0,A.toWidth,A.toHeight);return i.__mathlib.unsharp_mask(o.data,A.toWidth,A.toHeight,A.unsharpAmount,A.unsharpRadius,A.unsharpThreshold),r.putImageData(o,0,0),n.width=n.height=0,o=a=n=r=null,i.debug("Finished!"),e}))},b.prototype.resize=function(t,e,A){var i=this;this.debug("Start resize...");var r=n({},m);if(isNaN(A)?A&&(r=n(r,A)):r=n(r,{quality:A}),r.toWidth=e.width,r.toHeight=e.height,r.width=t.naturalWidth||t.width,r.height=t.naturalHeight||t.height,Object.prototype.hasOwnProperty.call(r,"quality")){if(r.quality<0||r.quality>3)throw new Error("Pica: .quality should be [0..3], got ".concat(r.quality));r.filter=g.q2f[r.quality]}if(0===e.width||0===e.height)return Promise.reject(new Error("Invalid output size: ".concat(e.width,"x").concat(e.height)));r.unsharpRadius>2&&(r.unsharpRadius=2),r.canceled=!1,r.cancelToken&&(r.cancelToken=r.cancelToken.then((function(t){throw r.canceled=!0,t}),(function(t){throw r.canceled=!0,t})));return r.__destTileBorder=Math.ceil(Math.max(3,2.5*r.unsharpRadius|0)),this.init().then((function(){if(r.canceled)return r.cancelToken;if(i.features.cib){if(g.q2f.indexOf(r.filter)>=0)return i.__resizeViaCreateImageBitmap(t,e,r);i.debug("cib is enabled, but not supports provided filter, fallback to manual math")}if(!E){var A=new Error("Pica: cannot use getImageData on canvas, make sure fingerprinting protection isn't enabled");throw A.code="ERR_GET_IMAGE_DATA",A}var n=c(r.width,r.height,r.toWidth,r.toHeight,i.options.tile,r.__destTileBorder);return i.__processStages(n,t,e,r)}))},b.prototype.resizeBuffer=function(t){var e=this,A=n({},m,t);if(Object.prototype.hasOwnProperty.call(A,"quality")){if(A.quality<0||A.quality>3)throw new Error("Pica: .quality should be [0..3], got ".concat(A.quality));A.filter=g.q2f[A.quality]}return this.init().then((function(){return e.__mathlib.resizeAndUnsharp(A)}))},b.prototype.toBlob=function(t,e,A){return e=e||"image/png",new Promise((function(i){if(t.toBlob)t.toBlob((function(t){return i(t)}),e,A);else if(t.convertToBlob)i(t.convertToBlob({type:e,quality:A}));else{for(var r=atob(t.toDataURL(e,A).split(",")[1]),n=r.length,a=new Uint8Array(n),o=0;o<n;o++)a[o]=r.charCodeAt(o);i(new Blob([a],{type:e}))}}))},b.prototype.debug=function(){},e.exports=b},{"./lib/mathlib":1,"./lib/mm_resize/resize_filter_info":7,"./lib/pool":13,"./lib/stepper":14,"./lib/tiler":15,"./lib/utils":16,"./lib/worker":17,"object-assign":22,webworkify:23}]},{},[])("/index.js")}));