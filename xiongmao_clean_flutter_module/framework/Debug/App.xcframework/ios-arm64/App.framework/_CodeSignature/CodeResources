<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Info.plist</key>
		<data>
		85iaADE5tc/NukRC+KGdiZaZ0ZA=
		</data>
		<key>flutter_assets/AssetManifest.bin</key>
		<data>
		kDOGBKJc1WQq2UhnFdwjs1dyVlg=
		</data>
		<key>flutter_assets/AssetManifest.json</key>
		<data>
		5oflYOUu6dHBPZhQ3eN7SBXRAeg=
		</data>
		<key>flutter_assets/FontManifest.json</key>
		<data>
		VE+Wae38ck9Os2HbVlUs6HGx/Ps=
		</data>
		<key>flutter_assets/NOTICES.Z</key>
		<data>
		BL8/Ngei7SRnj2NyaHUtiOQ4ZPs=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_add.png</key>
		<data>
		CBo4mJLx0ijjFPr1ZlggkvmiP5A=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_add_white.png</key>
		<data>
		LT+Y2mtqpqpVAk0BYzzXFgoZAIE=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_call.png</key>
		<data>
		+LgdMWQ028xxzqt++yLcvtD+VIQ=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_close.png</key>
		<data>
		DyFSnoMeXiStfl3MTrB1+qe6v+s=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_del.png</key>
		<data>
		V6QJ9EWSAw2mJWOH86g0D2utCfo=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_edit.png</key>
		<data>
		IKM/i+C2cq5HGb1Dcby0QtOr9Nk=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_filter.png</key>
		<data>
		mnySyFWzRyqbj4ZvQyR/c9CJixI=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_filter_select.png</key>
		<data>
		6/uywcpQQk/CcOxBYrYx2aUEeNg=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_gray_arrow.png</key>
		<data>
		nrhvhZy7lEUm21H9eunqLirUxvM=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_more.png</key>
		<data>
		2maIdxM1kdD8OhUUBB8NSzTB6+4=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_red_del.png</key>
		<data>
		6I017cC9ycHf2KidxJUykn1D8ZA=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_reset.png</key>
		<data>
		Kf8jHF1iRbNxUzjZKiG8OwT4JA4=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_selected.png</key>
		<data>
		BHV4BLrCJKNFgDXjTvrAIZXkRec=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_share.png</key>
		<data>
		bYhYmBlbdUUM28KXHFL1Zi4uEa8=
		</data>
		<key>flutter_assets/assets/images/base/icon_base_tab_select.png</key>
		<data>
		gX+RQYZKXMbRCCmmw8mFM/fJ+mA=
		</data>
		<key>flutter_assets/assets/images/base/icon_circle_left_arrow.png</key>
		<data>
		tL53a84cbuHB81DMSDQQTQkp3dk=
		</data>
		<key>flutter_assets/assets/images/base/icon_circle_right_arrow.png</key>
		<data>
		v/pU5iUfiy63YUym7Dc+9m+2+so=
		</data>
		<key>flutter_assets/assets/images/common/icon_add_image.png</key>
		<data>
		pjItspU//5SeGcOuhHAixayGJWQ=
		</data>
		<key>flutter_assets/assets/images/common/icon_approve.png</key>
		<data>
		voW0MXq1AuokQDs+FUs404gPsKo=
		</data>
		<key>flutter_assets/assets/images/common/icon_approve_add.png</key>
		<data>
		L4Y0n8m67fwrSRLJsmhKyn9MdXQ=
		</data>
		<key>flutter_assets/assets/images/common/icon_approve_delete.png</key>
		<data>
		mbFUuBc2zCudd/7MREx39185zbU=
		</data>
		<key>flutter_assets/assets/images/common/icon_approve_manager1.png</key>
		<data>
		5yds/v0CuFYy+xodMd/XmbuZH2I=
		</data>
		<key>flutter_assets/assets/images/common/icon_attendance_edit.png</key>
		<data>
		jp0si65vaMP5bW07lnPAfOW85Lg=
		</data>
		<key>flutter_assets/assets/images/common/icon_attendance_edit_gray.png</key>
		<data>
		E+Ogt0o8O0XL2xiFJE37uVNoJ7s=
		</data>
		<key>flutter_assets/assets/images/common/icon_attendance_group.png</key>
		<data>
		L7USGFlbLoBhlUX4b95zrZCLqcM=
		</data>
		<key>flutter_assets/assets/images/common/icon_bank_ocr.png</key>
		<data>
		dAAKSZldAeDi/TYot+w9AjkGAf8=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_company.png</key>
		<data>
		1PaQxJSnHxh9H9lOFrk2toippU8=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_puzzle_left.png</key>
		<data>
		DV5kaqh8EPtRgde7LDaBfSowJT0=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_puzzle_right.png</key>
		<data>
		5zMQnV8un9H1jM/U+FfCJGUtzHo=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_contract.png</key>
		<data>
		OdAUlb/1ZGwnrguQAQPo6qVC8Ao=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_d.png</key>
		<data>
		ta1MbwNleBX/lZjXVOZ0Sb4f6mA=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_in.png</key>
		<data>
		gXiIu6NQ1AsLy6saAOa1DI+q2Vo=
		</data>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_kq.png</key>
		<data>
		UZPaCh+0h1K8sNHslhzbvePZ374=
		</data>
		<key>flutter_assets/assets/images/common/icon_insure_tic.png</key>
		<data>
		1wWLphNGUgIs+Xw3cqFwCoSE+BY=
		</data>
		<key>flutter_assets/assets/images/common/icon_left_attendance.png</key>
		<data>
		JowCkeZShwW7Ndt3ClZHsGnUN8c=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_double_select.png</key>
		<data>
		QuQJEJRcZGxl8Y/8UXB87E6TDdg=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_double_unselect.png</key>
		<data>
		hAC7WHfxArCfsxY5KqSi6fkZEhE=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_jiu_select.png</key>
		<data>
		l34uE19RQ1VWLhNMiOY42ZRh+XA=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_jiu_unselect.png</key>
		<data>
		pkLndrrWDlBuNalQWjuZS/Ks+s8=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_photo.png</key>
		<data>
		N9fveEMmWIUBSSBYNW9J7PFAVcY=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_sing_select.png</key>
		<data>
		g88XRLIL27VMw3UYO8xDvsoRT8Q=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_sing_unselect.png</key>
		<data>
		X7ArdMWJ0fD217TkVQOa7NkvxSo=
		</data>
		<key>flutter_assets/assets/images/common/icon_puzzle_text.png</key>
		<data>
		ZOO6UdEDa3F8qPtyUdBoFz8IgDU=
		</data>
		<key>flutter_assets/assets/images/common/icon_work_overtime.png</key>
		<data>
		rJnFaYp0byuMfRHSk+I7kdJQhYY=
		</data>
		<key>flutter_assets/assets/images/common/id_card_f_bg.png</key>
		<data>
		7XS89V0E2+Mroz1R30pkkHqNUII=
		</data>
		<key>flutter_assets/assets/images/common/id_card_z_bg.png</key>
		<data>
		H9Yte8CRLbXwwH+fgVMqRyMSrMQ=
		</data>
		<key>flutter_assets/assets/images/icon_base_arrow.png</key>
		<data>
		HM5yVVA0i5XwqFIvK/d2Qg3oF9M=
		</data>
		<key>flutter_assets/assets/images/icon_base_arrow_blue.png</key>
		<data>
		Bp5ZSHouvKwMdTovTA4MsCXDMTs=
		</data>
		<key>flutter_assets/assets/images/icon_base_back.png</key>
		<data>
		SOiIZRjNUgrZXJsHQxh+4bRJ8oU=
		</data>
		<key>flutter_assets/assets/images/icon_base_bottom_arrow.png</key>
		<data>
		VzqVZQxbV7iFZ5sHKl8awqRpPdY=
		</data>
		<key>flutter_assets/assets/images/icon_base_round_close.png</key>
		<data>
		ntS4+/GlpJEZrFIBUiSz9OGVMok=
		</data>
		<key>flutter_assets/assets/images/icon_base_selected.png</key>
		<data>
		BHV4BLrCJKNFgDXjTvrAIZXkRec=
		</data>
		<key>flutter_assets/assets/images/icon_camera.png</key>
		<data>
		tP22Tzt2nfX4ZXSUWHNm7Euzu7s=
		</data>
		<key>flutter_assets/assets/images/icon_change.png</key>
		<data>
		x6kPlIg1NrukEs7mFAsxg4oB7aI=
		</data>
		<key>flutter_assets/assets/images/icon_check.png</key>
		<data>
		p4mjjGWawT4dKCxPXh+pwZNneeQ=
		</data>
		<key>flutter_assets/assets/images/icon_check_sample.png</key>
		<data>
		xqpO/QS8bSGtq/SpJb+k3/oQVpc=
		</data>
		<key>flutter_assets/assets/images/icon_delete.png</key>
		<data>
		gbs7ZYwOPYxlAyZ/Wnb+NyLkaHU=
		</data>
		<key>flutter_assets/assets/images/icon_depart_info.png</key>
		<data>
		cJ+JF3C/IcRjyXgAR3nLr0ayCF0=
		</data>
		<key>flutter_assets/assets/images/icon_depart_user.png</key>
		<data>
		OYxQdK9+BkYJPoUEaCRj2sQMmwQ=
		</data>
		<key>flutter_assets/assets/images/icon_fun.png</key>
		<data>
		c+Ia6EUcb0LIlq6cI4Zr5CrT8TU=
		</data>
		<key>flutter_assets/assets/images/icon_question.png</key>
		<data>
		TobFNfGNGMM9RiDztR2YtZ9oEhU=
		</data>
		<key>flutter_assets/assets/images/icon_sample.png</key>
		<data>
		6JwV2BbCoaxV9Ya/ZTskSJIUVt8=
		</data>
		<key>flutter_assets/assets/images/icon_search.png</key>
		<data>
		y2mcD52WHbnOpyw9g4BFOixPx80=
		</data>
		<key>flutter_assets/assets/images/icon_uncheck.png</key>
		<data>
		xHZgOMAa2YOLRh41UAerMvYWF8o=
		</data>
		<key>flutter_assets/assets/images/init_loading_pic.png</key>
		<data>
		G8EQnxcZxAqCjReaETjZIXKa5+Y=
		</data>
		<key>flutter_assets/assets/images/no_data.png</key>
		<data>
		4+JUu4F3D7iUjHWHO9/F23j7Ki4=
		</data>
		<key>flutter_assets/assets/images/pay_result_icon.png</key>
		<data>
		+Xr+bJaB64dOcmoGqOkpomw6ktw=
		</data>
		<key>flutter_assets/assets/images/state/icon_empty.png</key>
		<data>
		4+JUu4F3D7iUjHWHO9/F23j7Ki4=
		</data>
		<key>flutter_assets/assets/images/state/network.png</key>
		<data>
		TdUMDP5cMInmZwWJm3Ts1A1VZ94=
		</data>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<data>
		/CUoTuPQqqdexfyOT9lpJhV+2MQ=
		</data>
		<key>flutter_assets/isolate_snapshot_data</key>
		<data>
		tgPqj3XJHhCcJShAaBJTSgw3mJo=
		</data>
		<key>flutter_assets/kernel_blob.bin</key>
		<data>
		kLdV9sHNwCUz0IrVhTKGSTeWnBw=
		</data>
		<key>flutter_assets/packages/bruno/assets/fonts/Bebas-Regular.ttf</key>
		<data>
		ssHzjvjhlWyrB0y5nUl6+ZIbkto=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/grey_place_holder.png</key>
		<data>
		i7JrxwUJvy1Slga85duRoIXHqmY=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/ic_delete_grey.png</key>
		<data>
		SjLsxvYH5v6V6s1Aq0TI0OyetdM=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/ic_delete_white.png</key>
		<data>
		bhSlGm5Fn6AF6uLH/36aoZa3cuo=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/ic_quotation_mark.png</key>
		<data>
		jjemkrIOTQyU9pOfaTeVvx0sUMw=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/ic_search.png</key>
		<data>
		+P30Uwk6kBuYa2KmbRcJah2ljMQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_disable.png</key>
		<data>
		atFTgqzag6c/yfkI/NjccFnH+u4=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_enable.png</key>
		<data>
		U+xE4UCLyr9yGuJMjj/cVQilTOM=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_form_item.png</key>
		<data>
		RjPstXQg3cx/UrClaCSlPrLB4wE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_alert.png</key>
		<data>
		weBn3wY2bneK0i/tTx395DyRAPk=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_alter.png</key>
		<data>
		qqrqy459eBLzju/GuVmELJ+CZ7s=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_audioplayer_pause.png</key>
		<data>
		4FjbZt+x2EL0DXE6R7jrWPOgkKs=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_audioplayer_play.png</key>
		<data>
		cSX+pLHwGsfhxfoOWpTfnNbUfTc=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_back_black.png</key>
		<data>
		wyxouqPdRlVDlT8YoqN5CcQamqE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_back_white.png</key>
		<data>
		9qC1gQZ6wvImE311ajzK85XLbj8=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_bottom_btn.png</key>
		<data>
		289Nepewo+ET2Dccba4eh6CaBo0=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_calendar_next_month.png</key>
		<data>
		ToVpBEWeYCMdQ99fYi7S2D+7P5E=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_calendar_pre_month.png</key>
		<data>
		vnhBnAutEVlDjMtY0A/cJDN+lXg=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_close.png</key>
		<data>
		bD7OE+/o7Qtneg2bNaQM3HEI7wc=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_delete.png</key>
		<data>
		Mie47UuBd7kCfCr1YjEo6VLKuVo=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_down_arrow.png</key>
		<data>
		g9lQ6zo7Tq8jXLncxiDcCuVeI78=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_house_grey.png</key>
		<data>
		g2KxVv4EHuXUsMzSl3yt5LjM5gE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_minus_disable.png</key>
		<data>
		0f2HRJ4EMaSImWILUIUM8yGnojg=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_minus_enable.png</key>
		<data>
		PU3Uj4DuB30Xxj411hd+MYfJc5o=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice.png</key>
		<data>
		MahGz/8kRpEM/oeSiBgbXPd5ycg=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_blue.png</key>
		<data>
		d0gYXocK8E7zFdusZ8uyDblzW1I=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_green.png</key>
		<data>
		OmVhyKupxelErBgaji7A6VyDmyI=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_orange.png</key>
		<data>
		hdcT6U9DS5LRV+57q6280zQLqwc=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_red.png</key>
		<data>
		4ysvBAw8x6hw1kz8pCpT55ESfbc=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_blue.png</key>
		<data>
		CEOJUkgO3dqRyGZaEt8whK1RU0A=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_green.png</key>
		<data>
		HVJr1dZ/eotVlE47WCiFNKqhnQM=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_orange.png</key>
		<data>
		VEsxiEqCiisN0VqAOxzGitf/mFg=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_red.png</key>
		<data>
		xRO7CIRaX4eGGT+Z985Trf/55lY=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_fail.png</key>
		<data>
		OUuj62Ccpz/8p3VSQalW17E3Te8=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_running.png</key>
		<data>
		xS9bZjNcP0KWZ3iFmR4Sd9GhJXY=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_succeed.png</key>
		<data>
		+13KP+9VlRVUn9lgszk0r5w0Zt4=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_warning.png</key>
		<data>
		6Qkd+poh4mtIxmW6zzG4MEI9qsA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_operation_line_left.png</key>
		<data>
		JjPcnmeyCsvENmf7It4i3wLFgYA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_operation_line_right.png</key>
		<data>
		umuJ5YT8g3BZiIzIuAhUgCrn37o=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_pait_info_question.png</key>
		<data>
		QFCjWqOUIRWeDu2GrGsfYYcsXBA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_popup_close.png</key>
		<data>
		Z/erIMvODJGbAuGAG+4F2rStf80=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_question.png</key>
		<data>
		QfcfQ2RwQYww4aiHIaxGzI61Q/4=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_remove_form_item.png</key>
		<data>
		/HHlPf40UdFs99tcPvY6mnfFDtA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_require_red.png</key>
		<data>
		BjvQZBZwsI3jHh6pYoJGnmLIe3I=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_result_error.png</key>
		<data>
		CpDxF9SSJ0zKRTPl+ZEH6OJQleE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_result_success.png</key>
		<data>
		/F+4sxVvmhMwOQEm+srVfODz4Zo=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_right_arrow.png</key>
		<data>
		pF8SI6/63+O9rTTgKjec8Y+qZeo=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_right_top_pointer.png</key>
		<data>
		od1+/duCS3XHn2K8kMHHU4nuS3E=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_selcted_triangle.png</key>
		<data>
		eQVt8cYQFlJ2oXqd/Zb+KBYrFZA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_selection_reset.png</key>
		<data>
		MKZn3DZD8x7g8SZC9HG/mASApyQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star.png</key>
		<data>
		zuy4pksdcOIVk3zrEaqUw8rtnLQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_half.png</key>
		<data>
		oCUHsz5sQUbp9KG80HXV48yZ0kk=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_select.png</key>
		<data>
		Bjoc6Y+p5sHSldBP9p/zyZg4rOA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_unselect.png</key>
		<data>
		gm89emCUyqEy67gpzFE4eicpOyM=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_2.png</key>
		<data>
		+Jg5/lFfHVbZNyLT+GEP91UOjEE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_3.png</key>
		<data>
		2iKtUF/nsFNRpN4uumaBs4z/fyo=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_4.png</key>
		<data>
		L6gGEIX0M4sgnV+f8+HzTRTIXv8=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_5.png</key>
		<data>
		Mhg8WvVxJnT7Z207w+gks5X94Ew=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_completed.png</key>
		<data>
		OOdvl+IvxCsrbtYQJiyj9ieOiB0=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_doing.png</key>
		<data>
		3BGgTlEElcDgUwcQHAmRBwkRD0A=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_success.png</key>
		<data>
		qqrqy459eBLzju/GuVmELJ+CZ7s=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_trash.png</key>
		<data>
		dt0+9EwOEEjVh+SGQ5P5E7bu5fk=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_triangle_down.png</key>
		<data>
		0AQB95k6mnk1feNbnSjlEq7yX0c=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_triangle_up.png</key>
		<data>
		eQVt8cYQFlJ2oXqd/Zb+KBYrFZA=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_unselected_triangle.png</key>
		<data>
		0AQB95k6mnk1feNbnSjlEq7yX0c=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_up_arrow.png</key>
		<data>
		C1ykh4vN+Irth/avW/6t1wMMz+Y=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_vr.png</key>
		<data>
		+5dUDZ/B3E8qdnQyFjaZ8XDhLsM=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/icon_warning.png</key>
		<data>
		R9ls9RNE3/eOrfNPtoRxa9cxXBY=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/img_avatar_new.png</key>
		<data>
		iNH9nZE4rkpUrV5MxeY2xQnQ710=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/normol_border.png</key>
		<data>
		1jd+vsdscyziVWfuFphewMbfE9o=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_multi_selected.png</key>
		<data>
		7YqpfWcDoYRp9QvgKGoH0mspgtE=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_single_selected.png</key>
		<data>
		lweDDW9gomNiMdGEXzFmEX0rGvU=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_unselected.png</key>
		<data>
		yZV+qxuBFeEwNV2WqayO22X0MPY=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/multiple_selected.png</key>
		<data>
		hr3KUkhD+43/S7bfyZuK4Q05p5I=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/single_selected.png</key>
		<data>
		KgUcpDYeDcO3HrVZZcQlwBo+Bxk=
		</data>
		<key>flutter_assets/packages/bruno/assets/icons/radio/unselected.png</key>
		<data>
		SRTpOt15vnzrndA9P+jOJdESvws=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/content_failed.png</key>
		<data>
		l+K59qySG06K4ArkWEN8v+SZjvM=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/empty_state.png</key>
		<data>
		a4mYDbAX1v8QTSu8kybtzVkqp1M=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_default.png</key>
		<data>
		33Idn5APjCowserkWugDOMAT/vE=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_selected.gif</key>
		<data>
		ABdtYA5zOKSC1eIhlyAAUGLflzI=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_unselected.png</key>
		<data>
		eTKXqB+5f19v1PgiPzRrInqtq1Y=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_default.png</key>
		<data>
		VSM3xdFwbtouCbMQq4dArL/Ef0c=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_selected.gif</key>
		<data>
		+pgPFdiL2Z6hcapHrOjdORDGkv0=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_unselected.png</key>
		<data>
		waBOaHx/127wspaczmEnFKUlaiY=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_default.png</key>
		<data>
		or/r4CfxDBg/jU/szeCISOI7G/A=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_selected.gif</key>
		<data>
		TEZKbtcbGuolEQWiivsuUZrGX14=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_unselected.png</key>
		<data>
		Z468EfMuQ8H3W4vmpHeaX/od1Jw=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_default.png</key>
		<data>
		REpFNHH3WfgmV7q0K27et5hA7oQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_selected.gif</key>
		<data>
		***************************=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_unselected.png</key>
		<data>
		csObYEIhV3CoKdfjRSLLlahN+Ho=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_default.png</key>
		<data>
		81mdqAFwprhftuB5rPv/CwA2310=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_selected.gif</key>
		<data>
		fWoGbgm7uovpJZJwKxgugw5yJ6g=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_unselected.png</key>
		<data>
		f0swdj9KCOlsZmRctilnEWf/Qdk=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_down_selected.png</key>
		<data>
		3lBZMvJAwDJnl6UIFnZrSXgu5t4=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_down_unselected.png</key>
		<data>
		r1fqb747BQDZYpR7wYsGA/gfZTk=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_up_selected.png</key>
		<data>
		QzAMddtN33FO1t6Ell5wVFh2LB8=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_bottom_picker_right_top_bg.png</key>
		<data>
		hIQcFOLxJEpW143BGYos+16qVaw=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_call.png</key>
		<data>
		aljEx2dOaZQ7JTsg00qRHUNuw/c=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_call_disable.png</key>
		<data>
		DEGUdhKG8aRuH6YliSVcPWmL7So=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_custom_share.png</key>
		<data>
		20WIyQd3lMH0lDoZ048cuzjdUQs=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_guanbi.png</key>
		<data>
		MUi8rycRzIHw3lgDnQBYTLJtjVU=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_im.png</key>
		<data>
		R9oSCQz1pQQ08kaG17g7J4vdsLU=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_im_blue.png</key>
		<data>
		qtAfJWz5Ds9mkKePLqNMuQQllQU=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_more.png</key>
		<data>
		QB/1fKeJMdJjvrXY+j/iqDbRalw=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_multi_selected.png</key>
		<data>
		hr3KUkhD+43/S7bfyZuK4Q05p5I=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_qrcode_bg.png</key>
		<data>
		b2Ioi7EaPdm1Sz9UBn5BRGJkZ6U=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_qrcode_failed.png</key>
		<data>
		fpeR6mK+F0SRY4fGnl4MCK98tGY=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_refresh.png</key>
		<data>
		W/iDqWcIpoXncucV2iXg1Zbzn9A=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_refresh_white.png</key>
		<data>
		A2NTbGvAdbt5HuPd9+dxA1B63/Y=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_selection_reset.png</key>
		<data>
		MKZn3DZD8x7g8SZC9HG/mASApyQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_browser.png</key>
		<data>
		CIXXnW2JC31yXphIATjkfBTCTnE=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_browser_disable.png</key>
		<data>
		pDrXUDmvv0WlsHSMivUoACebnIw=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_copy.png</key>
		<data>
		H9fyiqmxhLweY3lmPxbvJUlWNbA=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_copy_disable.png</key>
		<data>
		6y056SlRvUr2FhTjcmpzjTewGOA=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_message.png</key>
		<data>
		6wZp7fwAfP7UN8FGBP+eukNJDqI=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_message_disable.png</key>
		<data>
		EgXvWdDwl+pZhUyAtXi/t3lq2cU=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_moments.png</key>
		<data>
		3gFRopa1lA3nKn/hweoL2/0Sxic=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_moments_disable.png</key>
		<data>
		dd9ocabJbmhzEcPpqcWRbbd5U04=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_qq.png</key>
		<data>
		CIuFe6WB7qAD2edajeZcSw6ZmEE=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_qq_disable.png</key>
		<data>
		SDQQlsPK7dbAABMEr22qMs8ux5A=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_save_image.png</key>
		<data>
		eZN6eiTxO+zG9sFG7XUczE3sU14=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_save_image_disable.png</key>
		<data>
		R+PHHD04PudZAwokiZ1MypabMLE=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_shareLink.png</key>
		<data>
		twa775ZVQuceTYWjgYQt1HBcors=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_shareLink_disable.png</key>
		<data>
		lVstwQeQOTwQsYI4YTmfg521B6o=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weChat.png</key>
		<data>
		rmKCG2pHuNleIA5MXDL2slagDWo=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_wechat_disable.png</key>
		<data>
		59H0/dLS1YBZVj6FVuMq3UtFzHE=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weibo.png</key>
		<data>
		ssNFktUY3aKBhEcvA2hPstIGGKM=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weibo_disable.png</key>
		<data>
		LZc0K397Yb7uxGkkKcTHo3FRuN0=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_zone.png</key>
		<data>
		0Ck8CtNKkrcPS2xnxq+EwAZao5Y=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_zone_disable.png</key>
		<data>
		K35CcU/13raAJEaMwNelsZpxvGw=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_single_selected.png</key>
		<data>
		KgUcpDYeDcO3HrVZZcQlwBo+Bxk=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_star_size.png</key>
		<data>
		npJQ23HEMZ8UDTl8eqbDk5+En1M=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_star_size_selected.png</key>
		<data>
		LQ3N8S7fFtRSrbDTppXGtwBYUOY=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/icon_unselect.png</key>
		<data>
		SRTpOt15vnzrndA9P+jOJdESvws=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_bg_blur.png</key>
		<data>
		06OLLVzTIdtJAbi6QZkj2qT5AJk=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_bg_card.png</key>
		<data>
		Q5ixB7XC+I9eWH6b1lIQNhE1l8Y=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_network.png</key>
		<data>
		j+XEzQjvEC7EwtDIJBpD99UQygQ=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_selecetd_default.png</key>
		<data>
		frYlbTSaX2QpWwTcjQPrx35HW48=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_step_title.png</key>
		<data>
		eQvh9Vh1ebBJXFmesnigrtp4wS0=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/img_unselected.png</key>
		<data>
		TO3lJmw0RVYM0BQ+3ac2Orlp+24=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/multi_select_btn_grey.png</key>
		<data>
		SRTpOt15vnzrndA9P+jOJdESvws=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/multi_select_btn_selected.png</key>
		<data>
		hr3KUkhD+43/S7bfyZuK4Q05p5I=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/network_error.png</key>
		<data>
		WNVPjZ+lhWPy3q7JAE8LhCNwsWM=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/no_data.png</key>
		<data>
		AbGgPudjx3Cq4dnu1vhSAGtlUBI=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/refresh_arrow_down.png</key>
		<data>
		k5xiT9XNdajm1WXCfe/kfdFPCog=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/refresh_arrow_up.png</key>
		<data>
		t7jTtVTM+HkyUO6GZuuY8X4aGb8=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/select_checked_status.png</key>
		<data>
		3yGoKIMtks1BfBcLMsJcg5jFHME=
		</data>
		<key>flutter_assets/packages/bruno/assets/images/two_line_bubble.png</key>
		<data>
		mmR/UnV5sg528WnQvNjpcf24D0w=
		</data>
		<key>flutter_assets/packages/bruno/assets/json/china.json</key>
		<data>
		rywMj4T373WSg74wawM/JbVEfV0=
		</data>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<data>
		Bvk+P1ykE1PGRdktwgwDyz6AOqM=
		</data>
		<key>flutter_assets/packages/flutter_image_compress_web/assets/pica.min.js</key>
		<data>
		Mo0xQmdAumhf5KoeUg6A8AWGnHw=
		</data>
		<key>flutter_assets/packages/tdesign_flutter/assets/tdesign/TCloudNumberVF.ttf</key>
		<data>
		PcpCuY9pqzu2mL9jCNF8AA+Wlcg=
		</data>
		<key>flutter_assets/packages/tdesign_flutter/assets/tdesign/td_icons.ttf</key>
		<data>
		K2+SdE3vW5Fd/xX30AR8B4K/fDw=
		</data>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<data>
		ZiX/DByz8A2xHGmziP9zxnaRW9M=
		</data>
		<key>flutter_assets/vm_snapshot_data</key>
		<data>
		cW0Pqsk2814YYWUEON+YgI6+YD0=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>flutter_assets/AssetManifest.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			wkNIb84DFCC/SnnhNoWEZcwrjCGWC9Hl3PvAPrL9CWk=
			</data>
		</dict>
		<key>flutter_assets/AssetManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			2Y1KTwMaPxC0sksRQ6Xy4PNK0ubD3DF2iLMZs0Vxuho=
			</data>
		</dict>
		<key>flutter_assets/FontManifest.json</key>
		<dict>
			<key>hash2</key>
			<data>
			GxnTfBevfQeOCQiV0MogxIWd1X3p10DRd//Z/+JSGyM=
			</data>
		</dict>
		<key>flutter_assets/NOTICES.Z</key>
		<dict>
			<key>hash2</key>
			<data>
			gKI6z/Dlvyy60AkJwFqZwWAikrBpgKLS+qkOf8mjKcQ=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_add.png</key>
		<dict>
			<key>hash2</key>
			<data>
			w7tV2ULLfV4rBITt8+wbsTBNM3uqBYY0dxrTbUvikzY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_add_white.png</key>
		<dict>
			<key>hash2</key>
			<data>
			X+q23b5lhAAnsI88Gx9A7AZq/p2DHLfjbTMqw1cAILo=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jTNApwiGSfi+6btAeUPiCnpSFOQg9bVVBQDiImurlw4=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4YYoPkJLjpdB5Q8PLW0VIIdtwTahHJahyl06f187HKw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_del.png</key>
		<dict>
			<key>hash2</key>
			<data>
			LIwartCQ5vFsufSoBXOnaW6P7G4FV/hO8I2XHfedIgE=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_edit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NtsCxEoaXIHIz7RXU0pPUUJA/LUtpMmK5MWGkzcUvUM=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_filter.png</key>
		<dict>
			<key>hash2</key>
			<data>
			smvDZM4XK9kloH6y3pPp++HVRZAdguESN+prP/DBrbs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_filter_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4DHWWlKskZ+kLNnrF5F4xq4qUqPEZLkYG1ATHB3a8dc=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_gray_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			W75xLl50Xr21bL7vWQR2JMgybrpYq+owXARTtlIRQSs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_more.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mE6F3vtjcdNZiLmhdFpIi7HxIbNu3F8vlopt5ieG8mo=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_red_del.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6NktM83gr214mHdyf37bA6c0XBfZNp8b1U6s7pf8xZI=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_reset.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SsxwjkRUnlTpURaA/BDJA0BJ/Cqr4nhAvx9AymmNMIk=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ku1Qlch9Ajunfyz55ujKECSXDAjFK1DdVB1emCtjxsE=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_share.png</key>
		<dict>
			<key>hash2</key>
			<data>
			uWKUN0WliCvKJup7OoVywST/cS5/h2OlqeA6zFbpPxk=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_base_tab_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			YA/5fWg5bk3HAha9+sIcQ2zH0ORjPWVKs/zym9M/e40=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_circle_left_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			a13yi/GJr9ExiW+dfuF5MkVMwc7f9ujBOwZ2T2QWFFU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/base/icon_circle_right_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			o82QpZk2rGjcb+mzArXChg3gg5nNC6j/6GN9S0Yrdiw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_add_image.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6PSpGnpDrMSzwZnyjjr+IY+6+rhLAv6n/+DHd43UEBU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_approve.png</key>
		<dict>
			<key>hash2</key>
			<data>
			w2dTqQMTV4CY0Yi1V7RE49CHiH32OMyF/1q7YHBIwMs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_approve_add.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mxg8jZcExBNuK76MYseEAIclQ0lZPnYS02Wb+gJTV9E=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_approve_delete.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WwEKlNr05z4rDp1E/vibXbBE8NOJay8uDyybonEbpgs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_approve_manager1.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ItKhfaB7QoQmh0mTOOfLJt5EijiJI3rv8nPCp1dB+0Y=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_attendance_edit.png</key>
		<dict>
			<key>hash2</key>
			<data>
			z9JV/N6r/2JiUI/dvS09KZTkoTMEammkmEocd+ognxc=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_attendance_edit_gray.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JZfJV0lXosBDX68GU3f3xUJge9Pp+SftWpsRalhjPtk=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_attendance_group.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jeI/8yvvN9q2PYunTNizVHST8YTioIJiLBCd8hIZues=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_bank_ocr.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VL1mfM24C/I5gRYE4l95VBZz7Ed1ytEnjj/NSmAaf5Y=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_company.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+AYO67NOAkL2CUMPxB3UzLW6R907G79Hmw+XjyfJ8s0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_puzzle_left.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8uRmryvXE11B5ClQRJXpfsjS7h/cbtVnMDHCiwYBg88=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_puzzle_right.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pAzlSGV7LID7SqLFmtbE4OOVkVezwjrMd4h0dfCC0q8=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_contract.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Q8pWb9HLUEHjqt2udPb/nDPRZD2d8m4TUD0cpvWfp1Q=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_d.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UMB1f68OwbdaPFmH0QodQtMR8JjNGfmtJsOKvcsihP8=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_in.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ycO39KuOp23Q/VXz+QPm+J/7XMGniOO8afFMwBcE6yw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_common_work_rules_kq.png</key>
		<dict>
			<key>hash2</key>
			<data>
			o3MrwJsnSWRCYITpNxRQSBYr5R3uFzF0Cv+tVova8UE=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_insure_tic.png</key>
		<dict>
			<key>hash2</key>
			<data>
			f5JZa7mKRVDN7Ph6/+p3ggGqxH1feCCPBAKQdj399jg=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_left_attendance.png</key>
		<dict>
			<key>hash2</key>
			<data>
			YZt1Ik5r51UdujqSTHZuIn8lzjuxsmEEHO58llE9aXg=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_double_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kkldps9aLh9UwJmjEwoapUhh8nSM+K2v1/7smQ/gzs0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_double_unselect.png</key>
		<dict>
			<key>hash2</key>
			<data>
			W2KscjVdH7zOe/qTKGCOo260yv/qyFRZkmXzyn+bK+s=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_jiu_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lLyzoBVcaRbhHr3Kb8B6S2+ugMFyT8w/n8KJTnWcbc0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_jiu_unselect.png</key>
		<dict>
			<key>hash2</key>
			<data>
			v35ApOfbwbWLV3x/wOoDY3C/7rdtEt1yndLIK0njaUc=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_photo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			xCdeGW857bQkln41AAngNii9jdXUN3p+FGN9cheKpeQ=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_sing_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FbsPDz2xhjYEe5DpmLNlBTT7GiCZeYvQ69JqK0Yx1hU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_sing_unselect.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MzUPyyk5GBfv+8UtwGUlN6suSGWTUVJeyoXs+XtdzFQ=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_puzzle_text.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lVI/RQsGXxd8unUfcQOopizzezjzFL0ZAe58QjO2Qxw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/icon_work_overtime.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1pF5pKSoX7uEEo9ms6FOZKJ93yTR6ZK7IcFZnrNsxMg=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/id_card_f_bg.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EUHSyJif+HlmiWdiXq9+h8nNsGnQZi2+S9k/HeFiK04=
			</data>
		</dict>
		<key>flutter_assets/assets/images/common/id_card_z_bg.png</key>
		<dict>
			<key>hash2</key>
			<data>
			NhhwDmIuRMj23HtDvPTjv67d9f+lcBDSVxrKm/zLE1s=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			oCxnVVzGScmB+h39t8ncBBW/tGSWtVcXh1F4hxcmKV4=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_arrow_blue.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/Fr0yiUWlmTFlQGlNSeiV4YqYWYdDwIuYK09BHSxWio=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_back.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Laf5/2FwQr0iwdyyH1+s5G8Bv8yJzZwYNEPaMFaMPMs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_bottom_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			aaH6bAlOfHlVjtOindtjl3z+S0o70CSmj9tYYocYqTU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_round_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			btgJA5o6AoR5MNBeGKNpbcAmVNk7DxybiC35vf2MRSs=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_base_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ku1Qlch9Ajunfyz55ujKECSXDAjFK1DdVB1emCtjxsE=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_camera.png</key>
		<dict>
			<key>hash2</key>
			<data>
			P4ENaQ7EHsDKSt6LlyF3eTe9JnDnmjGCp7yfqdm//Ss=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_change.png</key>
		<dict>
			<key>hash2</key>
			<data>
			m3xLa67AmvHeqxyizD+NV7+sUq+jDWwyV1OkWEkmNUI=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_check.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nixZOeUNI9NQN/b72s2w+wxnPFBOnDjO+lSQ2IF0UCY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_check_sample.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wUlGRNj2icNGfOZ5M42r8dfVzwrgiNSJJs2CWKYbkaI=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_delete.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lbmHCw4gMJEHX8NiFwVOlT3Yhva6YHPxeW9DgZwMkxU=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_depart_info.png</key>
		<dict>
			<key>hash2</key>
			<data>
			n5M3mJZbCLqq/PVLdcwrfAgYijsq5jn9CWk/zcLeuo0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_depart_user.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jB9b9lEeP0RbkQLR+iQabDd5nKPTvpRcIihUUgPAs3E=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_fun.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zj15LnLI+Zz+M/uMqR2n4nOWU0mZoG9+r1MQsqTgTuM=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_question.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jhKKroAYT/G8fRKt98iCJ6jPXy68XFRao6o2kErdCp0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_sample.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WXy8FCLhbmjuUVyrfM0KSQBYvclCXYIeMDtGbbKzUkA=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_search.png</key>
		<dict>
			<key>hash2</key>
			<data>
			3J1HCUB9Wb5IzbU6rz2ts+4TNL/C4uM+nW8+TwxYva0=
			</data>
		</dict>
		<key>flutter_assets/assets/images/icon_uncheck.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zPmwYAtNAqZZe+CckBQpET42h423e0W+DqWkxxMtEQw=
			</data>
		</dict>
		<key>flutter_assets/assets/images/init_loading_pic.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SXrhjxSG4xemwG/NPW8fSDb2ESBWHL641UzqKrd9WqI=
			</data>
		</dict>
		<key>flutter_assets/assets/images/no_data.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Fli7p940gxL2w38nDnCnTWYaFGs9kFeurYTMa/oBEUY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/pay_result_icon.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lSKTjLVr832QB4ZNNQcicbjxPUOWxUkeTE0ZmnwJCGo=
			</data>
		</dict>
		<key>flutter_assets/assets/images/state/icon_empty.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Fli7p940gxL2w38nDnCnTWYaFGs9kFeurYTMa/oBEUY=
			</data>
		</dict>
		<key>flutter_assets/assets/images/state/network.png</key>
		<dict>
			<key>hash2</key>
			<data>
			o3mNm28+zpWgtcMXHyswwBPTuwOLD2OD1qJmBmOriHY=
			</data>
		</dict>
		<key>flutter_assets/fonts/MaterialIcons-Regular.otf</key>
		<dict>
			<key>hash2</key>
			<data>
			2YZbZxoJ1oPROoYwidiCXg9ho3aWzl19RIvIAjqmJFM=
			</data>
		</dict>
		<key>flutter_assets/isolate_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			k8KAQoRcXwi8VjMYIesn3rGSY4RQG94lfl26bc7AZHw=
			</data>
		</dict>
		<key>flutter_assets/kernel_blob.bin</key>
		<dict>
			<key>hash2</key>
			<data>
			bwTqu2OgVz0ZtPn1aKzTKtlWFgFn5zbR1XB8vH7jVg8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/fonts/Bebas-Regular.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			OdLrF4/dUrTDUKxt7j0gkK5afBhyJbDRYaFHPMu2Mg0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/grey_place_holder.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JNipmiqR01QK901Gwsm4jhr3nng+IWyOGpUelUjdBSk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/ic_delete_grey.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9E9J0Abmb/k8SB41depCXRcBwJWK4DO/rI62WgQ7Ir4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/ic_delete_white.png</key>
		<dict>
			<key>hash2</key>
			<data>
			55POzSENWsDFGsrlJ42iDeDU+M/XmRFGe/YwMP6tVzI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/ic_quotation_mark.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cpaBiRjgNXlfKZuXLyCSOxnnznVjfjLzf8856U1E8wY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/ic_search.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pdssbfABcsHggRIgO3MHnk7EHOO99mZFWsOXUFcik3Y=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			G3icDFtFDeknkcCOAtVbA4JmP2MeFNfGTWVSGL2JKxI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_enable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			wKK6AxgoBoVbXfYBlH0qIwi6lhnLrk96LpZkxyJnqZY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_add_form_item.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TRoH3Uw3uPy+3Cmn9EaKQEPbnYs0DPpD5YjofrDZjiM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_alert.png</key>
		<dict>
			<key>hash2</key>
			<data>
			rlcalZpYWDyyZltBnOgh5SL3CBOyInXCIH3Japr1OGc=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_alter.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ghuqiW7LHV30YUe4qrQUQI1d1mol25noRLacTcVrS4w=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_audioplayer_pause.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Sga1yJYfVHNis3LRj8Bua5K7ZiNFiIEcori/BuPHPt8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_audioplayer_play.png</key>
		<dict>
			<key>hash2</key>
			<data>
			311oiyjawihbg3AoqEG2vBMZsTa1k/WgWV2DNPocSjM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_back_black.png</key>
		<dict>
			<key>hash2</key>
			<data>
			L5PCyfqdcKP3VQ77Z5G/gOdfkjUGFO4i4CHZOuSX0gM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_back_white.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pFRXEAKecX2bWxwlZVQQZu/udWAlnfp6x0lZYWkhOfg=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_bottom_btn.png</key>
		<dict>
			<key>hash2</key>
			<data>
			e6sW4ZLAHDo/oTu95IThRUJ5vFj/6GsLM8HiTDKTR4I=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_calendar_next_month.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EPHFp43h0YNsIKcwH4srIkigOc4QxMsO4sVSeX768jA=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_calendar_pre_month.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FnNwUxZEsK01CrBxYi8pqFgwydoWsTR2LtSMKqgbHag=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			21H/iaZuWnPJeqTjU6WRlPcjxfF36Cer5iBXobON8DU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_delete.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Iidy97F+hp3SW/jFE64kPQRk7U2i5sGeGTadUAcikeM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_down_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			PL+jnsr+EAl0pwvWEJjcFGN13D3I61lz8boHT40Quc4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_house_grey.png</key>
		<dict>
			<key>hash2</key>
			<data>
			K6/GjHOxlJg/Yttw33VWtlMo/VC7qLpEAJnYPs+1+pY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_minus_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UgP9uLjJYGOU/095nL7FRBUjQZwm+WkVQ3h6ENTjftI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_minus_enable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			EjCIY7jLWLKtCaVfL+Vm4R17G25OnHu37br6WBxCvis=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ssahT5Ens8QVqRAxTzcWUG+MePZeCnbbPW12PZgVg0g=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_blue.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Gl/OCG/ajDtnqK2gQQ5BSLfNbx+kOz+fP05Jhk51fzw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_green.png</key>
		<dict>
			<key>hash2</key>
			<data>
			i2h9yr22bRExo/H3ycblNLNbkQ8LZQpwQcxQmqc8fyM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_orange.png</key>
		<dict>
			<key>hash2</key>
			<data>
			juFSmjkF4g9Ztae4aZc56T7OOF1dUHISkk4GVcQv43M=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_arrow_red.png</key>
		<dict>
			<key>hash2</key>
			<data>
			iyWxKPseq6lJrCIzqLlOAr9R8lS9498c8MxAe/Rv9sw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_blue.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5bN9jUgX1e3sVAt3pCdIfEqizRpWeCM6EHY+6owkpHw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_green.png</key>
		<dict>
			<key>hash2</key>
			<data>
			d58G7WVYiMN/rCrA+1jC2JGK549NmPmYGXFzEP35OZ8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_orange.png</key>
		<dict>
			<key>hash2</key>
			<data>
			OT41eSOqv/uoRPBeueYuwfnu/q+xrQ4kpjYGzNM3RnA=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_close_red.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lHyxp2c5be58dAiH4JkN1Dwa4GIu4+dfa5dBtd4P3zU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_fail.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FVLA8emfNNSlLg38M8YEk6SsuLOvtlDeK+oX8zHINLI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_running.png</key>
		<dict>
			<key>hash2</key>
			<data>
			TgU4Rx32x0o6DZQcUjj329PkAnL/UphzMHGl8nYH/wI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_succeed.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+HqHYdUJgKSB+Vv+u6gAhyKeEoyU8yznGkq9u26Ky1w=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_notice_warning.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7TFUOxhT0gjfbuJXvRptMcJKtkubQRxe7yoSlHetC58=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_operation_line_left.png</key>
		<dict>
			<key>hash2</key>
			<data>
			1PSwIR5cnJ53Q9AaLXa+CuIRk3rDFUUEgq/Fd+LXd6g=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_operation_line_right.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HeWWMa0wFGpX9/UkolCI5N4ApG7XkOvVxf3JI+Z0hAY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_pait_info_question.png</key>
		<dict>
			<key>hash2</key>
			<data>
			uNgHxrm+3Sj8EgDZVoa57U0kelcjAYSvYhpCOnhPqyI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_popup_close.png</key>
		<dict>
			<key>hash2</key>
			<data>
			9yE+dyWBvwfhiMze3k53a6KRhi0Iy1sLQq2sK+ZGV08=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_question.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WQI+x4Oh5LuArhjdXJAx2gqq/CeAjRTT5FX9yB+19PE=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_remove_form_item.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nFR8oEY9ysjVPCB2rXHuJJXwXOgXFeTne1xOVNZFspQ=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_require_red.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7sCXTU03d+KM5bkw0ueODp1wlHzYv5QGED7qs7ccqbk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_result_error.png</key>
		<dict>
			<key>hash2</key>
			<data>
			DR6JXlJ+oXzzbBgNwTPsNJMG7tR8R9xsPFk7lXPnYDU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_result_success.png</key>
		<dict>
			<key>hash2</key>
			<data>
			BRHzwwnKWSeCFZqLNdvhl5FbnfCpLk6pTB1zHh0a720=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_right_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RProF8uZwloTAuf8bFxdcROj1+B7K9ZQu79auBj4nxo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_right_top_pointer.png</key>
		<dict>
			<key>hash2</key>
			<data>
			l+RCkWze1SczON0BIPSAztTzlayY67VtJDhLE/7yMZQ=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_selcted_triangle.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IUccq51bbaPqhardDLSjd2NcWUDrz8VMVAelkfrLsA0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_selection_reset.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ces9rHJIQos3TZW3s+3ugZ47gdhi0WmiEqf1Ad2pI2o=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cQtz1tC4bvXgKg8qMLYsF8+L3a0aujIcS/k4KrXj2bI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_half.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GijEzZiVU3mLMvPbWz2TSMTuC2AXKb6ovfPYrgYwb0I=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_select.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JQssYr3UScr3iKjXIZkYieT6MWexNxT3etJhgjz59l0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_star_unselect.png</key>
		<dict>
			<key>hash2</key>
			<data>
			WvYMN7l7B0rgtMjDOhcKoR4ka1vELqmy6u0w7ORPrIU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_2.png</key>
		<dict>
			<key>hash2</key>
			<data>
			h3lDYiDdZLHIeD0cIrajm8FwiiIK994/15/AoJKCQRw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_3.png</key>
		<dict>
			<key>hash2</key>
			<data>
			L+fq3rcr+7/ubAXA5zEqkB68Y+rLGYOy0qoomc1ClVQ=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_4.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2ltyRppQ10Qj/L9dv21UBNUeEgP06ZqGPdh+yPyHMcM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_5.png</key>
		<dict>
			<key>hash2</key>
			<data>
			A+MgmS41lWeR64uBXAKKFpiQRnc0U3TgWDi+lQW6+Jw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_completed.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+3TJhfrCLphimMgEhFEHLDFbCRkPfY4pGpoTQTMIhXw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_step_doing.png</key>
		<dict>
			<key>hash2</key>
			<data>
			X6ri8bXeuilqp0Cm/tYbQIYdFji9gXBwMsL1UENlxHU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_success.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ghuqiW7LHV30YUe4qrQUQI1d1mol25noRLacTcVrS4w=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_trash.png</key>
		<dict>
			<key>hash2</key>
			<data>
			COSWS2GV4hh1GbbcJpsdblVT7a2cxHca2o7ajBJBUDs=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_triangle_down.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZnIO+929CdBBDHxyfLa1u6Pwjp0nTWor9HFzVRn4QdU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_triangle_up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			IUccq51bbaPqhardDLSjd2NcWUDrz8VMVAelkfrLsA0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_unselected_triangle.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZnIO+929CdBBDHxyfLa1u6Pwjp0nTWor9HFzVRn4QdU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_up_arrow.png</key>
		<dict>
			<key>hash2</key>
			<data>
			sizQsYXQ4VBhshZpkiOv9N3082BF3HkqgY2dMfZMB98=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_vr.png</key>
		<dict>
			<key>hash2</key>
			<data>
			nPhRTxjslXaMzIqxd7w+Gnrlib5IspBeD4pNWWLOgxw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/icon_warning.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JZNHMeYy0TYHnuF1xNDoB1t2hC6MtPndwBTk4OAg/28=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/img_avatar_new.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+qrojCQpjXTzzM49j1ExZEcbYIOJxTZCPlTGHT8Ya/Y=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/normol_border.png</key>
		<dict>
			<key>hash2</key>
			<data>
			D35nuMB8WM2xNktMmYl46MRmmGzKO0N39hWOia/Mjf4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_multi_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			L4TYQ3U0P5eM3rkTw3tsuyBhGkq/A/ZdJ5tnaw6pCBM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_single_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			jqwrVZssN9f3oiNUAATu3IjkWA7NxWgqkJCF42GZuak=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/disable_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			SivNzTCR9mDJ2n7WoGDiSQd0BfrAjFfX5AAQ5CupF9g=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/multiple_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ip8kNoCHI1xPzxGg+CA7LiIauM2aWkG7QS8l8MBVI7c=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/single_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JInKNcRywA+kNp77dUo3TGvNKvhBCs842gPzJABZ1+A=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/icons/radio/unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ptq2/RSEbbmQGSsPp+vHiUHMhyA2sbo/2L6IGquR9Bs=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/content_failed.png</key>
		<dict>
			<key>hash2</key>
			<data>
			RA0W888IK4T23hi8czHw8VxbcRzy6o1j7el/h3qQXdo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/empty_state.png</key>
		<dict>
			<key>hash2</key>
			<data>
			prodfqj+yGPlZli1s5h3BBSV9ZTHsqiNIj1kzplIw0A=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			am0GCnrphfbQUQeBCV9Ey40VNAeJZmCiIXbJ8gXsxRI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_selected.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			WgqddoaQMzCIBTBdaLAjxaRAJmZ2WlGJF5LUBzKxijY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_bad_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7YPEf5GX6WcZrF9PtdNg6unvmx5rTaFQlQi83HasA80=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8JgL680f2hdoYaxctCBEJAXWIqpcIuDOOBEG3Ofx8ho=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_selected.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			k6fz10/PnS9OiJIfFNdJ2sQC65WuqQISfRLnwukuTJg=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_good_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			8YnihXyMMi3YTY1EUP5ujPRkpWCdqQwTS+8N/2Qa+CI=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			k79q9g81+mgdj7EQvDXZENZ1mK8bjJ8DW3NFuVS0bEo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_selected.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			bfp56SOtQ88n++UCJv2OC3tlN/M630wL5hjhxjLWby4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_not_good_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			c/zDvlhxbgwEVdtb92fT9vjHhEV8gN0hjZKSrWkNzr0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4ICkIS0TSO3M5wENDD7opfbw0tS41vKSLfGMgc0ER70=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_selected.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			Rek2vr1va/xaDwwf5oyTSi80n3Hf/MpZ/mT/vwuF5yw=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_ok_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			mzcxgw2XYDXkDmCNqxwGl99nQXdvpoOIKaT0OwWsKFg=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			/eLqDLydCwAXd4QiRLWUpSuunssc5mnkn/lvwWCDd5w=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_selected.gif</key>
		<dict>
			<key>hash2</key>
			<data>
			ctJgperCx5EbBUwJINYvCnWer+yVqntfdRYRxt33TB4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_appraise_surprise_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			d60SF1q7NGUbqMr/BpnuKAqMGNB1H6uGcbdsX3/Ge0Y=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_down_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			slsFRyO5R+PgL0njrhRi+lJwdAdpWiv2tDv8XpNSI7o=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_down_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			leQrKEGDMngl+7sYTPWx27YcgWhhv5HH4GWdPtzh1gc=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_arrow_up_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			qzqXEiYSO7NBXlwcEBeVM+hy5QG6c9OWGj7MR2MnJso=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_bottom_picker_right_top_bg.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+6lkecZzHPcUhkXQcvhFesRmFKJHZStPxew4RsKO1A8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_call.png</key>
		<dict>
			<key>hash2</key>
			<data>
			j066ZQRmOsI+dhqWlj6f5AZMe/nv+7n2H4fo8Rxv8/c=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_call_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			71bEgQ1oFRUiZ5ujUIXBMid2li/9zT4UTrgPCfec5aM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_custom_share.png</key>
		<dict>
			<key>hash2</key>
			<data>
			etT7IlLwQVW9trM4rESYwKR1GecrsFTWb/q5Yzz/p6Y=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_guanbi.png</key>
		<dict>
			<key>hash2</key>
			<data>
			6x0s5tEjyfvGEHZjwFeqUu+qLHv/YOJ2p8mhCpNzTh8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_im.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Gf1AR52B2jZXQgYWcMtPvvydH6ea9IDky9gHKOsifdA=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_im_blue.png</key>
		<dict>
			<key>hash2</key>
			<data>
			UOagVDJkKlxvRyHIzFgnrMqWCdHWj6ow4IN3ZEWoP0I=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_more.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JJIgx/hJpSCSYWFJzGLL4che8w/Z8kF/Xnj+U9TyeN8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_multi_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ip8kNoCHI1xPzxGg+CA7LiIauM2aWkG7QS8l8MBVI7c=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_qrcode_bg.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ED1fc5iR45a4B+br08Xz2zPbMCOJFwRsjchwZH0FebM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_qrcode_failed.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lwbfvpyYcRmURkt+KPYmCMtVaQkK8XqfxTwmiafsL1U=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_refresh.png</key>
		<dict>
			<key>hash2</key>
			<data>
			2lZ6cY/QPZFQJE2JvarMT07Jl4Ic6GUfHgbalT39WEM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_refresh_white.png</key>
		<dict>
			<key>hash2</key>
			<data>
			kWR+gzCGn+BuGnosE/i083gWqu9rSSjH2J74caHHRxM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_selection_reset.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ces9rHJIQos3TZW3s+3ugZ47gdhi0WmiEqf1Ad2pI2o=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_browser.png</key>
		<dict>
			<key>hash2</key>
			<data>
			HC6n2OX5ZQPjKikkcRMw0BzUPQN8sEzRR5Goj5kp9zk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_browser_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			uXLE9N3m49N/oGUwerSPh1PdzqkIKGWxMAAfDc8N50U=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_copy.png</key>
		<dict>
			<key>hash2</key>
			<data>
			da7GBPw5H1ouyCvDvjFcaLnawnYj1Jms32uSROrobuQ=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_copy_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QikvL1VH8dJHJlEZ4UsZo1VdHWGhKpmbtF1Cmwfm9Ko=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_message.png</key>
		<dict>
			<key>hash2</key>
			<data>
			L112aJxGwhJnaXUKrA0gCTw/y//ycxf9g5o2NvOdO2M=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_message_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			91YYAgs3xvgduEvwIUjc6Ya/zaQNGrVo3bTwRxlUVCM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_moments.png</key>
		<dict>
			<key>hash2</key>
			<data>
			CyGW90p6wbPia70Y0cbi1yn9BeFEJboS2toKCaNk3qE=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_moments_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Nta4oGrkgjB/t2//y3/sxsfWUokJfl133/a/nt1KfdY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_qq.png</key>
		<dict>
			<key>hash2</key>
			<data>
			QzpOVgcNtySzbxZfGzqPepyM5UVCG3ONVJEfq9//p9Y=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_qq_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			vLndCyMw2I29wPQnKvnlO7QYN/5CrJrrDTwlFlBAUHE=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_save_image.png</key>
		<dict>
			<key>hash2</key>
			<data>
			cyXuD1yvddgZs/Ty1lf0Ny13yIPMAOlyHMGNgX0sJ/k=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_save_image_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5uKtboJBrQfEb7lKceqZZzA1G4z0FH7B7pWNk80LZR0=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_shareLink.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ZTr6OgFnhhwXgBsYgrnsYeN22iSwC6lMIez8k12Ed8c=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_shareLink_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			7Q/mVQgRtjULcB9/itrG6oivnAMaYp2f7IKSTnhYZxo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weChat.png</key>
		<dict>
			<key>hash2</key>
			<data>
			lLx+aLYD1CZW3EVYHx+lR/uLm+rvsMDW1OETfoSDmOM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_wechat_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			zGe009NtkFBs1xlbCgX5AJljNBie17RftWUexV462KM=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weibo.png</key>
		<dict>
			<key>hash2</key>
			<data>
			eTUbaqeQ2UYly5yWNxTXx27vqV4u5123gvTM38io1Fk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_weibo_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			FRRBFIJm7NMMBZsCRTEl1wF+hOuK1XLdZrL0Z3AdsnU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_zone.png</key>
		<dict>
			<key>hash2</key>
			<data>
			pHxto5DfU4nwDo9PSLohMU9mD8Alhx68vpUZ3xyqGa8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_share_zone_disable.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MMoZWjTBH8plw1HbrB7Kejro5kiaNfAs5TDnuNBaCxU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_single_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			JInKNcRywA+kNp77dUo3TGvNKvhBCs842gPzJABZ1+A=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_star_size.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VS63760fJ+htVTl963fmfFywLON8txulcI1wuxqvwz8=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_star_size_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			VcGUrGmC+8jXaQ7UWaJiXTrOCgfgzc7FSC00EXZJ8po=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/icon_unselect.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ptq2/RSEbbmQGSsPp+vHiUHMhyA2sbo/2L6IGquR9Bs=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_bg_blur.png</key>
		<dict>
			<key>hash2</key>
			<data>
			gVTperdnYDM7DlmFzB9goBcmEMd4BMYXd8WL6RIQf1I=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_bg_card.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Fb4nFQ8m8Ty4DsX7DSP0s4/WUUtLvV9p8OI3FukBdyY=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_network.png</key>
		<dict>
			<key>hash2</key>
			<data>
			fYSj6j+STy8zRGe+5eefnIjlCBck7VdNyLm7CO021a4=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_selecetd_default.png</key>
		<dict>
			<key>hash2</key>
			<data>
			K9HTBpM27dpAaGo/taGAtb9jiRJxDDBV923iIbCdqbk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_step_title.png</key>
		<dict>
			<key>hash2</key>
			<data>
			+0dznhVqWnBvyYIA9r7jupeiYEiQrYVw9Iq8/NK82hk=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/img_unselected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			GGRKlMBtvRow8edfZSG5otv6eESJKxM3ibGnpN6A/Oo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/multi_select_btn_grey.png</key>
		<dict>
			<key>hash2</key>
			<data>
			Ptq2/RSEbbmQGSsPp+vHiUHMhyA2sbo/2L6IGquR9Bs=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/multi_select_btn_selected.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ip8kNoCHI1xPzxGg+CA7LiIauM2aWkG7QS8l8MBVI7c=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/network_error.png</key>
		<dict>
			<key>hash2</key>
			<data>
			5uKWVQ/zKeNRXtuORwPwclCHzKemBgbbe/TP0xgjPrE=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/no_data.png</key>
		<dict>
			<key>hash2</key>
			<data>
			ksREBwfB0eSksS+4PRvnOTt+VaTJ/BTQfLf0cuQGylU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/refresh_arrow_down.png</key>
		<dict>
			<key>hash2</key>
			<data>
			MLEz2tvRDQZND0JaxrN690JloHXxGXfbiHzCOFUF1qU=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/refresh_arrow_up.png</key>
		<dict>
			<key>hash2</key>
			<data>
			z/2PV5Fpq/t9tVba2rzMNCf1ikAOc95AqlY5/9SDnyo=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/select_checked_status.png</key>
		<dict>
			<key>hash2</key>
			<data>
			4E09SQ3mzdCZQVoCPsjBF4GenfU7zzukothlglSSTKA=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/images/two_line_bubble.png</key>
		<dict>
			<key>hash2</key>
			<data>
			c5vpJAqYj2P+I9fe9FVV0fSrCM6BvuLbINb22YL3ANc=
			</data>
		</dict>
		<key>flutter_assets/packages/bruno/assets/json/china.json</key>
		<dict>
			<key>hash2</key>
			<data>
			Bc5R5FB1a35wXKSu9igbDa8+XaURI17lvXy7NiwRR8E=
			</data>
		</dict>
		<key>flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Z8RP6Rg7AC553ef2l34piGYcmj5KPF/OloeH79vtgjw=
			</data>
		</dict>
		<key>flutter_assets/packages/flutter_image_compress_web/assets/pica.min.js</key>
		<dict>
			<key>hash2</key>
			<data>
			Chn6pGY+MG8wbFljM1nwrFZDT4pAa4TH/oRx9FLNmgM=
			</data>
		</dict>
		<key>flutter_assets/packages/tdesign_flutter/assets/tdesign/TCloudNumberVF.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			9xL3S9tTQ8EtaAGzSf1aGwfFS5qzECMJiAO5jcKsYGM=
			</data>
		</dict>
		<key>flutter_assets/packages/tdesign_flutter/assets/tdesign/td_icons.ttf</key>
		<dict>
			<key>hash2</key>
			<data>
			Ncs1DlZ5SGxmWNdyOIAIZSK6JwWbxv2M8oJPHRu27x8=
			</data>
		</dict>
		<key>flutter_assets/shaders/ink_sparkle.frag</key>
		<dict>
			<key>hash2</key>
			<data>
			49CVVGC0BQtpsarzJBllXjeVYst0OhUVq6Izlb7jGMQ=
			</data>
		</dict>
		<key>flutter_assets/vm_snapshot_data</key>
		<dict>
			<key>hash2</key>
			<data>
			sev5a27JT/ZUSpHrGmWBmxjqtUgq1PyqOI5pHwvTLwg=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
