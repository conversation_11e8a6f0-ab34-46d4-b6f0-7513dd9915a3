<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>files</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<data>
		Jfv2Vo3itUyjWGSxXIEV/9Skri8=
		</data>
		<key>Headers/FlutterAppDelegate.h</key>
		<data>
		QU3ZohDucENpee9k2C4s7Ovkwxw=
		</data>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<data>
		Qj6OtkzORWI38PIdXyyJc+eribM=
		</data>
		<key>Headers/FlutterCallbackCache.h</key>
		<data>
		lTUaQzdBsC/YVTRn48+amlv1iw4=
		</data>
		<key>Headers/FlutterChannels.h</key>
		<data>
		kk9YCchXApX3mGBMk6LGcCNaH1g=
		</data>
		<key>Headers/FlutterCodecs.h</key>
		<data>
		0hdw5jbuAAJJC64dk/bhma5HKIw=
		</data>
		<key>Headers/FlutterDartProject.h</key>
		<data>
		ONyPj3D2igT9nx6hVIYtVA5JFG0=
		</data>
		<key>Headers/FlutterEngine.h</key>
		<data>
		cupY3Xn8Bw2kFlGmh/gwYQCFMkU=
		</data>
		<key>Headers/FlutterEngineGroup.h</key>
		<data>
		nZcTgHGDD30QzPLlQfP8gP+S06o=
		</data>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<data>
		XnDDN+yQj6qLXTuhI0tgTMDbtbI=
		</data>
		<key>Headers/FlutterMacros.h</key>
		<data>
		T2wuUnVU/0f9v8Mc7CGjJQWLdGU=
		</data>
		<key>Headers/FlutterPlatformViews.h</key>
		<data>
		JofRibXJB+HPxhe0SAphfoKFSTE=
		</data>
		<key>Headers/FlutterPlugin.h</key>
		<data>
		s6183bDVVo6Dhe3jTvu6Lxr0Qj0=
		</data>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<data>
		eBwJo3E6jQLlhEPIm5a+XAUx3o0=
		</data>
		<key>Headers/FlutterTexture.h</key>
		<data>
		yBJovGku2dvjTDXp2km51wNc8yM=
		</data>
		<key>Headers/FlutterViewController.h</key>
		<data>
		6nBSlZSj+EZbyrAd2cS7H0pF3bA=
		</data>
		<key>Info.plist</key>
		<data>
		0GnJAIdPKLsHIVIgCB46/39Yos8=
		</data>
		<key>Modules/module.modulemap</key>
		<data>
		wJV5dCKEGl+FAtDc8wJJh/fvKXs=
		</data>
		<key>icudtl.dat</key>
		<data>
		Ubat0LvE4LUgCwHeyl0Anx2vnzk=
		</data>
	</dict>
	<key>files2</key>
	<dict>
		<key>Headers/Flutter.h</key>
		<dict>
			<key>hash2</key>
			<data>
			uwm8JZgId56AcAI6HgoYvB86L3U5XLTdogukvzdieH0=
			</data>
		</dict>
		<key>Headers/FlutterAppDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			ZE5n3wyEphmq0NvXEk+TgX9+IH2WWIRdDoZj+8Zmu+E=
			</data>
		</dict>
		<key>Headers/FlutterBinaryMessenger.h</key>
		<dict>
			<key>hash2</key>
			<data>
			4quj8X+HDK/AVExMhTcqLpIRxe/OjnnNTiON5KEv3hI=
			</data>
		</dict>
		<key>Headers/FlutterCallbackCache.h</key>
		<dict>
			<key>hash2</key>
			<data>
			1dNDb41OVcGRhdnQfLHPfpfNUSb9f9Dmg57Bqo4gJv0=
			</data>
		</dict>
		<key>Headers/FlutterChannels.h</key>
		<dict>
			<key>hash2</key>
			<data>
			DiCjzJtPXXO1x7uREO3UusxUMr++I+7wucfuPcuy9YE=
			</data>
		</dict>
		<key>Headers/FlutterCodecs.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Y1JL+Rn5pGoCA+qXfiSPa8dGiXxlpiHY4NzmmjVEuaY=
			</data>
		</dict>
		<key>Headers/FlutterDartProject.h</key>
		<dict>
			<key>hash2</key>
			<data>
			Z2cWf9G2hrQXXwHW+2ilnnP/58EqDYhte+THHob5IsI=
			</data>
		</dict>
		<key>Headers/FlutterEngine.h</key>
		<dict>
			<key>hash2</key>
			<data>
			nqtk8YLddo2rPiYBl7tgIviV+uOyWZO9Ey3zKZBCG2o=
			</data>
		</dict>
		<key>Headers/FlutterEngineGroup.h</key>
		<dict>
			<key>hash2</key>
			<data>
			I+878Ez9ZpVdAR2GOzKJKjaZ5m807AeAF++2pSMQss4=
			</data>
		</dict>
		<key>Headers/FlutterHeadlessDartRunner.h</key>
		<dict>
			<key>hash2</key>
			<data>
			sELlVsLARG1gBlPndKt24VxGVmBMgcXWeShflcVtZBQ=
			</data>
		</dict>
		<key>Headers/FlutterMacros.h</key>
		<dict>
			<key>hash2</key>
			<data>
			IEPKCkPf/10aPZiU0dKCx8/ZoIfvkjycwKB4vH8mwG4=
			</data>
		</dict>
		<key>Headers/FlutterPlatformViews.h</key>
		<dict>
			<key>hash2</key>
			<data>
			c4TS8HplkxEc+09dBX5h+BZ+vkI9QJU/3ljud7WmdTM=
			</data>
		</dict>
		<key>Headers/FlutterPlugin.h</key>
		<dict>
			<key>hash2</key>
			<data>
			letSh4jQvg70UeTZ+wjzYDOxUtryqDuNfxc0A8zgy58=
			</data>
		</dict>
		<key>Headers/FlutterPluginAppLifeCycleDelegate.h</key>
		<dict>
			<key>hash2</key>
			<data>
			yxMe4j+1eEByv8dhzIAxAdnDSnF01UZxK1q9r8DtR84=
			</data>
		</dict>
		<key>Headers/FlutterTexture.h</key>
		<dict>
			<key>hash2</key>
			<data>
			VkMu2v4CZSBLhGWCJdxdAPzVFOgUvVyyxpdwmfAnWrY=
			</data>
		</dict>
		<key>Headers/FlutterViewController.h</key>
		<dict>
			<key>hash2</key>
			<data>
			P0GsFW0zzP6Yi8y8zBx1bdWpXoBcaVpIbO3lgDBZoDY=
			</data>
		</dict>
		<key>Modules/module.modulemap</key>
		<dict>
			<key>hash2</key>
			<data>
			0VjriRpZ7AZZaP/0mMAPMJPhi6LoMB4MhXzL5j24tGs=
			</data>
		</dict>
		<key>icudtl.dat</key>
		<dict>
			<key>hash2</key>
			<data>
			ZepykIMSjfzhwAcmupMrkaqvXkhza1ZE3TdHjl8odaw=
			</data>
		</dict>
	</dict>
	<key>rules</key>
	<dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^version.plist$</key>
		<true/>
	</dict>
	<key>rules2</key>
	<dict>
		<key>.*\.dSYM($|/)</key>
		<dict>
			<key>weight</key>
			<real>11</real>
		</dict>
		<key>^(.*/)?\.DS_Store$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>2000</real>
		</dict>
		<key>^.*</key>
		<true/>
		<key>^.*\.lproj/</key>
		<dict>
			<key>optional</key>
			<true/>
			<key>weight</key>
			<real>1000</real>
		</dict>
		<key>^.*\.lproj/locversion.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>1100</real>
		</dict>
		<key>^Base\.lproj/</key>
		<dict>
			<key>weight</key>
			<real>1010</real>
		</dict>
		<key>^Info\.plist$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^PkgInfo$</key>
		<dict>
			<key>omit</key>
			<true/>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^embedded\.provisionprofile$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
		<key>^version\.plist$</key>
		<dict>
			<key>weight</key>
			<real>20</real>
		</dict>
	</dict>
</dict>
</plist>
